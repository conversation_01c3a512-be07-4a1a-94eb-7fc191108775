#!/usr/bin/env python3
"""
测试Facebook应用打开检测
"""
import subprocess
import time

def open_facebook_app():
    """打开Facebook应用"""
    print('=== 打开Facebook应用 ===')
    try:
        # 尝试启动Facebook主应用
        result = subprocess.run(
            ["adb", "shell", "am", "start", "-n", "com.facebook.katana/.LoginActivity"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print('✅ Facebook应用启动命令执行成功')
            print(f'   输出: {result.stdout.strip()}')
        else:
            print(f'❌ Facebook应用启动失败: {result.stderr}')
            
            # 尝试通用启动方式
            result = subprocess.run(
                ["adb", "shell", "monkey", "-p", "com.facebook.katana", "-c", "android.intent.category.LAUNCHER", "1"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print('✅ 通过monkey启动Facebook应用成功')
            else:
                print(f'❌ monkey启动也失败: {result.stderr}')
                
    except Exception as e:
        print(f'异常: {e}')

def test_facebook_detection_when_open():
    """测试Facebook应用打开时的检测"""
    print('\n=== 测试Facebook应用打开时的检测 ===')
    
    # 等待应用启动
    print('等待3秒让应用完全启动...')
    time.sleep(3)
    
    try:
        from pages.apps.ella.ella_app_detector import EllaAppDetector

        detector = EllaAppDetector()
        is_facebook_open = detector.check_facebook_app_opened()
        print(f'Facebook应用检测结果: {is_facebook_open}')
        
        if is_facebook_open:
            print('✅ 成功检测到Facebook应用已打开')
        else:
            print('❌ 未检测到Facebook应用打开，可能需要进一步优化')
            
    except Exception as e:
        print(f'检测失败: {e}')

def check_current_foreground_app():
    """检查当前前台应用"""
    print('\n=== 检查当前前台应用 ===')
    try:
        # 方法1: 检查当前焦点窗口
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "mCurrentFocus"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print(f'当前焦点窗口: {result.stdout.strip()}')
        else:
            print('未获取到焦点窗口信息')
            
        # 方法2: 检查顶部Activity
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "top"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')[:10]  # 只看前10行
            print('顶部Activity信息:')
            for line in lines:
                if line.strip():
                    print(f'  {line.strip()}')
                    
    except Exception as e:
        print(f'检查失败: {e}')

if __name__ == '__main__':
    # 1. 先检查当前状态
    print('=== 步骤1: 检查当前状态 ===')
    from pages.apps.ella.ella_app_detector import EllaAppDetector
    detector = EllaAppDetector()
    initial_status = detector.check_facebook_app_opened()
    print(f'初始Facebook状态: {initial_status}')
    
    # 2. 打开Facebook应用
    print('\n=== 步骤2: 打开Facebook应用 ===')
    open_facebook_app()
    
    # 3. 检查前台应用
    check_current_foreground_app()
    
    # 4. 测试检测功能
    test_facebook_detection_when_open()
    
    print('\n=== 测试完成 ===')
