"""
集成弹窗处理的基础页面类 - 适配UIAutomator2
继承原有BasePage并添加弹窗处理功能
"""
import time
import uiautomator2 as u2
from typing import Tuple, Any, Union
from core.base_driver import BaseDriver
from core.popup_monitor import PopupMonitor, PopupConfig
from utils.popup_utils import with_popup_handling, handle_popups_on_error
from core.logger import log


class BasePageWithPopup(BaseDriver):
    """集成弹窗处理的基础页面类 - 适配UIAutomator2"""

    def __init__(self, device_id: str = None):
        super().__init__(device_id)

        # 初始化弹窗处理
        self.popup_config = PopupConfig()
        self.popup_monitor = PopupMonitor(self.driver, self.popup_config)
        self.auto_handle_popups = True
        self._popup_monitoring_enabled = False

        # 配置弹窗处理回调
        self._setup_popup_callbacks()
    
    def _setup_popup_callbacks(self):
        """设置弹窗处理回调"""
        def on_popup_detected(popup_info):
            log.debug(f"检测到弹窗: {popup_info.type} (置信度: {popup_info.confidence:.2f})")
        
        def on_popup_handled(popup_info, result):
            if result.success:
                log.info(f"✅ 成功处理弹窗: {popup_info.type} - {result.method}")
            else:
                log.warning(f"❌ 处理弹窗失败: {popup_info.type} - {result.error}")
        
        def on_monitoring_error(error):
            log.error(f"弹窗监控错误: {error}")
        
        self.popup_monitor.set_callback('popup_detected', on_popup_detected)
        self.popup_monitor.set_callback('popup_handled', on_popup_handled)
        self.popup_monitor.set_callback('monitoring_error', on_monitoring_error)
    
    def enable_popup_monitoring(self):
        """启用弹窗监控"""
        if not self._popup_monitoring_enabled:
            self.popup_monitor.start_monitoring()
            self._popup_monitoring_enabled = True
            log.info("页面弹窗监控已启用")
    
    def disable_popup_monitoring(self):
        """禁用弹窗监控"""
        if self._popup_monitoring_enabled:
            self.popup_monitor.stop_monitoring()
            self._popup_monitoring_enabled = False
            log.info("页面弹窗监控已禁用")
    
    def handle_popups_immediately(self, popup_type: str = None) -> bool:
        """立即处理弹窗"""
        return self.popup_monitor.handle_popup_immediately(popup_type)
    
    def configure_popup_handling(self, **kwargs):
        """配置弹窗处理参数"""
        for key, value in kwargs.items():
            if key == 'auto_handle':
                self.auto_handle_popups = value
            elif key == 'min_confidence':
                self.popup_config.config['detection']['min_confidence'] = value
            elif key == 'max_retries':
                self.popup_config.config['handling']['max_retries'] = value
            elif key == 'detection_interval':
                self.popup_config.config['detection']['interval'] = value
            else:
                log.warning(f"未知的配置参数: {key}")
        
        self.popup_config.save_config()
    
    def add_custom_popup_rule(self, popup_type: str, rules: dict):
        """添加自定义弹窗规则"""
        self.popup_monitor.add_custom_popup_rule(popup_type, rules)
    
    # 重写基础操作方法，集成弹窗处理
    
    @with_popup_handling(handle_before=True, handle_after=True)
    def click(self, selector: Union[str, dict], timeout: int = 10, description: str = ""):
        """点击元素 - 自动处理弹窗 - UIAutomator2版本"""
        try:
            element = self.wait_for_element(selector, timeout, description)

            # 点击前截图
            screenshot_path = self.screenshot(f"before_click_{description or 'element'}.png")
            log.info(f"点击前截图: {screenshot_path}")

            element.click()
            log.info(f"✅ 成功点击元素: {description or selector}")

            # 点击后稍等，让可能的弹窗出现
            time.sleep(0.5)

            return element

        except Exception as e:
            screenshot_path = self.screenshot(f"click_failed_{description or 'element'}.png")
            log.error(f"❌ 点击元素失败: {description or selector} - {e}")
            log.error(f"失败截图: {screenshot_path}")
            raise
    
    @with_popup_handling(handle_before=True, handle_after=False)
    def input_text(self, selector: Union[str, dict], text: str, timeout: int = 10, description: str = ""):
        """输入文本 - 自动处理弹窗 - UIAutomator2版本"""
        try:
            element = self.wait_for_element(selector, timeout, description)

            # 清空并输入文本
            element.clear_text()
            element.set_text(text)
            log.info(f"✅ 成功输入文本: {description or selector} = '{text}'")

            return element

        except Exception as e:
            screenshot_path = self.screenshot(f"input_failed_{description or 'element'}.png")
            log.error(f"❌ 输入文本失败: {description or selector} - {e}")
            log.error(f"失败截图: {screenshot_path}")
            raise
    
    @handle_popups_on_error(max_retries=3, delay=1.0)
    def wait_for_element(self, selector: Union[str, dict], timeout: int = 10, description: str = ""):
        """等待元素出现 - 处理可能的弹窗干扰 - UIAutomator2版本"""
        try:
            log.info(f"等待元素出现: {description or selector}")

            # 使用UIAutomator2的等待方法
            if isinstance(selector, str):
                # 如果是字符串，假设是resourceId
                element = self.driver(resourceId=selector)
            elif isinstance(selector, dict):
                # 如果是字典，使用字典参数
                element = self.driver(**selector)
            else:
                raise ValueError(f"不支持的选择器类型: {type(selector)}")

            # 等待元素出现
            if element.wait(timeout=timeout):
                log.info(f"✅ 元素已出现: {description or selector}")
                return element
            else:
                raise TimeoutError(f"等待元素超时: {description or selector}")

        except Exception as e:
            # 超时时尝试处理弹窗
            log.warning(f"等待元素失败，尝试处理弹窗: {description or selector}")
            self.handle_popups_immediately()

            screenshot_path = self.screenshot(f"wait_timeout_{description or 'element'}.png")
            log.error(f"等待元素失败截图: {screenshot_path}")
            raise
    
    @with_popup_handling(handle_before=True, handle_after=True)
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5):
        """滑动操作 - 自动处理弹窗 - UIAutomator2版本"""
        try:
            # 使用UIAutomator2的滑动方法
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration)
            log.info(f"✅ 滑动操作完成: ({start_x},{start_y}) -> ({end_x},{end_y})")

            # 滑动后稍等
            time.sleep(0.5)

        except Exception as e:
            screenshot_path = self.screenshot("swipe_failed.png")
            log.error(f"❌ 滑动操作失败: {e}")
            log.error(f"失败截图: {screenshot_path}")
            raise
    
    def wait_for_page_load(self, timeout: int = 15, check_interval: float = 0.5) -> bool:
        """等待页面加载完成 - 处理加载过程中的弹窗"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查页面是否加载完成
                if self.is_page_loaded():
                    log.info("✅ 页面加载完成")
                    return True
                
                # 处理加载过程中可能出现的弹窗
                self.handle_popups_immediately()
                
                time.sleep(check_interval)
                
            except Exception as e:
                log.debug(f"页面加载检查异常: {e}")
                time.sleep(check_interval)
        
        log.warning(f"⚠️ 页面加载超时: {timeout}秒")
        screenshot_path = self.screenshot("page_load_timeout.png")
        log.warning(f"超时截图: {screenshot_path}")
        return False
    
    def is_page_loaded(self) -> bool:
        """检查页面是否加载完成 - 子类应重写此方法 - UIAutomator2版本"""
        try:
            # 使用UIAutomator2获取页面源码
            page_source = self.driver.dump_hierarchy()
            if not page_source or len(page_source) < 100:
                return False

            loading_keywords = ['loading', '加载中', '请稍候', 'please wait']
            return not any(keyword in page_source.lower() for keyword in loading_keywords)

        except Exception:
            return False
    
    def screenshot(self, filename: str = None) -> str:
        """截图 - 重写以支持弹窗信息"""
        screenshot_path = super().screenshot(filename)
        
        # 如果启用了调试模式，添加弹窗信息到截图
        if self.popup_config.config.get('debug', {}).get('enabled', False):
            try:
                popups = self.popup_monitor.handler.detector.detect_popups()
                if popups:
                    popup_info = f"检测到{len(popups)}个弹窗: " + ", ".join([p.type for p in popups])
                    log.info(f"截图弹窗信息: {popup_info}")
            except Exception as e:
                log.debug(f"获取弹窗信息失败: {e}")
        
        return screenshot_path
    
    def get_popup_stats(self) -> dict:
        """获取弹窗处理统计信息"""
        return self.popup_monitor.get_monitor_stats()
    
    def cleanup(self):
        """清理资源"""
        try:
            # 停止弹窗监控
            self.disable_popup_monitoring()
            
            # 调用父类清理
            if hasattr(super(), 'cleanup'):
                super().cleanup()
                
        except Exception as e:
            log.error(f"页面清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass


# 使用示例和最佳实践
"""
# 1. 基本使用
class MyAppPage(BasePageWithPopup):
    def __init__(self, driver):
        super().__init__(driver)
        
        # 启用弹窗监控
        self.enable_popup_monitoring()
        
        # 配置弹窗处理
        self.configure_popup_handling(
            auto_handle=True,
            min_confidence=0.7,
            max_retries=3
        )
    
    def login(self, username, password):
        # 这些操作会自动处理弹窗
        self.input_text(("id", "username"), username)
        self.input_text(("id", "password"), password)
        self.click(("id", "login_button"))

# 2. 添加自定义弹窗规则
page = MyAppPage(driver)
page.add_custom_popup_rule("custom_ad", {
    'keywords': ['广告', '推广'],
    'buttons': ['关闭', '跳过'],
    'package': 'com.myapp'
})

# 3. 手动处理特定弹窗
page.handle_popups_immediately("permission_dialog")

# 4. 获取统计信息
stats = page.get_popup_stats()
print(f"处理了{stats['handler_stats']['total_handled']}个弹窗")
"""
