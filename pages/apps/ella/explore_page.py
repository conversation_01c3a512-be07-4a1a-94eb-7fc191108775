"""
Ella语音助手探索页面
TECNO设备的AI语音助手应用探索/发现页面对象
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from core.logger import log
import time


class EllaExplorePage(CommonPage):
    """Ella语音助手探索页面"""
    
    def __init__(self):
        """初始化Ella探索页面"""
        super().__init__("ella", "explore_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素 - 探索页面特有元素"""
        # 应用包名验证
        self.app_package = self.create_element(
            {"packageName": "com.transsion.aivoiceassistant"},
            "Ella应用包"
        )
        
        # 探索页面标题
        self.explore_title = self.create_element(
            {"text": "探索"},
            "探索页面标题"
        )
        
        self.explore_title_en = self.create_element(
            {"text": "Explore"},
            "探索页面标题(英文)"
        )
        
        # 搜索框
        self.search_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_search"},
            "搜索框"
        )
        
        self.search_box_alt = self.create_element(
            {"className": "android.widget.EditText", "hint": "搜索"},
            "搜索框(备选)"
        )
        
        # 搜索按钮
        self.search_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_search"},
            "搜索按钮"
        )
        
        # 语音搜索按钮
        self.voice_search_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_voice_search"},
            "语音搜索按钮"
        )
        
        # 分类标签
        self.category_tabs = self.create_element(
            {"className": "android.widget.HorizontalScrollView"},
            "分类标签容器"
        )
        
        # 推荐内容列表
        self.recommendation_list = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "推荐内容列表"
        )
        
        # 热门话题
        self.hot_topics = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_hot_topics"},
            "热门话题列表"
        )
        
        # 功能卡片
        self.feature_cards = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_features"},
            "功能卡片列表"
        )
        
        # 刷新按钮
        self.refresh_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_refresh"},
            "刷新按钮"
        )
        
        # 设置按钮
        self.settings_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_settings"},
            "设置按钮"
        )
        
        # 返回主页按钮
        self.home_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_home"},
            "返回主页按钮"
        )
        
        # 底部导航栏
        self.bottom_navigation = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/bottom_navigation"},
            "底部导航栏"
        )
        
        # 探索页面导航项
        self.explore_nav_item = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/nav_explore"},
            "探索导航项"
        )
        
        # 主页导航项
        self.home_nav_item = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/nav_home"},
            "主页导航项"
        )
        
        # 内容项目（通用）
        self.content_item = self.create_element(
            {"className": "android.widget.LinearLayout"},
            "内容项目"
        )
        
        # 加载更多按钮
        self.load_more_button = self.create_element(
            {"text": "加载更多"},
            "加载更多按钮"
        )
        
        # 空状态提示
        self.empty_state = self.create_element(
            {"text": "暂无内容"},
            "空状态提示"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待探索页面加载完成

        Args:
            timeout: 超时时间，默认为10秒

        Returns:
            bool: 页面是否加载完成
        """
        # 设置默认超时时间
        if timeout is None:
            timeout = 10

        log.info(f"等待Ella探索页面加载完成，超时时间: {timeout}秒")

        try:
            # 首先等待应用包出现，确认应用已启动
            if self.app_package.wait_for_element(timeout=5):
                log.info("✅ Ella应用包已加载")

                # 然后等待探索页面特有元素出现
                if self.explore_title.wait_for_element(timeout=timeout):
                    log.info("✅ 探索页面标题已加载")
                    return True
                elif self.explore_title_en.wait_for_element(timeout=timeout):
                    log.info("✅ 探索页面标题(英文)已加载")
                    return True
                elif self.search_box.wait_for_element(timeout=timeout):
                    log.info("✅ 探索页面搜索框已加载")
                    return True
                elif self.recommendation_list.wait_for_element(timeout=timeout):
                    log.info("✅ 推荐内容列表已加载")
                    return True
                else:
                    log.warning("⚠️ 未找到探索页面特有元素")
                    # 即使没有找到特定元素，如果应用包存在，也认为页面加载成功
                    return True
            else:
                log.error("❌ Ella应用包未加载")
                return False

        except Exception as e:
            log.error(f"等待Ella探索页面加载失败: {e}")
            return False
    
    def navigate_to_explore(self) -> bool:
        """
        导航到探索页面

        Returns:
            bool: 导航是否成功
        """
        try:
            log.info("导航到探索页面")

            # 方法1: 通过底部导航栏
            if self.explore_nav_item.is_exists():
                log.info("通过底部导航栏进入探索页面")
                if self.explore_nav_item.click():
                    # 等待页面加载
                    if self.wait_for_page_load(timeout=5):
                        log.info("✅ 成功导航到探索页面")
                        return True
                    else:
                        log.warning("导航后页面加载失败")

            # 方法2: 通过滑动手势（如果是滑动切换页面）
            log.info("尝试通过滑动手势进入探索页面")
            screen_width, screen_height = self.driver.window_size()
            
            # 从右向左滑动（假设探索页面在主页右侧）
            start_x = int(screen_width * 0.8)
            end_x = int(screen_width * 0.2)
            y = int(screen_height * 0.5)
            
            self.driver.swipe(start_x, y, end_x, y, duration=0.5)
            time.sleep(1)
            
            # 检查是否成功进入探索页面
            if self.wait_for_page_load(timeout=3):
                log.info("✅ 通过滑动成功导航到探索页面")
                return True

            # 方法3: 通过菜单或其他入口
            log.info("尝试通过其他方式进入探索页面")
            # 可以添加其他导航方法

            log.error("❌ 无法导航到探索页面")
            return False

        except Exception as e:
            log.error(f"导航到探索页面失败: {e}")
            return False
    
    def navigate_to_home(self) -> bool:
        """
        从探索页面返回主页

        Returns:
            bool: 返回是否成功
        """
        try:
            log.info("从探索页面返回主页")

            # 方法1: 通过底部导航栏
            if self.home_nav_item.is_exists():
                log.info("通过底部导航栏返回主页")
                if self.home_nav_item.click():
                    time.sleep(1)
                    log.info("✅ 成功返回主页")
                    return True

            # 方法2: 通过主页按钮
            if self.home_button.is_exists():
                log.info("通过主页按钮返回主页")
                if self.home_button.click():
                    time.sleep(1)
                    log.info("✅ 成功返回主页")
                    return True

            # 方法3: 通过滑动手势
            log.info("尝试通过滑动手势返回主页")
            screen_width, screen_height = self.driver.window_size()
            
            # 从左向右滑动
            start_x = int(screen_width * 0.2)
            end_x = int(screen_width * 0.8)
            y = int(screen_height * 0.5)
            
            self.driver.swipe(start_x, y, end_x, y, duration=0.5)
            time.sleep(1)
            log.info("✅ 通过滑动返回主页")
            return True

        except Exception as e:
            log.error(f"返回主页失败: {e}")
            return False
    
    def search_content(self, query: str) -> bool:
        """
        在探索页面搜索内容

        Args:
            query: 搜索关键词

        Returns:
            bool: 搜索是否成功
        """
        try:
            log.info(f"在探索页面搜索: {query}")

            # 确保在探索页面
            if not self.wait_for_page_load(timeout=3):
                log.error("不在探索页面，无法执行搜索")
                return False

            # 点击搜索框
            search_box = None
            if self.search_box.is_exists():
                search_box = self.search_box
                log.info("使用主搜索框")
            elif self.search_box_alt.is_exists():
                search_box = self.search_box_alt
                log.info("使用备选搜索框")

            if search_box:
                # 点击搜索框激活
                if search_box.click():
                    time.sleep(0.5)
                    
                    # 输入搜索内容
                    if search_box.send_keys(query):
                        log.info(f"✅ 搜索内容输入成功: {query}")
                        
                        # 点击搜索按钮或按回车
                        if self.search_button.is_exists():
                            self.search_button.click()
                            log.info("点击搜索按钮")
                        else:
                            # 按回车键搜索
                            self.driver.press("enter")
                            log.info("按回车键搜索")
                        
                        time.sleep(2)  # 等待搜索结果
                        log.info("✅ 搜索执行完成")
                        return True
                    else:
                        log.error("搜索内容输入失败")
                        return False
                else:
                    log.error("搜索框点击失败")
                    return False
            else:
                log.error("未找到搜索框")
                return False

        except Exception as e:
            log.error(f"搜索内容失败: {e}")
            return False

    def voice_search(self, query: str) -> bool:
        """
        使用语音搜索功能

        Args:
            query: 搜索关键词

        Returns:
            bool: 语音搜索是否成功
        """
        try:
            log.info(f"使用语音搜索: {query}")

            # 确保在探索页面
            if not self.wait_for_page_load(timeout=3):
                log.error("不在探索页面，无法执行语音搜索")
                return False

            # 点击语音搜索按钮
            if self.voice_search_button.is_exists():
                if self.voice_search_button.click():
                    log.info("✅ 语音搜索按钮点击成功")
                    time.sleep(1)

                    # 这里可以集成TTS功能来模拟语音输入
                    # 暂时使用文本输入作为备选方案
                    log.info("语音搜索启动，使用文本输入作为备选")
                    return self.search_content(query)
                else:
                    log.error("语音搜索按钮点击失败")
                    return False
            else:
                log.warning("未找到语音搜索按钮，使用普通搜索")
                return self.search_content(query)

        except Exception as e:
            log.error(f"语音搜索失败: {e}")
            return False

    def browse_category(self, category_name: str) -> bool:
        """
        浏览特定分类内容

        Args:
            category_name: 分类名称

        Returns:
            bool: 浏览是否成功
        """
        try:
            log.info(f"浏览分类: {category_name}")

            # 确保在探索页面
            if not self.wait_for_page_load(timeout=3):
                log.error("不在探索页面，无法浏览分类")
                return False

            # 查找分类标签
            if self.category_tabs.is_exists():
                # 尝试找到指定分类的标签
                category_element = self.create_element(
                    {"text": category_name},
                    f"分类标签: {category_name}"
                )

                if category_element.is_exists():
                    if category_element.click():
                        log.info(f"✅ 成功点击分类: {category_name}")
                        time.sleep(2)  # 等待内容加载
                        return True
                    else:
                        log.error(f"分类标签点击失败: {category_name}")
                        return False
                else:
                    log.warning(f"未找到分类标签: {category_name}")
                    return False
            else:
                log.error("未找到分类标签容器")
                return False

        except Exception as e:
            log.error(f"浏览分类失败: {e}")
            return False

    def refresh_content(self) -> bool:
        """
        刷新探索页面内容

        Returns:
            bool: 刷新是否成功
        """
        try:
            log.info("刷新探索页面内容")

            # 方法1: 点击刷新按钮
            if self.refresh_button.is_exists():
                if self.refresh_button.click():
                    log.info("✅ 点击刷新按钮成功")
                    time.sleep(3)  # 等待内容刷新
                    return True

            # 方法2: 下拉刷新
            log.info("尝试下拉刷新")
            screen_width, screen_height = self.driver.window_size()

            # 从顶部向下滑动
            start_x = screen_width // 2
            start_y = int(screen_height * 0.2)
            end_y = int(screen_height * 0.6)

            self.driver.swipe(start_x, start_y, start_x, end_y, duration=1.0)
            time.sleep(3)  # 等待刷新完成

            log.info("✅ 下拉刷新完成")
            return True

        except Exception as e:
            log.error(f"刷新内容失败: {e}")
            return False

    def load_more_content(self) -> bool:
        """
        加载更多内容

        Returns:
            bool: 加载是否成功
        """
        try:
            log.info("加载更多内容")

            # 方法1: 点击加载更多按钮
            if self.load_more_button.is_exists():
                if self.load_more_button.click():
                    log.info("✅ 点击加载更多按钮成功")
                    time.sleep(3)  # 等待内容加载
                    return True

            # 方法2: 滚动到底部触发自动加载
            log.info("滚动到底部加载更多内容")
            screen_width, screen_height = self.driver.window_size()

            # 向上滑动到底部
            start_x = screen_width // 2
            start_y = int(screen_height * 0.8)
            end_y = int(screen_height * 0.2)

            self.driver.swipe(start_x, start_y, start_x, end_y, duration=1.0)
            time.sleep(2)  # 等待加载

            log.info("✅ 滚动加载更多完成")
            return True

        except Exception as e:
            log.error(f"加载更多内容失败: {e}")
            return False

    def click_content_item(self, item_index: int = 0) -> bool:
        """
        点击内容项目

        Args:
            item_index: 项目索引，默认点击第一个

        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击内容项目，索引: {item_index}")

            # 确保在探索页面
            if not self.wait_for_page_load(timeout=3):
                log.error("不在探索页面，无法点击内容项目")
                return False

            # 查找推荐内容列表
            if self.recommendation_list.is_exists():
                # 获取列表中的项目
                content_items = self.driver(className="android.widget.LinearLayout")

                if content_items.exists() and content_items.count > item_index:
                    target_item = content_items[item_index] if content_items.count > 1 else content_items

                    if target_item.click():
                        log.info(f"✅ 成功点击内容项目: {item_index}")
                        time.sleep(2)  # 等待页面跳转
                        return True
                    else:
                        log.error(f"内容项目点击失败: {item_index}")
                        return False
                else:
                    log.warning(f"未找到指定索引的内容项目: {item_index}")
                    return False
            else:
                log.error("未找到推荐内容列表")
                return False

        except Exception as e:
            log.error(f"点击内容项目失败: {e}")
            return False

    def check_empty_state(self) -> bool:
        """
        检查是否显示空状态

        Returns:
            bool: 是否显示空状态
        """
        try:
            log.info("检查探索页面空状态")

            # 检查空状态提示
            if self.empty_state.is_exists():
                log.info("✅ 检测到空状态提示")
                return True

            # 检查是否有内容
            if self.recommendation_list.is_exists():
                content_items = self.driver(className="android.widget.LinearLayout")
                if content_items.exists() and content_items.count > 0:
                    log.info("✅ 检测到内容项目，非空状态")
                    return False
                else:
                    log.info("✅ 内容列表为空")
                    return True
            else:
                log.warning("无法确定页面状态")
                return False

        except Exception as e:
            log.error(f"检查空状态失败: {e}")
            return False

    def get_content_count(self) -> int:
        """
        获取当前页面内容数量

        Returns:
            int: 内容数量，获取失败返回-1
        """
        try:
            log.info("获取探索页面内容数量")

            if self.recommendation_list.is_exists():
                content_items = self.driver(className="android.widget.LinearLayout")
                if content_items.exists():
                    count = content_items.count
                    log.info(f"✅ 当前内容数量: {count}")
                    return count
                else:
                    log.info("✅ 内容数量: 0")
                    return 0
            else:
                log.warning("未找到内容列表")
                return -1

        except Exception as e:
            log.error(f"获取内容数量失败: {e}")
            return -1

    def scroll_to_top(self) -> bool:
        """
        滚动到页面顶部

        Returns:
            bool: 滚动是否成功
        """
        try:
            log.info("滚动到探索页面顶部")

            screen_width, screen_height = self.driver.window_size()

            # 从底部向顶部滑动
            start_x = screen_width // 2
            start_y = int(screen_height * 0.8)
            end_y = int(screen_height * 0.2)

            # 多次滑动确保到达顶部
            for i in range(3):
                self.driver.swipe(start_x, start_y, start_x, end_y, duration=0.5)
                time.sleep(0.3)

            log.info("✅ 滚动到顶部完成")
            return True

        except Exception as e:
            log.error(f"滚动到顶部失败: {e}")
            return False

    def scroll_to_bottom(self) -> bool:
        """
        滚动到页面底部

        Returns:
            bool: 滚动是否成功
        """
        try:
            log.info("滚动到探索页面底部")

            screen_width, screen_height = self.driver.window_size()

            # 从顶部向底部滑动
            start_x = screen_width // 2
            start_y = int(screen_height * 0.7)
            end_y = int(screen_height * 0.3)

            # 持续滑动直到到达底部
            for i in range(5):
                self.driver.swipe(start_x, start_y, start_x, end_y, duration=0.8)
                time.sleep(0.5)

                # 检查是否有"加载更多"按钮出现
                if self.load_more_button.is_exists():
                    log.info("检测到加载更多按钮，已到达底部")
                    break

            log.info("✅ 滚动到底部完成")
            return True

        except Exception as e:
            log.error(f"滚动到底部失败: {e}")
            return False

    def get_hot_topics(self) -> list:
        """
        获取热门话题列表

        Returns:
            list: 热门话题文本列表，获取失败返回空列表
        """
        try:
            log.info("获取热门话题列表")

            topics = []

            if self.hot_topics.is_exists():
                # 查找话题文本元素
                topic_elements = self.driver(className="android.widget.TextView")

                if topic_elements.exists():
                    for i in range(min(topic_elements.count, 10)):  # 最多获取10个话题
                        try:
                            element = topic_elements[i] if topic_elements.count > 1 else topic_elements
                            text = element.get_text()

                            if text and len(text.strip()) > 0:
                                # 过滤掉非话题文本
                                if not any(filter_word in text.lower() for filter_word in
                                         ['搜索', 'search', '设置', 'settings', '返回']):
                                    topics.append(text.strip())
                        except:
                            continue

                log.info(f"✅ 获取到 {len(topics)} 个热门话题")
                return topics
            else:
                log.warning("未找到热门话题列表")
                return []

        except Exception as e:
            log.error(f"获取热门话题失败: {e}")
            return []

    def click_hot_topic(self, topic_text: str) -> bool:
        """
        点击指定的热门话题

        Args:
            topic_text: 话题文本

        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击热门话题: {topic_text}")

            # 创建话题元素定位器
            topic_element = self.create_element(
                {"text": topic_text},
                f"热门话题: {topic_text}"
            )

            if topic_element.is_exists():
                if topic_element.click():
                    log.info(f"✅ 成功点击热门话题: {topic_text}")
                    time.sleep(2)  # 等待页面跳转或内容加载
                    return True
                else:
                    log.error(f"热门话题点击失败: {topic_text}")
                    return False
            else:
                log.warning(f"未找到热门话题: {topic_text}")
                return False

        except Exception as e:
            log.error(f"点击热门话题失败: {e}")
            return False

    def open_settings(self) -> bool:
        """
        打开设置页面

        Returns:
            bool: 打开是否成功
        """
        try:
            log.info("打开探索页面设置")

            if self.settings_button.is_exists():
                if self.settings_button.click():
                    log.info("✅ 设置按钮点击成功")
                    time.sleep(2)  # 等待设置页面加载
                    return True
                else:
                    log.error("设置按钮点击失败")
                    return False
            else:
                log.warning("未找到设置按钮")
                return False

        except Exception as e:
            log.error(f"打开设置失败: {e}")
            return False

    def verify_page_elements(self) -> dict:
        """
        验证页面元素是否正常显示

        Returns:
            dict: 元素验证结果
        """
        try:
            log.info("验证探索页面元素")

            results = {
                'page_loaded': False,
                'search_available': False,
                'navigation_available': False,
                'content_available': False,
                'categories_available': False
            }

            # 检查页面是否加载
            if (self.explore_title.is_exists() or
                self.explore_title_en.is_exists() or
                self.search_box.is_exists()):
                results['page_loaded'] = True
                log.info("✅ 页面已加载")

            # 检查搜索功能
            if (self.search_box.is_exists() or
                self.search_box_alt.is_exists()):
                results['search_available'] = True
                log.info("✅ 搜索功能可用")

            # 检查导航功能
            if (self.bottom_navigation.is_exists() or
                self.home_button.is_exists()):
                results['navigation_available'] = True
                log.info("✅ 导航功能可用")

            # 检查内容显示
            if (self.recommendation_list.is_exists() or
                self.hot_topics.is_exists() or
                self.feature_cards.is_exists()):
                results['content_available'] = True
                log.info("✅ 内容显示正常")

            # 检查分类功能
            if self.category_tabs.is_exists():
                results['categories_available'] = True
                log.info("✅ 分类功能可用")

            # 统计验证结果
            passed_count = sum(results.values())
            total_count = len(results)

            log.info(f"页面元素验证完成: {passed_count}/{total_count} 项通过")

            return results

        except Exception as e:
            log.error(f"验证页面元素失败: {e}")
            return {
                'page_loaded': False,
                'search_available': False,
                'navigation_available': False,
                'content_available': False,
                'categories_available': False
            }

    def perform_comprehensive_test(self) -> bool:
        """
        执行探索页面综合测试

        Returns:
            bool: 测试是否通过
        """
        try:
            log.info("🧪 开始探索页面综合测试")

            test_results = []

            # 1. 页面加载测试
            log.info("1️⃣ 测试页面加载")
            if self.wait_for_page_load(timeout=10):
                test_results.append(True)
                log.info("✅ 页面加载测试通过")
            else:
                test_results.append(False)
                log.error("❌ 页面加载测试失败")

            # 2. 元素验证测试
            log.info("2️⃣ 测试页面元素")
            element_results = self.verify_page_elements()
            if element_results['page_loaded']:
                test_results.append(True)
                log.info("✅ 页面元素测试通过")
            else:
                test_results.append(False)
                log.error("❌ 页面元素测试失败")

            # 3. 搜索功能测试
            log.info("3️⃣ 测试搜索功能")
            if element_results['search_available']:
                if self.search_content("测试搜索"):
                    test_results.append(True)
                    log.info("✅ 搜索功能测试通过")
                else:
                    test_results.append(False)
                    log.error("❌ 搜索功能测试失败")
            else:
                test_results.append(False)
                log.warning("⚠️ 搜索功能不可用，跳过测试")

            # 4. 内容交互测试
            log.info("4️⃣ 测试内容交互")
            if element_results['content_available']:
                content_count = self.get_content_count()
                if content_count > 0:
                    test_results.append(True)
                    log.info(f"✅ 内容交互测试通过，发现 {content_count} 个内容项")
                else:
                    test_results.append(False)
                    log.error("❌ 内容交互测试失败，无内容项")
            else:
                test_results.append(False)
                log.warning("⚠️ 内容不可用，跳过测试")

            # 5. 刷新功能测试
            log.info("5️⃣ 测试刷新功能")
            if self.refresh_content():
                test_results.append(True)
                log.info("✅ 刷新功能测试通过")
            else:
                test_results.append(False)
                log.error("❌ 刷新功能测试失败")

            # 计算测试结果
            passed_tests = sum(test_results)
            total_tests = len(test_results)
            success_rate = (passed_tests / total_tests) * 100

            log.info(f"🎯 综合测试完成: {passed_tests}/{total_tests} 项通过 ({success_rate:.1f}%)")

            # 如果通过率大于80%，认为测试成功
            if success_rate >= 80:
                log.info("🎉 探索页面综合测试通过")
                return True
            else:
                log.error("💥 探索页面综合测试失败")
                return False

        except Exception as e:
            log.error(f"综合测试执行失败: {e}")
            return False

    def handle_network_error(self) -> bool:
        """
        处理网络错误状态

        Returns:
            bool: 处理是否成功
        """
        try:
            log.info("检查并处理网络错误")

            # 常见的网络错误提示
            network_error_indicators = [
                self.create_element({"text": "网络连接失败"}, "网络连接失败提示"),
                self.create_element({"text": "网络异常"}, "网络异常提示"),
                self.create_element({"text": "连接超时"}, "连接超时提示"),
                self.create_element({"text": "Network Error"}, "网络错误提示(英文)"),
                self.create_element({"text": "No Internet"}, "无网络提示"),
                self.create_element({"text": "重试"}, "重试按钮"),
                self.create_element({"text": "Retry"}, "重试按钮(英文)")
            ]

            for indicator in network_error_indicators:
                if indicator.is_exists():
                    log.info(f"检测到网络错误: {indicator.description}")

                    # 如果是重试按钮，点击重试
                    if "重试" in indicator.description or "Retry" in indicator.description:
                        if indicator.click():
                            log.info("✅ 点击重试按钮成功")
                            time.sleep(3)  # 等待重试完成
                            return True

                    # 否则尝试刷新页面
                    log.info("尝试刷新页面解决网络问题")
                    return self.refresh_content()

            log.info("未检测到网络错误")
            return True

        except Exception as e:
            log.error(f"处理网络错误失败: {e}")
            return False

    def wait_for_content_load(self, timeout: int = 15) -> bool:
        """
        等待内容加载完成

        Args:
            timeout: 超时时间

        Returns:
            bool: 内容是否加载完成
        """
        try:
            log.info(f"等待探索页面内容加载，超时时间: {timeout}秒")

            start_time = time.time()

            while time.time() - start_time < timeout:
                # 检查是否有内容加载
                if self.recommendation_list.is_exists():
                    content_count = self.get_content_count()
                    if content_count > 0:
                        log.info(f"✅ 内容加载完成，共 {content_count} 项")
                        return True

                # 检查是否有热门话题
                if self.hot_topics.is_exists():
                    topics = self.get_hot_topics()
                    if len(topics) > 0:
                        log.info(f"✅ 热门话题加载完成，共 {len(topics)} 个")
                        return True

                # 检查是否显示空状态
                if self.check_empty_state():
                    log.info("✅ 页面显示空状态，加载完成")
                    return True

                # 检查网络错误
                if not self.handle_network_error():
                    log.warning("网络错误处理失败")

                time.sleep(1)

            log.warning("等待内容加载超时")
            return False

        except Exception as e:
            log.error(f"等待内容加载失败: {e}")
            return False

    def smart_scroll_to_element(self, element_text: str, max_scrolls: int = 5) -> bool:
        """
        智能滚动查找元素

        Args:
            element_text: 要查找的元素文本
            max_scrolls: 最大滚动次数

        Returns:
            bool: 是否找到元素
        """
        try:
            log.info(f"智能滚动查找元素: {element_text}")

            target_element = self.create_element(
                {"text": element_text},
                f"目标元素: {element_text}"
            )

            # 首先检查元素是否已经可见
            if target_element.is_exists():
                log.info(f"✅ 元素已可见: {element_text}")
                return True

            screen_width, screen_height = self.driver.window_size()
            start_x = screen_width // 2
            start_y = int(screen_height * 0.7)
            end_y = int(screen_height * 0.3)

            # 向下滚动查找
            for i in range(max_scrolls):
                log.info(f"向下滚动查找，第 {i+1} 次")

                self.driver.swipe(start_x, start_y, start_x, end_y, duration=0.8)
                time.sleep(1)

                if target_element.is_exists():
                    log.info(f"✅ 找到元素: {element_text}")
                    return True

            # 滚动回顶部
            self.scroll_to_top()

            log.warning(f"未找到元素: {element_text}")
            return False

        except Exception as e:
            log.error(f"智能滚动查找失败: {e}")
            return False

    def get_page_screenshot_with_info(self, filename: str = None) -> str:
        """
        获取带有页面信息的截图

        Args:
            filename: 截图文件名

        Returns:
            str: 截图文件路径
        """
        try:
            # 获取页面信息
            element_results = self.verify_page_elements()
            content_count = self.get_content_count()
            topics = self.get_hot_topics()

            # 生成信息摘要
            info_summary = f"探索页面状态 - 内容数量: {content_count}, 话题数量: {len(topics)}, 元素验证: {sum(element_results.values())}/{len(element_results)}"
            log.info(info_summary)

            # 生成截图文件名
            if filename is None:
                from utils.file_utils import FileUtils
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"ella_explore_page_{timestamp}.png"

            # 截图
            screenshot_path = self.screenshot(filename)
            log.info(f"✅ 探索页面截图已保存: {screenshot_path}")

            return screenshot_path

        except Exception as e:
            log.error(f"获取页面截图失败: {e}")
            return ""

    def cleanup_and_return_home(self) -> bool:
        """
        清理页面状态并返回主页

        Returns:
            bool: 清理是否成功
        """
        try:
            log.info("清理探索页面状态并返回主页")

            # 1. 滚动到顶部
            self.scroll_to_top()
            time.sleep(1)

            # 2. 清空搜索框（如果有内容）
            if self.search_box.is_exists():
                try:
                    current_text = self.search_box.get_text()
                    if current_text and current_text.strip():
                        log.info("清空搜索框内容")
                        self.search_box.clear_text()
                        time.sleep(0.5)
                except:
                    log.debug("搜索框清空失败或无需清空")

            # 3. 返回主页
            if self.navigate_to_home():
                log.info("✅ 成功清理并返回主页")
                return True
            else:
                log.error("返回主页失败")
                return False

        except Exception as e:
            log.error(f"清理页面状态失败: {e}")
            return False

    def get_page_performance_info(self) -> dict:
        """
        获取页面性能信息

        Returns:
            dict: 性能信息字典
        """
        try:
            log.info("获取探索页面性能信息")

            performance_info = {
                'load_time': 0,
                'content_count': 0,
                'scroll_responsiveness': 'unknown',
                'search_responsiveness': 'unknown'
            }

            # 测试页面加载时间
            start_time = time.time()
            if self.wait_for_page_load(timeout=10):
                performance_info['load_time'] = round(time.time() - start_time, 2)
                log.info(f"页面加载时间: {performance_info['load_time']}秒")

            # 获取内容数量
            performance_info['content_count'] = self.get_content_count()

            # 测试滚动响应性
            scroll_start = time.time()
            if self.scroll_to_bottom():
                scroll_time = time.time() - scroll_start
                if scroll_time < 3:
                    performance_info['scroll_responsiveness'] = 'good'
                elif scroll_time < 6:
                    performance_info['scroll_responsiveness'] = 'fair'
                else:
                    performance_info['scroll_responsiveness'] = 'poor'
                log.info(f"滚动响应性: {performance_info['scroll_responsiveness']} ({scroll_time:.2f}秒)")

            # 测试搜索响应性
            search_start = time.time()
            if self.search_content("性能测试"):
                search_time = time.time() - search_start
                if search_time < 2:
                    performance_info['search_responsiveness'] = 'good'
                elif search_time < 5:
                    performance_info['search_responsiveness'] = 'fair'
                else:
                    performance_info['search_responsiveness'] = 'poor'
                log.info(f"搜索响应性: {performance_info['search_responsiveness']} ({search_time:.2f}秒)")

            log.info(f"✅ 性能信息获取完成: {performance_info}")
            return performance_info

        except Exception as e:
            log.error(f"获取性能信息失败: {e}")
            return {
                'load_time': -1,
                'content_count': -1,
                'scroll_responsiveness': 'error',
                'search_responsiveness': 'error'
            }
