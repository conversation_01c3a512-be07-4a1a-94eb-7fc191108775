"""
Ella主页面 - 集成弹窗处理功能 - 适配UIAutomator2
继承原有EllaMainPage并添加智能弹窗处理
"""
import time
from pages.apps.ella.main_page import EllaMainPage
from pages.base_page_with_popup import BasePageWithPopup
from core.logger import log


class EllaMainPageWithPopup(EllaMainPage):
    """Ella主页面 - 集成弹窗处理 - 适配UIAutomator2"""

    def __init__(self, device_id: str = None):
        # 初始化父类
        super().__init__()

        # 初始化弹窗处理
        from core.popup_monitor import PopupMonitor, PopupConfig
        self.popup_config = PopupConfig()
        self.popup_monitor = PopupMonitor(self.driver, self.popup_config)
        self.auto_handle_popups = True
        self._popup_monitoring_enabled = False

        # 配置弹窗处理回调
        self._setup_popup_callbacks()

        # 配置Ella特有的弹窗处理
        self._configure_ella_popup_handling()

        # 启用弹窗监控
        self.enable_popup_monitoring()

    def _setup_popup_callbacks(self):
        """设置弹窗处理回调"""
        def on_popup_detected(popup_info):
            log.debug(f"检测到弹窗: {popup_info.type} (置信度: {popup_info.confidence:.2f})")

        def on_popup_handled(popup_info, result):
            if result.success:
                log.info(f"✅ 成功处理弹窗: {popup_info.type} - {result.method}")
            else:
                log.warning(f"❌ 处理弹窗失败: {popup_info.type} - {result.error}")

        def on_monitoring_error(error):
            log.error(f"弹窗监控错误: {error}")

        self.popup_monitor.set_callback('popup_detected', on_popup_detected)
        self.popup_monitor.set_callback('popup_handled', on_popup_handled)
        self.popup_monitor.set_callback('monitoring_error', on_monitoring_error)

    def enable_popup_monitoring(self):
        """启用弹窗监控"""
        if not self._popup_monitoring_enabled:
            self.popup_monitor.start_monitoring()
            self._popup_monitoring_enabled = True
            log.info("页面弹窗监控已启用")

    def disable_popup_monitoring(self):
        """禁用弹窗监控"""
        if self._popup_monitoring_enabled:
            self.popup_monitor.stop_monitoring()
            self._popup_monitoring_enabled = False
            log.info("页面弹窗监控已禁用")

    def handle_popups_immediately(self, popup_type: str = None) -> bool:
        """立即处理弹窗"""
        return self.popup_monitor.handle_popup_immediately(popup_type)

    def get_popup_stats(self) -> dict:
        """获取弹窗处理统计信息"""
        return self.popup_monitor.get_monitor_stats()

    def add_custom_popup_rule(self, popup_type: str, rules: dict):
        """添加自定义弹窗规则"""
        return self.popup_monitor.add_custom_popup_rule(popup_type, rules)

    def configure_popup_handling(self, **kwargs):
        """配置弹窗处理参数"""
        for key, value in kwargs.items():
            if key == 'auto_handle':
                self.auto_handle_popups = value
            elif key == 'min_confidence':
                self.popup_config.config['detection']['min_confidence'] = value
            elif key == 'max_retries':
                self.popup_config.config['handling']['max_retries'] = value
            elif key == 'detection_interval':
                self.popup_config.config['detection']['interval'] = value
            else:
                log.warning(f"未知的配置参数: {key}")

        self.popup_config.save_config()
    
    def _configure_ella_popup_handling(self):
        """配置Ella特有的弹窗处理"""
        
        # 添加Ella特有的弹窗规则
        ella_popup_rules = {
            'ella_voice_permission': {
                'keywords': ['麦克风权限', 'microphone permission', 'Ella需要', '录音权限'],
                'buttons': ['允许', 'Allow', '确定', 'OK', '同意'],
                'package': 'com.transsion.ella'
            },
            'ella_update_dialog': {
                'keywords': ['Ella更新', 'Ella升级', 'new version', '新版本', '版本更新'],
                'buttons': ['立即更新', '稍后', 'Update', 'Later', '更新', '取消'],
                'package': 'com.transsion.ella'
            },
            'ella_network_error': {
                'keywords': ['网络连接失败', 'network error', '无法连接', '网络异常', '连接超时'],
                'buttons': ['重试', '设置', 'Retry', 'Settings', '刷新', '确定'],
                'package': 'com.transsion.ella'
            },
            'ella_first_launch': {
                'keywords': ['欢迎使用', 'Welcome', '首次使用', '用户协议', '隐私政策'],
                'buttons': ['同意', 'Agree', '确定', 'OK', '继续', 'Continue'],
                'package': 'com.transsion.ella'
            },
            'ella_feature_guide': {
                'keywords': ['功能介绍', 'Feature Guide', '使用教程', '新功能'],
                'buttons': ['知道了', 'Got it', '下一步', 'Next', '跳过', 'Skip'],
                'package': 'com.transsion.ella'
            },
            'ella_bluetooth_permission': {
                'keywords': ['蓝牙权限', 'bluetooth permission', '蓝牙访问'],
                'buttons': ['允许', 'Allow', '确定', 'OK'],
                'package': 'com.transsion.ella'
            }
        }
        
        # 注册弹窗规则
        for popup_type, rules in ella_popup_rules.items():
            self.popup_monitor.add_custom_popup_rule(popup_type, rules)
        
        # 配置处理策略
        self.popup_monitor.update_strategy_mapping('ella_voice_permission', ['click_close'])
        self.popup_monitor.update_strategy_mapping('ella_update_dialog', ['click_close', 'back_key'])
        self.popup_monitor.update_strategy_mapping('ella_network_error', ['click_close'])
        self.popup_monitor.update_strategy_mapping('ella_first_launch', ['click_close'])
        self.popup_monitor.update_strategy_mapping('ella_feature_guide', ['click_close', 'back_key'])
        self.popup_monitor.update_strategy_mapping('ella_bluetooth_permission', ['click_close'])
        
        # 配置弹窗处理参数
        self.configure_popup_handling(
            auto_handle=True,
            min_confidence=0.7,
            max_retries=3,
            detection_interval=1.5  # Ella应用检测间隔稍短
        )
        
        log.info("Ella弹窗处理配置完成")
    
    def start_app(self) -> bool:
        """启动Ella应用 - 处理启动时的弹窗"""
        try:
            log.info("🚀 启动Ella应用...")

            # 调用父类启动方法
            if not super().start_app_with_activity():
                return False
            
            # 启动后可能出现首次使用弹窗、权限弹窗等
            time.sleep(2)
            self.handle_popups_immediately()
            
            # 等待页面加载，期间处理可能的弹窗
            if self.wait_for_page_load(timeout=20):
                log.info("✅ Ella应用启动成功")
                return True
            else:
                log.error("❌ Ella应用启动超时")
                return False
                
        except Exception as e:
            log.error(f"❌ Ella应用启动失败: {e}")
            self.screenshot("ella_start_failed.png")
            return False
    
    def execute_text_command(self, command: str) -> bool:
        """执行文本命令 - 智能处理弹窗"""
        try:
            log.info(f"🎤 执行Ella文本命令: {command}")
            
            # 执行前处理可能的弹窗
            self.handle_popups_immediately()
            
            # 使用父类的方法执行文本命令
            result = super().execute_text_command(command)
            if not result:
                return False

            # 执行后可能出现权限弹窗
            time.sleep(0.5)
            self.handle_popups_immediately()
            
            # 发送后可能出现权限弹窗（如麦克风权限、蓝牙权限等）
            time.sleep(1)
            self.handle_popups_immediately('ella_voice_permission')  # 优先处理语音权限
            self.handle_popups_immediately('ella_bluetooth_permission')  # 处理蓝牙权限
            
            log.info(f"✅ 成功执行命令: {command}")
            return True
            
        except Exception as e:
            log.error(f"❌ 执行命令失败: {command} - {e}")
            self.screenshot(f"command_failed_{command.replace(' ', '_')}.png")
            
            # 出错时也尝试处理弹窗
            self.handle_popups_immediately()
            return False
    
    def wait_for_response(self, timeout: int = 15) -> bool:
        """等待AI响应 - 处理等待期间的弹窗"""
        try:
            log.info("⏳ 等待Ella AI响应...")
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # 检查是否有响应
                if self.has_response():
                    log.info("✅ 收到AI响应")
                    return True
                
                # 等待期间处理可能的弹窗（如网络错误弹窗）
                self.handle_popups_immediately('ella_network_error')
                
                time.sleep(0.5)
            
            log.warning(f"⚠️ 等待AI响应超时: {timeout}秒")
            
            # 超时时检查是否有网络错误弹窗
            self.handle_popups_immediately('ella_network_error')
            
            return False
            
        except Exception as e:
            log.error(f"❌ 等待响应异常: {e}")
            self.handle_popups_immediately()
            return False
    
    def check_bluetooth_status(self) -> bool:
        """检查蓝牙状态 - 处理可能的权限弹窗"""
        try:
            log.info("🔍 检查蓝牙状态...")
            
            # 检查前处理可能的弹窗
            self.handle_popups_immediately()
            
            # 调用父类方法
            status = super().check_bluetooth_status()
            
            # 检查后可能出现权限相关弹窗
            self.handle_popups_immediately('ella_bluetooth_permission')
            
            return status
            
        except Exception as e:
            log.error(f"❌ 检查蓝牙状态失败: {e}")
            self.handle_popups_immediately()
            return False
    
    def get_response_text(self) -> str:
        """获取响应文本 - 处理可能的弹窗"""
        try:
            # 获取前处理弹窗
            self.handle_popups_immediately()
            
            # 调用父类方法
            response = super().get_response_text()
            
            return response
            
        except Exception as e:
            log.error(f"❌ 获取响应文本失败: {e}")
            self.handle_popups_immediately()
            return ""
    
    def is_page_loaded(self) -> bool:
        """检查Ella页面是否加载完成"""
        try:
            # 调用父类方法
            return super().is_page_loaded()
        except Exception:
            return False
    
    def handle_ella_specific_popups(self):
        """处理Ella特有的弹窗"""
        handled_count = 0
        
        # 按优先级处理弹窗
        priority_popups = [
            'ella_voice_permission',
            'ella_bluetooth_permission', 
            'ella_network_error',
            'ella_update_dialog',
            'ella_first_launch',
            'ella_feature_guide'
        ]
        
        for popup_type in priority_popups:
            if self.handle_popups_immediately(popup_type):
                handled_count += 1
                time.sleep(0.5)  # 处理间隔
        
        if handled_count > 0:
            log.info(f"处理了{handled_count}个Ella特有弹窗")
        
        return handled_count > 0
    
    def cleanup(self):
        """清理资源"""
        try:
            # 获取弹窗处理统计
            stats = self.get_popup_stats()
            if stats.get('handler_stats', {}).get('total_handled', 0) > 0:
                log.info(f"Ella页面弹窗处理统计: {stats['handler_stats']}")
            
            # 调用父类清理
            super().cleanup()
            
        except Exception as e:
            log.error(f"Ella页面清理失败: {e}")


# 使用示例
"""
# 1. 基本使用
ella_page = EllaMainPageWithPopup()

# 启动应用（自动处理启动弹窗）
if ella_page.start_app():
    # 执行命令（自动处理权限弹窗）
    ella_page.execute_text_command("open bluetooth")
    
    # 等待响应（自动处理网络错误弹窗）
    if ella_page.wait_for_response():
        response = ella_page.get_response_text()
        print(f"AI响应: {response}")

# 2. 手动处理特定弹窗
ella_page.handle_ella_specific_popups()

# 3. 获取统计信息
stats = ella_page.get_popup_stats()
print(f"弹窗处理统计: {stats}")

# 4. 清理
ella_page.cleanup()
"""
