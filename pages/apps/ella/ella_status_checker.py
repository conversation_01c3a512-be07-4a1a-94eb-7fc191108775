"""
Ella状态检查器
负责检查各种系统状态、应用状态和服务状态
"""
import subprocess
import time
from core.logger import log


class EllaStatusChecker:
    """Ella-系统设置状态检查器"""
    
    def __init__(self, driver=None):
        """
        初始化状态检查器
        
        Args:
            driver: UIAutomator2驱动实例
        """
        self.driver = driver
    
    def check_bluetooth_status(self) -> bool:
        """
        检查蓝牙状态
        
        Returns:
            bool: 蓝牙是否已开启
        """
        try:
            log.info("检查蓝牙状态")
            
            # 通过ADB命令检查蓝牙状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "bluetooth_on"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                bluetooth_status = result.stdout.strip()
                log.info(f"蓝牙状态: (值: {bluetooth_status})")
                is_on = bluetooth_status == "1"
                log.info(f"蓝牙状态: {'开启' if is_on else '关闭'} (值: {bluetooth_status})")
                return is_on
            else:
                log.error(f"获取蓝牙状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查蓝牙状态失败: {e}")
            return False
    def check_wifi_status(self) -> bool:
        """
        检查WiFi状态

        Returns:
            bool: WiFi是否已开启
        """
        try:
            log.info("检查WiFi状态")

            # 方法1: 通过ADB命令检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "wifi_on"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                wifi_status = result.stdout.strip()
                is_on = wifi_status == "1"
                log.info(f"WiFi状态: {'开启' if is_on else '关闭'} (值: {wifi_status})")
                return is_on
            else:
                log.warning(f"方法1获取WiFi状态失败: {result.stderr}")

            # 方法2: 通过dumpsys wifi检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout
                # 检查WiFi是否启用
                if "Wi-Fi is enabled" in wifi_output or "mWifiEnabled: true" in wifi_output:
                    log.info("WiFi状态: 开启 (通过dumpsys检测)")
                    return True
                elif "Wi-Fi is disabled" in wifi_output or "mWifiEnabled: false" in wifi_output:
                    log.info("WiFi状态: 关闭 (通过dumpsys检测)")
                    return False
                else:
                    log.warning("无法从dumpsys输出中确定WiFi状态")

            # 方法3: 通过网络连接状态检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "connectivity"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                connectivity_output = result.stdout
                if "WIFI" in connectivity_output and "CONNECTED" in connectivity_output:
                    log.info("WiFi状态: 开启且已连接 (通过connectivity检测)")
                    return True

            log.warning("所有方法都无法确定WiFi状态，默认返回False")
            return False

        except Exception as e:
            log.error(f"检查WiFi状态失败: {e}")
            return False
    def check_flashlight_status(self) -> bool:
        """
        检查手电筒状态 - 优化版本

        Returns:
            bool: 手电筒是否已开启
        """
        try:
            log.info("检查手电筒状态")

            # 方法1: 优先检查LED硬件状态（最直接准确的方法）
            led_paths = [
                "/sys/class/leds/torch/brightness",
                "/sys/class/leds/flashlight/brightness",
                "/sys/class/leds/led:torch_0/brightness",
                "/sys/class/leds/led:torch_1/brightness",
                "/sys/class/leds/white:flash/brightness",
                "/sys/class/leds/torch-light0/brightness",
                "/sys/class/leds/torch-light1/brightness"
            ]

            for led_path in led_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", led_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness = result.stdout.strip()
                        try:
                            brightness_value = int(brightness)
                            if brightness_value > 0:
                                log.info(f"手电筒状态: 开启 (LED路径 {led_path} 亮度值: {brightness_value})")
                                return True
                            else:
                                log.info(f"手电筒状态: 关闭 (LED路径 {led_path} 亮度值: {brightness_value})")
                                return False
                        except ValueError:
                            continue
                except:
                    continue

            # 方法2: 检查快捷设置状态
            try:
                result = subprocess.run(
                    ["adb", "shell", "settings", "get", "secure", "flashlight_enabled"],
                    capture_output=True,
                    text=True,
                    timeout=3
                )

                if result.returncode == 0:
                    flashlight_setting = result.stdout.strip()
                    if flashlight_setting == "1":
                        log.info("手电筒状态: 开启 (通过快捷设置检测)")
                        return True
                    elif flashlight_setting == "0":
                        log.info("手电筒状态: 关闭 (通过快捷设置检测)")
                        return False
            except:
                pass

            # 方法3: 通过dumpsys media.camera检查手电筒状态（作为参考）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "media.camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            torch_operation_lines = []
            if result.returncode == 0:
                camera_output = result.stdout

                # 解析最近的torch日志来判断当前状态
                if camera_output:
                    lines = camera_output.split('\n')
                    # 查找真正的torch操作日志（包含时间戳和turned on/off的行）
                    torch_operation_lines = [
                        line.strip() for line in lines
                        if 'torch for camera' in line.lower() and ('turned on' in line.lower() or 'turned off' in line.lower())
                    ]

                    if torch_operation_lines:
                        # 获取最近的torch操作（第一行，因为日志是按时间倒序排列的）
                        latest_torch_operation = torch_operation_lines[0]

                        if 'turned off' in latest_torch_operation.lower():
                            log.info(f"手电筒状态: 关闭 (通过media.camera检测到最近关闭: {latest_torch_operation})")
                            return False
                        elif 'turned on' in latest_torch_operation.lower():
                            # 如果日志显示开启，但前面的硬件检测都没有确认，可能是过期日志
                            log.warning(f"日志显示开启但硬件检测未确认: {latest_torch_operation}")
                            log.info("手电筒状态: 关闭 (综合判断：日志可能过期)")
                            return False
            else:
                log.warning(f"获取media.camera日志失败: {result.stderr}")

            # 方法4: 通过系统属性检查手电筒状态
            torch_properties = [
                "sys.camera.torch",
                "persist.vendor.camera.torch",
                "vendor.camera.torch.enable"
            ]

            for prop in torch_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        torch_prop = result.stdout.strip()
                        if torch_prop == "1" or torch_prop.lower() == "true":
                            log.info(f"手电筒状态: 开启 (通过系统属性 {prop} 检测)")
                            return True
                        elif torch_prop == "0" or torch_prop.lower() == "false":
                            log.info(f"手电筒状态: 关闭 (通过系统属性 {prop} 检测)")
                            return False
                except:
                    continue

            log.info("手电筒状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查手电筒状态失败: {e}")
            return False


    def get_flashlight_detailed_status(self) -> dict:
        """
        获取手电筒详细状态信息 - 优化版本

        Returns:
            dict: 手电筒详细状态信息
        """
        try:
            log.info("获取手电筒详细状态")

            status_info = {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'OFF',
                'camera_in_use': False,
                'detection_method': None,
                'torch_logs': [],
                'led_path_found': None
            }

            # 检查基本状态
            status_info['flashlight_enabled'] = self.check_flashlight_status()

            # 获取详细的相机服务信息
            media_camera_result = subprocess.run(
                ["adb", "shell", "dumpsys", "media.camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if media_camera_result.returncode == 0:
                camera_output = media_camera_result.stdout
                if camera_output:
                    lines = camera_output.split('\n')
                    # 查找真正的torch操作日志
                    torch_operation_lines = [
                        line.strip() for line in lines
                        if 'torch for camera' in line.lower() and ('turned on' in line.lower() or 'turned off' in line.lower())
                    ]
                    status_info['torch_logs'] = torch_operation_lines[:5]  # 保存最近5条torch操作日志（前5条，因为是倒序）

                    if torch_operation_lines:
                        latest_torch_operation = torch_operation_lines[0]  # 第一条是最新的
                        if 'turned on' in latest_torch_operation.lower():
                            status_info['torch_mode'] = 'ON'
                            status_info['detection_method'] = 'MEDIA_CAMERA_LOG'
                        elif 'turned off' in latest_torch_operation.lower():
                            status_info['torch_mode'] = 'OFF'
                            status_info['detection_method'] = 'MEDIA_CAMERA_LOG'

            # 尝试多个LED路径获取亮度信息
            led_paths = [
                "/sys/class/leds/torch/brightness",
                "/sys/class/leds/flashlight/brightness",
                "/sys/class/leds/led:torch_0/brightness",
                "/sys/class/leds/led:torch_1/brightness",
                "/sys/class/leds/white:flash/brightness",
                "/sys/class/leds/torch-light0/brightness",
                "/sys/class/leds/torch-light1/brightness"
            ]

            for led_path in led_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", led_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness = result.stdout.strip()
                        try:
                            brightness_value = int(brightness)
                            status_info['brightness_level'] = brightness_value
                            status_info['led_path_found'] = led_path
                            if not status_info['detection_method']:
                                status_info['detection_method'] = 'LED_BRIGHTNESS'
                            log.info(f"LED亮度级别: {brightness_value} (路径: {led_path})")

                            # 尝试获取最大亮度
                            max_path = led_path.replace('/brightness', '/max_brightness')
                            max_result = subprocess.run(
                                ["adb", "shell", "cat", max_path],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )

                            if max_result.returncode == 0:
                                try:
                                    max_brightness = int(max_result.stdout.strip())
                                    status_info['max_brightness'] = max_brightness
                                    if brightness_value > 0:
                                        status_info['brightness_percentage'] = (brightness_value / max_brightness) * 100
                                        log.info(f"亮度百分比: {status_info['brightness_percentage']:.1f}%")
                                except ValueError:
                                    pass
                            break  # 找到有效路径就退出
                        except ValueError:
                            continue
                except:
                    continue

            # 检查相机服务状态（使用标准dumpsys camera作为备用）
            camera_result = subprocess.run(
                ["adb", "shell", "dumpsys", "camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if camera_result.returncode == 0 and camera_result.stdout.strip():
                camera_output = camera_result.stdout
                if "Camera in use" in camera_output or "camera_in_use: true" in camera_output:
                    status_info['camera_in_use'] = True
                    log.info("相机正在使用中")

                # 解析torch模式（如果之前没有检测到）
                if status_info['torch_mode'] == 'OFF':
                    if "torch_mode_on" in camera_output.lower() or "torch: on" in camera_output.lower():
                        status_info['torch_mode'] = 'ON'
                    elif "torch_mode_off" in camera_output.lower() or "torch: off" in camera_output.lower():
                        status_info['torch_mode'] = 'OFF'

            return status_info

        except Exception as e:
            log.error(f"获取手电筒详细状态失败: {e}")
            return {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'UNKNOWN',
                'camera_in_use': False,
                'detection_method': 'ERROR',
                'torch_logs': [],
                'led_path_found': None
            }

    def check_wifi_connection_status(self) -> dict:
        """
        检查WiFi连接详细状态

        Returns:
            dict: WiFi连接状态信息
        """
        try:
            log.info("检查WiFi连接详细状态")

            status_info = {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }

            # 检查WiFi是否启用
            status_info['wifi_enabled'] = self.check_wifi_status()

            if not status_info['wifi_enabled']:
                log.info("WiFi未启用")
                return status_info

            # 获取WiFi连接信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout

                # 解析SSID
                import re
                ssid_match = re.search(r'SSID: "([^"]+)"', wifi_output)
                if ssid_match:
                    status_info['ssid'] = ssid_match.group(1)
                    status_info['connected'] = True
                    log.info(f"已连接到WiFi: {status_info['ssid']}")

                # 解析信号强度
                signal_match = re.search(r'RSSI: (-?\d+)', wifi_output)
                if signal_match:
                    status_info['signal_strength'] = int(signal_match.group(1))
                    log.info(f"信号强度: {status_info['signal_strength']} dBm")

            # 获取IP地址
            if status_info['connected']:
                ip_result = subprocess.run(
                    ["adb", "shell", "ip", "route", "get", "*******"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if ip_result.returncode == 0:
                    ip_output = ip_result.stdout
                    ip_match = re.search(r'src (\d+\.\d+\.\d+\.\d+)', ip_output)
                    if ip_match:
                        status_info['ip_address'] = ip_match.group(1)
                        log.info(f"IP地址: {status_info['ip_address']}")

            return status_info

        except Exception as e:
            log.error(f"检查WiFi连接状态失败: {e}")
            return {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }


    
    def ensure_ella_process(self) -> bool:
        """
        确保当前进程是Ella应用
        
        Returns:
            bool: 当前是否在Ella进程
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查当前进程是否是Ella...")
            
            # 获取当前前台应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')
            
            log.info(f"当前应用: {current_package}")
            log.info(f"当前Activity: {current_activity}")
            
            # 检查是否是Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]
            
            if current_package in ella_packages:
                log.info("✅ 当前在Ella应用进程")
                return True
            else:
                log.warning(f"❌ 当前不在Ella应用进程，当前包名: {current_package}")
                return False
                
        except Exception as e:
            log.error(f"检查Ella进程失败: {e}")
            return False
    
    def check_service_health(self) -> bool:
        """
        检查UIAutomator2服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查UIAutomator2服务健康状态")
            
            # 检查设备信息是否可以正常获取
            device_info = self.driver.device_info
            
            # 检查关键信息是否存在
            if not device_info or not device_info.get('serial'):
                log.warning("设备信息不完整")
                return False
            
            # 尝试获取屏幕信息
            window_size = self.driver.window_size()
            if not window_size or len(window_size) != 2:
                log.warning("无法获取屏幕信息")
                return False
            
            log.info("✅ UIAutomator2服务健康状态良好")
            return True
            
        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查失败: {e}")
            return False
    
    def check_app_started(self, package_name: str) -> bool:
        """
        检查应用是否启动成功
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否启动成功
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            # 方法1: 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            
            if current_package == package_name:
                log.info(f"✅ 应用已在前台: {current_package}")
                return True
            
            # 方法2: 检查应用是否在运行
            running_apps = self.driver.app_list_running()
            if package_name in running_apps:
                log.info(f"✅ 应用正在运行: {package_name}")
                # 尝试切换到前台
                self.driver.app_start(package_name)
                time.sleep(1)
                return True
            
            return False
            
        except Exception as e:
            log.error(f"检查应用启动状态失败: {e}")
            return False
    



if __name__ == '__main__':
    from core.base_driver import driver_manager
    checker = EllaStatusChecker(driver_manager.driver)

    # # 测试蓝牙状态
    # print("蓝牙状态:", checker.check_bluetooth_status())
    #
    # # 测试WiFi状态
    # print("WiFi状态:", checker.check_wifi_status())
    #
    # # 测试WiFi连接详细状态
    # wifi_info = checker.check_wifi_connection_status()
    # print("WiFi详细信息:", wifi_info)

    # 测试手电筒状态
    print("手电筒状态:", checker.check_flashlight_status())

    # 测试手电筒详细状态
    # flashlight_info = checker.get_flashlight_detailed_status()
    # print("手电筒详细信息:", flashlight_info)

    # 测试闹钟状态
    # print("闹钟列表:", checker.get_alarm_list())

