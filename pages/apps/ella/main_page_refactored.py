"""
Ella语音助手主页面 - 重构版本
专注于页面元素定义和基本页面操作，其他功能委托给专门的处理器
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector
from pages.apps.ella.ella_response_handler import <PERSON>ResponseHandler
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from core.logger import log


class EllaMainPageRefactored(CommonPage):
    """Ella语音助手主页面 - 重构版本"""
    
    def __init__(self):
        """初始化Ella主页面"""
        super().__init__("ella", "main_page")
        
        # 初始化页面元素
        self._init_elements()
        
        # 初始化功能模块
        self._init_modules()
    
    def _init_elements(self):
        """初始化页面元素 - 基于实际脚本的元素ID"""
        # 主要输入框
        self.input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_input"},
            "输入框"
        )
        
        # 备选输入框定位
        self.text_input_box = self.create_element(
            {"className": "android.widget.EditText"},
            "文本输入框(备选)"
        )
        
        # 发送按钮
        self.send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_btn_three_btn"},
            "发送按钮"
        )
        
        # 语音输入按钮
        self.voice_input_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_voice"},
            "语音输入按钮"
        )
        
        # 备选语音按钮
        self.voice_button_alt = self.create_element(
            {"className": "android.widget.ImageView", "description": "语音输入"},
            "语音按钮(备选)"
        )
        
        # TTS播放按钮
        self.tts_play_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_tts_play"},
            "TTS播放按钮"
        )
        
        # 聊天消息列表
        self.chat_list = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "聊天消息列表"
        )
        
        # Ella的欢迎消息
        self.ella_greeting = self.create_element(
            {"text": "Hi，我是Ella"},
            "Ella欢迎消息"
        )
        
        # 通用文本视图
        self.text_view_generic = self.create_element(
            {"className": "android.widget.TextView"},
            "通用文本视图"
        )
    
    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用
        self.page_elements = {
            'input_box': self.input_box,
            'text_input_box': self.text_input_box,
            'send_button': self.send_button,
            'voice_input_button': self.voice_input_button,
            'voice_button_alt': self.voice_button_alt,
            'tts_play_button': self.tts_play_button,
            'chat_list': self.chat_list,
            'ella_greeting': self.ella_greeting,
            'text_view_generic': self.text_view_generic
        }
        
        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.response_handler = EllaResponseHandler(self.driver)
        self.command_executor = EllaCommandExecutor(self.driver, self.page_elements)
    
    # ==================== 应用启动和页面管理 ====================
    
    def start_app_with_activity(self) -> bool:
        """
        启动Ella应用并指定Activity
        
        Returns:
            bool: 启动是否成功
        """
        try:
            log.info("启动Ella应用（指定Activity）")
            
            package_name = "com.transsion.aivoiceassistant"
            activity_name = "com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"
            
            # 尝试启动指定Activity
            try:
                self.driver.app_start(package_name, activity_name)
                log.info(f"尝试启动Activity: {activity_name}")
                time.sleep(3)
                
                # 检查应用是否启动成功
                if self.status_checker.check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（指定Activity）")
                    return True
            except Exception as e:
                log.warning(f"指定Activity启动失败: {e}")
            
            # 备选方案：使用默认启动方式
            try:
                self.driver.app_start(package_name)
                log.info("尝试默认方式启动应用")
                time.sleep(3)
                
                if self.status_checker.check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（默认方式）")
                    return True
            except Exception as e:
                log.warning(f"默认启动方式失败: {e}")
            
            log.error("❌ Ella应用启动失败")
            return False
            
        except Exception as e:
            log.error(f"启动Ella应用失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间，默认为10秒
            
        Returns:
            bool: 页面是否加载完成
        """
        try:
            log.info(f"等待Ella页面加载完成，超时时间: {timeout}秒")
            
            # 检查服务健康状态
            if not self.status_checker.check_service_health():
                log.warning("UIAutomator2服务状态异常，但继续尝试")
            
            # 检查当前应用包名
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            log.info(f"当前应用包名: {current_package}")
            
            if current_package == "com.transsion.aivoiceassistant":
                log.info("✅ Ella应用包已确认")
                return self._wait_for_ui_elements(timeout)
            
            # 使用元素检测
            log.info("尝试通过元素检测确认页面加载")
            return self._wait_for_ui_elements(timeout)
            
        except Exception as e:
            log.error(f"等待页面加载失败: {e}")
            return False
    
    def _wait_for_ui_elements(self, timeout: int) -> bool:
        """
        等待UI元素加载
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: UI元素是否加载完成
        """
        try:
            log.info("等待Ella页面UI元素加载...")
            
            # 定义页面标识元素
            page_indicators = [
                self.input_box,
                self.text_input_box,
                self.voice_input_button,
                self.voice_button_alt,
                self.send_button,
                self.ella_greeting,
                self.chat_list
            ]
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 检查任何一个页面标识元素是否存在
                    for indicator in page_indicators:
                        try:
                            if indicator.is_exists():
                                log.info(f"✅ 检测到页面元素: {indicator.description}")
                                time.sleep(1)  # 额外等待确保页面完全加载
                                return True
                        except Exception as e:
                            log.debug(f"检查元素 {indicator.description} 时出错: {e}")
                    
                    time.sleep(0.5)
                    
                except Exception as e:
                    log.debug(f"UI元素检查循环出错: {e}")
                    time.sleep(1)
            
            log.warning(f"⚠️ UI元素加载超时 ({timeout}秒)")
            return False
            
        except Exception as e:
            log.error(f"等待UI元素失败: {e}")
            return False
    
    # ==================== 页面状态检查 ====================
    
    def ensure_on_chat_page(self) -> bool:
        """
        确保当前在对话页面
        
        Returns:
            bool: 是否成功回到对话页面
        """
        try:
            log.info("确保在对话页面...")
            
            # 检查当前进程是否是Ella
            if not self.status_checker.ensure_ella_process():
                log.warning("当前不在Ella进程，尝试返回Ella")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False
            
            # 检查是否在对话页面
            if self._check_chat_page_indicators():
                log.info("✅ 已在对话页面")
                return True
            
            # 尝试返回到对话页面
            if self._try_return_to_chat_page():
                log.info("✅ 成功返回到对话页面")
                return True
            
            # 宽松策略：如果在Ella应用中，就认为成功
            if self.status_checker.ensure_ella_process():
                log.info("✅ 在Ella应用中，使用宽松策略认为在对话页面")
                return True
            
            log.error("❌ 无法确保在对话页面")
            return False
            
        except Exception as e:
            log.error(f"确保在对话页面失败: {e}")
            return False
    
    def _check_chat_page_indicators(self) -> bool:
        """检查对话页面指示器"""
        try:
            # 检查输入框是否存在
            if self.input_box.is_exists() or self.text_input_box.is_exists():
                log.debug("检测到输入框，可能在对话页面")
                return True
            
            # 检查语音按钮是否存在
            if self.voice_input_button.is_exists() or self.voice_button_alt.is_exists():
                log.debug("检测到语音按钮，可能在对话页面")
                return True
            
            # 检查发送按钮是否存在
            if self.send_button.is_exists():
                log.debug("检测到发送按钮，可能在对话页面")
                return True
            
            return False
            
        except Exception as e:
            log.debug(f"对话页面指示器检测失败: {e}")
            return False
    
    def return_to_ella_app(self) -> bool:
        """
        返回到Ella应用
        
        Returns:
            bool: 是否成功返回Ella应用
        """
        try:
            log.info("尝试返回Ella应用...")
            
            # 方法1: 多次按返回键
            for i in range(5):
                log.info(f"第{i+1}次按返回键...")
                self.driver.press("back")
                time.sleep(1)
                
                if self.status_checker.ensure_ella_process():
                    log.info(f"✅ 通过返回键回到Ella应用 (第{i+1}次)")
                    return True
            
            # 方法2: 重新启动Ella应用
            log.info("尝试重新启动Ella应用...")
            if self.start_app_with_activity():
                if self.wait_for_page_load(timeout=10):
                    log.info("✅ 重新启动Ella应用成功")
                    return True
            
            log.error("❌ 无法返回Ella应用")
            return False
            
        except Exception as e:
            log.error(f"返回Ella应用失败: {e}")
            return False
    
    def _try_return_to_chat_page(self) -> bool:
        """尝试返回到对话页面"""
        try:
            # 按返回键回到主页
            log.info("按返回键回到主页...")
            self.driver.press("back")
            time.sleep(1)
            
            # 检查是否回到了对话页面
            if self._check_chat_page_indicators():
                log.info("✅ 通过返回键回到对话页面")
                return True
            
            return False
            
        except Exception as e:
            log.error(f"尝试返回对话页面失败: {e}")
            return False
    
    # ==================== 委托给专门模块的方法 ====================
    
    def execute_text_command(self, command: str) -> bool:
        """执行文本命令"""
        return self.command_executor.execute_text_command(command)
    
    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = 'zh-CN') -> bool:
        """执行语音命令"""
        return self.command_executor.execute_voice_command(command, duration, language)
    
    def wait_for_response(self, timeout: int = 10) -> bool:
        """等待AI响应"""
        return self.response_handler.wait_for_response(timeout)
    
    def get_response_text(self) -> str:
        """获取响应文本"""
        return self.response_handler.get_response_text()
    
    def verify_command_in_response(self, command: str, response: str) -> bool:
        """验证响应中是否包含命令内容"""
        return self.response_handler.verify_command_in_response(command, response)
    
    def check_bluetooth_status(self) -> bool:
        """检查蓝牙状态"""
        return self.status_checker.check_bluetooth_status()
    def check_wifi_status(self) -> bool:
        """检查WiFi状态"""
        return self.status_checker.check_wifi_status()

    def check_contacts_app_opened(self) -> bool:
        """检查联系人应用是否打开"""
        return self.app_detector.check_contacts_app_opened()
    def check_facebook_app_opened(self) -> bool:
        """检查facebook应用是否打开"""
        return self.app_detector.check_facebook_app_opened()

    def check_weather_app_opened(self) -> bool:
        """检查天气应用是否打开"""
        return self.app_detector.check_weather_app_opened()
    
    def check_camera_app_opened(self) -> bool:
        """检查相机应用是否打开"""
        return self.app_detector.check_camera_app_opened()

    def check_flashlight_status(self) -> bool:
        """检查手电筒状态"""
        return self.status_checker.check_flashlight_status()
    def check_alarm_status(self) -> bool:
        """检查闹钟状态"""
        return self.app_detector.check_alarm_status()

    def check_clock_status(self) -> bool:
        """检查时钟应用状态"""
        return self.app_detector.check_clock_status()

    def get_alarm_list(self) -> list:
        """获取闹钟列表"""
        return self.app_detector.get_alarm_list()

    def clear_all_alarms(self) -> bool:
        """清除所有闹钟"""
        return self.app_detector.clear_all_alarms()
    def check_settings_opened(self) -> bool:
        """检查闹钟状态"""
        return self.status_checker.check_settings_opened()

    def get_flashlight_detailed_status(self) -> dict:
        """获取手电筒详细状态信息"""
        return self.status_checker.get_flashlight_detailed_status()

    # ==================== 智能版本的方法 ====================
    
    def check_bluetooth_status_smart(self) -> bool:
        """智能检查蓝牙状态"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查蓝牙状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False
        return self.status_checker.check_bluetooth_status()
    
    def check_contacts_app_opened_smart(self) -> bool:
        """智能检查联系人应用状态 - 使用优化的检测器"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查联系人应用状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False

        # 使用优化的智能检测方法
        return self.app_detector.check_contacts_app_opened_smart()

    def check_contacts_app_opened(self) -> bool:
        """检查联系人应用是否打开 - 基础方法"""
        return self.app_detector.check_contacts_app_opened()
    
    def get_response_text_smart(self) -> str:
        """智能获取响应文本"""
        if not self.status_checker.ensure_ella_process():
            log.warning("获取响应文本时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return ""
        return self.response_handler.get_response_text()
    def get_response_all_text(self) -> list:
        """智能获取响应文本"""
        if not self.status_checker.ensure_ella_process():
            log.warning("获取响应文本时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return []
        return self.response_handler.get_response_all_text()

    def check_alarm_status_smart(self) -> bool:
        """智能检查闹钟状态 - 包含进程检测"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查闹钟状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False
        return self.app_detector.check_alarm_status()

    def verify_alarm_set(self, target_time: str = "10:00") -> bool:
        """验证闹钟是否设置成功"""
        try:
            log.info(f"验证闹钟设置: {target_time}")

            # 获取闹钟列表
            alarm_list = self.get_alarm_list()

            # 检查目标时间是否在列表中
            for alarm in alarm_list:
                if alarm.get('time') == target_time:
                    log.info(f"✅ 找到目标闹钟: {target_time}")
                    return True

            log.info(f"❌ 未找到目标闹钟: {target_time}")
            return False

        except Exception as e:
            log.error(f"验证闹钟设置失败: {e}")
            return False

    def verify_alarm_in_list(self, target_time: str) -> bool:
        """验证闹钟是否在列表中"""
        return self.verify_alarm_set(target_time)

    def ensure_input_box_ready(self) -> bool:
        """确保输入框就绪"""
        return self.command_executor._ensure_input_box_ready()
    
    # ==================== 兼容性方法 ====================
    
    def start_app(self) -> bool:
        """启动应用（兼容性方法）"""
        return self.start_app_with_activity()
    
    def _get_page_text_snapshot(self) -> str:
        """获取页面文本快照（兼容性方法）"""
        return self.response_handler._get_page_text_snapshot()
