"""
Ella联系人命令处理器
专门处理联系人相关命令的执行和验证逻辑
"""
import time
import subprocess
from core.logger import log


class EllaContactCommandHandler:
    """Ella联系人命令处理器"""
    
    def __init__(self, ella_app):
        """
        初始化联系人命令处理器
        
        Args:
            ella_app: Ella应用实例
        """
        self.ella_app = ella_app
    
    def execute_contact_command_with_retry(self, command: str):
        """
        执行联系人命令并重试获取响应
        优化逻辑：执行指令后判断当前页面，如果不是ella页面则返回ella页面再获取响应
        
        Args:
            command: 要执行的命令
            
        Returns:
            tuple: (initial_status, final_status, response_text)
        """
        # 记录初始状态
        initial_status = self.ella_app.check_contacts_app_opened()
        log.info(f"初始状态 - Dalier应用打开: {initial_status}")
        
        # 确保页面就绪
        assert self.ella_app.ensure_on_chat_page(), "无法确保在对话页面"
        assert self.ella_app.ensure_input_box_ready(), "输入框未就绪"
        
        # 执行命令
        success = self.ella_app.execute_text_command(command)
        assert success, f"执行命令失败: {command}"
        log.info(f"✅ 成功执行命令: {command}")
        
        # 等待命令执行和AI响应
        time.sleep(3)
        
        # 检查当前是否还在Ella页面
        log.info("检查执行命令后的当前页面状态...")
        is_on_ella_page = self.ella_app.ensure_on_chat_page()
        
        if not is_on_ella_page:
            log.info("⚠️ 当前不在Ella页面，可能已跳转到联系人应用，尝试返回Ella页面")
            # 返回Ella应用
            return_success = self.ella_app.return_to_ella_app()
            if return_success:
                log.info("✅ 成功返回Ella应用")
                time.sleep(2)  # 等待页面稳定
            else:
                log.warning("⚠️ 返回Ella应用失败，尝试重新确保在对话页面")
                self.ella_app.ensure_on_chat_page()
                time.sleep(2)
        else:
            log.info("✅ 当前仍在Ella页面")
        
        # 获取响应文本
        response_text = self._get_response_with_retry()
        
        # 检查最终状态 - Dalier应用是否打开
        log.info("检查最终状态 - Dalier应用是否已打开")
        final_status = self.ella_app.check_contacts_app_opened_smart()
        
        log.info(f"命令执行完成: 初始状态={initial_status}, 最终状态={final_status}, 响应='{response_text}'")
        return initial_status, final_status, response_text
    
    def _get_response_with_retry(self, max_attempts: int = 3) -> str:
        """
        重试获取响应文本
        
        Args:
            max_attempts: 最大重试次数
            
        Returns:
            str: 响应文本
        """
        response_text = ""
        for attempt in range(max_attempts):
            log.info(f"尝试获取响应文本 (第{attempt + 1}次)")
            
            # 确保在Ella页面
            if not self.ella_app.ensure_on_chat_page():
                log.warning("获取响应时不在Ella页面，重新返回")
                self.ella_app.return_to_ella_app()
                time.sleep(1)
            
            # 获取响应文本
            temp_response = self.ella_app.get_response_text_smart()
            if not temp_response:
                temp_response = self.ella_app.get_response_text()
            
            if temp_response and len(temp_response.strip()) > 0:
                response_text = temp_response
                log.info(f"✅ 成功获取响应文本: {response_text}")
                break
            
            log.warning(f"第{attempt + 1}次获取响应失败，等待后重试")
            time.sleep(1)
        
        return response_text
    
    def manual_check_dalier_app(self) -> bool:
        """
        手动检查Dalier应用是否打开
        
        Returns:
            bool: Dalier应用是否打开
        """
        try:
            log.info("执行手动Dalier应用检查")
            
            # 方法1: 检查当前应用包名
            if self._check_current_app_package():
                return True
            
            # 方法2: 使用adb检查
            if self._check_with_adb():
                return True
            
            # 方法3: 检查是否有联系人相关的UI元素
            if self._check_ui_elements():
                return True
            
            log.info("所有手动检查方法都未检测到Dalier应用")
            return False
            
        except Exception as e:
            log.error(f"手动检查Dalier应用失败: {e}")
            return False
    
    def _check_current_app_package(self) -> bool:
        """检查当前应用包名"""
        try:
            current_app = self.ella_app.driver.app_current()
            current_package = current_app.get('package', '')
            log.info(f"当前应用包名: {current_package}")
            
            if current_package == "com.sh.smart.caller":
                log.info("✅ 当前就在Dalier应用中")
                return True
        except Exception as e:
            log.warning(f"获取当前应用包名失败: {e}")
        return False
    
    def _check_with_adb(self) -> bool:
        """使用adb检查"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "top"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                top_activity = result.stdout
                log.info(f"顶层Activity信息（前200字符）: {top_activity[:200]}")
                
                if "com.sh.smart.caller" in top_activity:
                    log.info("✅ 通过顶层Activity检测到Dalier")
                    return True
                
                if "smart.caller" in top_activity:
                    log.info("✅ 通过顶层Activity检测到smart.caller")
                    return True
                    
        except Exception as e:
            log.warning(f"adb检查失败: {e}")
        return False
    
    def _check_ui_elements(self) -> bool:
        """检查UI元素"""
        try:
            # 简单检查是否有联系人应用的特征元素
            if self.ella_app.driver(text="联系人").exists() or self.ella_app.driver(text="通讯录").exists():
                log.info("✅ 检测到联系人应用UI元素")
                return True
                
            if self.ella_app.driver(text="Contacts").exists() or self.ella_app.driver(text="Phone").exists():
                log.info("✅ 检测到联系人应用UI元素（英文）")
                return True
                
        except Exception as e:
            log.warning(f"UI元素检查失败: {e}")
        return False
    
    def verify_contact_command_result(self, initial_status: bool, final_status: bool, response_text: str) -> bool:
        """
        验证联系人命令执行结果
        
        Args:
            initial_status: 初始状态
            final_status: 最终状态
            response_text: 响应文本
            
        Returns:
            bool: 验证是否通过
        """
        log.info(f"应用状态检查结果: 初始={initial_status}, 最终={final_status}")
        
        # 如果检测失败，尝试手动验证
        if not final_status:
            log.warning("自动检测失败，尝试手动验证Dalier应用状态")
            
            # 尝试直接检查应用是否在运行
            manual_check = self.manual_check_dalier_app()
            log.info(f"手动检查结果: {manual_check}")
            
            if manual_check:
                log.info("✅ 手动检查确认Dalier应用已打开")
                return True
            else:
                # 如果响应包含Done，说明命令执行成功，可能是检测方法的问题
                if response_text and "done" in response_text.lower():
                    log.warning("⚠️ 响应包含Done但未检测到应用打开，可能是检测方法问题")
                    log.warning("基于响应内容判断命令执行成功")
                    return True
        
        return final_status
