"""
计算器应用主页面
"""
from pages.base.common_page import CommonPage
from core.logger import log


class CalculatorMainPage(CommonPage):
    """计算器主页面"""
    
    def __init__(self):
        """初始化计算器主页面"""
        super().__init__("calculator", "main_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素"""
        # 数字按钮 0-9
        self.btn_0 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_0"},
            "数字0按钮"
        )
        
        self.btn_1 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_1"},
            "数字1按钮"
        )
        
        self.btn_2 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_2"},
            "数字2按钮"
        )
        
        self.btn_3 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_3"},
            "数字3按钮"
        )
        
        self.btn_4 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_4"},
            "数字4按钮"
        )
        
        self.btn_5 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_5"},
            "数字5按钮"
        )
        
        self.btn_6 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_6"},
            "数字6按钮"
        )
        
        self.btn_7 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_7"},
            "数字7按钮"
        )
        
        self.btn_8 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_8"},
            "数字8按钮"
        )
        
        self.btn_9 = self.create_element(
            {"resourceId": "com.google.android.calculator:id/digit_9"},
            "数字9按钮"
        )
        
        # 运算符按钮
        self.btn_plus = self.create_element(
            {"resourceId": "com.google.android.calculator:id/op_add"},
            "加号按钮"
        )
        
        self.btn_minus = self.create_element(
            {"resourceId": "com.google.android.calculator:id/op_sub"},
            "减号按钮"
        )
        
        self.btn_multiply = self.create_element(
            {"resourceId": "com.google.android.calculator:id/op_mul"},
            "乘号按钮"
        )
        
        self.btn_divide = self.create_element(
            {"resourceId": "com.google.android.calculator:id/op_div"},
            "除号按钮"
        )
        
        self.btn_equals = self.create_element(
            {"resourceId": "com.google.android.calculator:id/eq"},
            "等号按钮"
        )
        
        # 其他功能按钮
        self.btn_clear = self.create_element(
            {"resourceId": "com.google.android.calculator:id/clr"},
            "清除按钮"
        )
        
        self.btn_delete = self.create_element(
            {"resourceId": "com.google.android.calculator:id/del"},
            "删除按钮"
        )
        
        self.btn_decimal = self.create_element(
            {"resourceId": "com.google.android.calculator:id/dec_point"},
            "小数点按钮"
        )
        
        # 结果显示区域
        self.result_display = self.create_element(
            {"resourceId": "com.google.android.calculator:id/result"},
            "结果显示区域"
        )
        
        self.formula_display = self.create_element(
            {"resourceId": "com.google.android.calculator:id/formula"},
            "公式显示区域"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        return self.btn_0.wait_for_element(timeout)
    
    def input_number(self, number: str) -> bool:
        """
        输入数字
        
        Args:
            number: 要输入的数字字符串
            
        Returns:
            bool: 输入是否成功
        """
        try:
            log.info(f"输入数字: {number}")
            
            # 数字按钮映射
            digit_buttons = {
                '0': self.btn_0,
                '1': self.btn_1,
                '2': self.btn_2,
                '3': self.btn_3,
                '4': self.btn_4,
                '5': self.btn_5,
                '6': self.btn_6,
                '7': self.btn_7,
                '8': self.btn_8,
                '9': self.btn_9,
                '.': self.btn_decimal
            }
            
            # 逐个点击数字按钮
            for digit in number:
                if digit in digit_buttons:
                    if not digit_buttons[digit].click():
                        log.error(f"点击数字按钮失败: {digit}")
                        return False
                else:
                    log.error(f"不支持的字符: {digit}")
                    return False
            
            return True
            
        except Exception as e:
            log.error(f"输入数字失败: {e}")
            return False
    
    def click_operator(self, operator: str) -> bool:
        """
        点击运算符
        
        Args:
            operator: 运算符 (+, -, *, /)
            
        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击运算符: {operator}")
            
            operator_buttons = {
                '+': self.btn_plus,
                '-': self.btn_minus,
                '*': self.btn_multiply,
                '/': self.btn_divide
            }
            
            if operator in operator_buttons:
                return operator_buttons[operator].click()
            else:
                log.error(f"不支持的运算符: {operator}")
                return False
                
        except Exception as e:
            log.error(f"点击运算符失败: {e}")
            return False
    
    def click_equals(self) -> bool:
        """
        点击等号
        
        Returns:
            bool: 点击是否成功
        """
        return self.btn_equals.click()
    
    def clear_all(self) -> bool:
        """
        清除所有内容
        
        Returns:
            bool: 清除是否成功
        """
        return self.btn_clear.click()
    
    def delete_last(self) -> bool:
        """
        删除最后一个字符
        
        Returns:
            bool: 删除是否成功
        """
        return self.btn_delete.click()
    
    def get_result(self) -> str:
        """
        获取计算结果
        
        Returns:
            str: 计算结果
        """
        result = self.result_display.get_text()
        log.info(f"获取计算结果: {result}")
        return result or ""
    
    def get_formula(self) -> str:
        """
        获取计算公式
        
        Returns:
            str: 计算公式
        """
        formula = self.formula_display.get_text()
        log.info(f"获取计算公式: {formula}")
        return formula or ""
    
    def calculate(self, expression: str) -> str:
        """
        执行计算表达式
        
        Args:
            expression: 计算表达式，如 "2+3"
            
        Returns:
            str: 计算结果
        """
        try:
            log.info(f"执行计算: {expression}")
            
            # 清除之前的内容
            self.clear_all()
            
            # 解析表达式
            i = 0
            while i < len(expression):
                char = expression[i]
                
                if char.isdigit() or char == '.':
                    # 处理数字（可能是多位数）
                    number = ''
                    while i < len(expression) and (expression[i].isdigit() or expression[i] == '.'):
                        number += expression[i]
                        i += 1
                    
                    if not self.input_number(number):
                        return ""
                    
                    i -= 1  # 因为外层循环会+1
                    
                elif char in ['+', '-', '*', '/']:
                    # 处理运算符
                    if not self.click_operator(char):
                        return ""
                
                i += 1
            
            # 点击等号
            if not self.click_equals():
                return ""
            
            # 等待计算完成
            import time
            time.sleep(1)
            
            # 获取结果
            return self.get_result()
            
        except Exception as e:
            log.error(f"执行计算失败: {e}")
            return ""
