"""
TECNO计算器应用主页面
适配TECNO设备的计算器应用
"""
from pages.base.common_page import CommonPage
from core.logger import log


class TecnoCalculatorMainPage(CommonPage):
    """TECNO计算器主页面"""
    
    def __init__(self):
        """初始化TECNO计算器主页面"""
        super().__init__("calculator", "main_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素 - 基于TECNO设备实际结构"""
        # 数字按钮 0-9 - 使用文本定位
        self.btn_0 = self.create_element(
            {"text": "0"},
            "数字0按钮"
        )
        
        self.btn_1 = self.create_element(
            {"text": "1"},
            "数字1按钮"
        )
        
        self.btn_2 = self.create_element(
            {"text": "2"},
            "数字2按钮"
        )
        
        self.btn_3 = self.create_element(
            {"text": "3"},
            "数字3按钮"
        )
        
        self.btn_4 = self.create_element(
            {"text": "4"},
            "数字4按钮"
        )
        
        self.btn_5 = self.create_element(
            {"text": "5"},
            "数字5按钮"
        )
        
        self.btn_6 = self.create_element(
            {"text": "6"},
            "数字6按钮"
        )
        
        self.btn_7 = self.create_element(
            {"text": "7"},
            "数字7按钮"
        )
        
        self.btn_8 = self.create_element(
            {"text": "8"},
            "数字8按钮"
        )
        
        self.btn_9 = self.create_element(
            {"text": "9"},
            "数字9按钮"
        )
        
        # 运算符按钮 - 尝试常见的运算符文本
        self.btn_plus = self.create_element(
            {"text": "+"},
            "加号按钮"
        )
        
        self.btn_minus = self.create_element(
            {"text": "-"},
            "减号按钮"
        )
        
        # 乘号可能是 × 或 *
        self.btn_multiply = self.create_element(
            {"text": "×"},
            "乘号按钮"
        )
        
        self.btn_multiply_alt = self.create_element(
            {"text": "*"},
            "乘号按钮(备选)"
        )
        
        # 除号可能是 ÷ 或 /
        self.btn_divide = self.create_element(
            {"text": "÷"},
            "除号按钮"
        )
        
        self.btn_divide_alt = self.create_element(
            {"text": "/"},
            "除号按钮(备选)"
        )
        
        # 等号
        self.btn_equals = self.create_element(
            {"text": "="},
            "等号按钮"
        )
        
        # 清除按钮 - 可能的文本
        self.btn_clear = self.create_element(
            {"text": "C"},
            "清除按钮"
        )
        
        self.btn_clear_all = self.create_element(
            {"text": "AC"},
            "全部清除按钮"
        )
        
        self.btn_clear_cn = self.create_element(
            {"text": "清除"},
            "清除按钮(中文)"
        )
        
        # 删除按钮
        self.btn_delete = self.create_element(
            {"text": "⌫"},
            "删除按钮"
        )
        
        self.btn_delete_alt = self.create_element(
            {"text": "DEL"},
            "删除按钮(备选)"
        )
        
        # 小数点
        self.btn_decimal = self.create_element(
            {"text": "."},
            "小数点按钮"
        )
        
        # 结果显示区域 - 尝试常见的类名
        self.result_display = self.create_element(
            {"className": "android.widget.TextView"},
            "结果显示区域"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        # 等待数字0按钮出现
        return self.btn_0.wait_for_element(timeout)
    
    def input_number(self, number: str) -> bool:
        """
        输入数字
        
        Args:
            number: 要输入的数字字符串
            
        Returns:
            bool: 输入是否成功
        """
        try:
            log.info(f"输入数字: {number}")
            
            # 数字按钮映射
            digit_buttons = {
                '0': self.btn_0,
                '1': self.btn_1,
                '2': self.btn_2,
                '3': self.btn_3,
                '4': self.btn_4,
                '5': self.btn_5,
                '6': self.btn_6,
                '7': self.btn_7,
                '8': self.btn_8,
                '9': self.btn_9,
                '.': self.btn_decimal
            }
            
            # 逐个点击数字按钮
            for digit in number:
                if digit in digit_buttons:
                    if not digit_buttons[digit].click():
                        log.error(f"点击数字按钮失败: {digit}")
                        return False
                    # 添加小延时
                    import time
                    time.sleep(0.2)
                else:
                    log.error(f"不支持的字符: {digit}")
                    return False
            
            return True
            
        except Exception as e:
            log.error(f"输入数字失败: {e}")
            return False
    
    def click_operator(self, operator: str) -> bool:
        """
        点击运算符
        
        Args:
            operator: 运算符 (+, -, *, /)
            
        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击运算符: {operator}")
            
            if operator == '+':
                return self.btn_plus.click()
            elif operator == '-':
                return self.btn_minus.click()
            elif operator in ['*', '×']:
                # 先尝试 ×，再尝试 *
                if self.btn_multiply.is_exists():
                    return self.btn_multiply.click()
                elif self.btn_multiply_alt.is_exists():
                    return self.btn_multiply_alt.click()
                else:
                    log.error("未找到乘号按钮")
                    return False
            elif operator in ['/', '÷']:
                # 先尝试 ÷，再尝试 /
                if self.btn_divide.is_exists():
                    return self.btn_divide.click()
                elif self.btn_divide_alt.is_exists():
                    return self.btn_divide_alt.click()
                else:
                    log.error("未找到除号按钮")
                    return False
            else:
                log.error(f"不支持的运算符: {operator}")
                return False
                
        except Exception as e:
            log.error(f"点击运算符失败: {e}")
            return False
    
    def click_equals(self) -> bool:
        """
        点击等号
        
        Returns:
            bool: 点击是否成功
        """
        return self.btn_equals.click()
    
    def clear_all(self) -> bool:
        """
        清除所有内容
        
        Returns:
            bool: 清除是否成功
        """
        # 尝试不同的清除按钮
        if self.btn_clear_all.is_exists():
            return self.btn_clear_all.click()
        elif self.btn_clear.is_exists():
            return self.btn_clear.click()
        elif self.btn_clear_cn.is_exists():
            return self.btn_clear_cn.click()
        else:
            log.warning("未找到清除按钮")
            return False
    
    def delete_last(self) -> bool:
        """
        删除最后一个字符
        
        Returns:
            bool: 删除是否成功
        """
        if self.btn_delete.is_exists():
            return self.btn_delete.click()
        elif self.btn_delete_alt.is_exists():
            return self.btn_delete_alt.click()
        else:
            log.warning("未找到删除按钮")
            return False
    
    def get_result(self) -> str:
        """
        获取计算结果
        
        Returns:
            str: 计算结果
        """
        try:
            # 尝试获取显示区域的文本
            # 由于不知道具体的结果显示元素，我们尝试多种方法
            
            # 方法1: 查找包含数字的TextView
            elements = self.driver(className="android.widget.TextView")
            for element in elements:
                text = element.get_text()
                if text and (text.replace('.', '').replace('-', '').isdigit() or 
                           any(char.isdigit() for char in text)):
                    log.info(f"获取计算结果: {text}")
                    return text
            
            # 方法2: 截图并提示用户手动验证
            log.warning("无法自动获取计算结果，请查看截图验证")
            self.screenshot("calculation_result.png")
            return ""
            
        except Exception as e:
            log.error(f"获取计算结果失败: {e}")
            return ""
    
    def simple_calculate(self, num1: str, operator: str, num2: str) -> bool:
        """
        执行简单计算
        
        Args:
            num1: 第一个数字
            operator: 运算符
            num2: 第二个数字
            
        Returns:
            bool: 计算是否成功执行
        """
        try:
            log.info(f"执行计算: {num1} {operator} {num2}")
            
            # 清除之前的内容
            self.clear_all()
            import time
            time.sleep(0.5)
            
            # 输入第一个数字
            if not self.input_number(num1):
                return False
            
            time.sleep(0.3)
            
            # 点击运算符
            if not self.click_operator(operator):
                return False
            
            time.sleep(0.3)
            
            # 输入第二个数字
            if not self.input_number(num2):
                return False
            
            time.sleep(0.3)
            
            # 点击等号
            if not self.click_equals():
                return False
            
            # 等待计算完成
            time.sleep(1)
            
            # 截图保存结果
            self.screenshot(f"calc_{num1}_{operator}_{num2}.png")
            
            log.info("计算执行完成")
            return True
            
        except Exception as e:
            log.error(f"执行计算失败: {e}")
            return False
