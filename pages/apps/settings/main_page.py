"""
设置应用主页面
"""
from pages.base.common_page import CommonPage
from core.logger import log


class SettingsMainPage(CommonPage):
    """设置主页面"""
    
    def __init__(self):
        """初始化设置主页面"""
        super().__init__("settings", "main_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素"""
        # 搜索框
        self.search_box = self.create_element(
            {"resourceId": "android:id/search_src_text"},
            "搜索框"
        )
        
        self.search_button = self.create_element(
            {"resourceId": "com.android.settings:id/search"},
            "搜索按钮"
        )
        
        # 常见设置项
        self.wifi_settings = self.create_element(
            {"text": "WLAN"},
            "WLAN设置"
        )
        
        self.wifi_settings_en = self.create_element(
            {"text": "Wi-Fi"},
            "Wi-Fi设置"
        )
        
        self.bluetooth_settings = self.create_element(
            {"text": "蓝牙"},
            "蓝牙设置"
        )
        
        self.bluetooth_settings_en = self.create_element(
            {"text": "Bluetooth"},
            "Bluetooth设置"
        )
        
        self.display_settings = self.create_element(
            {"text": "显示"},
            "显示设置"
        )
        
        self.display_settings_en = self.create_element(
            {"text": "Display"},
            "Display设置"
        )
        
        self.sound_settings = self.create_element(
            {"text": "声音"},
            "声音设置"
        )
        
        self.sound_settings_en = self.create_element(
            {"text": "Sound"},
            "Sound设置"
        )
        
        self.apps_settings = self.create_element(
            {"text": "应用"},
            "应用设置"
        )
        
        self.apps_settings_en = self.create_element(
            {"text": "Apps"},
            "Apps设置"
        )
        
        self.storage_settings = self.create_element(
            {"text": "存储"},
            "存储设置"
        )
        
        self.storage_settings_en = self.create_element(
            {"text": "Storage"},
            "Storage设置"
        )
        
        self.battery_settings = self.create_element(
            {"text": "电池"},
            "电池设置"
        )
        
        self.battery_settings_en = self.create_element(
            {"text": "Battery"},
            "Battery设置"
        )
        
        self.security_settings = self.create_element(
            {"text": "安全"},
            "安全设置"
        )
        
        self.security_settings_en = self.create_element(
            {"text": "Security"},
            "Security设置"
        )
        
        self.privacy_settings = self.create_element(
            {"text": "隐私"},
            "隐私设置"
        )
        
        self.privacy_settings_en = self.create_element(
            {"text": "Privacy"},
            "Privacy设置"
        )
        
        self.about_phone = self.create_element(
            {"text": "关于手机"},
            "关于手机"
        )
        
        self.about_phone_en = self.create_element(
            {"text": "About phone"},
            "About phone"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        # 等待搜索按钮或WiFi设置项出现
        return (self.search_button.wait_for_element(timeout) or 
                self.wifi_settings.wait_for_element(timeout) or
                self.wifi_settings_en.wait_for_element(timeout))
    
    def search_setting(self, keyword: str) -> bool:
        """
        搜索设置项
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            bool: 搜索是否成功
        """
        try:
            log.info(f"搜索设置项: {keyword}")
            
            # 点击搜索按钮
            if not self.search_button.click():
                log.error("点击搜索按钮失败")
                return False
            
            # 输入搜索关键词
            if not self.search_box.send_keys(keyword):
                log.error("输入搜索关键词失败")
                return False
            
            # 按回车键搜索
            self.driver.press("enter")
            
            return True
            
        except Exception as e:
            log.error(f"搜索设置项失败: {e}")
            return False
    
    def enter_wifi_settings(self) -> bool:
        """
        进入WiFi设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入WiFi设置")
            
            if self.wifi_settings.is_exists():
                return self.wifi_settings.click()
            elif self.wifi_settings_en.is_exists():
                return self.wifi_settings_en.click()
            else:
                log.error("未找到WiFi设置项")
                return False
                
        except Exception as e:
            log.error(f"进入WiFi设置失败: {e}")
            return False
    
    def enter_bluetooth_settings(self) -> bool:
        """
        进入蓝牙设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入蓝牙设置")
            
            if self.bluetooth_settings.is_exists():
                return self.bluetooth_settings.click()
            elif self.bluetooth_settings_en.is_exists():
                return self.bluetooth_settings_en.click()
            else:
                log.error("未找到蓝牙设置项")
                return False
                
        except Exception as e:
            log.error(f"进入蓝牙设置失败: {e}")
            return False
    
    def enter_display_settings(self) -> bool:
        """
        进入显示设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入显示设置")
            
            if self.display_settings.is_exists():
                return self.display_settings.click()
            elif self.display_settings_en.is_exists():
                return self.display_settings_en.click()
            else:
                log.error("未找到显示设置项")
                return False
                
        except Exception as e:
            log.error(f"进入显示设置失败: {e}")
            return False
    
    def enter_apps_settings(self) -> bool:
        """
        进入应用设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入应用设置")
            
            if self.apps_settings.is_exists():
                return self.apps_settings.click()
            elif self.apps_settings_en.is_exists():
                return self.apps_settings_en.click()
            else:
                log.error("未找到应用设置项")
                return False
                
        except Exception as e:
            log.error(f"进入应用设置失败: {e}")
            return False
    
    def enter_about_phone(self) -> bool:
        """
        进入关于手机
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入关于手机")
            
            if self.about_phone.is_exists():
                return self.about_phone.click()
            elif self.about_phone_en.is_exists():
                return self.about_phone_en.click()
            else:
                log.error("未找到关于手机设置项")
                return False
                
        except Exception as e:
            log.error(f"进入关于手机失败: {e}")
            return False
    
    def scroll_to_setting(self, setting_text: str) -> bool:
        """
        滚动到指定设置项
        
        Args:
            setting_text: 设置项文本
            
        Returns:
            bool: 是否找到设置项
        """
        try:
            log.info(f"滚动查找设置项: {setting_text}")
            
            # 最多滚动5次
            for i in range(5):
                # 检查设置项是否存在
                element = self.create_element({"text": setting_text}, f"设置项_{setting_text}")
                if element.is_exists():
                    log.info(f"找到设置项: {setting_text}")
                    return True
                
                # 向下滚动
                self.swipe_up()
                import time
                time.sleep(1)
            
            log.warning(f"未找到设置项: {setting_text}")
            return False
            
        except Exception as e:
            log.error(f"滚动查找设置项失败: {e}")
            return False
