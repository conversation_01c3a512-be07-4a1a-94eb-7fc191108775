"""
设置应用主页面 - 已更新适配TECNO设备
"""
from pages.base.common_page import CommonPage
from core.logger import log


class SettingsMainPage(CommonPage):
    """设置主页面 - TECNO设备适配版"""
    
    def __init__(self):
        """初始化设置主页面"""
        super().__init__("settings", "main_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素 - 根据TECNO设备实际结构"""
        # 搜索框 - 使用TECNO设备的实际定位器
        self.search_box = self.create_element(
            {"resourceId": "com.android.settings:id/tran_homepage_top_search_text"},
            "搜索框"
        )
        
        self.search_button = self.create_element(
            {"resourceId": "com.android.settings:id/tran_homepage_top_search"},
            "搜索按钮"
        )
        
        # TECNO设备的设置项 - 使用实际的文本
        self.hotspot_connection_settings = self.create_element(
            {"text": "热点与连接"},
            "热点与连接设置"
        )
        
        self.tecno_ai_settings = self.create_element(
            {"text": "TECNO AI"},
            "TECNO AI设置"
        )
        
        self.wallpaper_settings = self.create_element(
            {"text": "壁纸与个性化"},
            "壁纸与个性化设置"
        )
        
        self.display_settings = self.create_element(
            {"text": "显示与亮度"},
            "显示与亮度设置"
        )
        
        self.sound_settings = self.create_element(
            {"text": "声音与振动"},
            "声音与振动设置"
        )
        
        self.notification_settings = self.create_element(
            {"text": "通知与状态栏"},
            "通知与状态栏设置"
        )
        
        self.security_settings = self.create_element(
            {"text": "密码与安全"},
            "密码与安全设置"
        )
        
        self.privacy_settings = self.create_element(
            {"text": "权限与隐私"},
            "权限与隐私设置"
        )
        
        self.apps_settings = self.create_element(
            {"text": "应用管理"},
            "应用管理设置"
        )
        
        self.location_settings = self.create_element(
            {"text": "位置信息"},
            "位置信息设置"
        )
        
        self.special_features = self.create_element(
            {"text": "特色功能"},
            "特色功能设置"
        )
        
        # 通用设置项定位器（使用android:id/title）
        self.setting_item_generic = self.create_element(
            {"resourceId": "android:id/title"},
            "通用设置项"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        # 等待搜索按钮或任一设置项出现
        return (self.search_button.wait_for_element(timeout) or 
                self.hotspot_connection_settings.wait_for_element(timeout))
    
    def search_setting(self, keyword: str) -> bool:
        """
        搜索设置项
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            bool: 搜索是否成功
        """
        try:
            log.info(f"搜索设置项: {keyword}")
            
            # 点击搜索按钮
            if not self.search_button.click():
                log.error("点击搜索按钮失败")
                return False
            
            # 等待搜索框出现并输入关键词
            import time
            time.sleep(1)
            
            if not self.search_box.send_keys(keyword):
                log.error("输入搜索关键词失败")
                return False
            
            # 按回车键搜索
            self.driver.press("enter")
            
            return True
            
        except Exception as e:
            log.error(f"搜索设置项失败: {e}")
            return False
    
    def enter_hotspot_connection_settings(self) -> bool:
        """
        进入热点与连接设置（包含WiFi）
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入热点与连接设置")
            return self.hotspot_connection_settings.click()
        except Exception as e:
            log.error(f"进入热点与连接设置失败: {e}")
            return False
    
    def enter_display_settings(self) -> bool:
        """
        进入显示与亮度设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入显示与亮度设置")
            return self.display_settings.click()
        except Exception as e:
            log.error(f"进入显示与亮度设置失败: {e}")
            return False
    
    def enter_apps_settings(self) -> bool:
        """
        进入应用管理设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入应用管理设置")
            return self.apps_settings.click()
        except Exception as e:
            log.error(f"进入应用管理设置失败: {e}")
            return False
    
    def enter_security_settings(self) -> bool:
        """
        进入密码与安全设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入密码与安全设置")
            return self.security_settings.click()
        except Exception as e:
            log.error(f"进入密码与安全设置失败: {e}")
            return False
    
    def scroll_to_setting(self, setting_text: str) -> bool:
        """
        滚动到指定设置项
        
        Args:
            setting_text: 设置项文本
            
        Returns:
            bool: 是否找到设置项
        """
        try:
            log.info(f"滚动查找设置项: {setting_text}")
            
            # 最多滚动5次
            for i in range(5):
                # 检查设置项是否存在
                element = self.create_element({"text": setting_text}, f"设置项_{setting_text}")
                if element.is_exists():
                    log.info(f"找到设置项: {setting_text}")
                    return True
                
                # 向下滚动
                self.swipe_up()
                import time
                time.sleep(1)
            
            log.warning(f"未找到设置项: {setting_text}")
            return False
            
        except Exception as e:
            log.error(f"滚动查找设置项失败: {e}")
            return False
    
    def click_setting_by_text(self, setting_text: str) -> bool:
        """
        根据文本点击设置项
        
        Args:
            setting_text: 设置项文本
            
        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击设置项: {setting_text}")
            
            # 先尝试直接点击
            element = self.create_element({"text": setting_text}, f"设置项_{setting_text}")
            if element.is_exists():
                return element.click()
            
            # 如果不存在，尝试滚动查找
            if self.scroll_to_setting(setting_text):
                return element.click()
            
            return False
            
        except Exception as e:
            log.error(f"点击设置项失败: {e}")
            return False
