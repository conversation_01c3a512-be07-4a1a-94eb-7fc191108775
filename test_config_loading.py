#!/usr/bin/env python3
"""
测试配置文件加载功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from testcases.test_ella.base_ella_test import BaseEllaTest

def test_config_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载功能...")
    
    # 创建测试实例
    test_instance = BaseEllaTest()
    
    # 测试状态检查配置加载
    print("\n📋 测试状态检查配置加载:")
    status_config = test_instance._load_status_check_config()
    print(f"✅ 状态检查配置项数量: {len(status_config)}")
    
    for cmd_type, config in status_config.items():
        keywords = config.get('keywords', [])
        description = config.get('description', '')
        print(f"  - {cmd_type}: {description} (关键词: {keywords[:3]}{'...' if len(keywords) > 3 else ''})")
    
    # 测试进程清理配置加载
    print("\n🧹 测试进程清理配置加载:")
    cleanup_config = test_instance._load_process_cleanup_config()
    
    common_apps = cleanup_config.get('common_user_apps', [])
    print(f"✅ 常见用户应用数量: {len(common_apps)}")
    
    for app in common_apps[:5]:  # 只显示前5个
        package = app.get('package', '')
        description = app.get('description', '')
        category = app.get('category', '')
        print(f"  - {description} ({package}) [{category}]")
    
    if len(common_apps) > 5:
        print(f"  ... 还有 {len(common_apps) - 5} 个应用")
    
    clear_positions = cleanup_config.get('recent_apps_clear_positions', [])
    print(f"✅ 清理按钮位置数量: {len(clear_positions)}")
    
    for pos in clear_positions:
        x = pos.get('x')
        y = pos.get('y')
        desc = pos.get('description', '')
        print(f"  - ({x}, {y}) - {desc}")
    
    cleanup_settings = cleanup_config.get('cleanup_settings', {})
    print(f"✅ 清理设置:")
    for key, value in cleanup_settings.items():
        print(f"  - {key}: {value}")
    
    print("\n🎉 配置文件加载测试完成!")

if __name__ == "__main__":
    test_config_loading()
