#!/usr/bin/env python3
"""
临时手电筒状态检查脚本
用于诊断手电筒检测问题
"""
import subprocess

def check_flashlight_detailed():
    """详细检查手电筒状态"""
    print('=== 手动检查手电筒状态 ===')

    # 方法1: dumpsys camera
    print('\n1. dumpsys camera:')
    try:
        result = subprocess.run(['adb', 'shell', 'dumpsys', 'camera'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            output = result.stdout
            print(f'   输出长度: {len(output)} 字符')
            # 查找torch相关内容
            lines = output.split('\n')
            torch_lines = [line for line in lines if 'torch' in line.lower() or 'flash' in line.lower()]
            if torch_lines:
                print('   torch/flash相关行:')
                for line in torch_lines[:10]:  # 只显示前10行
                    print(f'     {line.strip()}')
            else:
                print('   未找到torch/flash相关内容')
        else:
            print(f'   错误: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')

    # 方法2: 系统属性
    print('\n2. 系统属性 sys.camera.torch:')
    try:
        result = subprocess.run(['adb', 'shell', 'getprop', 'sys.camera.torch'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            prop_value = result.stdout.strip()
            print(f'   值: "{prop_value}"')
        else:
            print(f'   错误: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')

    # 方法3: LED亮度
    print('\n3. LED亮度:')
    try:
        result = subprocess.run(['adb', 'shell', 'cat', '/sys/class/leds/torch/brightness'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            brightness = result.stdout.strip()
            print(f'   亮度值: "{brightness}"')
        else:
            print(f'   错误: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')

    # 方法4: 检查LED目录结构
    print('\n4. LED目录结构:')
    try:
        result = subprocess.run(['adb', 'shell', 'ls', '-la', '/sys/class/leds/'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print('   LED目录内容:')
            for line in result.stdout.split('\n')[:10]:
                if line.strip():
                    print(f'     {line.strip()}')
        else:
            print(f'   错误: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')

    # 方法5: 检查其他可能的LED路径
    print('\n5. 其他LED路径:')
    led_paths = [
        '/sys/class/leds/flashlight/brightness',
        '/sys/class/leds/led:torch_0/brightness',
        '/sys/class/leds/led:torch_1/brightness',
        '/sys/class/leds/white:flash/brightness',
        '/sys/class/leds/torch-light0/brightness',
        '/sys/class/leds/torch-light1/brightness'
    ]
    
    for path in led_paths:
        try:
            result = subprocess.run(['adb', 'shell', 'cat', path], capture_output=True, text=True, timeout=3)
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f'   {path}: "{value}"')
        except:
            pass

    # 方法6: 检查相机服务状态
    print('\n6. 相机服务状态:')
    try:
        result = subprocess.run(['adb', 'shell', 'dumpsys', 'media.camera'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            output = result.stdout
            if 'torch' in output.lower() or 'flash' in output.lower():
                lines = output.split('\n')
                torch_lines = [line for line in lines if 'torch' in line.lower() or 'flash' in line.lower()]
                print('   相机服务torch/flash相关行:')
                for line in torch_lines[:5]:
                    print(f'     {line.strip()}')
            else:
                print('   相机服务中未找到torch/flash相关内容')
    except Exception as e:
        print(f'   异常: {e}')

    print('\n=== 检查完成 ===')

def test_optimized_flashlight_detection():
    """测试优化后的手电筒检测"""
    print('\n=== 测试优化后的手电筒检测 ===')

    try:
        from pages.apps.ella.ella_status_checker import EllaStatusChecker
        from core.base_driver import driver_manager

        checker = EllaStatusChecker(driver_manager.driver)

        print('\n1. 基本状态检测:')
        status = checker.check_flashlight_status()
        print(f'   手电筒状态: {status}')

        print('\n2. 详细状态检测:')
        detailed_status = checker.get_flashlight_detailed_status()
        for key, value in detailed_status.items():
            if key == 'torch_logs' and value:
                print(f'   {key}: {len(value)} 条日志')
                for i, log_line in enumerate(value):
                    print(f'     [{i+1}] {log_line}')
            else:
                print(f'   {key}: {value}')

        print('\n=== 优化测试完成 ===')
        return status, detailed_status

    except Exception as e:
        print(f'测试失败: {e}')
        return False, {}

if __name__ == '__main__':
    check_flashlight_detailed()
    test_optimized_flashlight_detection()
