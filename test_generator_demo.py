#!/usr/bin/env python3
"""
演示Ella测试生成器的使用
"""
from tools.ella_test_generator import generate_ella_test, EllaTestGenerator

def demo_generator():
    """演示生成器功能"""
    print("=== Ella测试生成器演示 ===")
    
    # 示例1: 单个期望文本
    print("\n1. 生成单个期望文本的测试:")
    filepath1 = generate_ella_test("turn on wifi", "WiFi is now enabled")
    print(f"   生成文件: {filepath1}")
    
    # 示例2: 多个期望文本
    print("\n2. 生成多个期望文本的测试:")
    filepath2 = generate_ella_test("check battery level", ["Battery level is", "Current battery", "Battery at"])
    print(f"   生成文件: {filepath2}")
    
    # 示例3: 复杂命令名称
    print("\n3. 生成复杂命令名称的测试:")
    filepath3 = generate_ella_test("what's the weather like today?", ["Today's weather", "Weather forecast"])
    print(f"   生成文件: {filepath3}")
    
    # 示例4: 使用生成器类
    print("\n4. 使用生成器类:")
    generator = EllaTestGenerator()
    
    # 显示文件名转换
    command = "open music player & play song"
    filename = generator.sanitize_filename(command)
    class_name = generator.sanitize_class_name(command)
    method_name = generator.sanitize_method_name(command)
    
    print(f"   原始命令: {command}")
    print(f"   文件名: {filename}.py")
    print(f"   类名: {class_name}")
    print(f"   方法名: {method_name}")
    
    filepath4 = generator.generate_test_file(command, ["Music player opened", "Playing song"])
    print(f"   生成文件: {filepath4}")
    
    print("\n=== 演示完成 ===")
    print("✅ 所有测试文件已生成到 testcases/test_ella/ 目录")

if __name__ == "__main__":
    demo_generator()
