"""
测试设备配置加载优化
验证设备配置自动识别和更新功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.logger import log
from core.base_driver import BaseDriver
from utils.device_discovery import device_discovery
from utils.device_config_manager import device_config_manager
from utils.yaml_utils import YamlUtils


def test_device_config_optimization():
    """测试设备配置优化功能"""
    log.info("=" * 60)
    log.info("🧪 测试设备配置加载优化")
    log.info("=" * 60)
    
    try:
        # 1. 显示当前配置状态
        log.info("\n📋 当前配置状态:")
        config_path = YamlUtils.get_config_path("devices.yaml")
        current_config = YamlUtils.load_yaml(config_path)
        current_device = current_config.get('current_device', 'default')
        devices = current_config.get('devices', {})
        
        log.info(f"当前设备: {current_device}")
        log.info(f"配置的设备数量: {len(devices)}")
        
        for config_name, device_config in devices.items():
            is_current = "✓" if config_name == current_device else " "
            log.info(f"[{is_current}] {config_name}: {device_config.get('device_name', '')} ({device_config.get('device_id', '')})")
        
        # 2. 手动发现当前连接的设备
        log.info("\n🔍 手动发现当前连接的设备:")
        discovered_devices = device_discovery.discover_all_devices()
        
        if not discovered_devices:
            log.warning("未发现任何连接的设备，无法测试优化功能")
            return False
        
        device_discovery.print_device_summary(discovered_devices)
        
        # 3. 测试BaseDriver的优化逻辑
        log.info("\n🚀 测试BaseDriver设备配置加载优化:")
        
        # 创建新的BaseDriver实例来触发设备配置加载
        # 注意：由于BaseDriver是单例，我们需要重置实例
        BaseDriver._instance = None
        BaseDriver._driver = None
        
        # 创建新实例，这会触发_load_device_config方法
        driver = BaseDriver()
        
        # 4. 验证配置是否正确更新
        log.info("\n✅ 验证配置更新结果:")
        updated_config = YamlUtils.load_yaml(config_path)
        updated_current_device = updated_config.get('current_device', 'default')
        updated_devices = updated_config.get('devices', {})
        
        log.info(f"更新后当前设备: {updated_current_device}")
        log.info(f"更新后设备数量: {len(updated_devices)}")
        
        # 检查当前连接的设备是否正确设置为current_device
        current_device_info = discovered_devices[0]
        current_device_id = current_device_info.get('device_id', '')
        
        # 查找当前设备在配置中的名称
        found_config_name = None
        for config_name, device_config in updated_devices.items():
            if device_config.get('device_id') == current_device_id:
                found_config_name = config_name
                break
        
        if found_config_name and updated_current_device == found_config_name:
            log.info(f"✅ 当前连接设备 ({current_device_id}) 已正确设置为当前设备: {found_config_name}")
        else:
            log.warning(f"❌ 当前连接设备 ({current_device_id}) 未正确设置为当前设备")
            log.warning(f"   期望: {found_config_name}, 实际: {updated_current_device}")
        
        # 5. 测试设备信息获取
        log.info("\n📱 测试设备信息获取:")
        device_info = driver.get_device_info()
        window_size = driver.get_window_size()
        
        log.info(f"设备序列号: {device_info.get('serial', '')}")
        log.info(f"设备型号: {device_info.get('model', '')}")
        log.info(f"屏幕尺寸: {window_size}")
        
        log.info("\n🎉 设备配置优化测试完成!")
        return True
        
    except Exception as e:
        log.error(f"测试失败: {e}")
        import traceback
        log.error(traceback.format_exc())
        return False


def test_device_discovery_only():
    """仅测试设备发现功能"""
    log.info("=" * 60)
    log.info("🔍 测试设备发现功能")
    log.info("=" * 60)
    
    try:
        # 发现所有设备
        discovered_devices = device_discovery.discover_all_devices()
        
        if not discovered_devices:
            log.warning("未发现任何连接的设备")
            log.info("\n💡 请检查:")
            log.info("1. 设备是否已连接并开启USB调试")
            log.info("2. ADB是否正常工作 (运行 'adb devices')")
            log.info("3. 设备是否信任此计算机")
            return False
        
        # 打印设备摘要
        device_discovery.print_device_summary(discovered_devices)
        
        return True
        
    except Exception as e:
        log.error(f"设备发现测试失败: {e}")
        return False


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='测试设备配置优化功能')
    parser.add_argument('--discovery-only', action='store_true', 
                       help='仅测试设备发现功能，不测试配置加载')
    
    args = parser.parse_args()
    
    if args.discovery_only:
        success = test_device_discovery_only()
    else:
        success = test_device_config_optimization()
    
    if success:
        log.info("✅ 测试成功完成")
        sys.exit(0)
    else:
        log.error("❌ 测试失败")
        sys.exit(1)
