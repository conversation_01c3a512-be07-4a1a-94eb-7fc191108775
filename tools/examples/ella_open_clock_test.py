"""
Ella语音助手打开时钟应用测试
基于实际脚本实现，输入"open clock"命令，验证时钟应用是否打开
"""
import time
import subprocess
from core.logger import log
from pages.apps.ella.history.main_page import EllaMainPage


class EllaOpenClockTest:
    """Ella打开时钟应用测试类"""
    
    def __init__(self):
        """初始化"""
        self.ella_page = EllaMainPage()
        self.clock_package_name = "com.transsion.deskclock"
        self.test_results = {}
    
    def setup(self):
        """设置测试环境"""
        log.info("🔧 设置Ella打开时钟测试环境")
        
        # 确保时钟应用未运行
        self._stop_clock_app()
        
        # 启动Ella应用
        if not self.ella_page.start_app_with_activity():
            raise Exception("Ella应用启动失败")
        
        # 验证应用是否正确启动
        if not self.ella_page.app_package.wait_for_element(timeout=5):
            raise Exception("Ella应用包验证失败")
        
        log.info("✅ Ella应用启动成功")
        
        # 等待页面加载
        if not self.ella_page.wait_for_page_load(timeout=10):
            raise Exception("Ella页面加载失败")
        
        log.info("✅ Ella页面加载完成")
        
        # 记录初始状态
        self.ella_page.screenshot("ella_clock_setup_complete.png")
    
    def teardown(self):
        """清理测试环境"""
        log.info("🧹 清理Ella测试环境")
        self.ella_page.stop_app()
        # 可选：也停止时钟应用
        # self._stop_clock_app()
    
    def _stop_clock_app(self):
        """停止时钟应用"""
        try:
            subprocess.run(
                ["adb", "shell", "am", "force-stop", self.clock_package_name],
                capture_output=True,
                timeout=5
            )
            log.info(f"停止时钟应用: {self.clock_package_name}")
        except Exception as e:
            log.debug(f"停止时钟应用失败: {e}")
    
    def _check_clock_app_running(self) -> bool:
        """
        检查时钟应用是否正在运行
        
        Returns:
            bool: 时钟应用是否正在运行
        """
        try:
            log.info("检查时钟应用运行状态")
            
            # 方法1: 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if self.clock_package_name in output:
                    log.info(f"✅ 时钟应用正在运行: {self.clock_package_name}")
                    return True
            
            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if self.clock_package_name in output and "mCurrentFocus" in output:
                    log.info(f"✅ 时钟应用在前台: {self.clock_package_name}")
                    return True
            
            # 方法3: 检查进程
            result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", self.clock_package_name],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                log.info(f"✅ 时钟应用进程存在: {self.clock_package_name}")
                return True
            
            log.info(f"❌ 时钟应用未运行: {self.clock_package_name}")
            return False
            
        except Exception as e:
            log.error(f"检查时钟应用状态失败: {e}")
            return False
    
    def _verify_clock_app_package(self) -> bool:
        """
        通过UIAutomator2验证时钟应用包名
        
        Returns:
            bool: 时钟应用是否在前台
        """
        try:
            log.info("通过UIAutomator2验证时钟应用")
            
            # 获取当前应用信息
            current_app = self.ella_page.driver.app_current()
            log.info(f"当前应用信息: {current_app}")
            
            if current_app.get('package') == self.clock_package_name:
                log.info(f"✅ 时钟应用在前台: {self.clock_package_name}")
                return True
            
            # 检查是否有时钟应用的元素
            clock_elements = self.ella_page.driver(packageName=self.clock_package_name)
            if clock_elements.exists:
                log.info(f"✅ 检测到时钟应用元素: {self.clock_package_name}")
                return True
            
            log.info(f"❌ 时钟应用未在前台: {self.clock_package_name}")
            return False
            
        except Exception as e:
            log.error(f"UIAutomator2验证时钟应用失败: {e}")
            return False
    
    def test_open_clock_command(self):
        """
        测试open clock命令
        
        测试步骤:
        1. 确认时钟应用初始状态（未运行）
        2. 输入"open clock"命令
        3. 验证TTS响应
        4. 验证时钟应用是否打开
        
        预期结果:
        - TTS响应正常
        - 时钟应用成功打开
        """
        log.info("🧪 测试open clock命令")
        log.info("=" * 70)
        
        command = "open clock"
        
        # 步骤1: 确认时钟应用初始状态
        log.info("1️⃣ 确认时钟应用初始状态")
        initial_clock_status = self._check_clock_app_running()
        log.info(f"时钟应用初始状态: {'运行中' if initial_clock_status else '未运行'}")
        
        self.test_results['initial_clock_status'] = initial_clock_status
        
        # 截图记录初始状态
        self.ella_page.screenshot("clock_test_initial.png")
        
        # 步骤2: 点击输入框
        log.info("2️⃣ 点击输入框")
        
        if not self.ella_page.input_box.is_exists():
            raise Exception("未找到输入框(et_input)")
        
        if not self.ella_page.input_box.click():
            raise Exception("点击输入框失败")
        
        log.info("✅ 输入框点击成功")
        
        # 步骤3: 输入指令
        log.info(f"3️⃣ 输入指令: {command}")
        
        if not self.ella_page.input_box.send_keys(command):
            raise Exception(f"输入指令失败: {command}")
        
        log.info(f"✅ 指令输入成功: {command}")
        
        # 等待3秒
        time.sleep(3)
        
        # 截图记录输入状态
        self.ella_page.screenshot("clock_command_input.png")
        
        # 步骤4: 点击发送按钮
        log.info("4️⃣ 点击发送按钮")
        
        if not self.ella_page.send_button.is_exists():
            raise Exception("未找到发送按钮(fl_btn_three_btn)")
        
        if not self.ella_page.send_button.click():
            raise Exception("点击发送按钮失败")
        
        log.info("✅ 发送按钮点击成功")
        
        # 等待3秒
        time.sleep(3)
        
        # 截图记录发送状态
        self.ella_page.screenshot("clock_command_sent.png")
        
        # 步骤5: 验证TTS响应
        log.info("5️⃣ 验证TTS响应")
        
        tts_response = self.ella_page.verify_tts_response(timeout=15)
        self.test_results['tts_response'] = tts_response
        
        if tts_response:
            log.info("✅ TTS响应验证成功")
        else:
            log.warning("⚠️ TTS响应验证失败")
        
        # 截图记录TTS状态
        self.ella_page.screenshot("clock_tts_response.png")
        
        # 步骤6: 等待时钟应用启动
        log.info("6️⃣ 等待时钟应用启动")
        
        # 等待应用启动
        time.sleep(5)
        
        # 验证时钟应用状态
        final_clock_status = self._check_clock_app_running()
        log.info(f"时钟应用最终状态: {'运行中' if final_clock_status else '未运行'}")
        
        self.test_results['final_clock_status'] = final_clock_status
        
        # 通过UIAutomator2验证
        ui_clock_status = self._verify_clock_app_package()
        self.test_results['ui_clock_status'] = ui_clock_status
        
        # 最终截图
        self.ella_page.screenshot("clock_test_final.png")
        
        # 步骤7: 生成测试报告
        self._generate_test_report(command)
        
        return self.test_results
    
    def _generate_test_report(self, command: str):
        """生成测试报告"""
        log.info("📊 生成测试报告")
        log.info("=" * 70)
        
        # 基本信息
        log.info(f"测试命令: {command}")
        log.info(f"目标应用: {self.clock_package_name}")
        
        # 应用状态
        initial_status = self.test_results.get('initial_clock_status', False)
        final_status = self.test_results.get('final_clock_status', False)
        ui_status = self.test_results.get('ui_clock_status', False)
        tts_response = self.test_results.get('tts_response', False)
        
        log.info(f"时钟应用初始状态: {'运行中' if initial_status else '未运行'}")
        log.info(f"时钟应用最终状态: {'运行中' if final_status else '未运行'}")
        log.info(f"UI验证状态: {'通过' if ui_status else '未通过'}")
        log.info(f"状态变化: {'是' if initial_status != final_status else '否'}")
        log.info(f"TTS响应: {'正常' if tts_response else '异常'}")
        
        # 总体评估
        app_opened = final_status or ui_status  # 任一验证通过即认为应用已打开
        
        if app_opened and tts_response:
            test_result = "完全成功"
            log.info("🎉 测试结果: 完全成功")
        elif app_opened:
            test_result = "部分成功"
            log.info("✅ 测试结果: 部分成功（应用已打开）")
        elif tts_response:
            test_result = "部分成功"
            log.info("✅ 测试结果: 部分成功（TTS响应正常）")
        else:
            test_result = "需要检查"
            log.info("⚠️ 测试结果: 需要检查")
        
        self.test_results['test_result'] = test_result
        
        # 断言结果
        log.info("\n🎯 断言验证:")
        
        # 断言1: TTS播放按钮出现
        if tts_response:
            log.info("✅ 断言1通过: TTS播放按钮正常出现")
        else:
            log.warning("⚠️ 断言1警告: TTS播放按钮未出现")
        
        # 断言2: 时钟应用已打开（核心断言）
        assert app_opened, f"❌ 断言失败: 时钟应用未打开 ({self.clock_package_name})"
        log.info(f"✅ 断言2通过: 时钟应用已打开 ({self.clock_package_name})")
        
        # 断言3: 命令执行成功（时钟应用打开说明Ella命令执行成功）
        # 注意：时钟应用打开后，Ella可能不在前台，这是正常的
        if app_opened:
            log.info("✅ 断言3通过: Ella命令执行成功（时钟应用已打开）")
        else:
            log.warning("⚠️ 断言3警告: Ella命令执行效果需要验证")
        
        log.info(f"\n🏆 最终结果: {test_result}")


def main():
    """主函数 - 运行Ella打开时钟应用测试"""
    log.info("🚀 Ella语音助手打开时钟应用测试")
    log.info("=" * 80)
    
    test_instance = EllaOpenClockTest()
    
    try:
        # 设置环境
        test_instance.setup()
        
        # 运行测试
        results = test_instance.test_open_clock_command()
        
        # 输出最终结果
        log.info("\n" + "=" * 80)
        log.info("🎯 测试执行完成")
        log.info("=" * 80)
        
        log.info("📋 执行总结:")
        log.info(f"  - 命令执行: 成功")
        log.info(f"  - TTS响应: {'正常' if results.get('tts_response') else '异常'}")
        log.info(f"  - 时钟应用状态: {'已打开' if results.get('final_clock_status') else '未打开'}")
        log.info(f"  - UI验证: {'通过' if results.get('ui_clock_status') else '未通过'}")
        log.info(f"  - 测试结果: {results.get('test_result', 'N/A')}")
        
        log.info("\n💡 测试对比:")
        log.info("蓝牙测试 vs 时钟测试:")
        log.info("1. 输入命令: 'open bluetooth' → 'open clock'")
        log.info("2. 验证方式: ADB蓝牙状态 → 应用包名检查")
        log.info("3. 断言目标: 蓝牙开启 → 时钟应用打开")
        
        log.info("\n📁 生成的截图:")
        log.info("  - ella_clock_setup_complete.png")
        log.info("  - clock_test_initial.png")
        log.info("  - clock_command_input.png")
        log.info("  - clock_command_sent.png")
        log.info("  - clock_tts_response.png")
        log.info("  - clock_test_final.png")
        
        log.info("\n🎯 验证的功能:")
        log.info("✅ 1. Ella语音命令识别")
        log.info("✅ 2. 应用启动控制")
        log.info("✅ 3. TTS响应验证")
        log.info("✅ 4. 应用包名断言")
        log.info("✅ 5. 多层次状态验证")
        
    except Exception as e:
        log.error(f"❌ 测试执行失败: {e}")
        
    finally:
        # 清理环境
        test_instance.teardown()


if __name__ == "__main__":
    main()
