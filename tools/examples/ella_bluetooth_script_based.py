"""
基于实际脚本的Ella蓝牙控制测试
参考Java脚本实现的Python版本
"""
import time
from core.logger import log
from pages.apps.ella.history.main_page import EllaMainPage


class EllaBluetoothScriptBased:
    """基于实际脚本的Ella蓝牙控制类"""
    
    def __init__(self):
        """初始化"""
        self.ella_page = EllaMainPage()
        self.test_results = {}
    
    def setup(self):
        """设置测试环境 - 基于实际脚本"""
        log.info("🔧 设置Ella测试环境（基于实际脚本）")
        
        # 启动应用并指定Activity
        if not self.ella_page.start_app_with_activity():
            raise Exception("Ella应用启动失败")
        
        # 验证应用是否正确启动
        if not self.ella_page.app_package.wait_for_element(timeout=5):
            raise Exception("Ella应用包验证失败")
        
        log.info("✅ Ella应用启动成功")
        
        # 等待页面加载
        if not self.ella_page.wait_for_page_load(timeout=10):
            raise Exception("Ella页面加载失败")
        
        log.info("✅ Ella页面加载完成")
        
        # 记录初始状态
        self.ella_page.screenshot("ella_script_setup_complete.png")
    
    def teardown(self):
        """清理测试环境"""
        log.info("🧹 清理Ella测试环境")
        self.ella_page.stop_app()
    
    def test_open_bluetooth_command_script_based(self):
        """
        基于实际脚本的蓝牙开启测试
        
        测试步骤（参考Java脚本）:
        1. 启动应用并验证包名
        2. 点击输入框
        3. 输入"open bluetooth"指令
        4. 点击发送按钮
        5. 验证TTS播放按钮出现
        6. 验证蓝牙状态
        """
        log.info("🧪 基于实际脚本的蓝牙开启测试")
        log.info("=" * 70)
        
        command = "open bluetooth"
        
        # 步骤1: 记录蓝牙初始状态
        log.info("1️⃣ 记录蓝牙初始状态")
        initial_bluetooth_status = self.ella_page.check_bluetooth_status()
        log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
        
        self.test_results['initial_bluetooth_status'] = initial_bluetooth_status
        
        # 截图记录初始状态
        self.ella_page.screenshot("bluetooth_script_initial.png")
        
        # 步骤2: 点击输入框（参考脚本）
        log.info("2️⃣ 点击输入框")
        
        if not self.ella_page.input_box.is_exists():
            raise Exception("未找到输入框(et_input)")
        
        if not self.ella_page.input_box.click():
            raise Exception("点击输入框失败")
        
        log.info("✅ 输入框点击成功")
        
        # 步骤3: 输入指令（参考脚本）
        log.info(f"3️⃣ 输入指令: {command}")
        
        # 使用setText方法设置文本
        if not self.ella_page.input_box.send_keys(command):
            raise Exception(f"输入指令失败: {command}")
        
        log.info(f"✅ 指令输入成功: {command}")
        
        # 等待3秒（参考脚本）
        time.sleep(3)
        
        # 截图记录输入状态
        self.ella_page.screenshot("bluetooth_script_input.png")
        
        # 步骤4: 点击发送按钮（参考脚本）
        log.info("4️⃣ 点击发送按钮")
        
        if not self.ella_page.send_button.is_exists():
            raise Exception("未找到发送按钮(fl_btn_three_btn)")
        
        if not self.ella_page.send_button.click():
            raise Exception("点击发送按钮失败")
        
        log.info("✅ 发送按钮点击成功")
        
        # 等待3秒（参考脚本）
        time.sleep(3)
        
        # 截图记录发送状态
        self.ella_page.screenshot("bluetooth_script_sent.png")
        
        # 步骤5: 验证TTS播放按钮（参考脚本）
        log.info("5️⃣ 验证TTS播放按钮")
        
        tts_response = self.ella_page.verify_tts_response(timeout=15)
        self.test_results['tts_response'] = tts_response
        
        if tts_response:
            log.info("✅ TTS响应验证成功")
        else:
            log.warning("⚠️ TTS响应验证失败")
        
        # 截图记录TTS状态
        self.ella_page.screenshot("bluetooth_script_tts.png")
        
        # 步骤6: 验证蓝牙状态
        log.info("6️⃣ 验证蓝牙状态")
        
        # 等待蓝牙状态可能的变化
        time.sleep(3)
        
        final_bluetooth_status = self.ella_page.check_bluetooth_status()
        log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
        
        self.test_results['final_bluetooth_status'] = final_bluetooth_status
        
        # 最终截图
        self.ella_page.screenshot("bluetooth_script_final.png")
        
        # 步骤7: 生成测试报告
        self._generate_test_report(command)
        
        return self.test_results
    
    def _generate_test_report(self, command: str):
        """生成测试报告"""
        log.info("📊 生成测试报告")
        log.info("=" * 70)
        
        # 基本信息
        log.info(f"测试命令: {command}")
        
        # 蓝牙状态
        initial_status = self.test_results.get('initial_bluetooth_status', False)
        final_status = self.test_results.get('final_bluetooth_status', False)
        tts_response = self.test_results.get('tts_response', False)
        
        log.info(f"蓝牙初始状态: {'开启' if initial_status else '关闭'}")
        log.info(f"蓝牙最终状态: {'开启' if final_status else '关闭'}")
        log.info(f"状态变化: {'是' if initial_status != final_status else '否'}")
        log.info(f"TTS响应: {'正常' if tts_response else '异常'}")
        
        # 总体评估
        bluetooth_ok = final_status  # 蓝牙最终应该是开启的
        
        if bluetooth_ok and tts_response:
            test_result = "完全成功"
            log.info("🎉 测试结果: 完全成功")
        elif bluetooth_ok:
            test_result = "部分成功"
            log.info("✅ 测试结果: 部分成功（蓝牙功能正常）")
        elif tts_response:
            test_result = "部分成功"
            log.info("✅ 测试结果: 部分成功（TTS响应正常）")
        else:
            test_result = "需要检查"
            log.info("⚠️ 测试结果: 需要检查")
        
        self.test_results['test_result'] = test_result
        
        # 断言结果（基于实际脚本的验证逻辑）
        log.info("\n🎯 断言验证:")
        
        # 断言1: TTS播放按钮出现（参考脚本的assertNotNullAndClick）
        if tts_response:
            log.info("✅ 断言1通过: TTS播放按钮正常出现")
        else:
            log.warning("⚠️ 断言1警告: TTS播放按钮未出现")
        
        # 断言2: 蓝牙状态正确
        assert final_status, "❌ 断言失败: 蓝牙未开启"
        log.info("✅ 断言2通过: 蓝牙已开启")
        
        # 断言3: 应用包验证（参考脚本的Assert.assertTrue）
        assert self.ella_page.app_package.is_exists(), "❌ 断言失败: 应用包验证失败"
        log.info("✅ 断言3通过: 应用包验证成功")
        
        log.info(f"\n🏆 最终结果: {test_result}")


def test_close_bluetooth_command():
    """测试关闭蓝牙命令"""
    log.info("🧪 测试关闭蓝牙命令")
    log.info("=" * 70)
    
    test_instance = EllaBluetoothScriptBased()
    
    try:
        # 设置环境
        test_instance.setup()
        
        command = "close bluetooth"
        
        # 记录初始状态
        initial_status = test_instance.ella_page.check_bluetooth_status()
        log.info(f"蓝牙初始状态: {'开启' if initial_status else '关闭'}")
        
        # 执行命令
        log.info(f"输入命令: {command}")
        
        # 点击输入框
        if not test_instance.ella_page.input_box.click():
            raise Exception("点击输入框失败")
        
        # 输入命令
        if not test_instance.ella_page.input_box.send_keys(command):
            raise Exception(f"输入命令失败: {command}")
        
        time.sleep(3)
        
        # 点击发送按钮
        if not test_instance.ella_page.send_button.click():
            raise Exception("点击发送按钮失败")
        
        time.sleep(3)
        
        # 验证TTS响应
        tts_response = test_instance.ella_page.verify_tts_response()
        
        # 验证蓝牙状态
        time.sleep(3)
        final_status = test_instance.ella_page.check_bluetooth_status()
        
        log.info(f"TTS响应: {'正常' if tts_response else '异常'}")
        log.info(f"蓝牙最终状态: {'开启' if final_status else '关闭'}")
        
        # 验证蓝牙是否关闭
        if not final_status:
            log.info("✅ 蓝牙关闭命令执行成功")
        else:
            log.warning("⚠️ 蓝牙仍然开启")
        
        # 截图
        test_instance.ella_page.screenshot("bluetooth_close_final.png")
        
    except Exception as e:
        log.error(f"❌ 关闭蓝牙测试失败: {e}")
        
    finally:
        test_instance.teardown()


def main():
    """主函数 - 运行基于实际脚本的Ella蓝牙控制测试"""
    log.info("🚀 基于实际脚本的Ella蓝牙控制测试")
    log.info("=" * 80)
    
    test_instance = EllaBluetoothScriptBased()
    
    try:
        # 设置环境
        test_instance.setup()
        
        # 运行开启蓝牙测试
        results = test_instance.test_open_bluetooth_command_script_based()
        
        # 输出最终结果
        log.info("\n" + "=" * 80)
        log.info("🎯 测试执行完成")
        log.info("=" * 80)
        
        log.info("📋 执行总结:")
        log.info(f"  - 命令执行: 成功")
        log.info(f"  - TTS响应: {'正常' if results.get('tts_response') else '异常'}")
        log.info(f"  - 蓝牙状态: {'开启' if results.get('final_bluetooth_status') else '关闭'}")
        log.info(f"  - 测试结果: {results.get('test_result', 'N/A')}")
        
        log.info("\n💡 脚本对比:")
        log.info("Java脚本步骤 → Python实现:")
        log.info("1. startApp() → start_app_with_activity()")
        log.info("2. findUiBy().click() → input_box.click()")
        log.info("3. setText() → send_keys()")
        log.info("4. findUiBy().click() → send_button.click()")
        log.info("5. assertNotNullAndClick() → verify_tts_response()")
        
        log.info("\n📁 生成的截图:")
        log.info("  - ella_script_setup_complete.png")
        log.info("  - bluetooth_script_initial.png")
        log.info("  - bluetooth_script_input.png")
        log.info("  - bluetooth_script_sent.png")
        log.info("  - bluetooth_script_tts.png")
        log.info("  - bluetooth_script_final.png")
        
        # 可选：测试关闭蓝牙
        log.info("\n" + "=" * 80)
        test_close_bluetooth_command()
        
    except Exception as e:
        log.error(f"❌ 测试执行失败: {e}")
        
    finally:
        # 清理环境
        test_instance.teardown()


if __name__ == "__main__":
    main()
