"""
查找时钟应用的包名
"""
import subprocess
import re
from core.logger import log


def find_clock_app_package():
    """查找时钟应用的包名"""
    log.info("🔍 查找时钟应用包名")
    
    try:
        # 获取所有已安装的应用包
        result = subprocess.run(
            ["adb", "shell", "pm", "list", "packages"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            log.error(f"获取应用包列表失败: {result.stderr}")
            return []
        
        packages = result.stdout.strip().split('\n')
        clock_packages = []
        
        # 查找可能的时钟应用包名
        clock_keywords = [
            'clock', 'time', 'alarm', 'timer', 'deskclock',
            'timekeeper', 'clockwork', 'chronometer'
        ]
        
        for package_line in packages:
            if package_line.startswith('package:'):
                package_name = package_line.replace('package:', '')
                
                # 检查是否包含时钟相关关键词
                for keyword in clock_keywords:
                    if keyword.lower() in package_name.lower():
                        clock_packages.append(package_name)
                        break
        
        log.info(f"找到可能的时钟应用包: {clock_packages}")
        return clock_packages
        
    except Exception as e:
        log.error(f"查找时钟应用包失败: {e}")
        return []


def get_app_info(package_name):
    """获取应用详细信息"""
    try:
        # 获取应用信息
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "package", package_name],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            
            # 提取应用名称
            app_name_match = re.search(r'applicationLabel=([^\n]+)', output)
            app_name = app_name_match.group(1) if app_name_match else "Unknown"
            
            # 提取主Activity
            activity_match = re.search(r'android\.intent\.action\.MAIN.*?cmp=([^/]+)/([^\s]+)', output)
            main_activity = activity_match.group(2) if activity_match else "Unknown"
            
            return {
                'package_name': package_name,
                'app_name': app_name,
                'main_activity': main_activity
            }
    except Exception as e:
        log.debug(f"获取应用信息失败 {package_name}: {e}")
    
    return None


def test_launch_app(package_name):
    """测试启动应用"""
    try:
        log.info(f"测试启动应用: {package_name}")
        
        # 启动应用
        result = subprocess.run(
            ["adb", "shell", "monkey", "-p", package_name, "-c", "android.intent.category.LAUNCHER", "1"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            log.info(f"✅ 应用启动成功: {package_name}")
            
            # 等待一下
            import time
            time.sleep(3)
            
            # 检查当前运行的应用
            current_result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "mCurrentFocus"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )
            
            if current_result.returncode == 0:
                log.info(f"当前焦点: {current_result.stdout.strip()}")
            
            return True
        else:
            log.warning(f"⚠️ 应用启动失败: {package_name}")
            return False
            
    except Exception as e:
        log.error(f"测试启动应用失败 {package_name}: {e}")
        return False


def main():
    """主函数"""
    log.info("🕐 时钟应用包名查找工具")
    log.info("=" * 60)
    
    # 查找时钟应用包
    clock_packages = find_clock_app_package()
    
    if not clock_packages:
        log.warning("未找到时钟应用包")
        
        # 尝试常见的时钟应用包名
        common_clock_packages = [
            "com.android.deskclock",
            "com.google.android.deskclock",
            "com.transsion.deskclock",
            "com.transsion.clock",
            "com.sec.android.app.clockpackage",
            "com.htc.android.worldclock",
            "com.miui.clock",
            "com.oneplus.deskclock"
        ]
        
        log.info("尝试常见的时钟应用包名...")
        for package in common_clock_packages:
            # 检查包是否存在
            check_result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages", package],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if check_result.returncode == 0 and check_result.stdout.strip():
                clock_packages.append(package)
                log.info(f"找到时钟应用: {package}")
    
    if not clock_packages:
        log.error("❌ 未找到任何时钟应用")
        return
    
    log.info(f"\n📱 找到 {len(clock_packages)} 个时钟应用:")
    
    best_clock_app = None
    
    for package in clock_packages:
        log.info(f"\n分析应用: {package}")
        
        # 获取应用信息
        app_info = get_app_info(package)
        if app_info:
            log.info(f"  应用名称: {app_info['app_name']}")
            log.info(f"  主Activity: {app_info['main_activity']}")
            
            # 测试启动
            if test_launch_app(package):
                log.info(f"  ✅ 启动测试: 成功")
                if not best_clock_app:
                    best_clock_app = app_info
            else:
                log.info(f"  ❌ 启动测试: 失败")
        else:
            log.info(f"  ⚠️ 无法获取应用信息")
    
    if best_clock_app:
        log.info("\n🎯 推荐的时钟应用配置:")
        log.info("=" * 60)
        log.info(f"包名: {best_clock_app['package_name']}")
        log.info(f"应用名: {best_clock_app['app_name']}")
        log.info(f"Activity: {best_clock_app['main_activity']}")
        
        log.info("\n📝 config.yaml配置:")
        print(f"""
  clock:
    package_name: "{best_clock_app['package_name']}"
    activity: "{best_clock_app['package_name']}/{best_clock_app['main_activity']}"
    app_name: "时钟"
""")
        
        return best_clock_app
    else:
        log.error("❌ 未找到可用的时钟应用")
        return None


if __name__ == "__main__":
    main()
