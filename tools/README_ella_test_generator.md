# Ella测试用例生成器

这个工具模块用于快速生成标准化的Ella语音助手测试用例文件。

## 功能特性

- 🚀 **快速生成**: 根据命令和期望文本自动生成完整的测试用例文件
- 📝 **标准化格式**: 生成的测试用例遵循项目标准格式和最佳实践
- 🔧 **智能命名**: 自动将命令转换为合法的文件名、类名和方法名
- 📋 **多种期望文本**: 支持单个或多个期望响应文本
- 🎯 **完整测试结构**: 包含Allure报告、pytest标记、断言验证等完整结构

## 使用方法

### 1. 命令行模式

```bash
# 单个期望文本
python tools/ella_test_generator.py "open flashlight" "Flashlight is turned on"

# 多个期望文本
python tools/ella_test_generator.py "switching charging speed" "Sorry" "I cannot" "Not supported"
```

### 2. 交互模式

```bash
python tools/ella_test_generator.py
```

然后按提示输入命令和期望文本。

### 3. Python API

```python
from tools.ella_test_generator import generate_ella_test, EllaTestGenerator

# 方式1: 使用便捷函数
filepath = generate_ella_test("open camera", "Camera opened successfully")

# 方式2: 使用生成器类
generator = EllaTestGenerator()
filepath = generator.generate_test_file("turn on wifi", ["WiFi enabled", "WiFi is on"])
```

## 生成的文件结构

生成的测试文件包含以下结构：

```python
"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("Ella语音助手基础指令")
class TestEllaOpenCamera(SimpleEllaTest):
    """Ella命令测试类"""
    command = "open camera"
    expected_text = ["Camera opened successfully"]

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_camera(self, ella_app):
        # 测试实现...
```

## 命名规则

### 文件名转换
- 原始命令: `"open flashlight"`
- 文件名: `test_open_flashlight.py`

### 类名转换
- 原始命令: `"open flashlight"`
- 类名: `TestEllaOpenFlashlight`

### 方法名转换
- 原始命令: `"open flashlight"`
- 方法名: `test_open_flashlight`

### 特殊字符处理
- 移除特殊字符: `"what's the weather?"` → `"whats the weather"`
- 空格转下划线: `"turn on wifi"` → `"turn_on_wifi"`
- 驼峰命名(类名): `"turn on wifi"` → `"TestEllaTurnOnWifi"`

## 期望文本格式

### 单个期望文本
```python
expected_text = ["Camera opened successfully"]
```

### 多个期望文本
```python
expected_text = ["Sorry", "I cannot", "Not supported"]
```

## 输出目录

默认输出到 `testcases/test_ella/` 目录，可以通过参数自定义输出目录。

## 示例

### 示例1: 基础命令
```bash
python tools/ella_test_generator.py "open flashlight" "Flashlight is turned on"
```

生成文件: `testcases/test_ella/test_open_flashlight.py`

### 示例2: 复杂命令
```bash
python tools/ella_test_generator.py "what's the weather like today?" "Today's weather" "Weather forecast"
```

生成文件: `testcases/test_ella/test_whats_the_weather_like_today.py`

### 示例3: 错误响应
```bash
python tools/ella_test_generator.py "switching charging speed" "Sorry" "I cannot help"
```

生成文件: `testcases/test_ella/test_switching_charging_speed.py`

## 注意事项

1. **文件覆盖**: 如果目标文件已存在，会被覆盖
2. **目录创建**: 如果输出目录不存在，会自动创建
3. **编码格式**: 生成的文件使用UTF-8编码
4. **依赖关系**: 生成的测试文件依赖 `SimpleEllaTest` 基类

## 扩展功能

可以通过修改 `EllaTestGenerator` 类来扩展功能：

- 自定义模板格式
- 添加更多测试步骤
- 修改命名规则
- 添加其他pytest标记

## 故障排除

### 常见问题

1. **导入错误**: 确保 `testcases.test_ella.base_ella_test` 模块存在
2. **权限错误**: 确保对输出目录有写权限
3. **编码问题**: 确保终端支持UTF-8编码

### 调试模式

可以在代码中添加调试信息来排查问题：

```python
generator = EllaTestGenerator()
print(f"文件名: {generator.sanitize_filename(command)}")
print(f"类名: {generator.sanitize_class_name(command)}")
print(f"方法名: {generator.sanitize_method_name(command)}")
```
