"""
设备管理工具
提供设备发现、配置更新、设备切换等功能
"""
import argparse
import sys
from core.logger import log
from utils.device_discovery import device_discovery
from utils.device_config_manager import device_config_manager
from utils.uiautomator2_manager import uiautomator2_manager


def discover_devices():
    """发现设备"""
    log.info("🔍 开始发现连接的设备...")
    
    # 发现所有设备
    devices = device_discovery.discover_all_devices()
    
    if not devices:
        log.warning("未发现任何连接的设备")
        log.info("\n💡 请检查:")
        log.info("1. 设备是否已连接并开启USB调试")
        log.info("2. ADB是否正常工作 (运行 'adb devices')")
        log.info("3. 设备是否信任此计算机")
        return False
    
    # 打印设备摘要
    device_discovery.print_device_summary(devices)
    
    return True


def auto_update_config():
    """自动更新设备配置"""
    log.info("🔄 自动发现设备并更新配置...")
    
    success = device_config_manager.auto_discover_and_update()
    
    if success:
        log.info("\n✅ 设备配置自动更新完成")
        log.info("💡 现在可以运行测试了:")
        log.info("  python quick_start.py")
        log.info("  python run_tests.py --smoke")
    else:
        log.error("\n❌ 设备配置更新失败")
    
    return success


def list_devices():
    """列出配置的设备"""
    log.info("📱 已配置的设备列表:")
    
    try:
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        devices = config.get('devices', {})
        
        if not devices:
            log.info("没有配置任何设备")
            return
        
        log.info("=" * 80)
        for config_name, device_config in devices.items():
            is_current = "✓ [当前]" if config_name == current_device else "  "
            log.info(f"{is_current} {config_name}:")
            log.info(f"    设备ID: {device_config.get('device_id', '')}")
            log.info(f"    设备名称: {device_config.get('device_name', '')}")
            log.info(f"    HiOS版本: {device_config.get('platform_version', '')}")
            log.info(f"    Android版本: {device_config.get('android_version', '')}")
            
            if device_config.get('auto_discovered'):
                log.info(f"    自动发现: 是 ({device_config.get('last_updated', '')})")
            else:
                log.info(f"    自动发现: 否")
            log.info("")
        
        auto_discovery = config.get('auto_discovery', {})
        if auto_discovery.get('last_update'):
            log.info(f"最后自动发现时间: {auto_discovery.get('last_update')}")
            log.info(f"发现设备数量: {auto_discovery.get('discovered_devices', 0)}")
        
    except Exception as e:
        log.error(f"列出设备失败: {e}")


def switch_device(config_name: str):
    """切换当前设备"""
    log.info(f"🔄 切换到设备: {config_name}")
    
    success = device_config_manager.switch_current_device(config_name)
    
    if success:
        log.info("✅ 设备切换成功")
        
        # 显示当前设备信息
        config = device_config_manager.load_current_config()
        device_info = config['devices'][config_name]
        log.info(f"当前设备: {device_info.get('device_name', '')} ({config_name})")
        log.info(f"设备ID: {device_info.get('device_id', '')}")
        log.info(f"HiOS版本: {device_info.get('platform_version', '')}")
    else:
        log.error("❌ 设备切换失败")
    
    return success


def remove_device(config_name: str):
    """删除设备配置"""
    log.info(f"🗑️ 删除设备配置: {config_name}")
    
    success = device_config_manager.remove_device_config(config_name)
    
    if success:
        log.info("✅ 设备配置删除成功")
    else:
        log.error("❌ 设备配置删除失败")
    
    return success


def show_current_device():
    """显示当前设备信息"""
    log.info("📱 当前设备信息:")
    
    try:
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        devices = config.get('devices', {})
        
        if current_device not in devices:
            log.error(f"当前设备配置 '{current_device}' 不存在")
            return
        
        device_info = devices[current_device]
        
        log.info("=" * 60)
        log.info(f"配置名称: {current_device}")
        log.info(f"设备ID: {device_info.get('device_id', '')}")
        log.info(f"设备名称: {device_info.get('device_name', '')}")
        log.info(f"HiOS版本: {device_info.get('platform_version', '')}")
        log.info(f"Android版本: {device_info.get('android_version', '')}")
        log.info(f"品牌: {device_info.get('brand', '')}")
        log.info(f"型号: {device_info.get('model', '')}")
        log.info(f"屏幕分辨率: {device_info.get('screen_resolution', '')}")
        log.info(f"CPU架构: {device_info.get('cpu_abi', '')}")
        
        if device_info.get('auto_discovered'):
            log.info(f"自动发现: 是")
            log.info(f"最后更新: {device_info.get('last_updated', '')}")
        else:
            log.info(f"自动发现: 否")
        
    except Exception as e:
        log.error(f"显示当前设备信息失败: {e}")


def test_device_connection():
    """测试设备连接"""
    log.info("🧪 测试设备连接...")

    try:
        from core.base_driver import driver_manager

        # 获取设备信息
        device_info = driver_manager.get_device_info()
        log.info("✅ 设备连接成功")
        log.info(f"连接的设备: {device_info}")

        # 获取屏幕尺寸
        width, height = driver_manager.get_window_size()
        log.info(f"屏幕尺寸: {width} x {height}")

        # 截图测试
        screenshot_path = driver_manager.screenshot("device_connection_test.png", use_test_class_dir=False)
        log.info(f"测试截图: {screenshot_path}")

        return True

    except Exception as e:
        log.error(f"❌ 设备连接测试失败: {e}")
        return False


def check_uiautomator2_service():
    """检查UIAutomator2服务状态"""
    log.info("🔍 检查UIAutomator2服务状态...")

    try:
        # 获取当前设备配置
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        device_config = config.get('devices', {}).get(current_device, {})
        device_id = device_config.get('device_id')

        # 获取服务状态
        status = uiautomator2_manager.get_service_status(device_id)

        log.info("=" * 60)
        log.info(f"设备: {status['device_id']}")
        log.info(f"服务运行: {'✅ 是' if status['service_running'] else '❌ 否'}")
        log.info(f"健康检查: {'✅ 通过' if status['health_check'] else '❌ 失败'}")

        if status['device_info']:
            device_info = status['device_info']
            log.info(f"设备序列号: {device_info.get('serial', 'Unknown')}")
            log.info(f"设备型号: {device_info.get('model', 'Unknown')}")
            log.info(f"Android版本: {device_info.get('version', 'Unknown')}")

        if status['error']:
            log.error(f"错误信息: {status['error']}")

        return status['health_check']

    except Exception as e:
        log.error(f"检查UIAutomator2服务状态失败: {e}")
        return False


def restart_uiautomator2_service():
    """重启UIAutomator2服务"""
    log.info("🔄 重启UIAutomator2服务...")

    try:
        # 获取当前设备配置
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        device_config = config.get('devices', {}).get(current_device, {})
        device_id = device_config.get('device_id')

        # 重启服务
        if uiautomator2_manager.restart_service(device_id):
            log.info("✅ UIAutomator2服务重启成功")

            # 验证服务状态
            if check_uiautomator2_service():
                log.info("✅ 服务重启后状态正常")
                return True
            else:
                log.warning("⚠️ 服务重启后状态异常")
                return False
        else:
            log.error("❌ UIAutomator2服务重启失败")
            return False

    except Exception as e:
        log.error(f"重启UIAutomator2服务失败: {e}")
        return False


def fix_version_issue():
    """修复版本相关问题"""
    log.info("🔧 修复UIAutomator2版本问题...")

    try:
        # 获取当前设备配置
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        device_config = config.get('devices', {}).get(current_device, {})
        device_id = device_config.get('device_id')

        # 修复版本问题
        if uiautomator2_manager.fix_version_issue(device_id):
            log.info("✅ 版本问题修复成功")
            return True
        else:
            log.error("❌ 版本问题修复失败")
            return False

    except Exception as e:
        log.error(f"修复版本问题失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Android设备管理工具")
    
    parser.add_argument("--discover", action="store_true", help="发现连接的设备")
    parser.add_argument("--auto-update", action="store_true", help="自动发现设备并更新配置")
    parser.add_argument("--list", action="store_true", help="列出已配置的设备")
    parser.add_argument("--current", action="store_true", help="显示当前设备信息")
    parser.add_argument("--switch", type=str, help="切换到指定设备")
    parser.add_argument("--remove", type=str, help="删除指定设备配置")
    parser.add_argument("--test", action="store_true", help="测试设备连接")
    parser.add_argument("--check-service", action="store_true", help="检查UIAutomator2服务状态")
    parser.add_argument("--restart-service", action="store_true", help="重启UIAutomator2服务")
    parser.add_argument("--fix-version", action="store_true", help="修复版本相关问题")

    args = parser.parse_args()
    
    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        log.info("🔧 Android设备管理工具")
        log.info("=" * 60)
        log.info("使用方法:")
        log.info("  python device_manager.py --discover        # 发现设备")
        log.info("  python device_manager.py --auto-update     # 自动更新配置")
        log.info("  python device_manager.py --list            # 列出设备")
        log.info("  python device_manager.py --current         # 显示当前设备")
        log.info("  python device_manager.py --switch <name>   # 切换设备")
        log.info("  python device_manager.py --test            # 测试连接")
        log.info("  python device_manager.py --check-service   # 检查UIAutomator2服务")
        log.info("  python device_manager.py --restart-service # 重启UIAutomator2服务")
        log.info("  python device_manager.py --fix-version     # 修复版本问题")
        log.info("")
        log.info("💡 推荐工作流程:")
        log.info("1. python device_manager.py --auto-update   # 首次使用")
        log.info("2. python device_manager.py --current       # 查看当前设备")
        log.info("3. python device_manager.py --test          # 测试连接")
        log.info("4. python device_manager.py --check-service # 检查服务状态")
        return
    
    try:
        if args.discover:
            discover_devices()
        
        elif args.auto_update:
            auto_update_config()
        
        elif args.list:
            list_devices()
        
        elif args.current:
            show_current_device()
        
        elif args.switch:
            switch_device(args.switch)
        
        elif args.remove:
            remove_device(args.remove)
        
        elif args.test:
            test_device_connection()

        elif args.check_service:
            check_uiautomator2_service()

        elif args.restart_service:
            restart_uiautomator2_service()

        elif args.fix_version:
            fix_version_issue()

        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        log.info("\n用户中断操作")
    except Exception as e:
        log.error(f"执行失败: {e}")


if __name__ == "__main__":
    main()
