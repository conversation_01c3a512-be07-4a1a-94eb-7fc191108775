"""
Allure自动安装脚本
自动下载并安装Allure报告工具
"""
import os
import sys
import zipfile
import requests
import subprocess
from pathlib import Path


def download_allure():
    """下载Allure"""
    print("🔽 正在下载Allure...")
    
    # Allure下载URL（最新版本）
    allure_version = "2.24.0"
    download_url = f"https://github.com/allure-framework/allure2/releases/download/{allure_version}/allure-{allure_version}.zip"
    
    # 下载目录
    download_dir = Path("temp")
    download_dir.mkdir(exist_ok=True)
    
    zip_file = download_dir / f"allure-{allure_version}.zip"
    
    try:
        print(f"下载地址: {download_url}")
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(zip_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r下载进度: {percent:.1f}%", end="", flush=True)
        
        print(f"\n✅ 下载完成: {zip_file}")
        return zip_file
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None


def extract_allure(zip_file):
    """解压Allure"""
    print("\n📦 正在解压Allure...")
    
    try:
        # 解压目录
        extract_dir = Path("C:/allure")
        extract_dir.mkdir(exist_ok=True)
        
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        
        # 查找解压后的目录
        allure_dirs = list(extract_dir.glob("allure-*"))
        if allure_dirs:
            allure_home = allure_dirs[0]
            print(f"✅ 解压完成: {allure_home}")
            return allure_home
        else:
            print("❌ 未找到Allure目录")
            return None
            
    except Exception as e:
        print(f"❌ 解压失败: {e}")
        return None


def add_to_path(allure_home):
    """添加Allure到系统PATH"""
    print("\n🔧 正在配置环境变量...")
    
    try:
        allure_bin = allure_home / "bin"
        
        # 使用PowerShell添加到用户PATH
        powershell_cmd = f'''
        $userPath = [Environment]::GetEnvironmentVariable("Path", "User")
        if ($userPath -notlike "*{allure_bin}*") {{
            $newPath = $userPath + ";{allure_bin}"
            [Environment]::SetEnvironmentVariable("Path", $newPath, "User")
            Write-Host "✅ 已添加到用户PATH: {allure_bin}"
        }} else {{
            Write-Host "⚠️ PATH中已存在Allure路径"
        }}
        '''
        
        result = subprocess.run(
            ["powershell", "-Command", powershell_cmd],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 环境变量配置成功")
            print("⚠️ 请重新启动命令行窗口使PATH生效")
            return True
        else:
            print(f"❌ 环境变量配置失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 配置环境变量失败: {e}")
        return False


def test_allure_installation():
    """测试Allure安装"""
    print("\n🧪 测试Allure安装...")
    
    try:
        # 尝试直接运行allure命令
        result = subprocess.run(
            ["allure", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print(f"✅ Allure安装成功!")
            print(f"版本信息: {result.stdout.strip()}")
            return True
        else:
            print("⚠️ Allure命令不可用，可能需要重启命令行")
            return False
            
    except FileNotFoundError:
        print("⚠️ Allure命令不可用，可能需要重启命令行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def create_test_report():
    """创建测试报告示例"""
    print("\n📊 创建测试报告示例...")
    
    try:
        # 确保有测试结果
        results_dir = Path("reports/allure-results")
        if not results_dir.exists() or not list(results_dir.glob("*.json")):
            print("⚠️ 没有测试结果，先运行一些测试...")
            
            # 运行一个简单的测试
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                "testcases/", "-v", "--alluredir=reports/allure-results",
                "--tb=short", "-x"  # 遇到第一个失败就停止
            ], capture_output=True, text=True, timeout=60)
            
            print("测试执行完成")
        
        # 生成报告
        report_dir = Path("reports/allure-report")
        
        result = subprocess.run([
            "allure", "generate", 
            "reports/allure-results", 
            "-o", str(report_dir), 
            "--clean"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✅ 测试报告生成成功: {report_dir}")
            
            # 尝试打开报告
            try:
                subprocess.Popen(["allure", "open", str(report_dir)])
                print("🌐 正在打开测试报告...")
            except:
                print(f"💡 手动打开报告: allure open {report_dir}")
            
            return True
        else:
            print(f"❌ 报告生成失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试报告失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Allure自动安装工具")
    print("=" * 50)
    
    # 检查是否已安装
    if test_allure_installation():
        print("✅ Allure已经安装，无需重复安装")
        
        # 询问是否生成测试报告
        choice = input("\n是否生成测试报告示例? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            create_test_report()
        return
    
    print("开始安装Allure...")
    
    # 步骤1: 下载Allure
    zip_file = download_allure()
    if not zip_file:
        print("❌ 下载失败，请手动安装")
        print("手动安装步骤:")
        print("1. 访问: https://github.com/allure-framework/allure2/releases")
        print("2. 下载最新版本的zip文件")
        print("3. 解压到 C:/allure/")
        print("4. 将 C:/allure/allure-x.x.x/bin 添加到系统PATH")
        return
    
    # 步骤2: 解压Allure
    allure_home = extract_allure(zip_file)
    if not allure_home:
        print("❌ 解压失败")
        return
    
    # 步骤3: 配置环境变量
    if not add_to_path(allure_home):
        print("❌ 环境变量配置失败")
        print(f"💡 请手动将以下路径添加到系统PATH:")
        print(f"   {allure_home / 'bin'}")
        return
    
    # 步骤4: 测试安装
    print("\n" + "=" * 50)
    print("🎉 Allure安装完成!")
    print("=" * 50)
    
    print("\n💡 下一步:")
    print("1. 重新启动命令行窗口")
    print("2. 运行 'allure --version' 验证安装")
    print("3. 运行 'python run_tests.py --open-report' 生成测试报告")
    
    # 清理临时文件
    try:
        zip_file.unlink()
        zip_file.parent.rmdir()
        print("\n🧹 临时文件已清理")
    except:
        pass


if __name__ == "__main__":
    main()
