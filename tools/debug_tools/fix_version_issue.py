#!/usr/bin/env python3
"""
修复UIAutomator2版本问题的脚本
用于解决 "Invalid version: ''" 错误
"""
import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.logger import log
from utils.uiautomator2_manager import uiautomator2_manager
from utils.device_config_manager import device_config_manager


def advanced_version_fix(device_id: str = None) -> bool:
    """
    高级版本问题修复

    Args:
        device_id: 设备ID

    Returns:
        bool: 修复是否成功
    """
    try:
        log.info("🔧 执行高级版本问题修复...")

        # 1. 强制重新安装UIAutomator2
        log.info("1️⃣ 强制重新安装UIAutomator2...")
        if not force_reinstall_uiautomator2(device_id):
            log.warning("重新安装失败，继续其他修复步骤")

        # 2. 清理缓存和临时文件
        log.info("2️⃣ 清理缓存和临时文件...")
        clear_uiautomator2_cache(device_id)

        # 3. 重启ADB服务
        log.info("3️⃣ 重启ADB服务...")
        restart_adb_service()

        # 4. 等待设备重新连接
        log.info("4️⃣ 等待设备重新连接...")
        time.sleep(5)

        # 5. 重新初始化UIAutomator2
        log.info("5️⃣ 重新初始化UIAutomator2...")
        if reinitialize_uiautomator2(device_id):
            log.info("✅ 高级修复成功")
            return True
        else:
            log.error("❌ 高级修复失败")
            return False

    except Exception as e:
        log.error(f"高级修复过程中发生错误: {e}")
        return False


def force_reinstall_uiautomator2(device_id: str = None) -> bool:
    """强制重新安装UIAutomator2"""
    try:
        # 构建adb命令前缀
        adb_prefix = ["adb"]
        if device_id:
            adb_prefix.extend(["-s", device_id])

        # 卸载UIAutomator2应用
        uninstall_cmd = adb_prefix + ["uninstall", "com.github.uiautomator"]
        subprocess.run(uninstall_cmd, capture_output=True, text=True, timeout=30)

        # 卸载ATX Agent
        uninstall_atx_cmd = adb_prefix + ["uninstall", "com.github.uiautomator.test"]
        subprocess.run(uninstall_atx_cmd, capture_output=True, text=True, timeout=30)

        # 重新安装
        if device_id:
            install_cmd = ["python", "-m", "uiautomator2", "init", "--serial", device_id, "--reinstall"]
        else:
            install_cmd = ["python", "-m", "uiautomator2", "init", "--reinstall"]

        result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            log.info("✅ UIAutomator2重新安装成功")
            return True
        else:
            log.warning(f"UIAutomator2重新安装警告: {result.stderr}")
            return False

    except Exception as e:
        log.error(f"强制重新安装UIAutomator2失败: {e}")
        return False


def clear_uiautomator2_cache(device_id: str = None) -> None:
    """清理UIAutomator2缓存"""
    try:
        # 构建adb命令前缀
        adb_prefix = ["adb"]
        if device_id:
            adb_prefix.extend(["-s", device_id])

        # 清理应用缓存
        cache_commands = [
            adb_prefix + ["shell", "pm", "clear", "com.github.uiautomator"],
            adb_prefix + ["shell", "pm", "clear", "com.github.uiautomator.test"],
            adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/minicap*"],
            adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/minitouch*"],
            adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/atx*"]
        ]

        for cmd in cache_commands:
            try:
                subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            except:
                pass

        log.info("✅ 缓存清理完成")

    except Exception as e:
        log.warning(f"清理缓存失败: {e}")


def restart_adb_service() -> None:
    """重启ADB服务"""
    try:
        # 停止ADB服务
        subprocess.run(["adb", "kill-server"], capture_output=True, text=True, timeout=10)
        time.sleep(2)

        # 启动ADB服务
        subprocess.run(["adb", "start-server"], capture_output=True, text=True, timeout=10)
        time.sleep(3)

        log.info("✅ ADB服务重启完成")

    except Exception as e:
        log.warning(f"重启ADB服务失败: {e}")


def reinitialize_uiautomator2(device_id: str = None) -> bool:
    """重新初始化UIAutomator2"""
    try:
        # 使用uiautomator2的init命令
        if device_id:
            init_cmd = ["python", "-m", "uiautomator2", "init", "--serial", device_id]
        else:
            init_cmd = ["python", "-m", "uiautomator2", "init"]

        result = subprocess.run(init_cmd, capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            log.info("✅ UIAutomator2重新初始化成功")
            return True
        else:
            log.warning(f"UIAutomator2重新初始化警告: {result.stderr}")
            return False

    except Exception as e:
        log.error(f"重新初始化UIAutomator2失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🔧 UIAutomator2版本问题修复工具 (增强版)")
    log.info("=" * 60)

    try:
        # 获取当前设备配置
        config = device_config_manager.load_current_config()
        current_device = config.get('current_device', 'default')
        device_config = config.get('devices', {}).get(current_device, {})
        device_id = device_config.get('device_id')
        device_name = device_config.get('device_name', 'Unknown')

        log.info(f"当前设备: {device_name} ({device_id or 'default'})")
        log.info("")

        # 步骤1: 检查当前服务状态
        log.info("📋 步骤1: 检查UIAutomator2服务状态")
        status = uiautomator2_manager.get_service_status(device_id)

        log.info(f"  服务运行: {'✅ 是' if status['service_running'] else '❌ 否'}")
        log.info(f"  健康检查: {'✅ 通过' if status['health_check'] else '❌ 失败'}")

        if status['error']:
            log.error(f"  错误信息: {status['error']}")

        # 步骤2: 如果服务有问题，尝试标准修复
        if not status['health_check']:
            log.info("")
            log.info("🔄 步骤2: 尝试标准修复")

            if uiautomator2_manager.fix_version_issue(device_id):
                log.info("✅ 标准修复成功")

                # 验证修复结果
                new_status = uiautomator2_manager.get_service_status(device_id)
                if new_status['health_check']:
                    return test_connection_and_finish(device_id)
                else:
                    log.warning("标准修复后状态仍然异常，尝试高级修复")
            else:
                log.warning("标准修复失败，尝试高级修复")

            # 步骤3: 高级修复
            log.info("")
            log.info("🔧 步骤3: 执行高级修复")

            if advanced_version_fix(device_id):
                # 验证高级修复结果
                final_status = uiautomator2_manager.get_service_status(device_id)
                if final_status['health_check']:
                    return test_connection_and_finish(device_id)
                else:
                    log.error("❌ 高级修复后服务状态仍然异常")
                    return False
            else:
                log.error("❌ 高级修复失败")
                return False
        else:
            log.info("")
            log.info("✅ UIAutomator2服务状态正常，无需修复")
            return test_connection_and_finish(device_id)

    except Exception as e:
        log.error(f"修复过程中发生错误: {e}")
        return False


def test_connection_and_finish(device_id: str = None) -> bool:
    """测试连接并完成修复"""
    try:
        log.info("")
        log.info("🎉 修复完成！现在测试设备连接...")

        # 测试连接
        log.info("🧪 测试设备连接...")
        from core.base_driver import driver_manager
        device_info = driver_manager.get_device_info()
        log.info("✅ 设备连接测试成功")
        log.info(f"  设备序列号: {device_info.get('serial', 'Unknown')}")
        log.info(f"  设备型号: {device_info.get('model', 'Unknown')}")
        log.info(f"  Android版本: {device_info.get('version', 'Unknown')}")

        return True

    except Exception as e:
        log.error(f"❌ 设备连接测试失败: {e}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            log.info("")
            log.info("💡 提示:")
            log.info("  现在可以运行你的测试脚本了")
            log.info("  如果问题仍然存在，请尝试:")
            log.info("  1. 重启设备")
            log.info("  2. 重新连接USB")
            log.info("  3. 检查ADB连接")
            sys.exit(0)
        else:
            log.error("")
            log.error("❌ 修复失败，请检查:")
            log.error("  1. 设备是否正常连接")
            log.error("  2. USB调试是否开启")
            log.error("  3. ADB是否正常工作")
            log.error("  4. UIAutomator2是否正确安装")
            sys.exit(1)
    except KeyboardInterrupt:
        log.info("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        log.error(f"程序异常退出: {e}")
        sys.exit(1)
