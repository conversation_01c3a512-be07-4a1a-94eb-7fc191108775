"""
元素定位器探测工具
帮助获取正确的元素定位信息
"""
import time
import json
from core.logger import log
from core.base_driver import driver_manager
from utils.file_utils import FileUtils


class ElementInspector:
    """元素探测器"""
    
    def __init__(self):
        """初始化探测器"""
        self.driver = driver_manager.driver
        
    def start_app_and_inspect(self, package_name: str, app_name: str = ""):
        """
        启动应用并探测元素
        
        Args:
            package_name: 应用包名
            app_name: 应用名称
        """
        log.info(f"=" * 60)
        log.info(f"开始探测应用: {app_name or package_name}")
        log.info(f"=" * 60)
        
        try:
            # 启动应用
            log.info(f"启动应用: {package_name}")
            self.driver.app_start(package_name)
            
            # 等待应用加载
            time.sleep(3)
            
            # 截图
            screenshot_name = f"{app_name or 'app'}_inspection.png"
            screenshot_path = driver_manager.screenshot(screenshot_name)
            log.info(f"截图保存: {screenshot_path}")
            
            # 获取页面结构
            self.dump_page_structure(app_name or package_name)
            
            # 查找常见元素
            self.find_common_elements(app_name or package_name)
            
        except Exception as e:
            log.error(f"应用探测失败: {e}")
    
    def dump_page_structure(self, app_name: str):
        """
        导出页面结构
        
        Args:
            app_name: 应用名称
        """
        try:
            log.info("获取页面结构...")
            
            # 获取XML结构
            xml_content = self.driver.dump_hierarchy()
            
            # 保存XML文件
            xml_filename = f"{app_name}_structure.xml"
            xml_path = f"reports/{xml_filename}"
            
            with open(xml_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            log.info(f"页面结构保存: {xml_path}")
            
            # 解析并显示关键信息
            self.parse_xml_structure(xml_content, app_name)
            
        except Exception as e:
            log.error(f"获取页面结构失败: {e}")
    
    def parse_xml_structure(self, xml_content: str, app_name: str):
        """
        解析XML结构并提取关键信息
        
        Args:
            xml_content: XML内容
            app_name: 应用名称
        """
        try:
            import xml.etree.ElementTree as ET
            
            root = ET.fromstring(xml_content)
            elements_info = []
            
            # 递归解析所有元素
            def parse_element(element, depth=0):
                # 获取元素属性
                attrs = element.attrib
                
                # 过滤有用的元素（有text、resource-id或content-desc的）
                if (attrs.get('text') or 
                    attrs.get('resource-id') or 
                    attrs.get('content-desc') or
                    attrs.get('class') in ['android.widget.Button', 'android.widget.EditText', 
                                          'android.widget.TextView', 'android.widget.ImageButton']):
                    
                    element_info = {
                        'class': attrs.get('class', ''),
                        'text': attrs.get('text', ''),
                        'resource_id': attrs.get('resource-id', ''),
                        'content_desc': attrs.get('content-desc', ''),
                        'clickable': attrs.get('clickable', 'false'),
                        'enabled': attrs.get('enabled', 'true'),
                        'bounds': attrs.get('bounds', ''),
                        'depth': depth
                    }
                    
                    # 只保存有意义的元素
                    if (element_info['text'] or 
                        element_info['resource_id'] or 
                        element_info['content_desc']):
                        elements_info.append(element_info)
                
                # 递归处理子元素
                for child in element:
                    parse_element(child, depth + 1)
            
            parse_element(root)
            
            # 保存元素信息到JSON文件
            json_filename = f"{app_name}_elements.json"
            json_path = f"reports/{json_filename}"
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(elements_info, f, ensure_ascii=False, indent=2)
            
            log.info(f"元素信息保存: {json_path}")
            
            # 显示关键元素
            self.display_key_elements(elements_info, app_name)
            
        except Exception as e:
            log.error(f"解析XML结构失败: {e}")
    
    def display_key_elements(self, elements_info: list, app_name: str):
        """
        显示关键元素信息
        
        Args:
            elements_info: 元素信息列表
            app_name: 应用名称
        """
        log.info(f"\n📋 {app_name} 关键元素信息:")
        log.info("=" * 80)
        
        # 按类型分组显示
        buttons = []
        inputs = []
        texts = []
        others = []
        
        for element in elements_info:
            class_name = element['class']
            
            if 'Button' in class_name:
                buttons.append(element)
            elif 'EditText' in class_name:
                inputs.append(element)
            elif 'TextView' in class_name:
                texts.append(element)
            else:
                others.append(element)
        
        # 显示按钮
        if buttons:
            log.info("\n🔘 按钮元素:")
            for btn in buttons[:10]:  # 只显示前10个
                self.log_element_info(btn)
        
        # 显示输入框
        if inputs:
            log.info("\n📝 输入框元素:")
            for inp in inputs[:5]:  # 只显示前5个
                self.log_element_info(inp)
        
        # 显示文本元素
        if texts:
            log.info("\n📄 文本元素:")
            for txt in texts[:10]:  # 只显示前10个
                self.log_element_info(txt)
        
        # 显示其他元素
        if others:
            log.info("\n🔧 其他可交互元素:")
            for other in others[:5]:  # 只显示前5个
                self.log_element_info(other)
    
    def log_element_info(self, element: dict):
        """
        记录元素信息
        
        Args:
            element: 元素信息字典
        """
        info_parts = []
        
        if element['text']:
            info_parts.append(f"text='{element['text']}'")
        
        if element['resource_id']:
            info_parts.append(f"resourceId='{element['resource_id']}'")
        
        if element['content_desc']:
            info_parts.append(f"description='{element['content_desc']}'")
        
        info_parts.append(f"class='{element['class']}'")
        info_parts.append(f"clickable={element['clickable']}")
        
        log.info(f"  - {' | '.join(info_parts)}")
    
    def find_common_elements(self, app_name: str):
        """
        查找常见元素
        
        Args:
            app_name: 应用名称
        """
        log.info(f"\n🔍 查找 {app_name} 常见元素...")
        
        # 常见的元素文本和描述
        common_texts = [
            # 中文
            "确定", "取消", "返回", "搜索", "设置", "更多", "菜单",
            "允许", "拒绝", "同意", "继续", "下一步", "完成",
            # 英文
            "OK", "Cancel", "Back", "Search", "Settings", "More", "Menu",
            "Allow", "Deny", "Agree", "Continue", "Next", "Done",
            # 数字
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            # 运算符
            "+", "-", "×", "÷", "=", ".", "C", "AC"
        ]
        
        found_elements = []
        
        for text in common_texts:
            try:
                # 尝试通过文本查找
                element = self.driver(text=text)
                if element.exists:
                    info = element.info
                    found_elements.append({
                        'locator_type': 'text',
                        'locator_value': text,
                        'resource_id': info.get('resourceId', ''),
                        'class': info.get('className', ''),
                        'bounds': info.get('bounds', {})
                    })
            except:
                pass
        
        if found_elements:
            log.info("✅ 找到的常见元素:")
            for elem in found_elements:
                log.info(f"  - {elem['locator_type']}='{elem['locator_value']}' | "
                        f"resourceId='{elem['resource_id']}' | "
                        f"class='{elem['class']}'")
        else:
            log.info("⚠️ 未找到预定义的常见元素，请查看完整的元素列表")
    
    def generate_locator_suggestions(self, app_name: str):
        """
        生成定位器建议
        
        Args:
            app_name: 应用名称
        """
        log.info(f"\n💡 {app_name} 定位器建议:")
        log.info("=" * 60)
        
        if app_name.lower() == 'calculator':
            log.info("📱 计算器应用建议:")
            log.info("1. 查看 reports/calculator_elements.json 文件")
            log.info("2. 寻找数字按钮的 resource-id 或 text 属性")
            log.info("3. 寻找运算符按钮的定位信息")
            log.info("4. 寻找结果显示区域的定位信息")
            
        elif app_name.lower() == 'settings':
            log.info("⚙️ 设置应用建议:")
            log.info("1. 查看 reports/settings_elements.json 文件")
            log.info("2. 寻找 WiFi、蓝牙等设置项的定位信息")
            log.info("3. 寻找搜索框的定位信息")
            log.info("4. 注意TECNO设备可能使用自定义的设置界面")


def main():
    """主函数"""
    log.info("🔍 Android元素定位器探测工具")
    log.info("=" * 60)
    
    inspector = ElementInspector()
    
    # 探测计算器应用
    log.info("\n1️⃣ 探测计算器应用...")
    inspector.start_app_and_inspect("com.google.android.calculator", "calculator")
    inspector.generate_locator_suggestions("calculator")
    
    time.sleep(3)
    
    # 探测设置应用
    log.info("\n2️⃣ 探测设置应用...")
    inspector.start_app_and_inspect("com.android.settings", "settings")
    inspector.generate_locator_suggestions("settings")
    
    log.info("\n" + "=" * 60)
    log.info("🎯 探测完成！")
    log.info("📁 请查看以下文件获取详细信息:")
    log.info("  - reports/calculator_elements.json")
    log.info("  - reports/settings_elements.json")
    log.info("  - reports/calculator_structure.xml")
    log.info("  - reports/settings_structure.xml")
    log.info("  - reports/screenshots/")
    log.info("\n💡 下一步:")
    log.info("1. 查看JSON文件中的元素信息")
    log.info("2. 根据实际的resource-id和text更新页面对象")
    log.info("3. 运行 'python update_locators.py' 自动更新定位器")


if __name__ == "__main__":
    main()
