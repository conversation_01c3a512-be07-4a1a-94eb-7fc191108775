"""
测试README.md中的截图功能示例
验证文档中的代码示例是否正确工作
"""
import pytest
import allure
from core.logger import log
from core.base_driver import driver_manager
from utils.screenshot_utils import screenshot_manager


@allure.feature("README示例验证")
class TestReadmeExamples:
    """验证README.md中的截图功能示例"""
    
    @allure.title("测试基础截图示例")
    @allure.description("验证README中基础截图的代码示例")
    @pytest.mark.smoke
    def test_basic_screenshot_example(self):
        """测试基础截图示例"""
        with allure.step("执行基础截图"):
            # README示例1: 基础截图（自动按测试类分文件夹）
            screenshot_path = driver_manager.screenshot("test_step.png")
            
            # 验证截图路径包含测试类名称
            assert "TestReadmeExamples" in screenshot_path
            log.info(f"✅ 基础截图示例验证通过: {screenshot_path}")
    
    @allure.title("测试禁用测试类目录示例")
    @allure.description("验证README中禁用测试类目录的代码示例")
    @pytest.mark.smoke
    def test_disable_test_class_dir_example(self):
        """测试禁用测试类目录示例"""
        with allure.step("执行禁用测试类目录截图"):
            # README示例2: 禁用测试类目录（保存到根目录）
            screenshot_path = driver_manager.screenshot("global_test.png", use_test_class_dir=False)
            
            # 验证截图路径不包含测试类名称
            assert "TestReadmeExamples" not in screenshot_path
            assert "global_test.png" in screenshot_path
            log.info(f"✅ 禁用测试类目录示例验证通过: {screenshot_path}")
    
    @allure.title("测试截图管理器示例")
    @allure.description("验证README中截图管理器的代码示例")
    @pytest.mark.smoke
    def test_screenshot_manager_example(self):
        """测试截图管理器示例"""
        with allure.step("使用截图管理器"):
            # README示例3: 使用截图管理器
            screenshot_path = screenshot_manager.take_screenshot(
                driver_manager.driver,
                "custom_screenshot.png",
                "自定义截图描述"
            )
            
            # 验证截图路径包含测试类名称
            assert "TestReadmeExamples" in screenshot_path
            assert "custom_screenshot.png" in screenshot_path
            log.info(f"✅ 截图管理器示例验证通过: {screenshot_path}")
    
    @allure.title("测试Allure报告截图示例")
    @allure.description("验证README中Allure报告截图的代码示例")
    @pytest.mark.smoke
    def test_allure_screenshot_example(self):
        """测试Allure报告截图示例"""
        with allure.step("验证页面加载"):
            # 启动设置应用进行测试
            driver_manager.start_app("com.android.settings")
            
            # README示例4: 在Allure报告中使用截图
            from core.base_page import BasePage
            settings_page = BasePage("settings", "main_page")
            
            # 截图会自动保存到TestReadmeExamples文件夹
            screenshot_path = settings_page.screenshot("page_verification.png")
            
            # 添加到Allure报告
            allure.attach.file(
                screenshot_path,
                name="页面验证截图",
                attachment_type=allure.attachment_type.PNG
            )
            
            # 验证截图路径
            assert "TestReadmeExamples" in screenshot_path
            assert "page_verification.png" in screenshot_path
            log.info(f"✅ Allure报告截图示例验证通过: {screenshot_path}")
            
            # 停止应用
            driver_manager.stop_app("com.android.settings")


def test_screenshot_manager_tool_examples():
    """测试截图管理工具的示例命令"""
    log.info("=" * 60)
    log.info("📋 验证README中的截图管理工具示例")
    log.info("=" * 60)
    
    # 验证截图统计功能
    log.info("1️⃣ 测试截图统计功能...")
    summary = screenshot_manager.get_screenshot_summary()
    
    log.info("📊 当前截图统计:")
    log.info(f"  总文件数: {summary['total_files']}")
    log.info(f"  总大小: {summary['total_size'] / 1024 / 1024:.2f} MB")
    log.info(f"  测试类数量: {len(summary['test_classes'])}")
    
    if summary['test_classes']:
        log.info("📂 按测试类分组:")
        for test_class in sorted(summary['test_classes']):
            class_info = summary['by_class'][test_class]
            log.info(f"  {test_class}: {class_info['file_count']}个文件, {class_info['size']/1024:.1f}KB")
    
    log.info("✅ 截图统计功能验证通过")
    
    # 验证目录结构
    log.info("\n2️⃣ 验证目录结构...")
    from utils.yaml_utils import YamlUtils
    import os
    
    project_root = YamlUtils.get_project_root()
    screenshot_base = os.path.join(project_root, "reports", "screenshots")
    
    if os.path.exists(screenshot_base):
        log.info(f"📁 截图根目录: {screenshot_base}")
        
        test_class_dirs = [d for d in os.listdir(screenshot_base) 
                          if os.path.isdir(os.path.join(screenshot_base, d))]
        
        log.info(f"📂 发现 {len(test_class_dirs)} 个测试类目录:")
        for test_dir in sorted(test_class_dirs):
            dir_path = os.path.join(screenshot_base, test_dir)
            png_files = [f for f in os.listdir(dir_path) if f.endswith('.png')]
            log.info(f"  {test_dir}/ ({len(png_files)} 个截图文件)")
    
    log.info("✅ 目录结构验证通过")
    
    log.info("\n🎯 README示例验证完成！")
    log.info("📖 所有README.md中的截图功能示例都能正常工作")


if __name__ == "__main__":
    # 运行示例验证
    test_screenshot_manager_tool_examples()
    
    log.info("\n💡 要运行完整的pytest测试，请执行:")
    log.info("pytest test_readme_examples.py -v --alluredir=reports/allure-results")
