"""
Ella语音助手简单测试
测试基本的文本输入和蓝牙命令功能
"""
import time
from core.logger import log
from pages.apps.ella.history.main_page import EllaMainPage


def test_ella_basic_functionality():
    """测试Ella基本功能"""
    log.info("🧪 测试Ella基本功能")
    log.info("=" * 80)
    
    try:
        # 创建Ella页面对象
        ella_page = EllaMainPage()
        
        # 启动应用
        log.info("启动Ella应用...")
        if not ella_page.start_app():
            log.error("Ella应用启动失败")
            return False
        
        # 等待页面加载
        log.info("等待页面加载...")
        if not ella_page.wait_for_page_load(timeout=10):
            log.error("Ella页面加载失败")
            ella_page.stop_app()
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 截图记录初始状态
        screenshot_path = ella_page.screenshot("ella_basic_test_start.png")
        log.info(f"初始状态截图: {screenshot_path}")
        
        # 测试文本输入功能
        log.info("\n1️⃣ 测试文本输入功能...")
        test_command = "hello"
        
        if ella_page.input_text_command(test_command):
            log.info("✅ 文本输入成功")
            
            # 截图记录输入状态
            screenshot_path = ella_page.screenshot("ella_text_input.png")
            log.info(f"输入状态截图: {screenshot_path}")
            
            # 发送命令
            if ella_page.send_command():
                log.info("✅ 命令发送成功")
                
                # 等待响应
                if ella_page.wait_for_response(timeout=15):
                    log.info("✅ 收到响应")
                    
                    # 获取响应文本
                    response = ella_page.get_response_text()
                    log.info(f"响应内容: {response}")
                    
                    # 截图记录响应
                    screenshot_path = ella_page.screenshot("ella_hello_response.png")
                    log.info(f"响应截图: {screenshot_path}")
                else:
                    log.warning("⚠️ 未收到响应")
            else:
                log.warning("⚠️ 命令发送失败")
        else:
            log.warning("⚠️ 文本输入失败")
        
        # 等待一下再进行下一个测试
        time.sleep(3)
        
        # 停止应用
        ella_page.stop_app()
        log.info("✅ Ella基本功能测试完成")
        return True
        
    except Exception as e:
        log.error(f"❌ Ella基本功能测试失败: {e}")
        return False


def test_ella_bluetooth_command():
    """测试Ella蓝牙命令"""
    log.info("🧪 测试Ella蓝牙命令")
    log.info("=" * 80)
    
    try:
        # 创建Ella页面对象
        ella_page = EllaMainPage()
        
        # 启动应用
        log.info("启动Ella应用...")
        if not ella_page.start_app():
            log.error("Ella应用启动失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load(timeout=10):
            log.error("Ella页面加载失败")
            ella_page.stop_app()
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 记录蓝牙初始状态
        initial_bluetooth_status = ella_page.check_bluetooth_status()
        log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
        
        # 测试open bluetooth命令
        log.info("\n1️⃣ 测试 'open bluetooth' 命令...")
        command = "open bluetooth"
        
        # 执行命令
        if ella_page.execute_text_command(command):
            log.info("✅ 命令执行成功")
            
            # 等待响应
            if ella_page.wait_for_response(timeout=20):
                log.info("✅ 收到AI响应")
                
                # 获取响应文本
                response_text = ella_page.get_response_text()
                log.info(f"AI响应: {response_text}")
                
                # 截图记录响应
                screenshot_path = ella_page.screenshot("ella_bluetooth_response.png")
                log.info(f"响应截图: {screenshot_path}")
                
                # 验证响应是否包含命令相关内容
                if ella_page.verify_command_in_response(command, response_text):
                    log.info("✅ 响应包含命令相关内容")
                else:
                    log.warning("⚠️ 响应未包含命令相关内容")
                
                # 等待蓝牙状态可能的变化
                time.sleep(3)
                
                # 检查蓝牙最终状态
                final_bluetooth_status = ella_page.check_bluetooth_status()
                log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
                
                # 验证结果
                if final_bluetooth_status:
                    log.info("✅ 蓝牙已开启")
                    test_result = True
                else:
                    log.warning("⚠️ 蓝牙未开启")
                    test_result = False
                
                # 总结测试结果
                log.info("\n📊 测试结果总结:")
                log.info(f"  命令: {command}")
                log.info(f"  响应: {response_text[:100]}..." if len(response_text) > 100 else f"  响应: {response_text}")
                log.info(f"  蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
                log.info(f"  蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
                log.info(f"  状态变化: {'是' if initial_bluetooth_status != final_bluetooth_status else '否'}")
                log.info(f"  测试结果: {'成功' if test_result else '部分成功'}")
                
            else:
                log.warning("⚠️ 未收到AI响应")
                test_result = False
        else:
            log.error("❌ 命令执行失败")
            test_result = False
        
        # 停止应用
        ella_page.stop_app()
        
        if test_result:
            log.info("🎉 Ella蓝牙命令测试成功")
        else:
            log.warning("⚠️ Ella蓝牙命令测试部分成功")
        
        return test_result
        
    except Exception as e:
        log.error(f"❌ Ella蓝牙命令测试失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 Ella语音助手测试")
    log.info("=" * 100)
    
    test_results = []
    
    # 测试1: 基本功能
    basic_result = test_ella_basic_functionality()
    test_results.append(("Ella基本功能", basic_result))
    
    time.sleep(3)
    
    # 测试2: 蓝牙命令
    bluetooth_result = test_ella_bluetooth_command()
    test_results.append(("Ella蓝牙命令", bluetooth_result))
    
    # 总结
    log.info("=" * 100)
    log.info("🎯 Ella测试结果总结")
    log.info("=" * 100)
    
    success_count = sum(1 for _, success in test_results if success)
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        log.info(f"  {test_name}: {status}")
    
    log.info(f"\n📊 总体结果: {success_count}/{total_count}")
    
    if success_count == total_count:
        log.info("🎉 恭喜！Ella语音助手测试完全成功！")
        log.info("\n✅ 验证的功能:")
        log.info("  - Ella应用启动和页面加载")
        log.info("  - 文本输入和命令发送")
        log.info("  - AI响应接收和解析")
        log.info("  - 蓝牙控制命令执行")
        log.info("  - 蓝牙状态验证")
        
        log.info("\n💡 现在可以:")
        log.info("1. 运行完整的pytest测试: pytest testcases/test_ella/ -v")
        log.info("2. 编写更多Ella功能测试用例")
        log.info("3. 集成到自动化测试流程中")
        
    elif success_count > 0:
        log.info("✅ Ella语音助手基本可用")
        log.info("\n💡 建议:")
        log.info("1. 检查失败的测试项目")
        log.info("2. 根据实际响应调整元素定位")
        log.info("3. 优化命令识别和响应解析")
        
    else:
        log.info("⚠️ Ella语音助手测试需要调试")
        log.info("\n💡 建议:")
        log.info("1. 检查应用是否正确启动")
        log.info("2. 验证元素定位是否准确")
        log.info("3. 查看截图了解实际界面状态")
    
    log.info("\n📁 生成的文件:")
    log.info("  - reports/screenshots/ (测试截图)")
    log.info("  - logs/test.log (详细日志)")


if __name__ == "__main__":
    main()
