"""
测试设备发现和配置更新功能
验证自动发现设备信息并更新devices.yaml的功能
"""
import os
from core.logger import log
from utils.device_discovery import device_discovery
from utils.device_config_manager import device_config_manager
from utils.yaml_utils import YamlUtils


def test_device_discovery():
    """测试设备发现功能"""
    log.info("=" * 80)
    log.info("🔍 测试设备发现功能")
    log.info("=" * 80)
    
    try:
        # 获取连接的设备
        connected_devices = device_discovery.get_connected_devices()
        
        if not connected_devices:
            log.warning("未发现连接的设备，请确保设备已连接并开启USB调试")
            return False
        
        log.info(f"发现 {len(connected_devices)} 个连接的设备")
        
        # 测试单个设备发现
        for device_id in connected_devices:
            log.info(f"\n测试设备: {device_id}")
            
            # 获取设备属性
            properties = device_discovery.get_device_properties(device_id)
            log.info(f"设备属性数量: {len(properties)}")
            
            # 显示关键属性
            key_props = ['brand', 'model', 'android_version', 'hios_version']
            for prop in key_props:
                if prop in properties:
                    log.info(f"  {prop}: {properties[prop]}")
            
            # 获取UIAutomator2信息
            ui_info = device_discovery.get_uiautomator2_info(device_id)
            if ui_info:
                log.info(f"  屏幕分辨率: {ui_info.get('screen_resolution', '')}")
            
            # 完整设备发现
            device_info = device_discovery.discover_device(device_id)
            if device_info:
                log.info(f"✅ 设备发现成功: {device_info['device_name']}")
                log.info(f"  HiOS版本: {device_info['platform_version']}")
            else:
                log.error(f"❌ 设备发现失败: {device_id}")
        
        return True
        
    except Exception as e:
        log.error(f"设备发现测试失败: {e}")
        return False


def test_config_update():
    """测试配置更新功能"""
    log.info("=" * 80)
    log.info("🔄 测试配置更新功能")
    log.info("=" * 80)
    
    try:
        # 备份当前配置
        backup_path = device_config_manager.backup_config()
        if backup_path:
            log.info(f"配置已备份: {backup_path}")
        
        # 自动发现并更新配置
        success = device_config_manager.auto_discover_and_update()
        
        if success:
            log.info("✅ 配置更新成功")
            
            # 验证配置文件
            config_path = YamlUtils.get_config_path("devices.yaml")
            if os.path.exists(config_path):
                config = YamlUtils.load_yaml(config_path)
                
                devices = config.get('devices', {})
                current_device = config.get('current_device', '')
                auto_discovery = config.get('auto_discovery', {})
                
                log.info(f"配置的设备数量: {len(devices)}")
                log.info(f"当前设备: {current_device}")
                log.info(f"自动发现启用: {auto_discovery.get('enabled', False)}")
                log.info(f"最后更新时间: {auto_discovery.get('last_update', '')}")
                
                # 验证当前设备配置
                if current_device in devices:
                    current_config = devices[current_device]
                    log.info(f"当前设备信息:")
                    log.info(f"  设备名称: {current_config.get('device_name', '')}")
                    log.info(f"  设备ID: {current_config.get('device_id', '')}")
                    log.info(f"  HiOS版本: {current_config.get('platform_version', '')}")
                    log.info(f"  自动发现: {current_config.get('auto_discovered', False)}")
                
                return True
            else:
                log.error("配置文件不存在")
                return False
        else:
            log.error("❌ 配置更新失败")
            return False
            
    except Exception as e:
        log.error(f"配置更新测试失败: {e}")
        return False


def test_driver_connection():
    """测试驱动连接功能"""
    log.info("=" * 80)
    log.info("🔌 测试驱动连接功能")
    log.info("=" * 80)
    
    try:
        from core.base_driver import driver_manager
        
        # 获取设备信息
        device_info = driver_manager.get_device_info()
        log.info(f"连接的设备信息: {device_info}")
        
        # 获取屏幕尺寸
        width, height = driver_manager.get_window_size()
        log.info(f"屏幕尺寸: {width} x {height}")
        
        # 测试截图
        screenshot_path = driver_manager.screenshot("device_discovery_test.png", use_test_class_dir=False)
        log.info(f"测试截图: {screenshot_path}")
        
        log.info("✅ 驱动连接测试成功")
        return True
        
    except Exception as e:
        log.error(f"❌ 驱动连接测试失败: {e}")
        return False


def test_config_management():
    """测试配置管理功能"""
    log.info("=" * 80)
    log.info("⚙️ 测试配置管理功能")
    log.info("=" * 80)
    
    try:
        # 列出可用设备
        available_devices = device_config_manager.list_available_devices()
        log.info(f"可用设备配置: {available_devices}")
        
        # 显示当前配置
        device_config_manager.print_updated_config()
        
        # 测试设备名称生成
        test_device_info = {
            'brand': 'TECNO',
            'model': 'TECNO CM8',
            'device_id': '13764254B4001229'
        }
        
        config_name = device_config_manager.generate_device_config_name(test_device_info)
        log.info(f"生成的配置名称: {config_name}")
        
        # 测试HiOS版本提取
        test_properties = {
            'hios_version': 'HiOS_8.6.0',
            'android_version': '15',
            'build_display_id': 'HiOS_8.6.0_TECNO_CM8'
        }
        
        hios_version = device_discovery.extract_hios_version(test_properties)
        log.info(f"提取的HiOS版本: {hios_version}")
        
        log.info("✅ 配置管理功能测试成功")
        return True
        
    except Exception as e:
        log.error(f"❌ 配置管理功能测试失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 设备发现和配置更新功能测试")
    log.info("=" * 100)
    
    test_results = []
    
    # 测试1: 设备发现
    discovery_result = test_device_discovery()
    test_results.append(("设备发现功能", discovery_result))
    
    # 测试2: 配置更新
    config_result = test_config_update()
    test_results.append(("配置更新功能", config_result))
    
    # 测试3: 驱动连接
    driver_result = test_driver_connection()
    test_results.append(("驱动连接功能", driver_result))
    
    # 测试4: 配置管理
    management_result = test_config_management()
    test_results.append(("配置管理功能", management_result))
    
    # 总结
    log.info("=" * 100)
    log.info("🎯 测试结果总结")
    log.info("=" * 100)
    
    success_count = sum(1 for _, success in test_results if success)
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        log.info(f"  {test_name}: {status}")
    
    log.info(f"\n📊 总体结果: {success_count}/{total_count}")
    
    if success_count == total_count:
        log.info("🎉 所有测试通过！设备发现和配置更新功能正常")
        log.info("\n💡 现在您可以:")
        log.info("1. 使用 'python device_manager.py --auto-update' 自动更新设备配置")
        log.info("2. 使用 'python device_manager.py --list' 查看配置的设备")
        log.info("3. 使用 'python device_manager.py --switch <name>' 切换设备")
        log.info("4. 运行测试时框架会自动使用配置的设备信息")
        
    elif success_count >= 2:
        log.info("✅ 主要功能正常，部分功能需要调整")
        log.info("\n💡 建议:")
        log.info("1. 检查失败的测试项目")
        log.info("2. 确保设备连接正常")
        log.info("3. 检查ADB和UIAutomator2环境")
        
    else:
        log.info("⚠️ 多个功能异常，需要检查环境配置")
        log.info("\n💡 建议:")
        log.info("1. 检查设备连接和USB调试")
        log.info("2. 确认ADB工具正常工作")
        log.info("3. 检查UIAutomator2安装")
    
    log.info("\n📁 生成的文件:")
    log.info("  - config/devices.yaml (更新的设备配置)")
    log.info("  - config/backups/ (配置备份)")
    log.info("  - reports/screenshots/ (测试截图)")


if __name__ == "__main__":
    main()
