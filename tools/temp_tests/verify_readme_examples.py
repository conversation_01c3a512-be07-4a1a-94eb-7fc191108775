"""
验证README.md中的所有示例代码
确保文档中的代码示例都能正常工作
"""
import subprocess
import sys
import os
from core.logger import log


def test_device_manager_commands():
    """测试设备管理命令"""
    log.info("=" * 80)
    log.info("📱 验证设备管理命令示例")
    log.info("=" * 80)
    
    commands = [
        ("查看当前设备", ["python", "device_manager.py", "--current"]),
        ("列出设备配置", ["python", "device_manager.py", "--list"]),
        ("测试设备连接", ["python", "device_manager.py", "--test"]),
    ]
    
    success_count = 0
    for desc, cmd in commands:
        try:
            log.info(f"测试: {desc}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                log.info(f"✅ {desc} - 成功")
                success_count += 1
            else:
                log.warning(f"⚠️ {desc} - 失败: {result.stderr}")
                
        except Exception as e:
            log.error(f"❌ {desc} - 异常: {e}")
    
    log.info(f"设备管理命令验证: {success_count}/{len(commands)}")
    return success_count == len(commands)


def test_screenshot_manager_commands():
    """测试截图管理命令"""
    log.info("=" * 80)
    log.info("📸 验证截图管理命令示例")
    log.info("=" * 80)
    
    commands = [
        ("查看截图统计", ["python", "screenshot_manager_tool.py", "--summary"]),
        ("查看目录结构", ["python", "screenshot_manager_tool.py", "--structure"]),
        ("预览清理", ["python", "screenshot_manager_tool.py", "--cleanup", "30"]),
    ]
    
    success_count = 0
    for desc, cmd in commands:
        try:
            log.info(f"测试: {desc}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                log.info(f"✅ {desc} - 成功")
                success_count += 1
            else:
                log.warning(f"⚠️ {desc} - 失败: {result.stderr}")
                
        except Exception as e:
            log.error(f"❌ {desc} - 异常: {e}")
    
    log.info(f"截图管理命令验证: {success_count}/{len(commands)}")
    return success_count == len(commands)


def test_code_examples():
    """测试代码示例"""
    log.info("=" * 80)
    log.info("💻 验证代码示例")
    log.info("=" * 80)
    
    try:
        # 测试截图功能示例
        log.info("1️⃣ 测试截图功能示例...")
        from core.base_driver import driver_manager
        from utils.screenshot_utils import screenshot_manager
        
        # 基础截图
        screenshot_path = driver_manager.screenshot("readme_example_test.png")
        log.info(f"✅ 基础截图示例: {screenshot_path}")
        
        # 截图管理器
        screenshot_path = screenshot_manager.take_screenshot(
            driver_manager.driver,
            "readme_manager_test.png",
            "README示例测试"
        )
        log.info(f"✅ 截图管理器示例: {screenshot_path}")
        
        # 测试设备发现示例
        log.info("\n2️⃣ 测试设备发现示例...")
        from utils.device_discovery import device_discovery
        from utils.device_config_manager import device_config_manager
        
        # 获取设备列表
        available_devices = device_config_manager.list_available_devices()
        log.info(f"✅ 可用设备配置: {available_devices}")
        
        # 获取截图统计
        summary = screenshot_manager.get_screenshot_summary()
        log.info(f"✅ 截图统计: {summary['total_files']} 个文件")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 代码示例验证失败: {e}")
        return False


def test_configuration_examples():
    """测试配置示例"""
    log.info("=" * 80)
    log.info("⚙️ 验证配置示例")
    log.info("=" * 80)
    
    try:
        from utils.yaml_utils import YamlUtils
        
        # 验证devices.yaml结构
        log.info("1️⃣ 验证devices.yaml结构...")
        devices_config = YamlUtils.load_yaml(YamlUtils.get_config_path("devices.yaml"))
        
        required_keys = ['devices', 'current_device', 'auto_discovery']
        for key in required_keys:
            if key in devices_config:
                log.info(f"✅ {key}: 存在")
            else:
                log.warning(f"⚠️ {key}: 缺失")
        
        # 验证自动发现信息
        auto_discovery = devices_config.get('auto_discovery', {})
        if auto_discovery.get('enabled'):
            log.info(f"✅ 自动发现已启用")
            log.info(f"  最后更新: {auto_discovery.get('last_update', '')}")
            log.info(f"  发现设备数: {auto_discovery.get('discovered_devices', 0)}")
        
        # 验证当前设备配置
        current_device = devices_config.get('current_device', '')
        devices = devices_config.get('devices', {})
        
        if current_device in devices:
            device_info = devices[current_device]
            log.info(f"✅ 当前设备配置完整:")
            log.info(f"  设备名称: {device_info.get('device_name', '')}")
            log.info(f"  HiOS版本: {device_info.get('platform_version', '')}")
            log.info(f"  自动发现: {device_info.get('auto_discovered', False)}")
        
        # 验证config.yaml结构
        log.info("\n2️⃣ 验证config.yaml结构...")
        app_config = YamlUtils.load_yaml(YamlUtils.get_config_path("config.yaml"))
        
        if 'apps' in app_config:
            apps = app_config['apps']
            log.info(f"✅ 应用配置: {len(apps)} 个应用")
            
            for app_name, app_info in apps.items():
                log.info(f"  {app_name}: {app_info.get('package_name', '')}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 配置示例验证失败: {e}")
        return False


def test_directory_structure():
    """验证目录结构"""
    log.info("=" * 80)
    log.info("📁 验证目录结构")
    log.info("=" * 80)
    
    # README中提到的关键目录
    expected_dirs = [
        "config",
        "config/backups",
        "core",
        "pages",
        "pages/base",
        "pages/apps",
        "testcases",
        "utils",
        "reports",
        "reports/screenshots",
        "logs"
    ]
    
    # README中提到的关键文件
    expected_files = [
        "config/config.yaml",
        "config/devices.yaml",
        "core/base_driver.py",
        "core/base_element.py",
        "core/base_page.py",
        "core/logger.py",
        "utils/device_discovery.py",
        "utils/device_config_manager.py",
        "utils/screenshot_utils.py",
        "device_manager.py",
        "screenshot_manager_tool.py",
        "requirements.txt",
        "pytest.ini"
    ]
    
    missing_dirs = []
    missing_files = []
    
    # 检查目录
    for dir_path in expected_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            log.info(f"✅ 目录存在: {dir_path}")
        else:
            log.warning(f"⚠️ 目录缺失: {dir_path}")
            missing_dirs.append(dir_path)
    
    # 检查文件
    for file_path in expected_files:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            log.info(f"✅ 文件存在: {file_path}")
        else:
            log.warning(f"⚠️ 文件缺失: {file_path}")
            missing_files.append(file_path)
    
    total_expected = len(expected_dirs) + len(expected_files)
    total_missing = len(missing_dirs) + len(missing_files)
    success_rate = (total_expected - total_missing) / total_expected * 100
    
    log.info(f"\n📊 目录结构完整性: {success_rate:.1f}%")
    log.info(f"  期望项目: {total_expected}")
    log.info(f"  缺失项目: {total_missing}")
    
    return success_rate >= 90  # 90%以上认为通过


def main():
    """主函数"""
    log.info("🔍 README.md示例验证工具")
    log.info("=" * 100)
    log.info("验证README.md文档中的所有示例代码和配置是否正确")
    log.info("=" * 100)
    
    test_results = []
    
    # 验证1: 目录结构
    structure_result = test_directory_structure()
    test_results.append(("目录结构", structure_result))
    
    # 验证2: 配置示例
    config_result = test_configuration_examples()
    test_results.append(("配置示例", config_result))
    
    # 验证3: 代码示例
    code_result = test_code_examples()
    test_results.append(("代码示例", code_result))
    
    # 验证4: 设备管理命令
    device_cmd_result = test_device_manager_commands()
    test_results.append(("设备管理命令", device_cmd_result))
    
    # 验证5: 截图管理命令
    screenshot_cmd_result = test_screenshot_manager_commands()
    test_results.append(("截图管理命令", screenshot_cmd_result))
    
    # 总结
    log.info("=" * 100)
    log.info("🎯 README.md示例验证结果")
    log.info("=" * 100)
    
    success_count = sum(1 for _, success in test_results if success)
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        log.info(f"  {test_name}: {status}")
    
    log.info(f"\n📊 总体结果: {success_count}/{total_count}")
    
    if success_count == total_count:
        log.info("🎉 恭喜！README.md中的所有示例都验证通过！")
        log.info("\n✅ 文档质量评估:")
        log.info("  - 目录结构完整")
        log.info("  - 配置示例正确")
        log.info("  - 代码示例可用")
        log.info("  - 命令示例有效")
        log.info("  - 功能描述准确")
        
    elif success_count >= 4:
        log.info("✅ README.md文档基本正确，少数示例需要调整")
        log.info("\n💡 建议:")
        log.info("  - 检查失败的验证项目")
        log.info("  - 更新相关示例代码")
        log.info("  - 确保文档与实际功能同步")
        
    else:
        log.info("⚠️ README.md文档需要更新")
        log.info("\n💡 建议:")
        log.info("  - 检查项目结构是否完整")
        log.info("  - 更新配置文件示例")
        log.info("  - 验证代码示例的正确性")
    
    log.info("\n📖 README.md文档包含以下优化内容:")
    log.info("  ✅ 自动设备发现和配置管理")
    log.info("  ✅ 智能截图管理功能")
    log.info("  ✅ TECNO设备适配说明")
    log.info("  ✅ HiOS版本识别机制")
    log.info("  ✅ 完整的使用示例")
    log.info("  ✅ 详细的配置说明")
    log.info("  ✅ 最佳实践指导")
    log.info("  ✅ 常见问题解答")


if __name__ == "__main__":
    main()
