"""
完整框架功能验证测试
验证所有优化后的功能是否正常工作
"""
import time
from core.logger import log


def test_device_management():
    """测试设备管理功能"""
    log.info("=" * 80)
    log.info("📱 测试设备管理功能")
    log.info("=" * 80)
    
    try:
        from utils.device_discovery import device_discovery
        from utils.device_config_manager import device_config_manager
        
        # 测试设备发现
        log.info("1️⃣ 测试设备发现...")
        devices = device_discovery.discover_all_devices()
        
        if devices:
            log.info(f"✅ 发现 {len(devices)} 个设备")
            device_discovery.print_device_summary(devices)
        else:
            log.warning("⚠️ 未发现设备")
            return False
        
        # 测试配置管理
        log.info("\n2️⃣ 测试配置管理...")
        available_devices = device_config_manager.list_available_devices()
        log.info(f"可用设备配置: {available_devices}")
        
        # 显示当前配置
        device_config_manager.print_updated_config()
        
        return True
        
    except Exception as e:
        log.error(f"❌ 设备管理功能测试失败: {e}")
        return False


def test_driver_with_auto_config():
    """测试使用自动配置的驱动连接"""
    log.info("=" * 80)
    log.info("🔌 测试自动配置驱动连接")
    log.info("=" * 80)
    
    try:
        from core.base_driver import driver_manager
        
        # 测试设备连接
        device_info = driver_manager.get_device_info()
        log.info(f"✅ 设备连接成功: {device_info}")
        
        # 测试屏幕信息
        width, height = driver_manager.get_window_size()
        log.info(f"屏幕尺寸: {width} x {height}")
        
        # 测试截图
        screenshot_path = driver_manager.screenshot("framework_complete_test.png", use_test_class_dir=False)
        log.info(f"测试截图: {screenshot_path}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 驱动连接测试失败: {e}")
        return False


def test_screenshot_management():
    """测试截图管理功能"""
    log.info("=" * 80)
    log.info("📸 测试截图管理功能")
    log.info("=" * 80)
    
    try:
        from utils.screenshot_utils import screenshot_manager
        from core.base_driver import driver_manager
        
        # 测试截图管理器
        log.info("1️⃣ 测试截图管理器...")
        screenshot_path = screenshot_manager.take_screenshot(
            driver_manager.driver,
            "screenshot_manager_test.png",
            "截图管理器测试"
        )
        log.info(f"✅ 截图管理器测试成功: {screenshot_path}")
        
        # 测试截图统计
        log.info("\n2️⃣ 测试截图统计...")
        summary = screenshot_manager.get_screenshot_summary()
        log.info(f"截图统计: {summary['total_files']} 个文件, {summary['total_size']/1024/1024:.2f} MB")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 截图管理功能测试失败: {e}")
        return False


def test_page_objects():
    """测试页面对象功能"""
    log.info("=" * 80)
    log.info("📄 测试页面对象功能")
    log.info("=" * 80)
    
    try:
        # 测试设置页面
        log.info("1️⃣ 测试设置页面...")
        from pages.apps.settings.main_page_updated import SettingsMainPage
        
        settings_page = SettingsMainPage()
        
        # 启动应用
        if settings_page.start_app():
            log.info("✅ 设置应用启动成功")
            
            # 等待页面加载
            if settings_page.wait_for_page_load():
                log.info("✅ 页面加载成功")
                
                # 截图
                screenshot_path = settings_page.screenshot("settings_page_test.png")
                log.info(f"页面截图: {screenshot_path}")
                
                # 停止应用
                settings_page.stop_app()
                
                return True
            else:
                log.warning("⚠️ 页面加载失败")
                settings_page.stop_app()
                return False
        else:
            log.warning("⚠️ 应用启动失败")
            return False
        
    except Exception as e:
        log.error(f"❌ 页面对象功能测试失败: {e}")
        return False


def test_tecno_calculator():
    """测试TECNO计算器功能"""
    log.info("=" * 80)
    log.info("🧮 测试TECNO计算器功能")
    log.info("=" * 80)
    
    try:
        from pages.apps.calculator.main_page_tecno import TecnoCalculatorMainPage
        
        calc_page = TecnoCalculatorMainPage()
        
        # 启动应用
        if calc_page.start_app():
            log.info("✅ TECNO计算器启动成功")
            
            # 等待页面加载
            if calc_page.wait_for_page_load():
                log.info("✅ 计算器页面加载成功")
                
                # 测试简单计算
                if calc_page.simple_calculate("2", "+", "3"):
                    log.info("✅ 计算功能测试成功")
                    
                    # 截图
                    screenshot_path = calc_page.screenshot("calculator_test.png")
                    log.info(f"计算器截图: {screenshot_path}")
                    
                    # 停止应用
                    calc_page.stop_app()
                    
                    return True
                else:
                    log.warning("⚠️ 计算功能测试失败")
                    calc_page.stop_app()
                    return False
            else:
                log.warning("⚠️ 计算器页面加载失败")
                calc_page.stop_app()
                return False
        else:
            log.warning("⚠️ 计算器启动失败")
            return False
        
    except Exception as e:
        log.error(f"❌ TECNO计算器功能测试失败: {e}")
        return False


def test_configuration_system():
    """测试配置系统"""
    log.info("=" * 80)
    log.info("⚙️ 测试配置系统")
    log.info("=" * 80)
    
    try:
        from utils.yaml_utils import YamlUtils
        
        # 测试配置加载
        log.info("1️⃣ 测试配置加载...")
        
        # 加载设备配置
        devices_config = YamlUtils.load_yaml(YamlUtils.get_config_path("devices.yaml"))
        log.info(f"✅ 设备配置加载成功，包含 {len(devices_config.get('devices', {}))} 个设备")
        
        # 加载应用配置
        app_config = YamlUtils.load_yaml(YamlUtils.get_config_path("config.yaml"))
        log.info(f"✅ 应用配置加载成功，包含 {len(app_config.get('apps', {}))} 个应用")
        
        # 验证自动发现信息
        auto_discovery = devices_config.get('auto_discovery', {})
        if auto_discovery.get('enabled'):
            log.info(f"✅ 自动发现已启用，最后更新: {auto_discovery.get('last_update', '')}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 配置系统测试失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 Android自动化测试框架 - 完整功能验证")
    log.info("=" * 100)
    
    test_results = []
    
    # 测试1: 设备管理功能
    device_result = test_device_management()
    test_results.append(("设备管理功能", device_result))
    
    time.sleep(2)
    
    # 测试2: 自动配置驱动连接
    driver_result = test_driver_with_auto_config()
    test_results.append(("自动配置驱动", driver_result))
    
    time.sleep(2)
    
    # 测试3: 截图管理功能
    screenshot_result = test_screenshot_management()
    test_results.append(("截图管理功能", screenshot_result))
    
    time.sleep(2)
    
    # 测试4: 页面对象功能
    page_result = test_page_objects()
    test_results.append(("页面对象功能", page_result))
    
    time.sleep(2)
    
    # 测试5: TECNO计算器功能
    calc_result = test_tecno_calculator()
    test_results.append(("TECNO计算器功能", calc_result))
    
    time.sleep(2)
    
    # 测试6: 配置系统
    config_result = test_configuration_system()
    test_results.append(("配置系统", config_result))
    
    # 总结
    log.info("=" * 100)
    log.info("🎯 完整功能验证结果")
    log.info("=" * 100)
    
    success_count = sum(1 for _, success in test_results if success)
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        log.info(f"  {test_name}: {status}")
    
    log.info(f"\n📊 总体结果: {success_count}/{total_count}")
    
    if success_count == total_count:
        log.info("🎉 恭喜！所有功能验证通过！")
        log.info("\n🚀 框架已完全就绪，包含以下优化功能:")
        log.info("✅ 1. 自动设备发现和配置管理")
        log.info("✅ 2. 智能截图管理（按测试类分文件夹）")
        log.info("✅ 3. HiOS版本自动识别")
        log.info("✅ 4. TECNO设备完美适配")
        log.info("✅ 5. 完整的页面对象模式")
        log.info("✅ 6. 强大的配置管理系统")
        
        log.info("\n💡 推荐使用流程:")
        log.info("1. python device_manager.py --auto-update  # 自动配置设备")
        log.info("2. python run_tests.py --smoke             # 运行冒烟测试")
        log.info("3. python screenshot_manager_tool.py --all # 查看截图统计")
        log.info("4. 编写自定义测试用例并运行")
        
    elif success_count >= 4:
        log.info("✅ 主要功能正常，框架基本可用")
        log.info("\n💡 建议:")
        log.info("1. 检查失败的功能模块")
        log.info("2. 根据具体应用调整页面对象")
        log.info("3. 框架核心功能已经完全可用")
        
    else:
        log.info("⚠️ 部分功能需要调整")
        log.info("\n💡 建议:")
        log.info("1. 检查设备连接和配置")
        log.info("2. 确认应用包名和元素定位")
        log.info("3. 查看详细日志进行问题定位")
    
    log.info("\n📁 生成的文件和目录:")
    log.info("  - config/devices.yaml (自动更新的设备配置)")
    log.info("  - config/backups/ (配置备份)")
    log.info("  - reports/screenshots/ (按测试类分组的截图)")
    log.info("  - logs/test.log (详细执行日志)")


if __name__ == "__main__":
    main()
