"""
Ella测试用例批量优化工具
将所有Ella测试用例从旧版本页面类迁移到重构后的页面类
"""
import os
import re
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
import sys
sys.path.insert(0, str(project_root))

try:
    from core.logger import log
except ImportError:
    # 如果无法导入logger，使用简单的打印
    class SimpleLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
    log = SimpleLogger()


class EllaTestOptimizer:
    """Ella测试用例优化器"""
    
    def __init__(self):
        """初始化优化器"""
        self.project_root = Path(__file__).parent.parent.parent
        self.test_dir = self.project_root / "testcases" / "test_ella"
        self.backup_dir = self.project_root / "testcases" / "test_ella_backup"
        
        # 需要优化的文件列表
        self.test_files = [
            "test_bluetooth_command.py",
            "test_explore_page.py", 
            "test_popup_handling_u2.py",
            "test_with_popup_handling.py",
            "test_excel_driven.py"
        ]
        
        # 替换规则
        self.replacements = [
            # 导入语句替换
            (
                r'from pages\.apps\.ella\.main_page import EllaMainPage',
                'from pages.apps.ella.dialogue_page import EllaDialoguePage'
            ),
            # 类实例化替换
            (
                r'EllaMainPage\(\)',
                'EllaDialoguePage()'
            ),
            # 变量赋值替换
            (
                r'ella_page = EllaMainPage\(\)',
                'ella_page = EllaDialoguePage()'
            ),
            (
                r'self\.ella_app = EllaMainPage\(\)',
                'self.ella_app = EllaDialoguePage()'
            ),
            # 类名替换（在类定义中）
            (
                r'class Test([A-Za-z]+):',
                r'class Test\1Optimized:'
            ),
            # 文档字符串更新
            (
                r'"""([^"]*测试[^"]*)"""',
                r'"""\1 - 优化版本"""'
            ),
            # 注释更新
            (
                r'# ([^#\n]*测试[^#\n]*)',
                r'# \1 - 优化版本'
            ),
            # Allure story更新
            (
                r'@allure\.story\("([^"]+)"\)',
                r'@allure.story("\1 - 优化版本")'
            ),
            # 截图文件名更新
            (
                r'screenshot\("([^"]+)\.png"\)',
                r'screenshot("\1_optimized.png")'
            ),
            # 日志消息更新
            (
                r'log\.info\("([^"]*测试[^"]*)"\)',
                r'log.info("\1（优化版本）")'
            )
        ]
    
    def create_backup(self):
        """创建备份"""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
            
            shutil.copytree(self.test_dir, self.backup_dir)
            log.info(f"✅ 已创建备份目录: {self.backup_dir}")
            return True
            
        except Exception as e:
            log.error(f"❌ 创建备份失败: {e}")
            return False
    
    def optimize_file(self, file_path: Path) -> bool:
        """
        优化单个测试文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 优化是否成功
        """
        try:
            log.info(f"🔧 优化文件: {file_path.name}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用替换规则
            original_content = content
            for pattern, replacement in self.replacements:
                content = re.sub(pattern, replacement, content)
            
            # 检查是否有变化
            if content == original_content:
                log.info(f"ℹ️ 文件无需优化: {file_path.name}")
                return True
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            log.info(f"✅ 文件优化完成: {file_path.name}")
            return True
            
        except Exception as e:
            log.error(f"❌ 优化文件失败 {file_path.name}: {e}")
            return False
    
    def add_optimization_header(self, file_path: Path):
        """为文件添加优化标识"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经有优化标识
            if "优化版本" in content or "使用重构后的页面类" in content:
                return
            
            # 在文档字符串中添加优化说明
            if '"""' in content:
                # 找到第一个文档字符串的结束位置
                first_docstring_end = content.find('"""', content.find('"""') + 3)
                if first_docstring_end != -1:
                    optimization_note = "\n使用重构后的页面类，提供更好的模块化和可维护性"
                    content = (content[:first_docstring_end] + 
                             optimization_note + 
                             content[first_docstring_end:])
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    log.info(f"✅ 已添加优化标识: {file_path.name}")
                    
        except Exception as e:
            log.error(f"❌ 添加优化标识失败 {file_path.name}: {e}")
    
    def optimize_all_tests(self):
        """优化所有测试文件"""
        try:
            log.info("🚀 开始批量优化Ella测试用例...")
            
            # 创建备份
            if not self.create_backup():
                log.error("❌ 备份失败，停止优化")
                return False
            
            success_count = 0
            total_count = 0
            
            # 优化指定的测试文件
            for file_name in self.test_files:
                file_path = self.test_dir / file_name
                if file_path.exists():
                    total_count += 1
                    if self.optimize_file(file_path):
                        self.add_optimization_header(file_path)
                        success_count += 1
                    else:
                        log.warning(f"⚠️ 文件优化失败: {file_name}")
                else:
                    log.warning(f"⚠️ 文件不存在: {file_name}")
            
            # 统计结果
            log.info(f"📊 优化完成: {success_count}/{total_count} 个文件成功")
            
            if success_count == total_count:
                log.info("🎉 所有文件优化成功！")
                return True
            else:
                log.warning(f"⚠️ {total_count - success_count} 个文件优化失败")
                return False
                
        except Exception as e:
            log.error(f"❌ 批量优化失败: {e}")
            return False
    
    def restore_backup(self):
        """从备份恢复"""
        try:
            if not self.backup_dir.exists():
                log.error("❌ 备份目录不存在")
                return False
            
            # 删除当前测试目录
            if self.test_dir.exists():
                shutil.rmtree(self.test_dir)
            
            # 从备份恢复
            shutil.copytree(self.backup_dir, self.test_dir)
            log.info("✅ 已从备份恢复")
            return True
            
        except Exception as e:
            log.error(f"❌ 恢复备份失败: {e}")
            return False
    
    def generate_optimization_report(self):
        """生成优化报告"""
        try:
            report_path = self.project_root / "reports" / "ella_optimization_report.md"
            report_path.parent.mkdir(exist_ok=True)
            
            report_content = f"""# Ella测试用例优化报告

## 优化概述

本次优化将所有Ella测试用例从旧版本页面类迁移到重构后的页面类。

## 优化文件列表

"""
            
            for file_name in self.test_files:
                file_path = self.test_dir / file_name
                if file_path.exists():
                    report_content += f"- ✅ {file_name}\n"
                else:
                    report_content += f"- ❌ {file_name} (文件不存在)\n"
            
            report_content += f"""
## 优化内容

1. **页面类替换**: `EllaMainPage` → `EllaMainPageRefactored`
2. **导入语句更新**: 使用重构后的页面类
3. **类名优化**: 添加 `Optimized` 后缀
4. **文档更新**: 添加优化版本标识
5. **截图文件名**: 添加 `_optimized` 后缀
6. **日志消息**: 添加优化版本标识

## 优化优势

1. **模块化设计**: 使用专门的状态检查器、应用检测器、响应处理器
2. **提高可维护性**: 代码结构更清晰，职责分离
3. **保持兼容性**: 公共接口保持不变
4. **增强可测试性**: 每个模块可以独立测试

## 备份信息

- 备份目录: `{self.backup_dir}`
- 备份时间: {self._get_current_time()}

## 使用说明

优化后的测试用例使用方法与原版本相同，只需要运行测试即可：

```bash
# 运行所有优化后的Ella测试
python run_tests.py --ella

# 运行特定的优化测试
python -m pytest testcases/test_ella/test_bluetooth_simple_command.py -v
```
"""
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            log.info(f"📄 优化报告已生成: {report_path}")
            return True
            
        except Exception as e:
            log.error(f"❌ 生成优化报告失败: {e}")
            return False
    
    def _get_current_time(self):
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    optimizer = EllaTestOptimizer()
    
    try:
        # 执行优化
        if optimizer.optimize_all_tests():
            # 生成报告
            optimizer.generate_optimization_report()
            log.info("🎉 Ella测试用例优化完成！")
        else:
            log.error("❌ 优化过程中出现错误")
            
            # 询问是否恢复备份
            user_input = input("是否从备份恢复？(y/n): ")
            if user_input.lower() == 'y':
                optimizer.restore_backup()
                
    except KeyboardInterrupt:
        log.info("⚠️ 用户中断操作")
    except Exception as e:
        log.error(f"❌ 优化过程异常: {e}")


if __name__ == "__main__":
    main()
