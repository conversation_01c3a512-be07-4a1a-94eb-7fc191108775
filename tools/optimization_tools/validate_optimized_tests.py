"""
优化后的Ella测试用例验证工具
验证所有优化后的测试用例是否能正常导入和运行
"""
import os
import sys
import importlib
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.logger import log
except ImportError:
    # 如果无法导入logger，使用简单的打印
    class SimpleLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
    log = SimpleLogger()


class OptimizedTestValidator:
    """优化测试验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.project_root = Path(__file__).parent.parent.parent
        self.test_dir = self.project_root / "testcases" / "test_ella"
        
        # 需要验证的测试文件
        self.test_files = [
            "test_bluetooth_simple_command.py",
            "test_weather_query_command.py", 
            "test_set_alarm_command.py",
            "test_take_photo_command.py",
            "test_open_clock_command.py",
            "test_open_bluetooth_voice.py",
            "test_open_contacts_command.py",
            "test_open_contacts_refactored.py"
        ]
        
        self.validation_results = {}
    
    def validate_import(self, test_file: str) -> bool:
        """
        验证测试文件是否能正常导入
        
        Args:
            test_file: 测试文件名
            
        Returns:
            bool: 导入是否成功
        """
        try:
            log.info(f"🔍 验证导入: {test_file}")
            
            # 构建模块名
            module_name = f"testcases.test_ella.{test_file[:-3]}"
            
            # 尝试导入模块
            spec = importlib.util.spec_from_file_location(
                module_name, 
                self.test_dir / test_file
            )
            
            if spec is None:
                log.error(f"❌ 无法创建模块规范: {test_file}")
                return False
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            log.info(f"✅ 导入成功: {test_file}")
            return True
            
        except ImportError as e:
            log.error(f"❌ 导入错误 {test_file}: {e}")
            return False
        except Exception as e:
            log.error(f"❌ 验证导入失败 {test_file}: {e}")
            log.debug(f"详细错误: {traceback.format_exc()}")
            return False
    
    def validate_class_structure(self, test_file: str) -> bool:
        """
        验证测试类结构是否正确
        
        Args:
            test_file: 测试文件名
            
        Returns:
            bool: 结构是否正确
        """
        try:
            log.info(f"🔍 验证类结构: {test_file}")
            
            file_path = self.test_dir / test_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否使用了重构后的页面类
            if "EllaMainPageRefactored" not in content:
                log.warning(f"⚠️ 未使用重构后的页面类: {test_file}")
                return False
            
            # 检查是否有测试类定义
            if "class Test" not in content:
                log.warning(f"⚠️ 未找到测试类: {test_file}")
                return False
            
            # 检查是否有测试方法
            if "def test_" not in content:
                log.warning(f"⚠️ 未找到测试方法: {test_file}")
                return False
            
            log.info(f"✅ 类结构正确: {test_file}")
            return True
            
        except Exception as e:
            log.error(f"❌ 验证类结构失败 {test_file}: {e}")
            return False
    
    def validate_page_class_usage(self, test_file: str) -> bool:
        """
        验证页面类使用是否正确
        
        Args:
            test_file: 测试文件名
            
        Returns:
            bool: 使用是否正确
        """
        try:
            log.info(f"🔍 验证页面类使用: {test_file}")
            
            file_path = self.test_dir / test_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查导入语句
            expected_import = "from pages.apps.ella.dialogue_page import EllaDialoguePage"
            if expected_import not in content:
                log.warning(f"⚠️ 导入语句不正确: {test_file}")
                return False
            
            # 检查实例化
            if "EllaMainPageRefactored()" not in content:
                log.warning(f"⚠️ 页面类实例化不正确: {test_file}")
                return False
            
            # 检查是否还有旧的导入
            old_import = "from pages.apps.ella.main_page import EllaMainPage"
            if old_import in content:
                log.warning(f"⚠️ 仍包含旧的导入语句: {test_file}")
                return False
            
            log.info(f"✅ 页面类使用正确: {test_file}")
            return True
            
        except Exception as e:
            log.error(f"❌ 验证页面类使用失败 {test_file}: {e}")
            return False
    
    def validate_method_compatibility(self, test_file: str) -> bool:
        """
        验证方法兼容性
        
        Args:
            test_file: 测试文件名
            
        Returns:
            bool: 兼容性是否正确
        """
        try:
            log.info(f"🔍 验证方法兼容性: {test_file}")
            
            file_path = self.test_dir / test_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查常用方法是否存在
            required_methods = [
                "execute_text_command",
                "wait_for_response", 
                "get_response_text",
                "check_bluetooth_status",
                "start_app",
                "wait_for_page_load"
            ]
            
            missing_methods = []
            for method in required_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                log.warning(f"⚠️ 缺少方法调用 {test_file}: {missing_methods}")
                # 这不是错误，因为不是所有测试都需要所有方法
            
            log.info(f"✅ 方法兼容性检查完成: {test_file}")
            return True
            
        except Exception as e:
            log.error(f"❌ 验证方法兼容性失败 {test_file}: {e}")
            return False
    
    def validate_single_file(self, test_file: str) -> dict:
        """
        验证单个测试文件
        
        Args:
            test_file: 测试文件名
            
        Returns:
            dict: 验证结果
        """
        result = {
            'file': test_file,
            'import_success': False,
            'class_structure': False,
            'page_class_usage': False,
            'method_compatibility': False,
            'overall_success': False
        }
        
        try:
            # 检查文件是否存在
            file_path = self.test_dir / test_file
            if not file_path.exists():
                log.error(f"❌ 文件不存在: {test_file}")
                return result
            
            # 执行各项验证
            result['import_success'] = self.validate_import(test_file)
            result['class_structure'] = self.validate_class_structure(test_file)
            result['page_class_usage'] = self.validate_page_class_usage(test_file)
            result['method_compatibility'] = self.validate_method_compatibility(test_file)
            
            # 计算总体成功率
            success_count = sum([
                result['import_success'],
                result['class_structure'], 
                result['page_class_usage'],
                result['method_compatibility']
            ])
            
            result['overall_success'] = success_count >= 3  # 至少3项成功
            
            if result['overall_success']:
                log.info(f"🎉 验证成功: {test_file}")
            else:
                log.warning(f"⚠️ 验证部分失败: {test_file}")
            
            return result
            
        except Exception as e:
            log.error(f"❌ 验证文件失败 {test_file}: {e}")
            return result
    
    def validate_all_tests(self):
        """验证所有测试文件"""
        try:
            log.info("🚀 开始验证所有优化后的测试用例...")
            
            total_files = len(self.test_files)
            success_count = 0
            
            for test_file in self.test_files:
                log.info(f"\n{'='*60}")
                log.info(f"验证文件 ({self.test_files.index(test_file)+1}/{total_files}): {test_file}")
                log.info(f"{'='*60}")
                
                result = self.validate_single_file(test_file)
                self.validation_results[test_file] = result
                
                if result['overall_success']:
                    success_count += 1
            
            # 生成验证报告
            self.generate_validation_report(success_count, total_files)
            
            log.info(f"\n📊 验证完成: {success_count}/{total_files} 个文件通过验证")
            
            if success_count == total_files:
                log.info("🎉 所有测试文件验证成功！")
                return True
            else:
                log.warning(f"⚠️ {total_files - success_count} 个文件验证失败")
                return False
                
        except Exception as e:
            log.error(f"❌ 验证过程失败: {e}")
            return False
    
    def generate_validation_report(self, success_count: int, total_files: int):
        """生成验证报告"""
        try:
            report_path = self.project_root / "reports" / "ella_validation_report.md"
            report_path.parent.mkdir(exist_ok=True)
            
            report_content = f"""# Ella测试用例验证报告

## 验证概述

验证了 {total_files} 个优化后的测试文件，其中 {success_count} 个通过验证。

## 验证结果详情

| 文件名 | 导入 | 类结构 | 页面类使用 | 方法兼容性 | 总体结果 |
|--------|------|--------|------------|------------|----------|
"""
            
            for test_file, result in self.validation_results.items():
                import_icon = "✅" if result['import_success'] else "❌"
                structure_icon = "✅" if result['class_structure'] else "❌"
                usage_icon = "✅" if result['page_class_usage'] else "❌"
                compatibility_icon = "✅" if result['method_compatibility'] else "❌"
                overall_icon = "✅" if result['overall_success'] else "❌"
                
                report_content += f"| {test_file} | {import_icon} | {structure_icon} | {usage_icon} | {compatibility_icon} | {overall_icon} |\n"
            
            report_content += f"""
## 验证统计

- **总文件数**: {total_files}
- **成功文件数**: {success_count}
- **失败文件数**: {total_files - success_count}
- **成功率**: {(success_count/total_files)*100:.1f}%

## 验证项说明

1. **导入验证**: 检查文件是否能正常导入，无语法错误
2. **类结构验证**: 检查是否有正确的测试类和测试方法
3. **页面类使用验证**: 检查是否正确使用重构后的页面类
4. **方法兼容性验证**: 检查是否使用了兼容的方法调用

## 建议

"""
            
            if success_count == total_files:
                report_content += "🎉 所有测试文件验证通过，可以正常使用！\n"
            else:
                report_content += "⚠️ 部分文件验证失败，建议检查以下内容：\n\n"
                report_content += "1. 确保导入语句正确\n"
                report_content += "2. 检查页面类实例化\n"
                report_content += "3. 验证方法调用是否兼容\n"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            log.info(f"📄 验证报告已生成: {report_path}")
            
        except Exception as e:
            log.error(f"❌ 生成验证报告失败: {e}")


def main():
    """主函数"""
    validator = OptimizedTestValidator()
    
    try:
        if validator.validate_all_tests():
            log.info("🎉 所有测试文件验证完成！")
        else:
            log.warning("⚠️ 部分测试文件验证失败，请查看报告")
            
    except KeyboardInterrupt:
        log.info("⚠️ 用户中断验证")
    except Exception as e:
        log.error(f"❌ 验证过程异常: {e}")


if __name__ == "__main__":
    main()
