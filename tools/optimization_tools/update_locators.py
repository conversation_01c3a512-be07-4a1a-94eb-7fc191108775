"""
自动更新定位器工具
根据探测到的元素信息自动更新页面对象的定位器
"""
import json
import os
from core.logger import log


class LocatorUpdater:
    """定位器更新器"""
    
    def __init__(self):
        """初始化更新器"""
        self.settings_elements = self._load_elements("settings")
        self.calculator_elements = self._load_elements("calculator")
    
    def _load_elements(self, app_name: str) -> list:
        """
        加载元素信息
        
        Args:
            app_name: 应用名称
            
        Returns:
            list: 元素信息列表
        """
        try:
            json_path = f"reports/{app_name}_elements.json"
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                log.warning(f"元素文件不存在: {json_path}")
                return []
        except Exception as e:
            log.error(f"加载元素信息失败: {e}")
            return []
    
    def find_element_by_text(self, elements: list, text: str) -> dict:
        """
        根据文本查找元素
        
        Args:
            elements: 元素列表
            text: 文本内容
            
        Returns:
            dict: 元素信息
        """
        for element in elements:
            if element.get('text') == text:
                return element
        return {}
    
    def find_element_by_resource_id(self, elements: list, resource_id: str) -> dict:
        """
        根据resource_id查找元素
        
        Args:
            elements: 元素列表
            resource_id: 资源ID
            
        Returns:
            dict: 元素信息
        """
        for element in elements:
            if element.get('resource_id') == resource_id:
                return element
        return {}
    
    def update_settings_locators(self):
        """更新设置应用的定位器"""
        log.info("🔄 更新设置应用定位器...")
        
        # 根据探测结果，TECNO设备的设置界面结构
        locator_mapping = {
            # 搜索相关
            'search_box': {
                'old': {"resourceId": "android:id/search_src_text"},
                'new': {"resourceId": "com.android.settings:id/tran_homepage_top_search_text"}
            },
            'search_button': {
                'old': {"resourceId": "com.android.settings:id/search"},
                'new': {"resourceId": "com.android.settings:id/tran_homepage_top_search"}
            },
            
            # 设置项 - 使用文本定位
            'wifi_settings': {
                'old': {"text": "WLAN"},
                'new': {"text": "热点与连接"}  # TECNO设备将WiFi归类到"热点与连接"
            },
            'wifi_settings_en': {
                'old': {"text": "Wi-Fi"},
                'new': {"text": "热点与连接"}
            },
            'display_settings': {
                'old': {"text": "显示"},
                'new': {"text": "显示与亮度"}
            },
            'display_settings_en': {
                'old': {"text": "Display"},
                'new': {"text": "显示与亮度"}
            },
            'sound_settings': {
                'old': {"text": "声音"},
                'new': {"text": "声音与振动"}
            },
            'sound_settings_en': {
                'old': {"text": "Sound"},
                'new': {"text": "声音与振动"}
            },
            'apps_settings': {
                'old': {"text": "应用"},
                'new': {"text": "应用管理"}
            },
            'apps_settings_en': {
                'old': {"text": "Apps"},
                'new': {"text": "应用管理"}
            },
            'security_settings': {
                'old': {"text": "安全"},
                'new': {"text": "密码与安全"}
            },
            'security_settings_en': {
                'old': {"text": "Security"},
                'new': {"text": "密码与安全"}
            },
            'privacy_settings': {
                'old': {"text": "隐私"},
                'new': {"text": "权限与隐私"}
            },
            'privacy_settings_en': {
                'old': {"text": "Privacy"},
                'new': {"text": "权限与隐私"}
            }
        }
        
        # 生成更新后的设置页面代码
        self._generate_updated_settings_page(locator_mapping)
    
    def _generate_updated_settings_page(self, locator_mapping: dict):
        """
        生成更新后的设置页面代码
        
        Args:
            locator_mapping: 定位器映射
        """
        updated_code = '''"""
设置应用主页面 - 已更新适配TECNO设备
"""
from pages.base.common_page import CommonPage
from core.logger import log


class SettingsMainPage(CommonPage):
    """设置主页面 - TECNO设备适配版"""
    
    def __init__(self):
        """初始化设置主页面"""
        super().__init__("settings", "main_page")
        
        # 初始化页面元素
        self._init_elements()
    
    def _init_elements(self):
        """初始化页面元素 - 根据TECNO设备实际结构"""
        # 搜索框 - 使用TECNO设备的实际定位器
        self.search_box = self.create_element(
            {"resourceId": "com.android.settings:id/tran_homepage_top_search_text"},
            "搜索框"
        )
        
        self.search_button = self.create_element(
            {"resourceId": "com.android.settings:id/tran_homepage_top_search"},
            "搜索按钮"
        )
        
        # TECNO设备的设置项 - 使用实际的文本
        self.hotspot_connection_settings = self.create_element(
            {"text": "热点与连接"},
            "热点与连接设置"
        )
        
        self.tecno_ai_settings = self.create_element(
            {"text": "TECNO AI"},
            "TECNO AI设置"
        )
        
        self.wallpaper_settings = self.create_element(
            {"text": "壁纸与个性化"},
            "壁纸与个性化设置"
        )
        
        self.display_settings = self.create_element(
            {"text": "显示与亮度"},
            "显示与亮度设置"
        )
        
        self.sound_settings = self.create_element(
            {"text": "声音与振动"},
            "声音与振动设置"
        )
        
        self.notification_settings = self.create_element(
            {"text": "通知与状态栏"},
            "通知与状态栏设置"
        )
        
        self.security_settings = self.create_element(
            {"text": "密码与安全"},
            "密码与安全设置"
        )
        
        self.privacy_settings = self.create_element(
            {"text": "权限与隐私"},
            "权限与隐私设置"
        )
        
        self.apps_settings = self.create_element(
            {"text": "应用管理"},
            "应用管理设置"
        )
        
        self.location_settings = self.create_element(
            {"text": "位置信息"},
            "位置信息设置"
        )
        
        self.special_features = self.create_element(
            {"text": "特色功能"},
            "特色功能设置"
        )
        
        # 通用设置项定位器（使用android:id/title）
        self.setting_item_generic = self.create_element(
            {"resourceId": "android:id/title"},
            "通用设置项"
        )
    
    def wait_for_page_load(self, timeout=None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        # 等待搜索按钮或任一设置项出现
        return (self.search_button.wait_for_element(timeout) or 
                self.hotspot_connection_settings.wait_for_element(timeout))
    
    def search_setting(self, keyword: str) -> bool:
        """
        搜索设置项
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            bool: 搜索是否成功
        """
        try:
            log.info(f"搜索设置项: {keyword}")
            
            # 点击搜索按钮
            if not self.search_button.click():
                log.error("点击搜索按钮失败")
                return False
            
            # 等待搜索框出现并输入关键词
            import time
            time.sleep(1)
            
            if not self.search_box.send_keys(keyword):
                log.error("输入搜索关键词失败")
                return False
            
            # 按回车键搜索
            self.driver.press("enter")
            
            return True
            
        except Exception as e:
            log.error(f"搜索设置项失败: {e}")
            return False
    
    def enter_hotspot_connection_settings(self) -> bool:
        """
        进入热点与连接设置（包含WiFi）
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入热点与连接设置")
            return self.hotspot_connection_settings.click()
        except Exception as e:
            log.error(f"进入热点与连接设置失败: {e}")
            return False
    
    def enter_display_settings(self) -> bool:
        """
        进入显示与亮度设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入显示与亮度设置")
            return self.display_settings.click()
        except Exception as e:
            log.error(f"进入显示与亮度设置失败: {e}")
            return False
    
    def enter_apps_settings(self) -> bool:
        """
        进入应用管理设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入应用管理设置")
            return self.apps_settings.click()
        except Exception as e:
            log.error(f"进入应用管理设置失败: {e}")
            return False
    
    def enter_security_settings(self) -> bool:
        """
        进入密码与安全设置
        
        Returns:
            bool: 进入是否成功
        """
        try:
            log.info("进入密码与安全设置")
            return self.security_settings.click()
        except Exception as e:
            log.error(f"进入密码与安全设置失败: {e}")
            return False
    
    def scroll_to_setting(self, setting_text: str) -> bool:
        """
        滚动到指定设置项
        
        Args:
            setting_text: 设置项文本
            
        Returns:
            bool: 是否找到设置项
        """
        try:
            log.info(f"滚动查找设置项: {setting_text}")
            
            # 最多滚动5次
            for i in range(5):
                # 检查设置项是否存在
                element = self.create_element({"text": setting_text}, f"设置项_{setting_text}")
                if element.is_exists():
                    log.info(f"找到设置项: {setting_text}")
                    return True
                
                # 向下滚动
                self.swipe_up()
                import time
                time.sleep(1)
            
            log.warning(f"未找到设置项: {setting_text}")
            return False
            
        except Exception as e:
            log.error(f"滚动查找设置项失败: {e}")
            return False
    
    def click_setting_by_text(self, setting_text: str) -> bool:
        """
        根据文本点击设置项
        
        Args:
            setting_text: 设置项文本
            
        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击设置项: {setting_text}")
            
            # 先尝试直接点击
            element = self.create_element({"text": setting_text}, f"设置项_{setting_text}")
            if element.is_exists():
                return element.click()
            
            # 如果不存在，尝试滚动查找
            if self.scroll_to_setting(setting_text):
                return element.click()
            
            return False
            
        except Exception as e:
            log.error(f"点击设置项失败: {e}")
            return False
'''
        
        # 保存更新后的文件
        updated_file_path = "pages/apps/settings/main_page_updated.py"
        with open(updated_file_path, 'w', encoding='utf-8') as f:
            f.write(updated_code)
        
        log.info(f"✅ 已生成更新后的设置页面: {updated_file_path}")
    
    def check_calculator_app(self):
        """检查计算器应用"""
        log.info("🔍 检查计算器应用...")
        
        if not self.calculator_elements:
            log.warning("⚠️ 计算器元素信息为空，可能是应用未正确启动")
            log.info("💡 建议:")
            log.info("1. 检查设备上是否安装了Google计算器")
            log.info("2. 尝试查找TECNO设备自带的计算器应用")
            log.info("3. 使用 'adb shell pm list packages | grep calc' 查找计算器包名")
            return False
        
        return True
    
    def suggest_calculator_package_names(self):
        """建议可能的计算器包名"""
        log.info("📱 常见的计算器包名:")
        common_calc_packages = [
            "com.google.android.calculator",  # Google计算器
            "com.android.calculator2",        # AOSP计算器
            "com.sec.android.app.popupcalculator",  # 三星计算器
            "com.miui.calculator",             # 小米计算器
            "com.huawei.calculator",           # 华为计算器
            "com.transsion.calculator",        # TECNO可能的计算器
            "com.tecno.calculator",            # TECNO计算器
            "com.itel.calculator",             # itel计算器
            "com.infinix.calculator"           # Infinix计算器
        ]
        
        for package in common_calc_packages:
            log.info(f"  - {package}")
        
        log.info("\n🔧 检查方法:")
        log.info("1. 运行: adb shell pm list packages | findstr calc")
        log.info("2. 或运行: adb shell pm list packages | findstr calculator")
        log.info("3. 找到正确的包名后更新 config/config.yaml")


def main():
    """主函数"""
    log.info("🔄 自动更新定位器工具")
    log.info("=" * 60)
    
    updater = LocatorUpdater()
    
    # 更新设置应用定位器
    updater.update_settings_locators()
    
    # 检查计算器应用
    updater.check_calculator_app()
    updater.suggest_calculator_package_names()
    
    log.info("\n" + "=" * 60)
    log.info("🎯 更新完成！")
    log.info("📁 生成的文件:")
    log.info("  - pages/apps/settings/main_page_updated.py")
    log.info("\n💡 下一步:")
    log.info("1. 查看更新后的设置页面代码")
    log.info("2. 找到正确的计算器包名并更新配置")
    log.info("3. 运行 'python test_updated_locators.py' 测试更新后的定位器")


if __name__ == "__main__":
    main()
