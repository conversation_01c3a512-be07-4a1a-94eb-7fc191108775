"""
快速开始示例脚本
演示如何使用框架进行简单的自动化测试
"""
import time
from core.logger import log
from core.base_driver import driver_manager
from pages.apps.calculator.main_page import CalculatorMainPage
from pages.apps.settings.main_page import SettingsMainPage


def test_calculator_demo():
    """计算器演示测试"""
    log.info("=" * 50)
    log.info("开始计算器演示测试")
    log.info("=" * 50)
    
    try:
        # 创建计算器页面对象
        calc_page = CalculatorMainPage()
        
        # 启动计算器应用
        log.info("启动计算器应用...")
        if not calc_page.start_app():
            log.error("启动计算器失败")
            return False
        
        # 等待页面加载
        if not calc_page.wait_for_page_load():
            log.error("计算器页面加载失败")
            return False
        
        # 执行简单计算
        log.info("执行计算: 2 + 3")
        result = calc_page.calculate("2+3")
        log.info(f"计算结果: {result}")
        
        # 验证结果
        if result == "5":
            log.info("✅ 计算器测试通过")
        else:
            log.error(f"❌ 计算器测试失败，期望: 5, 实际: {result}")
        
        # 截图
        calc_page.screenshot("calculator_demo.png")
        
        # 停止应用
        calc_page.stop_app()
        
        return True
        
    except Exception as e:
        log.error(f"计算器演示测试失败: {e}")
        return False


def test_settings_demo():
    """设置应用演示测试"""
    log.info("=" * 50)
    log.info("开始设置应用演示测试")
    log.info("=" * 50)
    
    try:
        # 创建设置页面对象
        settings_page = SettingsMainPage()
        
        # 启动设置应用
        log.info("启动设置应用...")
        if not settings_page.start_app():
            log.error("启动设置应用失败")
            return False
        
        # 等待页面加载
        if not settings_page.wait_for_page_load():
            log.error("设置页面加载失败")
            return False
        
        # 截图
        settings_page.screenshot("settings_main.png")
        
        # 尝试进入WiFi设置
        log.info("尝试进入WiFi设置...")
        if settings_page.enter_wifi_settings():
            log.info("✅ 成功进入WiFi设置")
            settings_page.screenshot("wifi_settings.png")
            
            # 返回主页面
            time.sleep(2)
            settings_page.go_back()
            time.sleep(2)
        else:
            log.warning("⚠️ 未能进入WiFi设置（可能是界面差异）")
        
        # 停止应用
        settings_page.stop_app()
        
        return True
        
    except Exception as e:
        log.error(f"设置应用演示测试失败: {e}")
        return False


def check_device_connection():
    """检查设备连接"""
    log.info("=" * 50)
    log.info("检查设备连接")
    log.info("=" * 50)
    
    try:
        # 获取设备信息
        device_info = driver_manager.get_device_info()
        log.info(f"设备信息: {device_info}")
        
        # 获取屏幕尺寸
        width, height = driver_manager.get_window_size()
        log.info(f"屏幕尺寸: {width} x {height}")
        
        # 截图测试
        screenshot_path = driver_manager.screenshot("device_check.png")
        log.info(f"截图保存: {screenshot_path}")
        
        log.info("✅ 设备连接正常")
        return True
        
    except Exception as e:
        log.error(f"❌ 设备连接检查失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 Android自动化测试框架 - 快速开始演示")
    
    # 检查设备连接
    if not check_device_connection():
        log.error("设备连接失败，请检查设备连接和配置")
        return
    
    # 运行演示测试
    success_count = 0
    total_count = 2
    
    # 计算器演示
    if test_calculator_demo():
        success_count += 1
    
    time.sleep(3)  # 等待一下再进行下一个测试
    
    # 设置应用演示
    if test_settings_demo():
        success_count += 1
    
    # 总结
    log.info("=" * 50)
    log.info("演示测试完成")
    log.info(f"成功: {success_count}/{total_count}")
    log.info("=" * 50)
    
    if success_count == total_count:
        log.info("🎉 所有演示测试通过！框架运行正常")
    else:
        log.warning("⚠️ 部分演示测试失败，可能需要调整配置或检查应用")
    
    log.info("📝 提示:")
    log.info("1. 检查 reports/screenshots/ 目录查看截图")
    log.info("2. 检查 logs/ 目录查看详细日志")
    log.info("3. 运行 'python run_tests.py --smoke' 执行完整的冒烟测试")
    log.info("4. 运行 'python run_tests.py --open-report' 生成并查看测试报告")


if __name__ == "__main__":
    main()
