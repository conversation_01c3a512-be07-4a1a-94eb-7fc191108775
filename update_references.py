#!/usr/bin/env python3
"""
批量更新EllaMainPageRefactored引用的脚本
"""
import os
import re

def update_file_references(file_path):
    """更新单个文件中的引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换import语句
        content = re.sub(
            r'from pages\.apps\.ella\.main_page_refactored import EllaMainPageRefactored',
            'from pages.apps.ella.dialogue_page import EllaDialoguePage',
            content
        )
        
        # 替换类实例化
        content = re.sub(
            r'EllaMainPageRefactored\(\)',
            'EllaDialoguePage()',
            content
        )
        
        # 替换变量赋值
        content = re.sub(
            r'ella_page = EllaMainPageRefactored\(\)',
            'ella_page = EllaDialoguePage()',
            content
        )
        
        content = re.sub(
            r'self\.ella_app = EllaMainPageRefactored\(\)',
            'self.ella_app = EllaDialoguePage()',
            content
        )
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def find_and_update_files():
    """查找并更新所有相关文件"""
    files_to_update = [
        "tmp/history/test_bluetooth_simple_command_concise.py",
        "tmp/history/test_open_bluetooth_voice.py", 
        "tmp/history/test_open_clock_command.py",
        "tmp/history/test_open_contacts_command.py",
        "tmp/history/test_open_contacts_refactored.py",
        "tmp/history/test_set_alarm_command.py",
        "tmp/history/test_take_photo_command.py",
        "tmp/history/test_weather_query_command.py"
    ]
    
    updated_count = 0
    for file_path in files_to_update:
        if os.path.exists(file_path):
            if update_file_references(file_path):
                updated_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n📊 更新完成: {updated_count}/{len(files_to_update)} 个文件")

if __name__ == "__main__":
    print("🔄 开始批量更新EllaMainPageRefactored引用...")
    find_and_update_files()
    print("✅ 批量更新完成!")
