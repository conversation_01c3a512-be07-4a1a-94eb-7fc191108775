#!/usr/bin/env python3
"""
测试优化后的XML文本提取功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pages.apps.ella.ella_response_handler import <PERSON>ResponseHandler
from core.logger import log


class TestXMLTextExtraction:
    """测试XML文本提取功能"""
    
    def __init__(self):
        # 创建处理器实例（不需要真实驱动）
        self.handler = EllaResponseHandler(driver=None)
    
    def create_test_xml(self):
        """创建测试用的XML内容"""
        return '''<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]">
    <node index="0" text="Ella语音助手" resource-id="com.transsion.aivoiceassistant:id/title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,100][300,150]">
    </node>
    <node index="1" text="打开蓝牙" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,200][300,250]">
    </node>
    <node index="2" text="蓝牙已打开" resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,300][300,350]">
    </node>
    <node index="3" text="蓝牙" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,400][300,450]">
    </node>
    <node index="4" text="已打开" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,500][300,550]">
    </node>
    <node index="5" text="" resource-id="com.transsion.aivoiceassistant:id/empty_view" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,600][300,650]">
    </node>
    <node index="6" text="123" resource-id="com.transsion.aivoiceassistant:id/number_view" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,700][300,750]">
    </node>
    <node index="7" text="Special &amp; Characters &lt;test&gt;" resource-id="com.transsion.aivoiceassistant:id/special_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[40,800][300,850]">
    </node>
  </node>
</hierarchy>'''
    
    def test_extract_all_text(self):
        """测试提取所有文本"""
        print("\n📋 测试1: 提取所有text属性文本")
        
        xml_content = self.create_test_xml()
        texts = self.handler.extract_all_text_from_xml(xml_content)
        
        print(f"提取到 {len(texts)} 个文本:")
        for i, text in enumerate(texts, 1):
            print(f"  {i}. '{text}'")
        
        # 验证是否包含期望的文本
        expected_texts = ["Ella语音助手", "打开蓝牙", "蓝牙已打开", "蓝牙", "已打开"]
        found_count = sum(1 for expected in expected_texts if expected in texts)
        print(f"✅ 找到期望文本: {found_count}/{len(expected_texts)}")
    
    def test_extract_with_filter(self):
        """测试带关键词过滤的提取"""
        print("\n📋 测试2: 带关键词过滤的文本提取")
        
        xml_content = self.create_test_xml()
        filter_keywords = ["蓝牙", "打开"]
        texts = self.handler.extract_all_text_from_xml(xml_content, filter_keywords)
        
        print(f"过滤关键词 {filter_keywords} 后提取到 {len(texts)} 个文本:")
        for i, text in enumerate(texts, 1):
            print(f"  {i}. '{text}'")
    
    def test_extract_by_resource_id(self):
        """测试按resource-id提取文本"""
        print("\n📋 测试3: 按resource-id提取文本")
        
        xml_content = self.create_test_xml()
        
        # 测试不同的resource-id
        test_ids = [
            "com.transsion.aivoiceassistant:id/asr_text",
            "com.transsion.aivoiceassistant:id/robot_text", 
            "com.transsion.aivoiceassistant:id/function_name",
            "com.transsion.aivoiceassistant:id/function_control",
            "com.transsion.aivoiceassistant:id/nonexistent"
        ]
        
        for resource_id in test_ids:
            texts = self.handler.extract_text_by_resource_id_from_xml(xml_content, resource_id)
            if texts:
                print(f"✅ {resource_id}: {texts}")
            else:
                print(f"❌ {resource_id}: 未找到文本")
    
    def test_clean_extracted_text(self):
        """测试文本清理功能"""
        print("\n📋 测试4: 文本清理功能")
        
        test_texts = [
            "Special &amp; Characters &lt;test&gt;",  # 转义字符
            "  空格文本  ",  # 首尾空格
            "",  # 空文本
            "123",  # 纯数字
            "正常文本",  # 正常文本
            "&lt;xml&gt;标签&amp;符号",  # 复杂转义
        ]
        
        for original in test_texts:
            cleaned = self.handler._clean_extracted_text(original)
            print(f"原始: '{original}' -> 清理后: '{cleaned}'")
    
    def test_regex_pattern_edge_cases(self):
        """测试正则表达式的边界情况"""
        print("\n📋 测试5: 正则表达式边界情况")
        
        edge_case_xml = '''
        <node text="正常文本" resource-id="test1" />
        <node text="" resource-id="test2" />
        <node text="包含\"引号\"的文本" resource-id="test3" />
        <node resource-id="test4" text="属性顺序不同" />
        <node text="多行
        文本" resource-id="test5" />
        '''
        
        texts = self.handler._extract_text_attributes_from_xml(edge_case_xml)
        print(f"边界情况测试提取到 {len(texts)} 个文本:")
        for i, text in enumerate(texts, 1):
            print(f"  {i}. '{text}'")
    
    def test_performance_comparison(self):
        """测试性能对比"""
        print("\n📋 测试6: 性能对比")
        
        import time
        xml_content = self.create_test_xml()
        
        # 测试正则表达式方法
        start_time = time.time()
        for _ in range(1000):
            texts = self.handler._extract_text_attributes_from_xml(xml_content)
        regex_time = time.time() - start_time
        
        print(f"正则表达式方法 1000次: {regex_time:.4f}秒")
        print(f"平均每次: {regex_time/1000:.6f}秒")
        print(f"提取文本数量: {len(texts)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始测试优化后的XML文本提取功能...")
        
        test_methods = [
            self.test_extract_all_text,
            self.test_extract_with_filter,
            self.test_extract_by_resource_id,
            self.test_clean_extracted_text,
            self.test_regex_pattern_edge_cases,
            self.test_performance_comparison
        ]
        
        passed = 0
        total = len(test_methods)
        
        for test_method in test_methods:
            try:
                test_method()
                passed += 1
            except Exception as e:
                print(f"❌ 测试方法 {test_method.__name__} 执行异常: {e}")
        
        print(f"\n🎉 测试完成: {passed}/{total} 个测试通过")


if __name__ == "__main__":
    tester = TestXMLTextExtraction()
    tester.run_all_tests()
