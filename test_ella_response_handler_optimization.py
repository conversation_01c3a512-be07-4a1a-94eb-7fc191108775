#!/usr/bin/env python3
"""
测试Ella响应处理器优化后的功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pages.apps.ella.ella_response_handler import <PERSON><PERSON><PERSON>ponseH<PERSON><PERSON>
from core.logger import log
from unittest.mock import Mock, MagicMock
import time


class MockElement:
    """模拟UIAutomator2元素"""
    
    def __init__(self, exists=True, text="测试文本"):
        self._exists = exists
        self._text = text
    
    def exists(self):
        return self._exists
    
    def get_text(self):
        return self._text


class MockDriver:
    """模拟UIAutomator2驱动"""
    
    def __init__(self):
        self.elements = {}
    
    def __call__(self, resourceId=None, **kwargs):
        # 根据resourceId返回不同的模拟元素
        if resourceId == "com.transsion.aivoiceassistant:id/asr_text":
            return self.elements.get("asr_text", MockElement(exists=True, text="打开蓝牙"))
        elif resourceId == "com.transsion.aivoiceassistant:id/robot_text":
            return self.elements.get("robot_text", MockElement(exists=True, text="蓝牙已打开"))
        elif resourceId == "com.transsion.aivoiceassistant:id/function_name":
            return self.elements.get("function_name", MockElement(exists=True, text="蓝牙"))
        elif resourceId == "com.transsion.aivoiceassistant:id/function_control":
            return self.elements.get("function_control", MockElement(exists=True, text="已打开"))
        else:
            return MockElement(exists=False, text="")
    
    def set_element(self, element_name, mock_element):
        """设置模拟元素"""
        self.elements[element_name] = mock_element


def test_optimized_methods():
    """测试优化后的方法"""
    print("🧪 开始测试优化后的Ella响应处理器...")
    
    # 创建模拟驱动和处理器
    mock_driver = MockDriver()
    handler = EllaResponseHandler(mock_driver)
    
    # 测试1: 正常情况下获取文本
    print("\n📋 测试1: 正常情况下获取文本")
    
    asr_text = handler.get_response_from_asr_txt()
    print(f"ASR文本: {asr_text}")
    
    robot_text = handler.get_response_from_robot_text()
    print(f"Robot文本: {robot_text}")
    
    function_name = handler.get_response_from_function_name()
    print(f"Function名称: {function_name}")
    
    function_control = handler.get_response_from_function_control()
    print(f"Function控制: {function_control}")
    
    # 测试2: 元素不存在的情况
    print("\n📋 测试2: 元素不存在的情况")
    mock_driver.set_element("asr_text", MockElement(exists=False, text=""))
    
    asr_text = handler.get_response_from_asr_txt()
    print(f"不存在的ASR文本: '{asr_text}' (应该为空)")
    
    # 测试3: 元素存在但文本为空的情况
    print("\n📋 测试3: 元素存在但文本为空的情况")
    mock_driver.set_element("robot_text", MockElement(exists=True, text=""))
    
    robot_text = handler.get_response_from_robot_text()
    print(f"空文本的Robot文本: '{robot_text}' (应该为空)")
    
    # 测试4: 元素存在但文本只有空格的情况
    print("\n📋 测试4: 元素存在但文本只有空格的情况")
    mock_driver.set_element("function_name", MockElement(exists=True, text="   "))
    
    function_name = handler.get_response_from_function_name()
    print(f"空格文本的Function名称: '{function_name}' (应该为空)")
    
    # 测试5: 测试通用方法的直接调用
    print("\n📋 测试5: 测试通用方法的直接调用")
    
    # 重置为正常元素
    mock_driver.set_element("function_control", MockElement(exists=True, text="蓝牙已关闭"))
    
    # 测试不验证AI响应格式的情况
    text = handler._get_element_text_with_retry(
        resource_id="com.transsion.aivoiceassistant:id/function_control",
        element_name="function_control",
        validate_ai_response=False
    )
    print(f"不验证AI响应格式的文本: {text}")
    
    # 测试自定义重试参数
    text = handler._get_element_text_with_retry(
        resource_id="com.transsion.aivoiceassistant:id/function_control",
        element_name="function_control",
        max_retries=1,
        retry_delay=0.1,
        validate_ai_response=True
    )
    print(f"自定义重试参数的文本: {text}")
    
    print("\n✅ 所有测试完成!")


def test_performance_comparison():
    """测试性能对比"""
    print("\n🚀 性能测试...")
    
    mock_driver = MockDriver()
    handler = EllaResponseHandler(mock_driver)
    
    # 测试多次调用的性能
    start_time = time.time()
    for i in range(100):
        handler.get_response_from_asr_txt()
        handler.get_response_from_robot_text()
        handler.get_response_from_function_name()
        handler.get_response_from_function_control()
    
    end_time = time.time()
    print(f"100次调用耗时: {end_time - start_time:.3f}秒")
    print(f"平均每次调用耗时: {(end_time - start_time) / 400:.6f}秒")


if __name__ == "__main__":
    try:
        test_optimized_methods()
        test_performance_comparison()
        print("\n🎉 测试全部通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
