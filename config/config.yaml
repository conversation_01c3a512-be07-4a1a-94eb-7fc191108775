# 全局配置文件
app:
  # 默认等待时间(秒)
  default_timeout: 10
  # 元素查找超时时间(秒)
  element_timeout: 5
  # 页面加载超时时间(秒)
  page_load_timeout: 30
  # 截图保存路径
  screenshot_path: "reports/screenshots"

# 日志配置 - 优化版本
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

  # 分类日志配置
  categories:
    general:
      rotation: "5 MB"
      retention: "10 days"
    test:
      rotation: "10 MB"
      retention: "7 days"
    debug:
      rotation: "3 MB"
      retention: "5 days"
    error:
      rotation: "5 MB"
      retention: "30 days"
    performance:
      rotation: "2 MB"
      retention: "14 days"

  # 自动清理配置
  auto_cleanup:
    enabled: true
    cleanup_days: 30

  # 控制台输出配置
  console:
    level: "INFO"
    colorize: true
    show_debug: false

# 测试报告配置
report:
  allure_results_path: "reports/allure-results"
  allure_report_path: "reports/allure-report"

# 应用配置
apps:
  calculator:
    package_name: "com.transsion.calculator"
    activity: "com.transsion.calculator.Calculator"
    app_name: "计算器"
  
  settings:
    package_name: "com.android.settings"
    activity: "com.android.settings.Settings"
    app_name: "设置"

  ella:
    package_name: "com.transsion.aivoiceassistant"
    activity: "com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"
    app_name: "ella"

  clock:
    package_name: "com.transsion.deskclock"
    activity: "com.transsion.deskclock.DeskClock"
    app_name: "时钟"

