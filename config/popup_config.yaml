advanced:
  image_recognition:
    enabled: false
    match_threshold: 0.8
    template_path: templates/popups/
  ml:
    enabled: false
    feature_extraction: text_and_layout
    model_path: models/popup_classifier.pkl
  ocr:
    confidence_threshold: 0.8
    enabled: false
    language: chi_sim
app_specific:
  com.eg.android.AlipayGphone:
    ignored_types:
    - promotion_banner
    priority_types:
    - security_dialog
  com.tencent.mm:
    custom_strategies:
      payment_dialog:
      - click_close
      - back_key
    ignored_types:
    - moments_ad
    priority_types:
    - payment_dialog
custom_rules:
  ella_bluetooth_permission:
    buttons:
    - 允许
    - Allow
    - 确定
    - OK
    keywords:
    - 蓝牙权限
    - bluetooth permission
    - 蓝牙访问
    package: com.transsion.ella
  ella_feature_guide:
    buttons:
    - 知道了
    - Got it
    - 下一步
    - Next
    - 跳过
    - Skip
    keywords:
    - 功能介绍
    - Feature Guide
    - 使用教程
    - 新功能
    package: com.transsion.ella
  ella_first_launch:
    buttons:
    - 同意
    - Agree
    - 确定
    - OK
    - 继续
    - Continue
    keywords:
    - 欢迎使用
    - Welcome
    - 首次使用
    - 用户协议
    - 隐私政策
    package: com.transsion.ella
  ella_network_error:
    buttons:
    - 重试
    - 设置
    - Retry
    - Settings
    - 刷新
    - 确定
    keywords:
    - 网络连接失败
    - network error
    - 无法连接
    - 网络异常
    - 连接超时
    package: com.transsion.ella
  ella_update_dialog:
    buttons:
    - 立即更新
    - 稍后
    - Update
    - Later
    - 更新
    - 取消
    keywords:
    - Ella更新
    - Ella升级
    - new version
    - 新版本
    - 版本更新
    package: com.transsion.ella
  ella_voice_permission:
    buttons:
    - 允许
    - Allow
    - 确定
    - OK
    - 同意
    keywords:
    - 麦克风权限
    - microphone permission
    - Ella需要
    - 录音权限
    package: com.transsion.ella
  test_popup:
    buttons:
    - 确定
    - OK
    keywords:
    - 测试
    - test
    package: com.test.app
debug:
  enabled: false
  save_detection_screenshots: false
  save_handling_screenshots: false
  step_by_step: false
  verbose_logging: false
detection:
  enabled: true
  interval: 1.5
  methods:
  - elements
  - text
  - layout
  - package
  min_confidence: 0.7
  timeout: 30
handling:
  auto_handle: true
  log_all_attempts: true
  max_retries: 3
  retry_delay: 1
  screenshot_on_failure: true
ignored_types:
- loading_dialog
- progress_dialog
logging:
  backup_count: 5
  file: logs/popup_handler.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_size: 10MB
monitoring:
  daemon_thread: true
  enabled: false
  stop_on_error: false
performance:
  cache_enabled: true
  cache_size: 100
  max_detection_time: 5
  max_handling_time: 10
priority_types:
- permission_dialog
- error_dialog
- system_update
statistics:
  enabled: true
  file: reports/popup_stats.json
  include_screenshots: false
  save_interval: 300
strategies:
  advertisement:
  - click_close
  - swipe_dismiss
  default:
  - click_close
  - back_key
  - swipe_dismiss
  loading_dialog:
  - wait_disappear
  notification:
  - swipe_dismiss
  - click_close
  permission_dialog:
  - click_close
  system_update:
  - click_close
  - back_key
uiautomator2:
  click_timeout: 5
  implicit_wait: 1
  swipe_duration: 0.5
  wait_timeout: 10
