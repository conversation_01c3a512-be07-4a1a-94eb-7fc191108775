{"process_cleanup_config": {"common_user_apps": [{"package": "com.transsion.aivoiceassistant", "description": "Ella语音助手", "category": "system_app"}, {"package": "com.android.chrome", "description": "Chrome浏览器", "category": "browser"}, {"package": "com.facebook.katana", "description": "Facebook", "category": "social"}, {"package": "com.facebook.lite", "description": "Facebook Lite", "category": "social"}, {"package": "com.whatsapp", "description": "WhatsApp", "category": "social"}, {"package": "com.instagram.android", "description": "Instagram", "category": "social"}, {"package": "com.twitter.android", "description": "Twitter", "category": "social"}, {"package": "com.spotify.music", "description": "Spotify", "category": "media"}, {"package": "com.netflix.mediaclient", "description": "Netflix", "category": "media"}, {"package": "com.tencent.mm", "description": "微信", "category": "social"}, {"package": "com.alibaba.android.rimet", "description": "钉钉", "category": "work"}, {"package": "com.tencent.mobileqq", "description": "QQ", "category": "social"}, {"package": "com.sina.weibo", "description": "微博", "category": "social"}, {"package": "com.taobao.taobao", "description": "淘宝", "category": "shopping"}, {"package": "com.jingdong.app.mall", "description": "京东", "category": "shopping"}, {"package": "com.tencent.tmgp.sgame", "description": "王者荣耀", "category": "game"}, {"package": "com.tencent.ig", "description": "和平精英", "category": "game"}, {"package": "com.ss.android.ugc.aweme", "description": "抖音", "category": "media"}, {"package": "com.smile.gifmaker", "description": "快手", "category": "media"}, {"package": "com.UCMobile", "description": "UC浏览器", "category": "browser"}], "recent_apps_clear_positions": [{"x": 540, "y": 1800, "description": "底部中央"}, {"x": 1000, "y": 1800, "description": "底部右侧"}, {"x": 540, "y": 200, "description": "顶部中央"}, {"x": 1000, "y": 200, "description": "顶部右侧"}, {"x": 200, "y": 1800, "description": "底部左侧"}, {"x": 800, "y": 1600, "description": "右下角"}], "cleanup_settings": {"gentle_cleanup_enabled": true, "recent_apps_fallback_enabled": true, "min_apps_for_fallback": 5, "cleanup_timeout": 5, "stabilization_wait": 1, "max_retry_attempts": 3}}}