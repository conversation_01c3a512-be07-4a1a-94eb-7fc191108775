{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.open_app", "children": [{"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "22bc5ed4f935d1a3829689267bacfded", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "22bc5ed4f935d1a3829689267bacfded"}], "uid": "086f5bf9c9f3fb23c1b297b00253dc88"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "f73e43036f2e212", "parentUid": "f35b52a29f6fea6cff033275e7496912", "status": "passed", "time": {"start": 1753349793794, "stop": 1753349806727, "duration": 12933}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f35b52a29f6fea6cff033275e7496912"}], "uid": "ceecf20edc4749ff2576074a707ec1ca"}, {"name": "test_open_camera", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open camera能正常执行", "uid": "2bf0fd732ad7815e", "parentUid": "64370619fbbf199cc9c18035e094da12", "status": "passed", "time": {"start": 1753349810541, "stop": 1753349826049, "duration": 15508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64370619fbbf199cc9c18035e094da12"}], "uid": "6e0a17a71edb82c1a70055256084672b"}, {"name": "test_open_clock", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "open clock", "uid": "664f1be505275c9b", "parentUid": "776d941a91f13bf88fc2c66c582c08e4", "status": "failed", "time": {"start": 1753349829956, "stop": 1753349843254, "duration": 13298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "776d941a91f13bf88fc2c66c582c08e4"}], "uid": "f412de33850a9c3df2d0a27f7d697558"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "62b6a6470c04e2aa", "parentUid": "73036eafa970cbaaecf8e20d47c36b8a", "status": "passed", "time": {"start": 1753349847621, "stop": 1753349860616, "duration": 12995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73036eafa970cbaaecf8e20d47c36b8a"}], "uid": "d5a89f320d504424f15ba1b2d05e9301"}, {"name": "test_open_countdown", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open countdown能正常执行", "uid": "bc0d71dbeec9f327", "parentUid": "02bb163ba9b0461a01270d79502eb0d7", "status": "passed", "time": {"start": 1753349864535, "stop": 1753349875675, "duration": 11140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02bb163ba9b0461a01270d79502eb0d7"}], "uid": "9dd776f619132af5350a7f02cf6a6fc6"}, {"name": "test_open_dialer", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open dialer能正常执行", "uid": "9cce20a894e7e918", "parentUid": "f0d6b65568294b71289c4eb1013f4e96", "status": "passed", "time": {"start": 1753349879617, "stop": 1753349896986, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d6b65568294b71289c4eb1013f4e96"}], "uid": "e07528eabcdb99fc288b2cfb121e5638"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "4cf17a74f7f4abcc", "parentUid": "0d5c8ac21677836d6f7a49c0936c9891", "status": "passed", "time": {"start": 1753349900881, "stop": 1753349911555, "duration": 10674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d5c8ac21677836d6f7a49c0936c9891"}], "uid": "d0fce179da86cd8f45965f03019ac3d6"}, {"name": "test_open_facebook", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open facebook能正常执行", "uid": "a7d79bb018aea1d8", "parentUid": "240763b9dfe875e9c5056bd7f41bbfab", "status": "passed", "time": {"start": 1753349915484, "stop": 1753349931111, "duration": 15627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "240763b9dfe875e9c5056bd7f41bbfab"}], "uid": "89b55600a5e9e0d64e802939fafb2553"}, {"name": "test_open_flashlight", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open flashlight", "uid": "133fadc0512f9d9e", "parentUid": "8750d9dfd8381a1d11e9a5408ae1e6fa", "status": "failed", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8750d9dfd8381a1d11e9a5408ae1e6fa"}], "uid": "c365728660b2bc3b75ca5beadf72b429"}, {"name": "test_open_folax", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open folax能正常执行", "uid": "1d06a4cce5c28620", "parentUid": "7e44618b026a8b415ce5be639cdc0565", "status": "passed", "time": {"start": 1753349954708, "stop": 1753349965810, "duration": 11102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e44618b026a8b415ce5be639cdc0565"}], "uid": "53c94a67d2c93df68bda54920b4905bb"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "8d9ae3f36df760a2", "parentUid": "4b93fd0a964dfd2663a40d1d99770c73", "status": "failed", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4b93fd0a964dfd2663a40d1d99770c73"}], "uid": "61d419fade19be3434f57de2df14a00b"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c945f938b4a38fe", "parentUid": "a65b23b1885acf1a73cc3246232e6005", "status": "passed", "time": {"start": 1753349990953, "stop": 1753350005012, "duration": 14059}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a65b23b1885acf1a73cc3246232e6005"}], "uid": "8308904546111e821cb3c99426a7a035"}], "uid": "6d079dc82513aed148d199a00f967765"}, {"name": "testcases.test_ella.third_coupling", "children": [{"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "2a27bc9bb770e828", "parentUid": "e76f8316427676fec23d9fe3fdb4a026", "status": "passed", "time": {"start": 1753350008791, "stop": 1753350019777, "duration": 10986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e76f8316427676fec23d9fe3fdb4a026"}], "uid": "fc89c3a18d9317ae2a61de239911f23f"}, {"name": "test_download_app", "children": [{"name": "TestEllaDownloadApp", "children": [{"name": "测试download app能正常执行", "uid": "32ff1aa9094a8100", "parentUid": "3437a290da1a678bac85f4666839d881", "status": "passed", "time": {"start": 1753350023658, "stop": 1753350038533, "duration": 14875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3437a290da1a678bac85f4666839d881"}], "uid": "ddb6cf5770aedc759f08823b3e6d8b65"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball能正常执行", "uid": "8100a37ec6e5ba5c", "parentUid": "588cc0c6328e2e8a712130edc3aed897", "status": "passed", "time": {"start": 1753350042366, "stop": 1753350052142, "duration": 9776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "588cc0c6328e2e8a712130edc3aed897"}], "uid": "1d4ceb2f76f4a7e0f4095f5c1f138613"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试order a burger能正常执行", "uid": "8d58e3db6ef12645", "parentUid": "43a31fc74a106d981123a4cb94e40290", "status": "passed", "time": {"start": 1753350056024, "stop": 1753350065897, "duration": 9873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43a31fc74a106d981123a4cb94e40290"}], "uid": "3e2cc02d0935d06e8fb6f72bae4c8c83"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderATakeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "71dd4b69357ff2c2", "parentUid": "d9e81a960347bb8bb7bfbcc70b663c4b", "status": "failed", "time": {"start": 1753350069623, "stop": 1753350079905, "duration": 10282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e81a960347bb8bb7bfbcc70b663c4b"}], "uid": "24271799b32debb7d5f05bce65733a10"}, {"name": "test_smart_charge", "children": [{"name": "TestEllaSmartCharge", "children": [{"name": "测试smart charge能正常执行", "uid": "a6fbba3d6c034725", "parentUid": "288e80290080f39936aa79cb06df8c25", "status": "passed", "time": {"start": 1753350084098, "stop": 1753350094352, "duration": 10254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "288e80290080f39936aa79cb06df8c25"}], "uid": "f8cb91d661adf9cd3faeeae19b745fe4"}, {"name": "test_switch_charging_modes", "children": [{"name": "TestEllaSwitchChargingModes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "2c45318d974babe0", "parentUid": "2614841ac01567b9806456a738fe2ba5", "status": "passed", "time": {"start": 1753350098134, "stop": 1753350109624, "duration": 11490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2614841ac01567b9806456a738fe2ba5"}], "uid": "573f67808601861a6e1e8763204df9c7"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "TestEllaSwitchMagicVoiceToMango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "12eb50903ea12475", "parentUid": "d3bc907cf41a86c4e94642e8e5f8814b", "status": "failed", "time": {"start": 1753350113511, "stop": 1753350124587, "duration": 11076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d3bc907cf41a86c4e94642e8e5f8814b"}], "uid": "c0f43a875358bf6d25f935baef5591de"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchToEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "d2fe2d61517ed5c2", "parentUid": "c202bd5c7b88befbe81d4292417831a3", "status": "failed", "time": {"start": 1753350128728, "stop": 1753350136981, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c202bd5c7b88befbe81d4292417831a3"}], "uid": "37d56ee6bb382d2aa266f1fff8c8de08"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "TestEllaSwitchToFlashNotification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "8a5287e14b41a89b", "parentUid": "a1530b3a017db39f15ec9a74dbbf8089", "status": "failed", "time": {"start": 1753350141136, "stop": 1753350155622, "duration": 14486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1530b3a017db39f15ec9a74dbbf8089"}], "uid": "467abedaf5a7053eb681899b34025bcc"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "TestEllaSwitchToHyperCharge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "decd1499c4c83eb9", "parentUid": "7f240e9d71c0d56081987716b78c2918", "status": "passed", "time": {"start": 1753350159949, "stop": 1753350170283, "duration": 10334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f240e9d71c0d56081987716b78c2918"}], "uid": "e3765d5daf93192855f25668b85a1484"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "TestEllaSwitchToLowtempCharge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "ee5c24ded022a1e7", "parentUid": "e0adb614c6557c948abd06fffb9dc676", "status": "passed", "time": {"start": 1753350174077, "stop": 1753350183598, "duration": 9521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e0adb614c6557c948abd06fffb9dc676"}], "uid": "68fd4aff165550da2e0eb183cadb6ea1"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchToPerformanceMode", "children": [{"name": "测试switch to performance mode能正常执行", "uid": "e324c3f616cfbf75", "parentUid": "4dc1251306daba0f35c868308504255b", "status": "failed", "time": {"start": 1753350187320, "stop": 1753350198665, "duration": 11345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4dc1251306daba0f35c868308504255b"}], "uid": "24a3d9cb1f5299535dcc05a9d2b92edc"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchToPowerSavingMode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "64afbae6c960bf96", "parentUid": "94fb2f4586c0afe14069c820752660a2", "status": "passed", "time": {"start": 1753350202833, "stop": 1753350212741, "duration": 9908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94fb2f4586c0afe14069c820752660a2"}], "uid": "def649b1560e28c2a3cdd5b0b96f1004"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "TestEllaSwitchToSmartCharge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "f84da7e480fb17c8", "parentUid": "a75b883d196a7ebcf4a2be11b171c44e", "status": "passed", "time": {"start": 1753350216613, "stop": 1753350226356, "duration": 9743}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a75b883d196a7ebcf4a2be11b171c44e"}], "uid": "b2c47d1d9ca150c86ee264c8249c73e2"}], "uid": "2660f6320a566ad526d6ea679fb2528f"}]}