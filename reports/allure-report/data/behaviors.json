{"uid": "b1a8273437954620fa374b796ffaacdd", "name": "behaviors", "children": [{"name": "设备信息", "children": [{"name": "设备型号: TECNO CM8", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "909599d57996062ec07af67f58bf3885", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "909599d57996062ec07af67f58bf3885"}, {"name": "打开", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "e2a914691cac5198014efb72c568efb0", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2a914691cac5198014efb72c568efb0"}], "uid": "9e40a99c1eef5f208d65955f7b1bb981"}, {"name": "Ella语音助手", "children": [{"name": "设备型号: TECNO CM8", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "08a882de4692d7cb6810138d7e4a0b02", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "08a882de4692d7cb6810138d7e4a0b02"}, {"name": "打开", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "4cf17a74f7f4abcc", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753349900881, "stop": 1753349911555, "duration": 10674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "133fadc0512f9d9e", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "failed", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "c945f938b4a38fe", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753349990953, "stop": 1753350005012, "duration": 14059}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4cfc2e89c37070d46c837256dd442628"}, {"name": "联系人控制命令 - 简洁版本", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "f73e43036f2e212", "parentUid": "5a62d9fbc0d3df56263439fe5374ad4d", "status": "passed", "time": {"start": 1753349793794, "stop": 1753349806727, "duration": 12933}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a62d9fbc0d3df56263439fe5374ad4d"}, {"name": "Ella语音助手基础指令", "children": [{"name": "测试open camera能正常执行", "uid": "2bf0fd732ad7815e", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753349810541, "stop": 1753349826049, "duration": 15508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "bc0d71dbeec9f327", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753349864535, "stop": 1753349875675, "duration": 11140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "9cce20a894e7e918", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753349879617, "stop": 1753349896986, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "a7d79bb018aea1d8", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753349915484, "stop": 1753349931111, "duration": 15627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "1d06a4cce5c28620", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753349954708, "stop": 1753349965810, "duration": 11102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "2a27bc9bb770e828", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350008791, "stop": 1753350019777, "duration": 10986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "32ff1aa9094a8100", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350023658, "stop": 1753350038533, "duration": 14875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "8100a37ec6e5ba5c", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350042366, "stop": 1753350052142, "duration": 9776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "8d58e3db6ef12645", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350056024, "stop": 1753350065897, "duration": 9873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "71dd4b69357ff2c2", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "failed", "time": {"start": 1753350069623, "stop": 1753350079905, "duration": 10282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "a6fbba3d6c034725", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350084098, "stop": 1753350094352, "duration": 10254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "2c45318d974babe0", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350098134, "stop": 1753350109624, "duration": 11490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "12eb50903ea12475", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "failed", "time": {"start": 1753350113511, "stop": 1753350124587, "duration": 11076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "d2fe2d61517ed5c2", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "failed", "time": {"start": 1753350128728, "stop": 1753350136981, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "8a5287e14b41a89b", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "failed", "time": {"start": 1753350141136, "stop": 1753350155622, "duration": 14486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "decd1499c4c83eb9", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350159949, "stop": 1753350170283, "duration": 10334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "ee5c24ded022a1e7", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350174077, "stop": 1753350183598, "duration": 9521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode能正常执行", "uid": "e324c3f616cfbf75", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "failed", "time": {"start": 1753350187320, "stop": 1753350198665, "duration": 11345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "64afbae6c960bf96", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350202833, "stop": 1753350212741, "duration": 9908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "f84da7e480fb17c8", "parentUid": "a1b7d4dbedf251981dff2973f2b77599", "status": "passed", "time": {"start": 1753350216613, "stop": 1753350226356, "duration": 9743}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1b7d4dbedf251981dff2973f2b77599"}, {"name": "open clock", "children": [{"name": "open clock", "uid": "664f1be505275c9b", "parentUid": "4d94bb3c201029ec2e515cd928d5aa1c", "status": "failed", "time": {"start": 1753349829956, "stop": 1753349843254, "duration": 13298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d94bb3c201029ec2e515cd928d5aa1c"}, {"name": "联系人控制命令", "children": [{"name": "测试open contact命令", "uid": "62b6a6470c04e2aa", "parentUid": "e4d92d6a5a89b66287c9ab32ec09d79a", "status": "passed", "time": {"start": 1753349847621, "stop": 1753349860616, "duration": 12995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "8d9ae3f36df760a2", "parentUid": "e4d92d6a5a89b66287c9ab32ec09d79a", "status": "failed", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e4d92d6a5a89b66287c9ab32ec09d79a"}], "uid": "6f38a4272edded31ee153844d5e32f8b"}]}