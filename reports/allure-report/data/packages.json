{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "open_app", "children": [{"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "839216c064f49cc2cc43bf3a7e25a68e", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "839216c064f49cc2cc43bf3a7e25a68e"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "f73e43036f2e212", "parentUid": "161553a24343056d1e09aedacf58fc9e", "status": "passed", "time": {"start": 1753349793794, "stop": 1753349806727, "duration": 12933}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "161553a24343056d1e09aedacf58fc9e"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "2bf0fd732ad7815e", "parentUid": "f45fa681bd9042ada6caa7d67eff2ea3", "status": "passed", "time": {"start": 1753349810541, "stop": 1753349826049, "duration": 15508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f45fa681bd9042ada6caa7d67eff2ea3"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "664f1be505275c9b", "parentUid": "a8f6a957031d846c2f15480734562056", "status": "failed", "time": {"start": 1753349829956, "stop": 1753349843254, "duration": 13298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a8f6a957031d846c2f15480734562056"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "62b6a6470c04e2aa", "parentUid": "a5fc9b529b57043e92c02952ed6adf9b", "status": "passed", "time": {"start": 1753349847621, "stop": 1753349860616, "duration": 12995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a5fc9b529b57043e92c02952ed6adf9b"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "bc0d71dbeec9f327", "parentUid": "bd8ed827ef1df55e4325c28e82147e9a", "status": "passed", "time": {"start": 1753349864535, "stop": 1753349875675, "duration": 11140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd8ed827ef1df55e4325c28e82147e9a"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "9cce20a894e7e918", "parentUid": "d0558ff1a8314b73eb0eea5a64282520", "status": "passed", "time": {"start": 1753349879617, "stop": 1753349896986, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d0558ff1a8314b73eb0eea5a64282520"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "4cf17a74f7f4abcc", "parentUid": "574ecf0baa2d37e797ec4286b3831ce8", "status": "passed", "time": {"start": 1753349900881, "stop": 1753349911555, "duration": 10674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "574ecf0baa2d37e797ec4286b3831ce8"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "a7d79bb018aea1d8", "parentUid": "d720916dc3a541772315efa2157d4c08", "status": "passed", "time": {"start": 1753349915484, "stop": 1753349931111, "duration": 15627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d720916dc3a541772315efa2157d4c08"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "133fadc0512f9d9e", "parentUid": "1e6ca10acf22ec0149ad8426a3b4b433", "status": "failed", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e6ca10acf22ec0149ad8426a3b4b433"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "1d06a4cce5c28620", "parentUid": "06a943ea7f15f16232448889fd1cde1f", "status": "passed", "time": {"start": 1753349954708, "stop": 1753349965810, "duration": 11102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06a943ea7f15f16232448889fd1cde1f"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "8d9ae3f36df760a2", "parentUid": "49220359a707bab2ead48b7bdebd6a51", "status": "failed", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49220359a707bab2ead48b7bdebd6a51"}, {"name": "test_open_wifi", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c945f938b4a38fe", "parentUid": "9adc5c3babaadb354198a6903f546856", "status": "passed", "time": {"start": 1753349990953, "stop": 1753350005012, "duration": 14059}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9adc5c3babaadb354198a6903f546856"}], "uid": "31ac50ea93bef768f953c51ba1fbe79c"}, {"name": "third_coupling", "children": [{"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "2a27bc9bb770e828", "parentUid": "809c362478b74111cd0a657c04a00a2d", "status": "passed", "time": {"start": 1753350008791, "stop": 1753350019777, "duration": 10986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "809c362478b74111cd0a657c04a00a2d"}, {"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "32ff1aa9094a8100", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1753350023658, "stop": 1753350038533, "duration": 14875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "8100a37ec6e5ba5c", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "passed", "time": {"start": 1753350042366, "stop": 1753350052142, "duration": 9776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "8d58e3db6ef12645", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "passed", "time": {"start": 1753350056024, "stop": 1753350065897, "duration": 9873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "71dd4b69357ff2c2", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1753350069623, "stop": 1753350079905, "duration": 10282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "a6fbba3d6c034725", "parentUid": "de7058af0cf3cf96a3a6e44547d2d3da", "status": "passed", "time": {"start": 1753350084098, "stop": 1753350094352, "duration": 10254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de7058af0cf3cf96a3a6e44547d2d3da"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "2c45318d974babe0", "parentUid": "c7915c7ab29f0806d615486d98c7cab4", "status": "passed", "time": {"start": 1753350098134, "stop": 1753350109624, "duration": 11490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7915c7ab29f0806d615486d98c7cab4"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "12eb50903ea12475", "parentUid": "236b7ec5c8d088425390038c61bf4150", "status": "failed", "time": {"start": 1753350113511, "stop": 1753350124587, "duration": 11076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "236b7ec5c8d088425390038c61bf4150"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "d2fe2d61517ed5c2", "parentUid": "21b62b9f9e0197c5c593e8b03d43e09f", "status": "failed", "time": {"start": 1753350128728, "stop": 1753350136981, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21b62b9f9e0197c5c593e8b03d43e09f"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "8a5287e14b41a89b", "parentUid": "25aad62b9ff3f2dd577046aa113a288b", "status": "failed", "time": {"start": 1753350141136, "stop": 1753350155622, "duration": 14486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25aad62b9ff3f2dd577046aa113a288b"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "decd1499c4c83eb9", "parentUid": "73afe98212b2fc40c9855f4a7be48daf", "status": "passed", "time": {"start": 1753350159949, "stop": 1753350170283, "duration": 10334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73afe98212b2fc40c9855f4a7be48daf"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "ee5c24ded022a1e7", "parentUid": "07bdca6e1c13ba5e6d7c3e3454137163", "status": "passed", "time": {"start": 1753350174077, "stop": 1753350183598, "duration": 9521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07bdca6e1c13ba5e6d7c3e3454137163"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode能正常执行", "uid": "e324c3f616cfbf75", "parentUid": "d7f4582c3f4d05ee5efbf8060d02c515", "status": "failed", "time": {"start": 1753350187320, "stop": 1753350198665, "duration": 11345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7f4582c3f4d05ee5efbf8060d02c515"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "64afbae6c960bf96", "parentUid": "241ed41067951f0a374a6ccf1ef3d880", "status": "passed", "time": {"start": 1753350202833, "stop": 1753350212741, "duration": 9908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "241ed41067951f0a374a6ccf1ef3d880"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "f84da7e480fb17c8", "parentUid": "fced12919209867648851208b26f748a", "status": "passed", "time": {"start": 1753350216613, "stop": 1753350226356, "duration": 9743}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fced12919209867648851208b26f748a"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}], "uid": "testcases.test_ella"}]}