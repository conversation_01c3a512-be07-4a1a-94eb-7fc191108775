{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "SHCYbucy-pc", "children": [{"name": "60304-MainThread", "children": [{"name": "测试open camera能正常执行", "uid": "2bf0fd732ad7815e", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349810541, "stop": 1753349826049, "duration": 15508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "64afbae6c960bf96", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350202833, "stop": 1753350212741, "duration": 9908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "d2fe2d61517ed5c2", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753350128728, "stop": 1753350136981, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "947160afee2f749a", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "open clock", "uid": "664f1be505275c9b", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753349829956, "stop": 1753349843254, "duration": 13298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode能正常执行", "uid": "e324c3f616cfbf75", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753350187320, "stop": 1753350198665, "duration": 11345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "2a27bc9bb770e828", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350008791, "stop": 1753350019777, "duration": 10986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "12eb50903ea12475", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753350113511, "stop": 1753350124587, "duration": 11076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "ee5c24ded022a1e7", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350174077, "stop": 1753350183598, "duration": 9521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "8d9ae3f36df760a2", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "71dd4b69357ff2c2", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753350069623, "stop": 1753350079905, "duration": 10282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "2c45318d974babe0", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350098134, "stop": 1753350109624, "duration": 11490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "8100a37ec6e5ba5c", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350042366, "stop": 1753350052142, "duration": 9776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "9cce20a894e7e918", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349879617, "stop": 1753349896986, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "4cf17a74f7f4abcc", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349900881, "stop": 1753349911555, "duration": 10674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "c945f938b4a38fe", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349990953, "stop": 1753350005012, "duration": 14059}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "f73e43036f2e212", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349793794, "stop": 1753349806727, "duration": 12933}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "133fadc0512f9d9e", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "a7d79bb018aea1d8", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349915484, "stop": 1753349931111, "duration": 15627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "bc0d71dbeec9f327", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349864535, "stop": 1753349875675, "duration": 11140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "62b6a6470c04e2aa", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349847621, "stop": 1753349860616, "duration": 12995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "a6fbba3d6c034725", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350084098, "stop": 1753350094352, "duration": 10254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "decd1499c4c83eb9", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350159949, "stop": 1753350170283, "duration": 10334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "1d06a4cce5c28620", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753349954708, "stop": 1753349965810, "duration": 11102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "32ff1aa9094a8100", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350023658, "stop": 1753350038533, "duration": 14875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "8d58e3db6ef12645", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350056024, "stop": 1753350065897, "duration": 9873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "f84da7e480fb17c8", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "passed", "time": {"start": 1753350216613, "stop": 1753350226356, "duration": 9743}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "8a5287e14b41a89b", "parentUid": "c4c1bf6f41e122c85bfb40e37592ec38", "status": "failed", "time": {"start": 1753350141136, "stop": 1753350155622, "duration": 14486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c4c1bf6f41e122c85bfb40e37592ec38"}], "uid": "5209b93f42624278d4a59a103348bd3d"}]}