"DESCRIPTION","DURATION IN MS","NAME","PARENT SUITE","START TIME","STATUS","STOP TIME","SUB SUITE","SUITE","TEST CLASS","TEST METHOD"
"disable magic voice changer","10986","测试disable magic voice changer能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:40:08 CST 2025","passed","Thu Jul 24 17:40:19 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"smart charge","10254","测试smart charge能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:41:24 CST 2025","passed","Thu Jul 24 17:41:34 CST 2025","TestEllaSmartCharge","test_smart_charge","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","14059","测试open contact命令 - 简洁版本","testcases.test_ella.open_app","Thu Jul 24 17:39:50 CST 2025","passed","Thu Jul 24 17:40:05 CST 2025","TestEllaCommandConcise","test_open_wifi","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","15469","测试open flashlight","testcases.test_ella.open_app","Thu Jul 24 17:38:55 CST 2025","failed","Thu Jul 24 17:39:10 CST 2025","TestEllaCommandConcise","test_open_flashlight","",""
"switch charging modes","11490","测试switch charging modes能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:41:38 CST 2025","passed","Thu Jul 24 17:41:49 CST 2025","TestEllaSwitchChargingModes","test_switch_charging_modes","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","17315","测试open contact命令","testcases.test_ella.open_app","Thu Jul 24 17:39:29 CST 2025","failed","Thu Jul 24 17:39:46 CST 2025","TestEllaContactCommandConcise","test_open_phone","",""
"open facebook","15627","测试open facebook能正常执行","testcases.test_ella.open_app","Thu Jul 24 17:38:35 CST 2025","passed","Thu Jul 24 17:38:51 CST 2025","TestEllaCommandConcise","test_open_facebook","",""
"switch to equilibrium mode","8253","测试switch to equilibrium mode能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:42:08 CST 2025","failed","Thu Jul 24 17:42:16 CST 2025","TestEllaSwitchToEquilibriumMode","test_switch_to_equilibrium_mode","",""
"open camera","15508","测试open camera能正常执行","testcases.test_ella.open_app","Thu Jul 24 17:36:50 CST 2025","passed","Thu Jul 24 17:37:06 CST 2025","TestEllaCommandConcise","test_open_camera","",""
"order a takeaway","10282","测试order a takeaway能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:41:09 CST 2025","failed","Thu Jul 24 17:41:19 CST 2025","TestEllaOrderATakeaway","test_order_a_takeaway","",""
"switch magic voice to Mango","11076","测试switch magic voice to Mango能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:41:53 CST 2025","failed","Thu Jul 24 17:42:04 CST 2025","TestEllaSwitchMagicVoiceToMango","test_switch_magic_voice_to_mango","",""
"switch to power saving mode","9908","测试switch to power saving mode能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:43:22 CST 2025","passed","Thu Jul 24 17:43:32 CST 2025","TestEllaSwitchToPowerSavingMode","test_switch_to_power_saving_mode","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","10006","测试open contact命令 - 简洁版本","testcases.test_ella.open_app","Thu Jul 24 17:36:19 CST 2025","passed","Thu Jul 24 17:36:29 CST 2025","TestEllaCommandConcise","test_open_app","",""
"open folax","11102","测试open folax能正常执行","testcases.test_ella.open_app","Thu Jul 24 17:39:14 CST 2025","passed","Thu Jul 24 17:39:25 CST 2025","TestEllaCommandConcise","test_open_folax","",""
"open countdown","11140","测试open countdown能正常执行","testcases.test_ella.open_app","Thu Jul 24 17:37:44 CST 2025","passed","Thu Jul 24 17:37:55 CST 2025","TestEllaCommandConcise","test_open_countdown","",""
"使用open clock命令，验证响应包含Done且实际打开clock命令","13298","open clock","testcases.test_ella.open_app","Thu Jul 24 17:37:09 CST 2025","failed","Thu Jul 24 17:37:23 CST 2025","TestEllaCommandConcise","test_open_clock","",""
"Switch to Low-Temp Charge","9521","测试Switch to Low-Temp Charge能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:42:54 CST 2025","passed","Thu Jul 24 17:43:03 CST 2025","TestEllaSwitchToLowtempCharge","test_switch_to_low_temp_charge","",""
"open dialer","17369","测试open dialer能正常执行","testcases.test_ella.open_app","Thu Jul 24 17:37:59 CST 2025","passed","Thu Jul 24 17:38:16 CST 2025","TestEllaCommandConcise","test_open_dialer","",""
"download app","14875","测试download app能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:40:23 CST 2025","passed","Thu Jul 24 17:40:38 CST 2025","TestEllaDownloadApp","test_download_app","",""
"switch to performance mode","11345","测试switch to performance mode能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:43:07 CST 2025","failed","Thu Jul 24 17:43:18 CST 2025","TestEllaSwitchToPerformanceMode","test_switch_to_performance_mode","",""
"download basketball","9776","测试download basketball能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:40:42 CST 2025","passed","Thu Jul 24 17:40:52 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"Switch to Hyper Charge","10334","测试Switch to Hyper Charge能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:42:39 CST 2025","passed","Thu Jul 24 17:42:50 CST 2025","TestEllaSwitchToHyperCharge","test_switch_to_hyper_charge","",""
"switch to smart charge","9743","测试switch to smart charge能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:43:36 CST 2025","passed","Thu Jul 24 17:43:46 CST 2025","TestEllaSwitchToSmartCharge","test_switch_to_smart_charge","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","12933","测试open contact命令 - 简洁版本","testcases.test_ella.open_app","Thu Jul 24 17:36:33 CST 2025","passed","Thu Jul 24 17:36:46 CST 2025","TestEllaCommandConcise","test_open_bluetooth","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","10674","测试open contact命令 - 简洁版本","testcases.test_ella.open_app","Thu Jul 24 17:38:20 CST 2025","passed","Thu Jul 24 17:38:31 CST 2025","TestEllaCommandConcise","test_open_ella","",""
"order a burger","9873","测试order a burger能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:40:56 CST 2025","passed","Thu Jul 24 17:41:05 CST 2025","TestEllaCommandConcise","test_order_a_burger","",""
"switch to flash notification","14486","测试switch to flash notification能正常执行","testcases.test_ella.third_coupling","Thu Jul 24 17:42:21 CST 2025","failed","Thu Jul 24 17:42:35 CST 2025","TestEllaSwitchToFlashNotification","test_switch_to_flash_notification","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","12995","测试open contact命令","testcases.test_ella.open_app","Thu Jul 24 17:37:27 CST 2025","passed","Thu Jul 24 17:37:40 CST 2025","TestEllaContactCommandConcise","test_open_contact","",""
