2025-06-16 13:53:59 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-16 13:53:59 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-16 13:53:59 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:53:59 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-16 13:54:02 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-16 13:54:02 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:141 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:146 | ✅ Ella应用包已加载
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:150 | ✅ Ella输入框已加载
2025-06-16 13:54:02 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-16 13:54:02 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-16 13:54:02 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:execute_text_command:260 | 执行文本命令: bluetooth status
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:input_text_command:178 | 输入文本命令: bluetooth status
2025-06-16 13:54:02 | INFO | pages.apps.ella.main_page:input_text_command:182 | 找到输入框(et_input)，点击激活
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:03 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-16 13:54:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:54:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:04 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: bluetooth status
2025-06-16 13:54:04 | INFO | pages.apps.ella.main_page:input_text_command:192 | ✅ 成功输入文本: bluetooth status
2025-06-16 13:54:04 | INFO | pages.apps.ella.main_page:send_command:226 | 发送命令
2025-06-16 13:54:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-16 13:54:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-16 13:54:08 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-16 13:54:08 | INFO | pages.apps.ella.main_page:send_command:234 | 使用发送按钮(fl_btn_three_btn)
2025-06-16 13:54:08 | INFO | pages.apps.ella.main_page:send_command:236 | ✅ 命令发送成功
2025-06-16 13:54:08 | INFO | pages.apps.ella.main_page:execute_text_command:270 | ✅ 文本命令执行完成
2025-06-16 13:54:08 | INFO | pages.apps.ella.main_page:wait_for_response:323 | 等待AI响应，超时时间: 10秒
2025-06-16 13:54:10 | INFO | pages.apps.ella.main_page:wait_for_response:336 | 初始文本内容数量: 14
2025-06-16 13:54:34 | WARNING | pages.apps.ella.main_page:wait_for_response:399 | 等待AI响应超时
2025-06-16 13:54:36 | INFO | pages.apps.ella.main_page:execute_text_command:260 | 执行文本命令: is bluetooth on
2025-06-16 13:54:36 | INFO | pages.apps.ella.main_page:input_text_command:178 | 输入文本命令: is bluetooth on
2025-06-16 13:54:36 | ERROR | pages.apps.ella.main_page:input_text_command:211 | 未找到任何输入框
2025-06-16 13:54:36 | WARNING | test_bluetooth_command:test_bluetooth_status_query:260 | 命令执行失败: is bluetooth on
2025-06-16 13:54:36 | INFO | pages.apps.ella.main_page:execute_text_command:260 | 执行文本命令: check bluetooth
2025-06-16 13:54:36 | INFO | pages.apps.ella.main_page:input_text_command:178 | 输入文本命令: check bluetooth
2025-06-16 13:54:36 | ERROR | pages.apps.ella.main_page:input_text_command:211 | 未找到任何输入框
2025-06-16 13:54:36 | WARNING | test_bluetooth_command:test_bluetooth_status_query:260 | 命令执行失败: check bluetooth
2025-06-16 13:54:36 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-16 13:54:36 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:54:36 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-16 13:54:37 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
