2025-07-24 17:42:17 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-07-24 17:42:17 | INFO | pages.apps.ella.dialogue_page:start_app:128 | 启动Ella应用
2025-07-24 17:42:17 | INFO | pages.apps.ella.dialogue_page:start_app:136 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:_check_app_started:194 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:start_app:141 | ✅ Ella应用启动成功（指定Activity）
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:215 | 等待Ella页面加载完成 (超时: 15秒)
2025-07-24 17:42:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-24 17:42:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:42:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:219 | ✅ 输入框已出现，页面加载完成
2025-07-24 17:42:21 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-24 17:42:21 | INFO | pages.base.app_detector:check_settings_opened:121 | 检查Settings应用状态
2025-07-24 17:42:21 | INFO | pages.base.app_detector:check_settings_opened:153 | ✅ 检测到Settings应用: com.android.settings
2025-07-24 17:42:21 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令switch to flash notification，状态: True
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:42:21 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:42:21 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:42:21 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:42:21 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:42:21 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:42:21 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:42:21 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:42:21 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: switch to flash notification
2025-07-24 17:42:21 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:42:22 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:42:22 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: switch to flash notification
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:42:22 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:42:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:42:22 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: switch to flash notification
2025-07-24 17:42:22 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-24 17:42:22 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-24 17:42:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-24 17:42:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:42:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-24 17:42:23 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-24 17:42:23 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-24 17:42:23 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-24 17:42:23 | INFO | testcases.test_ella.base_ella_test:_execute_command:371 | ✅ 成功执行命令: switch to flash notification
2025-07-24 17:42:23 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-24 17:42:24 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-24 17:42:27 | INFO | pages.base.app_detector:check_settings_opened:121 | 检查Settings应用状态
2025-07-24 17:42:27 | INFO | pages.base.app_detector:check_settings_opened:153 | ✅ 检测到Settings应用: com.android.settings
2025-07-24 17:42:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-24 17:42:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-24 17:42:27 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:42:27 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:42:28 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:42:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:42:28 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:42:28 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-24 17:42:30 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | asr_txt节点不存在，已达到最大重试次数
2025-07-24 17:42:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | robot_text节点不存在，已达到最大重试次数
2025-07-24 17:42:32 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-24 17:42:33 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-24 17:42:33 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-24 17:42:33 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:539 | 从TextView元素获取响应
2025-07-24 17:42:34 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:555 | 查找RecyclerView中的最新消息
2025-07-24 17:42:35 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:387 | 从dump正则提取文本: 5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist app to access the screen contents as text Use screenshot Allow the assist app to access an image of the screen Flash screen Flash edges of screen when assist app accesses text from screen or screenshot Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.
2025-07-24 17:42:35 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: 5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist app to access the screen contents as text Use screenshot Allow the assist app to access an image of the screen Flash screen Flash edges of screen when assist app accesses text from screen or screenshot Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.
2025-07-24 17:42:35 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-24 17:42:35 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['', '', '', '', '5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist app to access the screen contents as text Use screenshot Allow the assist app to access an image of the screen Flash screen Flash edges of screen when assist app accesses text from screen or screenshot Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']'
2025-07-24 17:42:35 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:471 | ✅ 状态验证通过: True -> True
2025-07-24 17:42:35 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSwitchToFlashNotification\test_completed.png
2025-07-24 17:42:35 | INFO | testcases.test_ella.base_ella_test:simple_command_test:696 | 🎉 switch to flash notification 测试完成
2025-07-24 17:42:35 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:508 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', '5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist app to access the screen contents as text Use screenshot Allow the assist app to access an image of the screen Flash screen Flash edges of screen when assist app accesses text from screen or screenshot Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']
2025-07-24 17:42:35 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:555 | ⚠️ 响应未包含期望内容: 'Done'
2025-07-24 17:42:35 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:562 | ❌ 部分期望内容未找到 (0/1)
2025-07-24 17:42:35 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:563 | 缺失内容: ['Done']
2025-07-24 17:42:35 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:564 | 搜索文本: '5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist app to access the screen contents as text Use screenshot Allow the assist app to access an image of the screen Flash screen Flash edges of screen when assist app accesses text from screen or screenshot Assist apps can help you based on information from the screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.'
2025-07-24 17:42:36 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSwitchToFlashNotification\failure_test_switch_to_flash_notification_20250724_174235.png
2025-07-24 17:42:36 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaSwitchToFlashNotification\failure_test_switch_to_flash_notification_20250724_174235.png
2025-07-24 17:42:36 | INFO | pages.apps.ella.dialogue_page:stop_app:235 | 停止Ella应用
2025-07-24 17:42:36 | WARNING | pages.apps.ella.dialogue_page:stop_app:241 | ⚠️ 停止Ella应用可能失败
