2025-06-16 13:54:56 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-16 13:54:56 | INFO | pages.apps.ella.main_page:start_app_with_activity:103 | 启动Ella应用（指定Activity）
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:54:59 | INFO | pages.apps.ella.main_page:start_app_with_activity:117 | ✅ Ella应用启动成功
2025-06-16 13:54:59 | INFO | test_open_clock_command:ella_app:36 | Ella应用启动成功
2025-06-16 13:54:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:141 | 等待Ella页面加载完成，超时时间: 10秒
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:54:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:146 | ✅ Ella应用包已加载
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 10秒
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:150 | ✅ Ella输入框已加载
2025-06-16 13:54:59 | INFO | test_open_clock_command:ella_app:40 | Ella页面加载完成
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:55:00 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-16 13:55:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:55:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:55:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:55:01 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open clock
2025-06-16 13:55:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-16 13:55:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:55:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-16 13:55:04 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-16 13:55:10 | INFO | test_open_clock_command:test_clock_app_package_assertion:254 | 当前应用包名: com.transsion.deskclock
2025-06-16 13:55:10 | INFO | test_open_clock_command:test_clock_app_package_assertion:255 | 期望包名: com.transsion.deskclock
2025-06-16 13:55:10 | INFO | test_open_clock_command:test_clock_app_package_assertion:261 | ✅ 时钟应用包名验证通过: com.transsion.deskclock
2025-06-16 13:55:10 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:55:10 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-16 13:55:10 | INFO | test_open_clock_command:ella_app:51 | Ella应用已停止
2025-06-16 13:55:10 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-16 13:55:10 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-16 13:55:10 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
