2025-06-23 19:46:01 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 19:46:01 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 19:46:01 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 19:46:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 19:46:02 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 19:46:02 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-23 19:46:02 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:46:02 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-23 19:46:04 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-23 19:46:04 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-23 19:46:04 | INFO | pages.apps.ella.main_page:wait_for_page_load:138 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 19:46:04 | INFO | pages.apps.ella.main_page:wait_for_page_load:143 | ✅ Ella应用包已加载
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:04 | INFO | pages.apps.ella.main_page:wait_for_page_load:147 | ✅ Ella输入框已加载
2025-06-23 19:46:04 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-23 19:46:05 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:05 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:05 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-06-23 19:46:05 | INFO | pages.apps.ella.main_page:check_bluetooth_status:517 | 检查蓝牙状态
2025-06-23 19:46:06 | INFO | pages.apps.ella.main_page:check_bluetooth_status:531 | 蓝牙状态: 关闭 (值: 0)
2025-06-23 19:46:06 | INFO | test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 关闭
2025-06-23 19:46:06 | INFO | pages.apps.ella.main_page:execute_text_command:254 | 执行文本命令: open bluetooth
2025-06-23 19:46:06 | INFO | pages.apps.ella.main_page:input_text_command:175 | 输入文本命令: open bluetooth
2025-06-23 19:46:06 | INFO | pages.apps.ella.main_page:input_text_command:179 | 找到输入框(et_input)，点击激活
2025-06-23 19:46:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:07 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 3秒
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:07 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 19:46:07 | INFO | pages.apps.ella.main_page:input_text_command:188 | ✅ 成功输入文本: open bluetooth
2025-06-23 19:46:07 | INFO | pages.apps.ella.main_page:send_command:223 | 发送命令
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:08 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 19:46:08 | INFO | pages.apps.ella.main_page:send_command:228 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 19:46:08 | INFO | pages.apps.ella.main_page:send_command:230 | ✅ 命令发送成功
2025-06-23 19:46:08 | INFO | pages.apps.ella.main_page:execute_text_command:264 | ✅ 文本命令执行完成
2025-06-23 19:46:08 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-06-23 19:46:08 | INFO | test_bluetooth_command:test_open_bluetooth_command:101 | ✅ 成功执行命令: open bluetooth
2025-06-23 19:46:08 | INFO | pages.apps.ella.main_page:wait_for_response:314 | 等待AI响应，超时时间: 15秒
2025-06-23 19:46:10 | INFO | pages.apps.ella.main_page:wait_for_response:327 | 初始文本内容数量: 13
2025-06-23 19:46:12 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:14 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:16 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:19 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:21 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:23 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:25 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['换一换', 'AI 壁纸']
2025-06-23 19:46:26 | WARNING | pages.apps.ella.main_page:wait_for_response:391 | 等待AI响应超时
2025-06-23 19:46:26 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250623_194626.png
2025-06-23 19:46:26 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250623_194626.png
2025-06-23 19:46:26 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-23 19:46:26 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:46:26 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 19:46:26 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
