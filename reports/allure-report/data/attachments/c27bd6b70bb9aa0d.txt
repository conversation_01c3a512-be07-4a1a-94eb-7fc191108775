2025-06-16 13:54:37 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-16 13:54:37 | INFO | pages.apps.ella.main_page:start_app_with_activity:103 | 启动Ella应用（指定Activity）
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:54:40 | INFO | pages.apps.ella.main_page:start_app_with_activity:117 | ✅ Ella应用启动成功
2025-06-16 13:54:40 | INFO | test_open_clock_command:ella_app:36 | Ella应用启动成功
2025-06-16 13:54:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:141 | 等待Ella页面加载完成，超时时间: 10秒
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:54:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:146 | ✅ Ella应用包已加载
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 10秒
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:150 | ✅ Ella输入框已加载
2025-06-16 13:54:40 | INFO | test_open_clock_command:ella_app:40 | Ella页面加载完成
2025-06-16 13:54:40 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaOpenClockCommand\ella_clock_initial_state.png
2025-06-16 13:54:41 | INFO | test_open_clock_command:test_open_clock_command:114 | 时钟应用初始状态: 未运行
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:41 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-16 13:54:41 | INFO | test_open_clock_command:test_open_clock_command:126 | ✅ 输入框点击成功
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:54:42 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open clock
2025-06-16 13:54:45 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaOpenClockCommand\ella_clock_command_input.png
2025-06-16 13:54:45 | INFO | test_open_clock_command:test_open_clock_command:140 | ✅ 成功输入命令: open clock
2025-06-16 13:54:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-16 13:54:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:54:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-16 13:54:46 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-16 13:54:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaOpenClockCommand\ella_clock_command_sent.png
2025-06-16 13:54:50 | INFO | test_open_clock_command:test_open_clock_command:155 | ✅ 命令发送成功
2025-06-16 13:54:55 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaOpenClockCommand\ella_clock_wait_complete.png
2025-06-16 13:54:55 | INFO | test_open_clock_command:test_open_clock_command:169 | 时钟应用最终状态: 运行中
2025-06-16 13:54:55 | INFO | test_open_clock_command:test_open_clock_command:173 | UI验证状态: 通过
2025-06-16 13:54:55 | INFO | test_open_clock_command:test_open_clock_command:187 | ✅ 时钟应用已成功打开: com.transsion.deskclock
2025-06-16 13:54:55 | INFO | test_open_clock_command:test_open_clock_command:195 | ✅ 输入命令得到正确响应（时钟应用已打开）
2025-06-16 13:54:56 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaOpenClockCommand\ella_clock_test_completed.png
2025-06-16 13:54:56 | INFO | test_open_clock_command:test_open_clock_command:228 | 🎉 open clock命令测试完成
2025-06-16 13:54:56 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:54:56 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-16 13:54:56 | INFO | test_open_clock_command:ella_app:51 | Ella应用已停止
