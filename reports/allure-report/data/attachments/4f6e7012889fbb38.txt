2025-06-16 13:52:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-16 13:52:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-16 13:52:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-16 13:52:54 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-16 13:52:54 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-16 13:52:54 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-16 13:52:54 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:52:54 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-16 13:52:57 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-16 13:52:57 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-16 13:52:57 | INFO | pages.apps.ella.main_page:wait_for_page_load:141 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-16 13:52:57 | INFO | pages.apps.ella.main_page:wait_for_page_load:146 | ✅ Ella应用包已加载
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:52:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:52:57 | INFO | pages.apps.ella.main_page:wait_for_page_load:150 | ✅ Ella输入框已加载
2025-06-16 13:52:57 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-16 13:52:57 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-16 13:52:57 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-16 13:52:57 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-06-16 13:52:58 | INFO | pages.apps.ella.main_page:check_bluetooth_status:525 | 检查蓝牙状态
2025-06-16 13:52:58 | INFO | pages.apps.ella.main_page:check_bluetooth_status:539 | 蓝牙状态: 关闭 (值: 0)
2025-06-16 13:52:58 | INFO | test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 关闭
2025-06-16 13:52:58 | INFO | pages.apps.ella.main_page:execute_text_command:260 | 执行文本命令: open bluetooth
2025-06-16 13:52:58 | INFO | pages.apps.ella.main_page:input_text_command:178 | 输入文本命令: open bluetooth
2025-06-16 13:52:58 | INFO | pages.apps.ella.main_page:input_text_command:182 | 找到输入框(et_input)，点击激活
2025-06-16 13:52:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:52:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:52:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:52:58 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-16 13:52:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-16 13:53:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:53:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-16 13:53:00 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-16 13:53:00 | INFO | pages.apps.ella.main_page:input_text_command:192 | ✅ 成功输入文本: open bluetooth
2025-06-16 13:53:00 | INFO | pages.apps.ella.main_page:send_command:226 | 发送命令
2025-06-16 13:53:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-16 13:53:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-16 13:53:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-16 13:53:04 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-16 13:53:04 | INFO | pages.apps.ella.main_page:send_command:234 | 使用发送按钮(fl_btn_three_btn)
2025-06-16 13:53:04 | INFO | pages.apps.ella.main_page:send_command:236 | ✅ 命令发送成功
2025-06-16 13:53:04 | INFO | pages.apps.ella.main_page:execute_text_command:270 | ✅ 文本命令执行完成
2025-06-16 13:53:04 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-06-16 13:53:04 | INFO | test_bluetooth_command:test_open_bluetooth_command:101 | ✅ 成功执行命令: open bluetooth
2025-06-16 13:53:04 | INFO | pages.apps.ella.main_page:wait_for_response:323 | 等待AI响应，超时时间: 15秒
2025-06-16 13:53:05 | INFO | pages.apps.ella.main_page:wait_for_response:336 | 初始文本内容数量: 11
2025-06-16 13:53:18 | INFO | pages.apps.ella.main_page:wait_for_response:355 | 检测到新文本: ['设置', 'DeepSeek-R1', '有问题尽管问我…']
2025-06-16 13:53:18 | INFO | pages.apps.ella.main_page:wait_for_response:385 | 检测到AI响应: 有问题尽管问我…...
2025-06-16 13:53:23 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_response_received.png
2025-06-16 13:53:23 | INFO | test_bluetooth_command:test_open_bluetooth_command:116 | ✅ 收到AI响应
2025-06-16 13:53:23 | INFO | pages.apps.ella.main_page:get_response_text:414 | 获取AI响应文本
2025-06-16 13:53:24 | INFO | pages.apps.ella.main_page:get_response_text:429 | 页面上所有文本元素: ['对话', '发现', '下滑查看历史对话', '换一换', '榴莲成熟为什么会自然坠落？', '许嵩镇江演唱会圆满落幕', '神舟二十号乘组状态良好', 'open bluetooth', '蓝牙 已打开', '蓝牙', '设置', 'DeepSeek-R1', '有问题尽管问我…']
2025-06-16 13:53:24 | INFO | pages.apps.ella.main_page:get_response_text:464 | 找到蓝牙状态响应: 蓝牙 已打开
2025-06-16 13:53:24 | INFO | test_bluetooth_command:test_open_bluetooth_command:123 | AI响应内容: 蓝牙 已打开
2025-06-16 13:53:24 | INFO | pages.apps.ella.main_page:verify_command_in_response:561 | 验证响应是否包含命令: open bluetooth
2025-06-16 13:53:24 | INFO | pages.apps.ella.main_page:verify_command_in_response:591 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-16 13:53:24 | INFO | test_bluetooth_command:test_open_bluetooth_command:131 | ✅ 响应包含命令相关内容: open bluetooth
2025-06-16 13:53:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:525 | 检查蓝牙状态
2025-06-16 13:53:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:539 | 蓝牙状态: 开启 (值: 1)
2025-06-16 13:53:26 | INFO | test_bluetooth_command:test_open_bluetooth_command:139 | 蓝牙最终状态: 开启
2025-06-16 13:53:26 | INFO | test_bluetooth_command:test_open_bluetooth_command:150 | ✅ 蓝牙已成功开启
2025-06-16 13:53:26 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_test_completed.png
2025-06-16 13:53:26 | INFO | test_bluetooth_command:test_open_bluetooth_command:170 | 🎉 open bluetooth命令测试完成
2025-06-16 13:53:26 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-16 13:53:26 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-16 13:53:26 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-16 13:53:26 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
