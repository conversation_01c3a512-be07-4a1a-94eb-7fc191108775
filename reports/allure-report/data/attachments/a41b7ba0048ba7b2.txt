2025-06-23 19:46:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 19:46:26 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-23 19:46:26 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:46:26 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-23 19:46:29 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-23 19:46:29 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-23 19:46:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:138 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 19:46:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:143 | ✅ Ella应用包已加载
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:147 | ✅ Ella输入框已加载
2025-06-23 19:46:29 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-23 19:46:29 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:29 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:30 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_initial_state.png
2025-06-23 19:46:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:517 | 检查蓝牙状态
2025-06-23 19:46:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:531 | 蓝牙状态: 开启 (值: 1)
2025-06-23 19:46:30 | INFO | test_bluetooth_command:test_close_bluetooth_command:188 | 蓝牙初始状态: 开启
2025-06-23 19:46:30 | INFO | pages.apps.ella.main_page:execute_text_command:254 | 执行文本命令: close bluetooth
2025-06-23 19:46:30 | INFO | pages.apps.ella.main_page:input_text_command:175 | 输入文本命令: close bluetooth
2025-06-23 19:46:30 | INFO | pages.apps.ella.main_page:input_text_command:179 | 找到输入框(et_input)，点击激活
2025-06-23 19:46:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:30 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 19:46:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 3秒
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:31 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: close bluetooth
2025-06-23 19:46:31 | INFO | pages.apps.ella.main_page:input_text_command:188 | ✅ 成功输入文本: close bluetooth
2025-06-23 19:46:31 | INFO | pages.apps.ella.main_page:send_command:223 | 发送命令
2025-06-23 19:46:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:32 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 19:46:32 | INFO | pages.apps.ella.main_page:send_command:228 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 19:46:32 | INFO | pages.apps.ella.main_page:send_command:230 | ✅ 命令发送成功
2025-06-23 19:46:32 | INFO | pages.apps.ella.main_page:execute_text_command:264 | ✅ 文本命令执行完成
2025-06-23 19:46:32 | INFO | test_bluetooth_command:test_close_bluetooth_command:195 | ✅ 成功执行命令: close bluetooth
2025-06-23 19:46:32 | INFO | pages.apps.ella.main_page:wait_for_response:314 | 等待AI响应，超时时间: 15秒
2025-06-23 19:46:34 | INFO | pages.apps.ella.main_page:wait_for_response:327 | 初始文本内容数量: 9
2025-06-23 19:46:36 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['贾玲新片《转念花开》聚焦反传销', '画一个可爱的猫咪', '蓝牙', '设置', 'DeepSeek-R1', '有问题尽管问我…']
2025-06-23 19:46:36 | INFO | pages.apps.ella.main_page:wait_for_response:368 | 检测到蓝牙相关AI响应: 蓝牙
2025-06-23 19:46:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [TTS播放按钮], 超时时间: 3秒
2025-06-23 19:46:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [TTS播放按钮]
2025-06-23 19:46:39 | INFO | test_bluetooth_command:test_close_bluetooth_command:205 | ✅ 收到AI响应
2025-06-23 19:46:39 | INFO | pages.apps.ella.main_page:get_response_text:406 | 获取AI响应文本
2025-06-23 19:46:41 | INFO | pages.apps.ella.main_page:get_response_text:421 | 页面上所有文本元素: ['对话', '发现', '我可以为你答疑解惑，总结摘要，提供创作灵感。', '换一换', '贾玲新片《转念花开》聚焦反传销', '画一个可爱的猫咪', '周继红出任国家跳水队总教练', 'close bluetooth', '蓝牙 已关闭', '蓝牙', '设置', 'DeepSeek-R1', '有问题尽管问我…']
2025-06-23 19:46:41 | INFO | pages.apps.ella.main_page:get_response_text:467 | 找到蓝牙相关响应候选: close bluetooth
2025-06-23 19:46:41 | INFO | pages.apps.ella.main_page:get_response_text:456 | 找到蓝牙状态响应: 蓝牙 已关闭
2025-06-23 19:46:41 | INFO | test_bluetooth_command:test_close_bluetooth_command:212 | AI响应内容: 蓝牙 已关闭
2025-06-23 19:46:41 | INFO | pages.apps.ella.main_page:verify_command_in_response:553 | 验证响应是否包含命令: close bluetooth
2025-06-23 19:46:41 | INFO | pages.apps.ella.main_page:verify_command_in_response:583 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已关闭', '已关闭', '关闭']
2025-06-23 19:46:41 | INFO | test_bluetooth_command:test_close_bluetooth_command:220 | ✅ 响应包含命令相关内容: close bluetooth
2025-06-23 19:46:43 | INFO | pages.apps.ella.main_page:check_bluetooth_status:517 | 检查蓝牙状态
2025-06-23 19:46:43 | INFO | pages.apps.ella.main_page:check_bluetooth_status:531 | 蓝牙状态: 关闭 (值: 0)
2025-06-23 19:46:43 | INFO | test_bluetooth_command:test_close_bluetooth_command:228 | 蓝牙最终状态: 关闭
2025-06-23 19:46:43 | INFO | test_bluetooth_command:test_close_bluetooth_command:233 | ✅ 蓝牙已成功关闭
2025-06-23 19:46:43 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_test_completed.png
2025-06-23 19:46:43 | INFO | test_bluetooth_command:test_close_bluetooth_command:241 | 🎉 close bluetooth命令测试完成
2025-06-23 19:46:43 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-23 19:46:43 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:46:43 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 19:46:44 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
