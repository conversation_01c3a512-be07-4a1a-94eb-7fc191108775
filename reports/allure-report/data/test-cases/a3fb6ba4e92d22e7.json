{"uid": "a3fb6ba4e92d22e7", "name": "测试蓝牙状态查询命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_bluetooth_status_query", "historyId": "51d696b831178c19578ea68b891a77c8", "time": {"start": 1750053242481, "stop": 1750053276947, "duration": 34466}, "description": "通过Ella查询蓝牙状态", "descriptionHtml": "<p>通过Ella查询蓝牙状态</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750053174219, "stop": 1750053174631, "duration": 412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750053239495, "stop": 1750053242480, "duration": 2985}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750053242480, "stop": 1750053242480, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella查询蓝牙状态", "status": "passed", "steps": [{"name": "测试命令: bluetooth status", "time": {"start": 1750053242481, "stop": 1750053276416, "duration": 33935}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "测试命令: is bluetooth on", "time": {"start": 1750053276416, "stop": 1750053276722, "duration": 306}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "测试命令: check bluetooth", "time": {"start": 1750053276722, "stop": 1750053276947, "duration": 225}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "fa8e4bfbba771111", "name": "stdout", "source": "fa8e4bfbba771111.txt", "type": "text/plain", "size": 5764}], "parameters": [], "attachmentStep": false, "stepsCount": 3, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750053276948, "stop": 1750053276948, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750053276949, "stop": 1750053277097, "duration": 148}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750053310295, "stop": 1750053310295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "minor"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "19212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "minor", "retries": [], "categories": [], "tags": ["regression"]}, "source": "a3fb6ba4e92d22e7.json", "parameterValues": []}