{"uid": "8100a37ec6e5ba5c", "name": "测试download basketball能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "historyId": "6f2c4144233271771cdd01a5c48ea3ca", "time": {"start": 1753350042366, "stop": 1753350052142, "duration": 9776}, "description": "download basketball", "descriptionHtml": "<p>download basketball</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350038695, "stop": 1753350042365, "duration": 3670}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350042365, "stop": 1753350042365, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "download basketball", "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1753350042366, "stop": 1753350051777, "duration": 9411}, "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1753350042366, "stop": 1753350051454, "duration": 9088}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350051454, "stop": 1753350051777, "duration": 323}, "status": "passed", "steps": [], "attachments": [{"uid": "8a837d7e5157f0ac", "name": "测试总结", "source": "8a837d7e5157f0ac.txt", "type": "text/plain", "size": 229}, {"uid": "6b48d3920cd04f26", "name": "test_completed", "source": "6b48d3920cd04f26.png", "type": "image/png", "size": 609443}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350051777, "stop": 1753350051780, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350051780, "stop": 1753350052142, "duration": 362}, "status": "passed", "steps": [], "attachments": [{"uid": "2b4f6417922f5ce8", "name": "测试总结", "source": "2b4f6417922f5ce8.txt", "type": "text/plain", "size": 229}, {"uid": "b4c54650e4b1f9e4", "name": "test_completed", "source": "b4c54650e4b1f9e4.png", "type": "image/png", "size": 609443}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "19417c4b977adcd4", "name": "stdout", "source": "19417c4b977adcd4.txt", "type": "text/plain", "size": 9282}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350052144, "stop": 1753350052144, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350052146, "stop": 1753350052277, "duration": 131}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_basketball"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8100a37ec6e5ba5c.json", "parameterValues": []}