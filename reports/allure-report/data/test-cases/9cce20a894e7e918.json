{"uid": "9cce20a894e7e918", "name": "测试open dialer能正常执行", "fullName": "testcases.test_ella.open_app.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "historyId": "fe0f276e426d9c85ddc61723d4e516b7", "time": {"start": 1753349879617, "stop": 1753349896986, "duration": 17369}, "description": "open dialer", "descriptionHtml": "<p>open dialer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349875889, "stop": 1753349879615, "duration": 3726}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349879616, "stop": 1753349879616, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1753349879617, "stop": 1753349896679, "duration": 17062}, "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1753349879617, "stop": 1753349896367, "duration": 16750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349896367, "stop": 1753349896678, "duration": 311}, "status": "passed", "steps": [], "attachments": [{"uid": "d0b5fecc0b3552fb", "name": "测试总结", "source": "d0b5fecc0b3552fb.txt", "type": "text/plain", "size": 151}, {"uid": "4cbe2529bae94c07", "name": "test_completed", "source": "4cbe2529bae94c07.png", "type": "image/png", "size": 478927}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753349896679, "stop": 1753349896682, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349896682, "stop": 1753349896985, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "df74c7120568d4f5", "name": "测试总结", "source": "df74c7120568d4f5.txt", "type": "text/plain", "size": 151}, {"uid": "60323b0a47586701", "name": "test_completed", "source": "60323b0a47586701.png", "type": "image/png", "size": 479192}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "40fe38bd826d325b", "name": "stdout", "source": "40fe38bd826d325b.txt", "type": "text/plain", "size": 12403}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349896988, "stop": 1753349896988, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349896990, "stop": 1753349897127, "duration": 137}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_dialer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9cce20a894e7e918.json", "parameterValues": []}