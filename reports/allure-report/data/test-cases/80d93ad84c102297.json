{"uid": "80d93ad84c102297", "name": "测试open bluetooth命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_open_bluetooth_command", "historyId": "e25c945dfd6c1c874b0927b961b40416", "time": {"start": 1750679165655, "stop": 1750679186246, "duration": 20591}, "description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "descriptionHtml": "<p>通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态</p>\n", "status": "failed", "statusMessage": "AssertionError: 未收到AI响应\nassert False", "statusTrace": "self = <test_bluetooth_command.TestEllaBluetoothCommand object at 0x0000018BB8F1F250>, ella_app = <pages.apps.ella.main_page.EllaMainPage object at 0x0000018BB930E510>\n\n    @allure.title(\"测试open bluetooth命令\")\n    @allure.description(\"通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_bluetooth_command(self, ella_app):\n        \"\"\"测试open bluetooth命令\"\"\"\n        command = \"open bluetooth\"\n    \n        with allure.step(\"记录测试开始状态\"):\n            # 截图记录初始状态\n            screenshot_path = ella_app.screenshot(\"ella_initial_state.png\")\n            allure.attach.file(screenshot_path, name=\"Ella初始状态\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            # 记录蓝牙初始状态\n            initial_bluetooth_status = ella_app.check_bluetooth_status()\n            log.info(f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\")\n            allure.attach(\n                f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\",\n                name=\"蓝牙初始状态\",\n                attachment_type=allure.attachment_type.TEXT\n            )\n    \n        with allure.step(f\"输入命令: {command}\"):\n            # 执行文本命令\n            success = ella_app.execute_text_command(command)\n            assert success, f\"执行命令失败: {command}\"\n    \n            # 截图记录命令输入后的状态\n            screenshot_path = ella_app.screenshot(\"ella_command_sent.png\")\n            allure.attach.file(screenshot_path, name=\"命令发送后\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            log.info(f\"✅ 成功执行命令: {command}\")\n    \n        with allure.step(\"等待AI响应\"):\n            # 等待AI响应\n            response_received = ella_app.wait_for_response(timeout=15)\n>           assert response_received, \"未收到AI响应\"\nE           AssertionError: 未收到AI响应\nE           assert False\n\ntestcases\\test_ella\\test_bluetooth_command.py:106: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750679161863, "stop": 1750679162228, "duration": 365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750679162229, "stop": 1750679165653, "duration": 3424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750679165653, "stop": 1750679165653, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "status": "failed", "statusMessage": "AssertionError: 未收到AI响应\nassert False", "statusTrace": "self = <test_bluetooth_command.TestEllaBluetoothCommand object at 0x0000018BB8F1F250>, ella_app = <pages.apps.ella.main_page.EllaMainPage object at 0x0000018BB930E510>\n\n    @allure.title(\"测试open bluetooth命令\")\n    @allure.description(\"通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_bluetooth_command(self, ella_app):\n        \"\"\"测试open bluetooth命令\"\"\"\n        command = \"open bluetooth\"\n    \n        with allure.step(\"记录测试开始状态\"):\n            # 截图记录初始状态\n            screenshot_path = ella_app.screenshot(\"ella_initial_state.png\")\n            allure.attach.file(screenshot_path, name=\"Ella初始状态\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            # 记录蓝牙初始状态\n            initial_bluetooth_status = ella_app.check_bluetooth_status()\n            log.info(f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\")\n            allure.attach(\n                f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\",\n                name=\"蓝牙初始状态\",\n                attachment_type=allure.attachment_type.TEXT\n            )\n    \n        with allure.step(f\"输入命令: {command}\"):\n            # 执行文本命令\n            success = ella_app.execute_text_command(command)\n            assert success, f\"执行命令失败: {command}\"\n    \n            # 截图记录命令输入后的状态\n            screenshot_path = ella_app.screenshot(\"ella_command_sent.png\")\n            allure.attach.file(screenshot_path, name=\"命令发送后\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            log.info(f\"✅ 成功执行命令: {command}\")\n    \n        with allure.step(\"等待AI响应\"):\n            # 等待AI响应\n            response_received = ella_app.wait_for_response(timeout=15)\n>           assert response_received, \"未收到AI响应\"\nE           AssertionError: 未收到AI响应\nE           assert False\n\ntestcases\\test_ella\\test_bluetooth_command.py:106: AssertionError", "steps": [{"name": "记录测试开始状态", "time": {"start": 1750679165655, "stop": 1750679166183, "duration": 528}, "status": "passed", "steps": [], "attachments": [{"uid": "4d7b01ef5bd3a2fc", "name": "Ella初始状态", "source": "4d7b01ef5bd3a2fc.png", "type": "image/png", "size": 419124}, {"uid": "35c373e3701ddd7a", "name": "蓝牙初始状态", "source": "35c373e3701ddd7a.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 2, "shouldDisplayMessage": false}, {"name": "输入命令: open bluetooth", "time": {"start": 1750679166183, "stop": 1750679168720, "duration": 2537}, "status": "passed", "steps": [], "attachments": [{"uid": "6eb296fafd5b19a6", "name": "命令发送后", "source": "6eb296fafd5b19a6.png", "type": "image/png", "size": 604663}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "等待AI响应", "time": {"start": 1750679168720, "stop": 1750679186239, "duration": 17519}, "status": "failed", "statusMessage": "AssertionError: 未收到AI响应\nassert False\n", "statusTrace": "  File \"D:\\PythonProject\\app_test\\testcases\\test_ella\\test_bluetooth_command.py\", line 106, in test_open_bluetooth_command\n    assert response_received, \"未收到AI响应\"\n", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": true}], "attachments": [{"uid": "2058e2bde9dee3da", "name": "stdout", "source": "2058e2bde9dee3da.txt", "type": "text/plain", "size": 8128}], "parameters": [], "stepsCount": 3, "hasContent": true, "attachmentStep": false, "attachmentsCount": 4, "shouldDisplayMessage": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750679186267, "stop": 1750679186610, "duration": 343}, "status": "passed", "steps": [], "attachments": [{"uid": "f7597e416be73710", "name": "失败截图-TestEllaBluetoothCommand", "source": "f7597e416be73710.png", "type": "image/png", "size": 428866}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750679186612, "stop": 1750679186774, "duration": 162}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750679305631, "stop": 1750679305631, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "80d93ad84c102297.json", "parameterValues": []}