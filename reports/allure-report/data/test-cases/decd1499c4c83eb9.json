{"uid": "decd1499c4c83eb9", "name": "测试Switch to Hyper Charge能正常执行", "fullName": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge.TestEllaSwitchToHyperCharge#test_switch_to_hyper_charge", "historyId": "cc16e6af6c17da447c32eeb71c66d71c", "time": {"start": 1753350159949, "stop": 1753350170283, "duration": 10334}, "description": "Switch to Hyper Charge", "descriptionHtml": "<p>Switch to Hyper Charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350156149, "stop": 1753350159947, "duration": 3798}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350159947, "stop": 1753350159947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Switch to Hyper Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Hyper Charge", "time": {"start": 1753350159949, "stop": 1753350169919, "duration": 9970}, "status": "passed", "steps": [{"name": "执行命令: Switch to Hyper Charge", "time": {"start": 1753350159949, "stop": 1753350169540, "duration": 9591}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350169540, "stop": 1753350169918, "duration": 378}, "status": "passed", "steps": [], "attachments": [{"uid": "f3775a6499d97268", "name": "测试总结", "source": "f3775a6499d97268.txt", "type": "text/plain", "size": 223}, {"uid": "f55cb11f2586b82", "name": "test_completed", "source": "f55cb11f2586b82.png", "type": "image/png", "size": 612484}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350169919, "stop": 1753350169925, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350169925, "stop": 1753350170282, "duration": 357}, "status": "passed", "steps": [], "attachments": [{"uid": "db374af77e1492ff", "name": "测试总结", "source": "db374af77e1492ff.txt", "type": "text/plain", "size": 223}, {"uid": "43c0fd251bfb3f68", "name": "test_completed", "source": "43c0fd251bfb3f68.png", "type": "image/png", "size": 612484}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5b6a9065addf6791", "name": "stdout", "source": "5b6a9065addf6791.txt", "type": "text/plain", "size": 9265}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350170285, "stop": 1753350170285, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350170287, "stop": 1753350170441, "duration": 154}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_hyper_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToHyperCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "decd1499c4c83eb9.json", "parameterValues": []}