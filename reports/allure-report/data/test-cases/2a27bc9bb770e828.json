{"uid": "2a27bc9bb770e828", "name": "测试disable magic voice changer能正常执行", "fullName": "testcases.test_ella.third_coupling.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "8dd76fb2f1440324f5c0805128da5079", "time": {"start": 1753350008791, "stop": 1753350019777, "duration": 10986}, "description": "disable magic voice changer", "descriptionHtml": "<p>disable magic voice changer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350005161, "stop": 1753350008791, "duration": 3630}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350008791, "stop": 1753350008791, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "disable magic voice changer", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753350008792, "stop": 1753350019432, "duration": 10640}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753350008792, "stop": 1753350019047, "duration": 10255}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350019047, "stop": 1753350019431, "duration": 384}, "status": "passed", "steps": [], "attachments": [{"uid": "cc2efd292da39ad3", "name": "测试总结", "source": "cc2efd292da39ad3.txt", "type": "text/plain", "size": 536}, {"uid": "6e2973ed37604c96", "name": "test_completed", "source": "6e2973ed37604c96.png", "type": "image/png", "size": 636056}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350019432, "stop": 1753350019437, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350019437, "stop": 1753350019776, "duration": 339}, "status": "passed", "steps": [], "attachments": [{"uid": "311368e03b221a88", "name": "测试总结", "source": "311368e03b221a88.txt", "type": "text/plain", "size": 536}, {"uid": "24502612e093f5ca", "name": "test_completed", "source": "24502612e093f5ca.png", "type": "image/png", "size": 636056}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f8566b00fe63c29a", "name": "stdout", "source": "f8566b00fe63c29a.txt", "type": "text/plain", "size": 11376}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350019778, "stop": 1753350019778, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350019781, "stop": 1753350019901, "duration": 120}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2a27bc9bb770e828.json", "parameterValues": []}