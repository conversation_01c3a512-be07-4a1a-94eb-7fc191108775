{"uid": "7c5d28837baf22b2", "name": "测试open clock命令", "fullName": "testcases.test_ella.test_open_clock_command.TestEllaOpenClockCommand#test_open_clock_command", "historyId": "223502082c9f702187e2124a0ad4ff5f", "time": {"start": 1750053280780, "stop": 1750053296072, "duration": 15292}, "description": "通过Ella输入'open clock'命令，验证时钟应用是否打开", "descriptionHtml": "<p>通过Ella输入'open clock'命令，验证时钟应用是否打开</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750053174219, "stop": 1750053174631, "duration": 412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750053277100, "stop": 1750053280778, "duration": 3678}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750053280779, "stop": 1750053280779, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella输入'open clock'命令，验证时钟应用是否打开", "status": "passed", "steps": [{"name": "记录测试开始状态", "time": {"start": 1750053280780, "stop": 1750053281093, "duration": 313}, "status": "passed", "steps": [], "attachments": [{"uid": "deeee9ed69e135ec", "name": "Ella初始状态", "source": "deeee9ed69e135ec.png", "type": "image/png", "size": 506011}, {"uid": "852799443ea99fe2", "name": "时钟应用初始状态", "source": "852799443ea99fe2.txt", "type": "text/plain", "size": 35}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 2, "shouldDisplayMessage": false}, {"name": "点击输入框", "time": {"start": 1750053281093, "stop": 1750053281730, "duration": 637}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "输入命令: open clock", "time": {"start": 1750053281730, "stop": 1750053285709, "duration": 3979}, "status": "passed", "steps": [], "attachments": [{"uid": "7a4e120994dc5b91", "name": "命令输入完成", "source": "7a4e120994dc5b91.png", "type": "image/png", "size": 692807}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "发送命令", "time": {"start": 1750053285709, "stop": 1750053290056, "duration": 4347}, "status": "passed", "steps": [], "attachments": [{"uid": "6a338ad501f17863", "name": "命令发送完成", "source": "6a338ad501f17863.png", "type": "image/png", "size": 137692}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "等待时钟应用启动", "time": {"start": 1750053290056, "stop": 1750053295216, "duration": 5160}, "status": "passed", "steps": [], "attachments": [{"uid": "cb110de03d61b346", "name": "等待应用启动", "source": "cb110de03d61b346.png", "type": "image/png", "size": 136176}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "验证时钟应用状态", "time": {"start": 1750053295216, "stop": 1750053295928, "duration": 712}, "status": "passed", "steps": [], "attachments": [{"uid": "cb62fc52e1db4c63", "name": "时钟应用状态验证", "source": "cb62fc52e1db4c63.txt", "type": "text/plain", "size": 76}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "验证输入命令在响应中的体现", "time": {"start": 1750053295928, "stop": 1750053295930, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "43a07eb06248b9f8", "name": "命令执行验证", "source": "43a07eb06248b9f8.txt", "type": "text/plain", "size": 101}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "记录测试完成状态", "time": {"start": 1750053295930, "stop": 1750053296072, "duration": 142}, "status": "passed", "steps": [], "attachments": [{"uid": "94ad92e422a0d489", "name": "测试完成状态", "source": "94ad92e422a0d489.png", "type": "image/png", "size": 136176}, {"uid": "10d140651eb17705", "name": "测试总结", "source": "10d140651eb17705.txt", "type": "text/plain", "size": 198}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 2, "shouldDisplayMessage": false}], "attachments": [{"uid": "c27bd6b70bb9aa0d", "name": "stdout", "source": "c27bd6b70bb9aa0d.txt", "type": "text/plain", "size": 5266}], "parameters": [], "attachmentStep": false, "stepsCount": 8, "hasContent": true, "attachmentsCount": 10, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750053296073, "stop": 1750053296073, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750053296074, "stop": 1750053296192, "duration": 118}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750053310295, "stop": 1750053310295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "应用启动控制"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_clock_command"}, {"name": "subSuite", "value": "TestEllaOpenClockCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "19212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_clock_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7c5d28837baf22b2.json", "parameterValues": []}