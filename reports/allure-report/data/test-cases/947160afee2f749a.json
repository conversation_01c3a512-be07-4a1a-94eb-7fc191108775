{"uid": "947160afee2f749a", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.open_app.test_open_app.TestEllaCommandConcise#test_open_app", "historyId": "99f21cb6aab54e7c38798c0bd39b97d0", "time": {"start": 1753349779966, "stop": 1753349789972, "duration": 10006}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349776277, "stop": 1753349779965, "duration": 3688}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349779965, "stop": 1753349779965, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1753349779966, "stop": 1753349789649, "duration": 9683}, "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1753349779966, "stop": 1753349788890, "duration": 8924}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349788890, "stop": 1753349789648, "duration": 758}, "status": "passed", "steps": [], "attachments": [{"uid": "c7e8e4e2a3604580", "name": "测试总结", "source": "c7e8e4e2a3604580.txt", "type": "text/plain", "size": 162}, {"uid": "3aa9f4abdcced31f", "name": "test_completed", "source": "3aa9f4abdcced31f.png", "type": "image/png", "size": 490303}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1753349789649, "stop": 1753349789654, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349789654, "stop": 1753349789971, "duration": 317}, "status": "passed", "steps": [], "attachments": [{"uid": "36175c6c7fecfb6b", "name": "测试总结", "source": "36175c6c7fecfb6b.txt", "type": "text/plain", "size": 162}, {"uid": "52f0c3afa03e806", "name": "test_completed", "source": "52f0c3afa03e806.png", "type": "image/png", "size": 490114}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1e52c33e6e006979", "name": "stdout", "source": "1e52c33e6e006979.txt", "type": "text/plain", "size": 9621}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349789975, "stop": 1753349789975, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349789978, "stop": 1753349790157, "duration": 179}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "947160afee2f749a.json", "parameterValues": []}