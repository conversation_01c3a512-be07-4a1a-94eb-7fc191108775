{"uid": "32ff1aa9094a8100", "name": "测试download app能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_app.TestEllaDownloadApp#test_download_app", "historyId": "578e52c6d5e868d5464682b454971c51", "time": {"start": 1753350023658, "stop": 1753350038533, "duration": 14875}, "description": "download app", "descriptionHtml": "<p>download app</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350019908, "stop": 1753350023657, "duration": 3749}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350023658, "stop": 1753350023658, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "download app", "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1753350023658, "stop": 1753350038196, "duration": 14538}, "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1753350023658, "stop": 1753350037885, "duration": 14227}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350037885, "stop": 1753350038195, "duration": 310}, "status": "passed", "steps": [], "attachments": [{"uid": "a2e80e683998e649", "name": "测试总结", "source": "a2e80e683998e649.txt", "type": "text/plain", "size": 174}, {"uid": "b3a9c66b13877545", "name": "test_completed", "source": "b3a9c66b13877545.png", "type": "image/png", "size": 555948}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350038196, "stop": 1753350038204, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350038204, "stop": 1753350038532, "duration": 328}, "status": "passed", "steps": [], "attachments": [{"uid": "e11d327aac22661f", "name": "测试总结", "source": "e11d327aac22661f.txt", "type": "text/plain", "size": 174}, {"uid": "f660afb301e4ebde", "name": "test_completed", "source": "f660afb301e4ebde.png", "type": "image/png", "size": 556036}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6a2ab2264953ca90", "name": "stdout", "source": "6a2ab2264953ca90.txt", "type": "text/plain", "size": 9216}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350038533, "stop": 1753350038533, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350038535, "stop": 1753350038689, "duration": 154}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_app"}, {"name": "subSuite", "value": "TestEllaDownloadApp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "32ff1aa9094a8100.json", "parameterValues": []}