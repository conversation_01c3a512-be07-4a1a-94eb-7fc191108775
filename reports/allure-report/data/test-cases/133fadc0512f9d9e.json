{"uid": "133fadc0512f9d9e", "name": "测试open flashlight", "fullName": "testcases.test_ella.open_app.test_open_flashlight.TestEllaCommandConcise#test_open_flashlight", "historyId": "4c3ac983971198f129e754475a2ddba8", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "failed", "statusMessage": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.open_app.test_open_flashlight.TestEllaCommandConcise object at 0x0000028F123825D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F12380F50>\n\n    @allure.title(\"测试open flashlight\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_flashlight(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open flashlight\"\n    \n        app_name = 'flashlight'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            expected_text =[\"flashlight\"]\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_flashlight.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349931269, "stop": 1753349935010, "duration": 3741}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349935010, "stop": 1753349935010, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "failed", "statusMessage": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.open_app.test_open_flashlight.TestEllaCommandConcise object at 0x0000028F123825D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F12380F50>\n\n    @allure.title(\"测试open flashlight\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_flashlight(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open flashlight\"\n    \n        app_name = 'flashlight'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            expected_text =[\"flashlight\"]\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_flashlight.py:35: AssertionError", "steps": [{"name": "执行命令: open flashlight", "time": {"start": 1753349935010, "stop": 1753349950473, "duration": 15463}, "status": "passed", "steps": [{"name": "执行命令: open flashlight", "time": {"start": 1753349935010, "stop": 1753349950183, "duration": 15173}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349950183, "stop": 1753349950472, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "ca7bb2f6f1fdeeae", "name": "测试总结", "source": "ca7bb2f6f1fdeeae.txt", "type": "text/plain", "size": 192}, {"uid": "fb415677bde90117", "name": "test_completed", "source": "fb415677bde90117.png", "type": "image/png", "size": 556998}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1753349950473, "stop": 1753349950477, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证flashlight已打开", "time": {"start": 1753349950477, "stop": 1753349950477, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_flashlight.py\", line 35, in test_open_flashlight\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "40c84a8ca23eaeba", "name": "stdout", "source": "40c84a8ca23eaeba.txt", "type": "text/plain", "size": 9938}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349950492, "stop": 1753349950844, "duration": 352}, "status": "passed", "steps": [], "attachments": [{"uid": "18036af549afe3e6", "name": "失败截图-TestEllaCommandConcise", "source": "18036af549afe3e6.png", "type": "image/png", "size": 557558}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1753349950846, "stop": 1753349951016, "duration": 170}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_flashlight"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "133fadc0512f9d9e.json", "parameterValues": []}