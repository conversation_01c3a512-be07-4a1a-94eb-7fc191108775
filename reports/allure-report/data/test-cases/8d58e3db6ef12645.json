{"uid": "8d58e3db6ef12645", "name": "测试order a burger能正常执行", "fullName": "testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise#test_order_a_burger", "historyId": "09f397887d36f3ef1e86e3c78272f1d6", "time": {"start": 1753350056024, "stop": 1753350065897, "duration": 9873}, "description": "order a burger", "descriptionHtml": "<p>order a burger</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350052281, "stop": 1753350056023, "duration": 3742}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350056024, "stop": 1753350056024, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "order a burger", "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1753350056024, "stop": 1753350065619, "duration": 9595}, "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1753350056024, "stop": 1753350065348, "duration": 9324}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350065348, "stop": 1753350065618, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "1a3aa6ae11d767d3", "name": "测试总结", "source": "1a3aa6ae11d767d3.txt", "type": "text/plain", "size": 212}, {"uid": "daf25b3cd7583eaf", "name": "test_completed", "source": "daf25b3cd7583eaf.png", "type": "image/png", "size": 531128}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350065619, "stop": 1753350065622, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350065622, "stop": 1753350065896, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "e21a88da3f8147de", "name": "测试总结", "source": "e21a88da3f8147de.txt", "type": "text/plain", "size": 212}, {"uid": "d44d4150bf4e0978", "name": "test_completed", "source": "d44d4150bf4e0978.png", "type": "image/png", "size": 531128}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ce141afbd94e3036", "name": "stdout", "source": "ce141afbd94e3036.txt", "type": "text/plain", "size": 9208}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350065898, "stop": 1753350065898, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350065900, "stop": 1753350066030, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_burger"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8d58e3db6ef12645.json", "parameterValues": []}