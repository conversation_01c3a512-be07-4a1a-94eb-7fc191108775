{"uid": "f79b47f62230aa55", "name": "测试open bluetooth命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_open_bluetooth_command", "historyId": "e25c945dfd6c1c874b0927b961b40416", "time": {"start": 1750053177796, "stop": 1750053206649, "duration": 28853}, "description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "descriptionHtml": "<p>通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750053174219, "stop": 1750053174631, "duration": 412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750053174631, "stop": 1************, "duration": 3164}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1************, "stop": 1************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "status": "passed", "steps": [{"name": "记录测试开始状态", "time": {"start": 1750053177796, "stop": 1750053178244, "duration": 448}, "status": "passed", "steps": [], "attachments": [{"uid": "fe90200451b7923d", "name": "Ella初始状态", "source": "fe90200451b7923d.png", "type": "image/png", "size": 476568}, {"uid": "19f6e999794c08a4", "name": "蓝牙初始状态", "source": "19f6e999794c08a4.txt", "type": "text/plain", "size": 26}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 2, "shouldDisplayMessage": false}, {"name": "输入命令: open bluetooth", "time": {"start": 1750053178244, "stop": 1750053184542, "duration": 6298}, "status": "passed", "steps": [], "attachments": [{"uid": "a7924c73c7811fa8", "name": "命令发送后", "source": "a7924c73c7811fa8.png", "type": "image/png", "size": 664068}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "等待AI响应", "time": {"start": 1750053184542, "stop": 1750053203206, "duration": 18664}, "status": "passed", "steps": [], "attachments": [{"uid": "b179644e09fec337", "name": "收到AI响应", "source": "b179644e09fec337.png", "type": "image/png", "size": 471215}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "获取并验证响应内容", "time": {"start": 1750053203206, "stop": 1750053204350, "duration": 1144}, "status": "passed", "steps": [], "attachments": [{"uid": "d1bd77e2e27fe4b7", "name": "AI响应内容", "source": "d1bd77e2e27fe4b7.txt", "type": "text/plain", "size": 16}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "验证蓝牙状态", "time": {"start": 1750053204350, "stop": 1750053206469, "duration": 2119}, "status": "passed", "steps": [], "attachments": [{"uid": "ea6a457cd87533f", "name": "蓝牙最终状态", "source": "ea6a457cd87533f.txt", "type": "text/plain", "size": 26}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "记录测试完成状态", "time": {"start": 1750053206469, "stop": 1750053206649, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "681d4d46314e7c8b", "name": "测试完成状态", "source": "681d4d46314e7c8b.png", "type": "image/png", "size": 471198}, {"uid": "4831a33854ea7c42", "name": "测试总结", "source": "4831a33854ea7c42.txt", "type": "text/plain", "size": 154}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 2, "shouldDisplayMessage": false}], "attachments": [{"uid": "4f6e7012889fbb38", "name": "stdout", "source": "4f6e7012889fbb38.txt", "type": "text/plain", "size": 8540}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "attachmentsCount": 9, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750053206649, "stop": 1750053206649, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750053206650, "stop": 1750053206832, "duration": 182}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750053310295, "stop": 1750053310295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "19212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f79b47f62230aa55.json", "parameterValues": []}