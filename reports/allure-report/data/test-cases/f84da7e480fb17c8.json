{"uid": "f84da7e480fb17c8", "name": "测试switch to smart charge能正常执行", "fullName": "testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "historyId": "6b1dfe37fc0819486f6cda64c12143be", "time": {"start": 1753350216613, "stop": 1753350226356, "duration": 9743}, "description": "switch to smart charge", "descriptionHtml": "<p>switch to smart charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350212904, "stop": 1753350216611, "duration": 3707}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350216612, "stop": 1753350216612, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch to smart charge", "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "time": {"start": 1753350216614, "stop": 1753350226043, "duration": 9429}, "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "time": {"start": 1753350216614, "stop": 1753350225723, "duration": 9109}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350225723, "stop": 1753350226043, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "c3228fcf3c49e9e7", "name": "测试总结", "source": "c3228fcf3c49e9e7.txt", "type": "text/plain", "size": 223}, {"uid": "fb8d821f3873d705", "name": "test_completed", "source": "fb8d821f3873d705.png", "type": "image/png", "size": 622746}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350226043, "stop": 1753350226048, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350226048, "stop": 1753350226355, "duration": 307}, "status": "passed", "steps": [], "attachments": [{"uid": "4952a80250295373", "name": "测试总结", "source": "4952a80250295373.txt", "type": "text/plain", "size": 223}, {"uid": "d1ec100f75663459", "name": "test_completed", "source": "d1ec100f75663459.png", "type": "image/png", "size": 622547}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "196df1814543df83", "name": "stdout", "source": "196df1814543df83.txt", "type": "text/plain", "size": 9623}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350226357, "stop": 1753350226358, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350226391, "stop": 1753350226544, "duration": 153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_smart_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f84da7e480fb17c8.json", "parameterValues": []}