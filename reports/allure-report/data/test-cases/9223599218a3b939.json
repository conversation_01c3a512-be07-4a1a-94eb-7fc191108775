{"uid": "9223599218a3b939", "name": "测试蓝牙状态查询命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_bluetooth_status_query", "historyId": "51d696b831178c19578ea68b891a77c8", "time": {"start": 1750679206967, "stop": 1750679305491, "duration": 98524}, "description": "通过Ella查询蓝牙状态", "descriptionHtml": "<p>通过Ella查询蓝牙状态</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750679161863, "stop": 1750679162228, "duration": 365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750679204054, "stop": 1750679206965, "duration": 2911}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750679206966, "stop": 1750679206966, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella查询蓝牙状态", "status": "passed", "steps": [{"name": "测试命令: bluetooth status", "time": {"start": 1750679206967, "stop": 1750679304884, "duration": 97917}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "测试命令: is bluetooth on", "time": {"start": 1750679304884, "stop": 1750679305201, "duration": 317}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "测试命令: check bluetooth", "time": {"start": 1750679305202, "stop": 1750679305490, "duration": 288}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "attachments": [{"uid": "71b7f9fefc2406da", "name": "stdout", "source": "71b7f9fefc2406da.txt", "type": "text/plain", "size": 7936}], "parameters": [], "stepsCount": 3, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750679305493, "stop": 1750679305493, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750679305495, "stop": 1750679305630, "duration": 135}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750679305631, "stop": 1750679305631, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "severity", "value": "minor"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "minor", "retries": [], "categories": [], "tags": ["regression"]}, "source": "9223599218a3b939.json", "parameterValues": []}