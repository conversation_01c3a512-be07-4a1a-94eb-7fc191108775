{"uid": "17c4ebea191d9716", "name": "测试close bluetooth命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_close_bluetooth_command", "historyId": "dc95ac7c06b3ef70ff231af88ffd03a1", "time": {"start": 1750053209659, "stop": 1750053239327, "duration": 29668}, "description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "descriptionHtml": "<p>通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750053174219, "stop": 1750053174631, "duration": 412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750053206841, "stop": 1750053209659, "duration": 2818}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750053209659, "stop": 1750053209659, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "status": "passed", "steps": [{"name": "记录测试开始状态", "time": {"start": 1750053209659, "stop": 1750053210036, "duration": 377}, "status": "passed", "steps": [], "attachments": [{"uid": "529e962360426133", "name": "Ella初始状态", "source": "529e962360426133.png", "type": "image/png", "size": 496429}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "输入命令: close bluetooth", "time": {"start": 1750053210036, "stop": 1750053217340, "duration": 7304}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "等待AI响应", "time": {"start": 1750053217340, "stop": 1750053235750, "duration": 18410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "获取并验证响应内容", "time": {"start": 1750053235750, "stop": 1750053236884, "duration": 1134}, "status": "passed", "steps": [], "attachments": [{"uid": "6ceabf632cd1e08b", "name": "AI响应内容", "source": "6ceabf632cd1e08b.txt", "type": "text/plain", "size": 16}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "验证蓝牙状态", "time": {"start": 1750053236884, "stop": 1750053239013, "duration": 2129}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "记录测试完成状态", "time": {"start": 1750053239013, "stop": 1750053239327, "duration": 314}, "status": "passed", "steps": [], "attachments": [{"uid": "581d7442459352b5", "name": "测试完成状态", "source": "581d7442459352b5.png", "type": "image/png", "size": 477595}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "attachments": [{"uid": "def64e8401e6e4f9", "name": "stdout", "source": "def64e8401e6e4f9.txt", "type": "text/plain", "size": 7844}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "attachmentsCount": 4, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750053239328, "stop": 1750053239329, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750053239330, "stop": 1750053239486, "duration": 156}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750053310295, "stop": 1750053310295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "19212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "17c4ebea191d9716.json", "parameterValues": []}