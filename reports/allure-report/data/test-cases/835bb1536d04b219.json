{"uid": "835bb1536d04b219", "name": "测试close bluetooth命令", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_close_bluetooth_command", "historyId": "dc95ac7c06b3ef70ff231af88ffd03a1", "time": {"start": 1750679189720, "stop": 1750679203912, "duration": 14192}, "description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "descriptionHtml": "<p>通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750679161863, "stop": 1750679162228, "duration": 365}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750679186785, "stop": 1750679189718, "duration": 2933}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750679189718, "stop": 1750679189718, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "status": "passed", "steps": [{"name": "记录测试开始状态", "time": {"start": 1750679189720, "stop": 1750679190147, "duration": 427}, "status": "passed", "steps": [], "attachments": [{"uid": "95d382e08ec228cb", "name": "Ella初始状态", "source": "95d382e08ec228cb.png", "type": "image/png", "size": 461244}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "输入命令: close bluetooth", "time": {"start": 1750679190147, "stop": 1750679192872, "duration": 2725}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "等待AI响应", "time": {"start": 1750679192872, "stop": 1750679199461, "duration": 6589}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "获取并验证响应内容", "time": {"start": 1750679199461, "stop": 1750679201384, "duration": 1923}, "status": "passed", "steps": [], "attachments": [{"uid": "34eb4a422fa1eee2", "name": "AI响应内容", "source": "34eb4a422fa1eee2.txt", "type": "text/plain", "size": 16}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "验证蓝牙状态", "time": {"start": 1750679201384, "stop": 1750679203595, "duration": 2211}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "记录测试完成状态", "time": {"start": 1750679203595, "stop": 1750679203912, "duration": 317}, "status": "passed", "steps": [], "attachments": [{"uid": "c46b07254e31f820", "name": "测试完成状态", "source": "c46b07254e31f820.png", "type": "image/png", "size": 453284}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "attachmentsCount": 1, "shouldDisplayMessage": false}], "attachments": [{"uid": "a41b7ba0048ba7b2", "name": "stdout", "source": "a41b7ba0048ba7b2.txt", "type": "text/plain", "size": 8860}], "parameters": [], "stepsCount": 6, "hasContent": true, "attachmentStep": false, "attachmentsCount": 4, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750679203913, "stop": 1750679203913, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750679203915, "stop": 1750679204048, "duration": 133}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750679305631, "stop": 1750679305631, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "835bb1536d04b219.json", "parameterValues": []}