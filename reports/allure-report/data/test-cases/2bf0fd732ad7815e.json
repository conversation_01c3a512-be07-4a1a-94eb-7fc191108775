{"uid": "2bf0fd732ad7815e", "name": "测试open camera能正常执行", "fullName": "testcases.test_ella.open_app.test_open_camera.TestEllaCommandConcise#test_open_camera", "historyId": "8004710ab61d7d3a654919d2ee030d79", "time": {"start": 1753349810541, "stop": 1753349826049, "duration": 15508}, "description": "open camera", "descriptionHtml": "<p>open camera</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349806920, "stop": 1753349810540, "duration": 3620}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349810540, "stop": 1753349810541, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1753349810541, "stop": 1753349825767, "duration": 15226}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1753349810541, "stop": 1753349825452, "duration": 14911}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349825452, "stop": 1753349825766, "duration": 314}, "status": "passed", "steps": [], "attachments": [{"uid": "e86a301cbb44ed49", "name": "测试总结", "source": "e86a301cbb44ed49.txt", "type": "text/plain", "size": 149}, {"uid": "6db4baee6556bcfe", "name": "test_completed", "source": "6db4baee6556bcfe.png", "type": "image/png", "size": 531377}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753349825767, "stop": 1753349825770, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349825770, "stop": 1753349826048, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "e1a19d5fb3ca1518", "name": "测试总结", "source": "e1a19d5fb3ca1518.txt", "type": "text/plain", "size": 149}, {"uid": "194213d70af75c34", "name": "test_completed", "source": "194213d70af75c34.png", "type": "image/png", "size": 531222}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f4e7a2d2e35d11c0", "name": "stdout", "source": "f4e7a2d2e35d11c0.txt", "type": "text/plain", "size": 10981}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349826050, "stop": 1753349826050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349826055, "stop": 1753349826219, "duration": 164}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2bf0fd732ad7815e.json", "parameterValues": []}