{"uid": "2c45318d974babe0", "name": "测试switch charging modes能正常执行", "fullName": "testcases.test_ella.third_coupling.test_switch_charging_modes.TestEllaSwitchChargingModes#test_switch_charging_modes", "historyId": "eab84c8d9454644261f5e07341993e63", "time": {"start": 1753350098134, "stop": 1753350109624, "duration": 11490}, "description": "switch charging modes", "descriptionHtml": "<p>switch charging modes</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350094496, "stop": 1753350098133, "duration": 3637}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350098133, "stop": 1753350098133, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch charging modes", "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "time": {"start": 1753350098134, "stop": 1753350109281, "duration": 11147}, "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "time": {"start": 1753350098134, "stop": 1753350108970, "duration": 10836}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350108970, "stop": 1753350109280, "duration": 310}, "status": "passed", "steps": [], "attachments": [{"uid": "2aaac14b40b2d9fd", "name": "测试总结", "source": "2aaac14b40b2d9fd.txt", "type": "text/plain", "size": 513}, {"uid": "92666c48c320aeb5", "name": "test_completed", "source": "92666c48c320aeb5.png", "type": "image/png", "size": 595348}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350109281, "stop": 1753350109284, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350109284, "stop": 1753350109623, "duration": 339}, "status": "passed", "steps": [], "attachments": [{"uid": "e55f51c7491bbee", "name": "测试总结", "source": "e55f51c7491bbee.txt", "type": "text/plain", "size": 513}, {"uid": "561811a051d79ef9", "name": "test_completed", "source": "561811a051d79ef9.png", "type": "image/png", "size": 595348}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3bf84dba3c9dd699", "name": "stdout", "source": "3bf84dba3c9dd699.txt", "type": "text/plain", "size": 11258}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350109626, "stop": 1753350109626, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350109628, "stop": 1753350109773, "duration": 145}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_charging_modes"}, {"name": "subSuite", "value": "TestEllaSwitchChargingModes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_charging_modes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2c45318d974babe0.json", "parameterValues": []}