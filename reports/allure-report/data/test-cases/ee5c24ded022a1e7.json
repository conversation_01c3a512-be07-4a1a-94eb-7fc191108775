{"uid": "ee5c24ded022a1e7", "name": "测试Switch to Low-Temp Charge能正常执行", "fullName": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "historyId": "ee231dd22ce3bf43d8c81633728f58b1", "time": {"start": 1753350174077, "stop": 1753350183598, "duration": 9521}, "description": "Switch to Low-Temp Charge", "descriptionHtml": "<p>Switch to Low-Temp Charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350170454, "stop": 1753350174075, "duration": 3621}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350174075, "stop": 1753350174075, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Switch to Low-Temp Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "time": {"start": 1753350174077, "stop": 1753350183300, "duration": 9223}, "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "time": {"start": 1753350174077, "stop": 1753350183030, "duration": 8953}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350183030, "stop": 1753350183300, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "2479a3e088fc4f2d", "name": "测试总结", "source": "2479a3e088fc4f2d.txt", "type": "text/plain", "size": 229}, {"uid": "6c1dc6da04367098", "name": "test_completed", "source": "6c1dc6da04367098.png", "type": "image/png", "size": 540501}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350183300, "stop": 1753350183303, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350183303, "stop": 1753350183596, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "c88cde3353a83d6a", "name": "测试总结", "source": "c88cde3353a83d6a.txt", "type": "text/plain", "size": 229}, {"uid": "5108a9566f011545", "name": "test_completed", "source": "5108a9566f011545.png", "type": "image/png", "size": 540538}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "14a05d76b0131ad5", "name": "stdout", "source": "14a05d76b0131ad5.txt", "type": "text/plain", "size": 9305}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350183599, "stop": 1753350183599, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350183602, "stop": 1753350183707, "duration": 105}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ee5c24ded022a1e7.json", "parameterValues": []}