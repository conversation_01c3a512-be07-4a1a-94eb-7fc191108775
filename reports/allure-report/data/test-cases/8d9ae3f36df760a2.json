{"uid": "8d9ae3f36df760a2", "name": "测试open contact命令", "fullName": "testcases.test_ella.open_app.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "historyId": "47f6f542e363a70ee1e571c7805a9526", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.open_app.test_open_phone.TestEllaContactCommandConcise object at 0x0000028F12382850>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F127B87D0>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_phone(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open phone\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            result = self.verify_expected_in_response(\"done\", response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(\"验证Dalier应用已打开\"):\n>           assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_phone.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349965993, "stop": 1753349969589, "duration": 3596}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349969589, "stop": 1753349969589, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.open_app.test_open_phone.TestEllaContactCommandConcise object at 0x0000028F12382850>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F127B87D0>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_phone(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open phone\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            result = self.verify_expected_in_response(\"done\", response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(\"验证Dalier应用已打开\"):\n>           assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_phone.py:35: AssertionError", "steps": [{"name": "执行命令: open phone", "time": {"start": 1753349969590, "stop": 1753349986900, "duration": 17310}, "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1753349969590, "stop": 1753349986590, "duration": 17000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349986591, "stop": 1753349986899, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "2070329b081bfeb1", "name": "测试总结", "source": "2070329b081bfeb1.txt", "type": "text/plain", "size": 149}, {"uid": "558cabf6250017a1", "name": "test_completed", "source": "558cabf6250017a1.png", "type": "image/png", "size": 599078}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1753349986900, "stop": 1753349986903, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证Dalier应用已打开", "time": {"start": 1753349986903, "stop": 1753349986903, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_phone.py\", line 35, in test_open_phone\n    assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "9ec5b33b13c20525", "name": "stdout", "source": "9ec5b33b13c20525.txt", "type": "text/plain", "size": 12648}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349986915, "stop": 1753349987173, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "e7fc25aee7b7561f", "name": "失败截图-TestEllaContactCommandConcise", "source": "e7fc25aee7b7561f.png", "type": "image/png", "size": 599239}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1753349987176, "stop": 1753349987301, "duration": 125}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "8d9ae3f36df760a2.json", "parameterValues": []}