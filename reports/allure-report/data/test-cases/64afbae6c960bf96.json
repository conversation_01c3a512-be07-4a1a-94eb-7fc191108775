{"uid": "64afbae6c960bf96", "name": "测试switch to power saving mode能正常执行", "fullName": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "8fd5e40ff38046a9fe7fe83ba6136249", "time": {"start": 1753350202833, "stop": 1753350212741, "duration": 9908}, "description": "switch to power saving mode", "descriptionHtml": "<p>switch to power saving mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350199216, "stop": 1753350202832, "duration": 3616}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350202832, "stop": 1753350202832, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1753350202834, "stop": 1753350212373, "duration": 9539}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1753350202834, "stop": 1753350212045, "duration": 9211}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350212045, "stop": 1753350212373, "duration": 328}, "status": "passed", "steps": [], "attachments": [{"uid": "cbfde5d0e3ed9ccf", "name": "测试总结", "source": "cbfde5d0e3ed9ccf.txt", "type": "text/plain", "size": 251}, {"uid": "9cffd6b47fc41f72", "name": "test_completed", "source": "9cffd6b47fc41f72.png", "type": "image/png", "size": 646499}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350212373, "stop": 1753350212376, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350212376, "stop": 1753350212740, "duration": 364}, "status": "passed", "steps": [], "attachments": [{"uid": "f01299cad5087e4c", "name": "测试总结", "source": "f01299cad5087e4c.txt", "type": "text/plain", "size": 251}, {"uid": "6e1041500268078b", "name": "test_completed", "source": "6e1041500268078b.png", "type": "image/png", "size": 646499}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "21a3f30d0dec7c1f", "name": "stdout", "source": "21a3f30d0dec7c1f.txt", "type": "text/plain", "size": 9382}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350212742, "stop": 1753350212742, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350212745, "stop": 1753350212894, "duration": 149}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "64afbae6c960bf96.json", "parameterValues": []}