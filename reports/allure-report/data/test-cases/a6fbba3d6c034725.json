{"uid": "a6fbba3d6c034725", "name": "测试smart charge能正常执行", "fullName": "testcases.test_ella.third_coupling.test_smart_charge.TestEllaSmartCharge#test_smart_charge", "historyId": "547e7a68915f4e05c1ef0182ac744dc1", "time": {"start": 1753350084098, "stop": 1753350094352, "duration": 10254}, "description": "smart charge", "descriptionHtml": "<p>smart charge</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753350080350, "stop": 1753350084097, "duration": 3747}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753350084097, "stop": 1753350084097, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "smart charge", "status": "passed", "steps": [{"name": "执行命令: smart charge", "time": {"start": 1753350084098, "stop": 1753350094033, "duration": 9935}, "status": "passed", "steps": [{"name": "执行命令: smart charge", "time": {"start": 1753350084098, "stop": 1753350093709, "duration": 9611}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350093709, "stop": 1753350094033, "duration": 324}, "status": "passed", "steps": [], "attachments": [{"uid": "4de66f83e14a509b", "name": "测试总结", "source": "4de66f83e14a509b.txt", "type": "text/plain", "size": 203}, {"uid": "689de03ab956b153", "name": "test_completed", "source": "689de03ab956b153.png", "type": "image/png", "size": 511391}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753350094033, "stop": 1753350094037, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350094037, "stop": 1753350094351, "duration": 314}, "status": "passed", "steps": [], "attachments": [{"uid": "f8562e9eebff571d", "name": "测试总结", "source": "f8562e9eebff571d.txt", "type": "text/plain", "size": 203}, {"uid": "16f22c25cd848b97", "name": "test_completed", "source": "16f22c25cd848b97.png", "type": "image/png", "size": 511391}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b9ad7f732cc04542", "name": "stdout", "source": "b9ad7f732cc04542.txt", "type": "text/plain", "size": 9159}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350094353, "stop": 1753350094353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350094356, "stop": 1753350094491, "duration": 135}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_smart_charge"}, {"name": "subSuite", "value": "TestEllaSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_smart_charge"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a6fbba3d6c034725.json", "parameterValues": []}