{"uid": "f73e43036f2e212", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.open_app.test_open_bluetooth.TestEllaCommandConcise#test_open_bluetooth", "historyId": "6ccf50a90a4364fc2767466e6c0283fc", "time": {"start": 1753349793794, "stop": 1753349806727, "duration": 12933}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349790163, "stop": 1753349793792, "duration": 3629}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349793792, "stop": 1753349793792, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1753349793794, "stop": 1753349806412, "duration": 12618}, "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1753349793794, "stop": 1753349806116, "duration": 12322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349806116, "stop": 1753349806410, "duration": 294}, "status": "passed", "steps": [], "attachments": [{"uid": "1399e49beb2b7142", "name": "测试总结", "source": "1399e49beb2b7142.txt", "type": "text/plain", "size": 186}, {"uid": "5037e114c13a5c9e", "name": "test_completed", "source": "5037e114c13a5c9e.png", "type": "image/png", "size": 524881}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1753349806412, "stop": 1753349806423, "duration": 11}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证bluetooth已打开", "time": {"start": 1753349806423, "stop": 1753349806423, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349806423, "stop": 1753349806726, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "86bea9d8e04a52bd", "name": "测试总结", "source": "86bea9d8e04a52bd.txt", "type": "text/plain", "size": 186}, {"uid": "93f5bb83578f7317", "name": "test_completed", "source": "93f5bb83578f7317.png", "type": "image/png", "size": 525006}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "671a2bd85eb51766", "name": "stdout", "source": "671a2bd85eb51766.txt", "type": "text/plain", "size": 10656}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 6, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349806729, "stop": 1753349806729, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349806731, "stop": 1753349806915, "duration": 184}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "联系人控制命令 - 简洁版本"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f73e43036f2e212.json", "parameterValues": []}