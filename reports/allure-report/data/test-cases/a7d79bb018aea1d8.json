{"uid": "a7d79bb018aea1d8", "name": "测试open facebook能正常执行", "fullName": "testcases.test_ella.open_app.test_open_facebook.TestEllaCommandConcise#test_open_facebook", "historyId": "60e3b7ad9599b952591bccdea8d4bf1b", "time": {"start": 1753349915484, "stop": 1753349931111, "duration": 15627}, "description": "open facebook", "descriptionHtml": "<p>open facebook</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349911702, "stop": 1753349915483, "duration": 3781}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349915483, "stop": 1753349915483, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open facebook", "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1753349915484, "stop": 1753349930800, "duration": 15316}, "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1753349915484, "stop": 1753349930489, "duration": 15005}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349930489, "stop": 1753349930799, "duration": 310}, "status": "passed", "steps": [], "attachments": [{"uid": "1740e112d71efe5e", "name": "测试总结", "source": "1740e112d71efe5e.txt", "type": "text/plain", "size": 154}, {"uid": "72ed448f669ea4ed", "name": "test_completed", "source": "72ed448f669ea4ed.png", "type": "image/png", "size": 520085}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753349930800, "stop": 1753349930808, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349930808, "stop": 1753349931110, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "368060a4db7badc0", "name": "测试总结", "source": "368060a4db7badc0.txt", "type": "text/plain", "size": 154}, {"uid": "350d6dfd1e4156cc", "name": "test_completed", "source": "350d6dfd1e4156cc.png", "type": "image/png", "size": 520542}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d3bf5c5886c72a0", "name": "stdout", "source": "d3bf5c5886c72a0.txt", "type": "text/plain", "size": 11057}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349931112, "stop": 1753349931112, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349931115, "stop": 1753349931249, "duration": 134}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_facebook"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_facebook"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a7d79bb018aea1d8.json", "parameterValues": []}