{"uid": "b64769b5cf4a403a", "name": "测试时钟应用包名断言", "fullName": "testcases.test_ella.test_open_clock_command.TestEllaOpenClockCommand#test_clock_app_package_assertion", "historyId": "4201972586f01bfc70320b306e6e50a6", "time": {"start": 1750053299857, "stop": 1750053310161, "duration": 10304}, "description": "验证时钟应用的包名是否正确", "descriptionHtml": "<p>验证时钟应用的包名是否正确</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1750053174219, "stop": 1750053174631, "duration": 412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app", "time": {"start": 1750053296202, "stop": 1750053299856, "duration": 3654}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1750053299856, "stop": 1750053299856, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"description": "验证时钟应用的包名是否正确", "status": "passed", "steps": [{"name": "执行open clock命令", "time": {"start": 1750053299857, "stop": 1750053309743, "duration": 9886}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "验证时钟应用包名", "time": {"start": 1750053309744, "stop": 1750053310161, "duration": 417}, "status": "passed", "steps": [], "attachments": [{"uid": "1f11775eb4ff0256", "name": "包名验证结果", "source": "1f11775eb4ff0256.txt", "type": "text/plain", "size": 96}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "attachments": [{"uid": "d18f1d21eb535965", "name": "stdout", "source": "d18f1d21eb535965.txt", "type": "text/plain", "size": 4069}], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "attachmentsCount": 2, "shouldDisplayMessage": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1750053310162, "stop": 1750053310162, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "ella_app::0", "time": {"start": 1750053310163, "stop": 1750053310294, "duration": 131}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "setup_test_environment::0", "time": {"start": 1750053310295, "stop": 1750053310295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "应用启动控制"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_clock_command"}, {"name": "subSuite", "value": "TestEllaOpenClockCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "19212-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_clock_command"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["regression"]}, "source": "b64769b5cf4a403a.json", "parameterValues": []}