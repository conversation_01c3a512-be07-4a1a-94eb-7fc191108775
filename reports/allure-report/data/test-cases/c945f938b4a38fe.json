{"uid": "c945f938b4a38fe", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.open_app.test_open_wifi.TestEllaCommandConcise#test_open_wifi", "historyId": "10b1ce60a8d05504eb6d58494ac3ec0c", "time": {"start": 1753349990953, "stop": 1753350005012, "duration": 14059}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349987307, "stop": 1753349990952, "duration": 3645}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349990953, "stop": 1753349990953, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1753349990953, "stop": 1753350004751, "duration": 13798}, "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1753349990953, "stop": 1753350004443, "duration": 13490}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350004443, "stop": 1753350004751, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "3dc2c14ce9861e00", "name": "测试总结", "source": "3dc2c14ce9861e00.txt", "type": "text/plain", "size": 168}, {"uid": "44767189815bc4eb", "name": "test_completed", "source": "44767189815bc4eb.png", "type": "image/png", "size": 497323}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1753350004751, "stop": 1753350004756, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证wifi已打开", "time": {"start": 1753350004756, "stop": 1753350004756, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753350004756, "stop": 1753350005011, "duration": 255}, "status": "passed", "steps": [], "attachments": [{"uid": "5a418f6013edc7c", "name": "测试总结", "source": "5a418f6013edc7c.txt", "type": "text/plain", "size": 168}, {"uid": "f455b4b069bc01e", "name": "test_completed", "source": "f455b4b069bc01e.png", "type": "image/png", "size": 496808}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d5a3498e9353b2a4", "name": "stdout", "source": "d5a3498e9353b2a4.txt", "type": "text/plain", "size": 9510}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 6, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753350005014, "stop": 1753350005014, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753350005017, "stop": 1753350005157, "duration": 140}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c945f938b4a38fe.json", "parameterValues": []}