{"uid": "bc0d71dbeec9f327", "name": "测试open countdown能正常执行", "fullName": "testcases.test_ella.open_app.test_open_countdown.TestEllaCommandConcise#test_open_countdown", "historyId": "4435e72d0d42f3a5a2ebd4b283ecb50b", "time": {"start": 1753349864535, "stop": 1753349875675, "duration": 11140}, "description": "open countdown", "descriptionHtml": "<p>open countdown</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349860811, "stop": 1753349864535, "duration": 3724}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349864535, "stop": 1753349864535, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open countdown", "status": "passed", "steps": [{"name": "执行命令: open countdown", "time": {"start": 1753349864535, "stop": 1753349875347, "duration": 10812}, "status": "passed", "steps": [{"name": "执行命令: open countdown", "time": {"start": 1753349864535, "stop": 1753349875041, "duration": 10506}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349875041, "stop": 1753349875347, "duration": 306}, "status": "passed", "steps": [], "attachments": [{"uid": "7793c3d65ae05c8a", "name": "测试总结", "source": "7793c3d65ae05c8a.txt", "type": "text/plain", "size": 379}, {"uid": "36164db384847406", "name": "test_completed", "source": "36164db384847406.png", "type": "image/png", "size": 562332}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753349875347, "stop": 1753349875351, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349875351, "stop": 1753349875674, "duration": 323}, "status": "passed", "steps": [], "attachments": [{"uid": "bba23fd36c6d29e", "name": "测试总结", "source": "bba23fd36c6d29e.txt", "type": "text/plain", "size": 379}, {"uid": "e92f2530171f3f55", "name": "test_completed", "source": "e92f2530171f3f55.png", "type": "image/png", "size": 562332}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7e8f62653ecad37b", "name": "stdout", "source": "7e8f62653ecad37b.txt", "type": "text/plain", "size": 11144}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349875677, "stop": 1753349875677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349875679, "stop": 1753349875880, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_countdown"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_countdown"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bc0d71dbeec9f327.json", "parameterValues": []}