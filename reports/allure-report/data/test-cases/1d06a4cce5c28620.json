{"uid": "1d06a4cce5c28620", "name": "测试open folax能正常执行", "fullName": "testcases.test_ella.open_app.test_open_folax.TestEllaCommandConcise#test_open_folax", "historyId": "665d2298fe67372a7d3111361b2a1434", "time": {"start": 1753349954708, "stop": 1753349965810, "duration": 11102}, "description": "open folax", "descriptionHtml": "<p>open folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753349775851, "stop": 1753349776277, "duration": 426}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753349951020, "stop": 1753349954707, "duration": 3687}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753349954707, "stop": 1753349954707, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1753349954708, "stop": 1753349965476, "duration": 10768}, "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1753349954708, "stop": 1753349965176, "duration": 10468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349965176, "stop": 1753349965475, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "5ebabbba43ab2f49", "name": "测试总结", "source": "5ebabbba43ab2f49.txt", "type": "text/plain", "size": 482}, {"uid": "48a89cf7bb56dc66", "name": "test_completed", "source": "48a89cf7bb56dc66.png", "type": "image/png", "size": 610539}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 2, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1753349965476, "stop": 1753349965480, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753349965480, "stop": 1753349965809, "duration": 329}, "status": "passed", "steps": [], "attachments": [{"uid": "e20b175ca91b7c0a", "name": "测试总结", "source": "e20b175ca91b7c0a.txt", "type": "text/plain", "size": 482}, {"uid": "77bf3d8dc72e2ab2", "name": "test_completed", "source": "77bf3d8dc72e2ab2.png", "type": "image/png", "size": 610539}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5b1e4885d9ea1f47", "name": "stdout", "source": "5b1e4885d9ea1f47.txt", "type": "text/plain", "size": 11489}], "parameters": [], "hasContent": true, "attachmentStep": false, "stepsCount": 5, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753349965811, "stop": 1753349965812, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753349965814, "stop": 1753349965984, "duration": 170}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753350226546, "stop": 1753350226548, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "attachmentStep": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1d06a4cce5c28620.json", "parameterValues": []}