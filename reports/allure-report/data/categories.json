{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "children": [{"name": "open clock", "uid": "664f1be505275c9b", "parentUid": "2b4278c79623ec60452d142f3f58c9b1", "status": "failed", "time": {"start": 1753349829956, "stop": 1753349843254, "duration": 13298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2b4278c79623ec60452d142f3f58c9b1"}, {"name": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False", "children": [{"name": "测试open flashlight", "uid": "133fadc0512f9d9e", "parentUid": "0e864b1bae34b8f4d3100c8afad3ecfd", "status": "failed", "time": {"start": 1753349935010, "stop": 1753349950479, "duration": 15469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0e864b1bae34b8f4d3100c8afad3ecfd"}, {"name": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False", "children": [{"name": "测试open contact命令", "uid": "8d9ae3f36df760a2", "parentUid": "b8f4154d9961d55fe94021728bb43c57", "status": "failed", "time": {"start": 1753349969590, "stop": 1753349986905, "duration": 17315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b8f4154d9961d55fe94021728bb43c57"}, {"name": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "children": [{"name": "测试order a takeaway能正常执行", "uid": "71dd4b69357ff2c2", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1753350069623, "stop": 1753350079905, "duration": 10282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "d2fe2d61517ed5c2", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1753350128728, "stop": 1753350136981, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode能正常执行", "uid": "e324c3f616cfbf75", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1753350187320, "stop": 1753350198665, "duration": 11345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c7fcb7e700713ec9d3081e0b69d1f2e"}, {"name": "AssertionError: 响应未包含期望内容: ['the voice is switched']\nassert False", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "12eb50903ea12475", "parentUid": "a1d48541ca887bbbfcd2f8f254a9f3fc", "status": "failed", "time": {"start": 1753350113511, "stop": 1753350124587, "duration": 11076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1d48541ca887bbbfcd2f8f254a9f3fc"}, {"name": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "8a5287e14b41a89b", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1753350141136, "stop": 1753350155622, "duration": 14486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a710013a126f24211d2305c197d8623"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}]}