2025-07-24 17:38:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-07-24 17:38:51 | INFO | pages.apps.ella.dialogue_page:start_app:128 | 启动Ella应用
2025-07-24 17:38:51 | INFO | pages.apps.ella.dialogue_page:start_app:136 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:38:54 | INFO | pages.apps.ella.dialogue_page:_check_app_started:194 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-24 17:38:54 | INFO | pages.apps.ella.dialogue_page:start_app:141 | ✅ Ella应用启动成功（指定Activity）
2025-07-24 17:38:54 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:215 | 等待Ella页面加载完成 (超时: 15秒)
2025-07-24 17:38:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-24 17:38:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:38:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:38:55 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:219 | ✅ 输入框已出现，页面加载完成
2025-07-24 17:38:55 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-24 17:38:55 | INFO | pages.base.system_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-24 17:38:55 | INFO | pages.base.system_status_checker:check_flashlight_status:179 | 手电筒状态: 关闭 (通过快捷设置检测)
2025-07-24 17:38:55 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-24 17:38:55 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:38:55 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:38:56 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:38:56 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:38:56 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:38:56 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:38:56 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-24 17:38:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:38:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:38:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:38:56 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:38:57 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-24 17:38:57 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-24 17:38:57 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:38:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-24 17:38:57 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-24 17:38:57 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-24 17:38:57 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-24 17:38:57 | INFO | testcases.test_ella.base_ella_test:_execute_command:371 | ✅ 成功执行命令: open flashlight
2025-07-24 17:38:57 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-24 17:38:58 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-24 17:39:01 | INFO | pages.base.system_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:check_flashlight_status:179 | 手电筒状态: 关闭 (通过快捷设置检测)
2025-07-24 17:39:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-24 17:39:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-24 17:39:02 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:39:02 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:39:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-24 17:39:02 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:39:03 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:39:03 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:39:03 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:39:03 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-24 17:39:05 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-24 17:39:06 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-24 17:39:08 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-24 17:39:10 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:471 | ✅ 状态验证通过: False -> False
2025-07-24 17:39:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:simple_command_test:696 | 🎉 open flashlight 测试完成
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:508 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:552 | ✅ 响应包含期望内容: 'flashlight'
2025-07-24 17:39:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:560 | 🎉 所有期望内容都已找到 (1/1)
2025-07-24 17:39:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250724_173910.png
2025-07-24 17:39:10 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250724_173910.png
2025-07-24 17:39:10 | INFO | pages.apps.ella.dialogue_page:stop_app:235 | 停止Ella应用
2025-07-24 17:39:11 | WARNING | pages.apps.ella.dialogue_page:stop_app:241 | ⚠️ 停止Ella应用可能失败
