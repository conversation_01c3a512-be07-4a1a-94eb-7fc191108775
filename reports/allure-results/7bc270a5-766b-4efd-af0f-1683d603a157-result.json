{"name": "测试open camera能正常执行", "status": "passed", "description": "open camera", "steps": [{"name": "执行命令: open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "status": "passed", "start": 1753349810541, "stop": 1753349825452}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a7bf4159-6c3e-408e-a231-abe5163e2ed5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f9fe722f-838b-41b7-bc62-9cc1578fe10d-attachment.png", "type": "image/png"}], "start": 1753349825452, "stop": 1753349825766}], "start": 1753349810541, "stop": 1753349825767}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753349825767, "stop": 1753349825770}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a4d3b0f1-5bbc-4173-b3db-b4fa3cc6bd8b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0e04c889-f1b0-45ef-9634-8c9a66a66732-attachment.png", "type": "image/png"}], "start": 1753349825770, "stop": 1753349826048}], "attachments": [{"name": "stdout", "source": "f0515e69-2cc1-4ca5-b679-b0d8030ad2bf-attachment.txt", "type": "text/plain"}], "start": 1753349810541, "stop": 1753349826049, "uuid": "3108e19d-fe39-4572-bbe3-3c45031d4182", "historyId": "8004710ab61d7d3a654919d2ee030d79", "testCaseId": "8004710ab61d7d3a654919d2ee030d79", "fullName": "testcases.test_ella.open_app.test_open_camera.TestEllaCommandConcise#test_open_camera", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_camera"}]}