{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open ella", "status": "passed", "steps": [{"name": "执行命令: open ella", "status": "passed", "start": 1753193202597, "stop": 1753193210598}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c7ed0435-4a1c-43d2-af1f-28c3d920b0ac-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a713263f-8734-4c7e-b880-a317903a0a05-attachment.png", "type": "image/png"}], "start": 1753193210598, "stop": 1753193211001}], "start": 1753193202597, "stop": 1753193211002}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193211002, "stop": 1753193211008}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "663f03c5-61f4-4f78-9ee2-d6f28613efe0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "938e7cba-8852-4ed9-b32f-099096e98624-attachment.png", "type": "image/png"}], "start": 1753193211008, "stop": 1753193211376}], "attachments": [{"name": "stdout", "source": "e1c903da-8f09-42ed-9759-6aceb678e4dc-attachment.txt", "type": "text/plain"}], "start": 1753193202596, "stop": 1753193211376, "uuid": "376a28ee-63cc-48dc-88d7-83ad143347e9", "historyId": "4665de45505b728dffd3d02fb2efaea4", "testCaseId": "4665de45505b728dffd3d02fb2efaea4", "fullName": "testcases.test_ella.test_open_ella.TestEllaCommandConcise#test_open_ella", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_ella"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_ella"}]}