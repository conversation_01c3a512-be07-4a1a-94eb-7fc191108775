{"name": "测试switch to smart charge能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDBD90>.start_app", "trace": "self = <testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge object at 0x0000022887793D90>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDBD90>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge object at 0x0000022887793D90>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDBD90>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "switch to smart charge", "attachments": [{"name": "stdout", "source": "6d7f131f-4cf2-4838-a64c-d7d8d87ff6a6-attachment.txt", "type": "text/plain"}], "start": 1753349219733, "stop": 1753349219733, "uuid": "69942499-41a0-47d8-8f9c-bb11746734cc", "historyId": "6b1dfe37fc0819486f6cda64c12143be", "testCaseId": "6b1dfe37fc0819486f6cda64c12143be", "fullName": "testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_smart_charge"}]}