{"name": "测试open contact命令 - 简洁版本", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app", "trace": "self = <testcases.test_ella.open_app.test_open_bluetooth.TestEllaCommandConcise object at 0x0000022887791F90>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.open_app.test_open_bluetooth.TestEllaCommandConcise object at 0x0000022887791F90>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "attachments": [{"name": "stdout", "source": "a0f440b5-5744-4900-b3e7-ee9ca58b7e36-attachment.txt", "type": "text/plain"}], "start": 1753349208849, "stop": 1753349208849, "uuid": "dfb7027f-172b-4f25-b80f-8b24b0c2f927", "historyId": "6ccf50a90a4364fc2767466e6c0283fc", "testCaseId": "6ccf50a90a4364fc2767466e6c0283fc", "fullName": "testcases.test_ella.open_app.test_open_bluetooth.TestEllaCommandConcise#test_open_bluetooth", "labels": [{"name": "story", "value": "联系人控制命令 - 简洁版本"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_bluetooth"}]}