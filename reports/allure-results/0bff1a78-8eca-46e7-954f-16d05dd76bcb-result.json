{"name": "测试switch to smart charge能正常执行", "status": "passed", "description": "switch to smart charge", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "steps": [{"name": "执行命令: switch to smart charge", "status": "passed", "start": 1753350216614, "stop": 1753350225723}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8dcf3eed-f67e-4aa9-8bda-43aa1d10c0d5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e2f86092-f5f5-4262-8778-49352477a6a1-attachment.png", "type": "image/png"}], "start": 1753350225723, "stop": 1753350226043}], "start": 1753350216614, "stop": 1753350226043}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350226043, "stop": 1753350226048}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4c199d1c-4ea0-44da-a9d5-68fa7698af26-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d326a79f-d42f-49d9-b595-3cfe08b349d4-attachment.png", "type": "image/png"}], "start": 1753350226048, "stop": 1753350226355}], "attachments": [{"name": "stdout", "source": "7cbfac19-c120-48c8-ac57-bcda576de558-attachment.txt", "type": "text/plain"}], "start": 1753350216613, "stop": 1753350226356, "uuid": "6f6033fe-4622-492a-8cab-abdb798bb787", "historyId": "6b1dfe37fc0819486f6cda64c12143be", "testCaseId": "6b1dfe37fc0819486f6cda64c12143be", "fullName": "testcases.test_ella.third_coupling.test_switch_to_smart_charge.TestEllaSwitchToSmartCharge#test_switch_to_smart_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_smart_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_smart_charge"}]}