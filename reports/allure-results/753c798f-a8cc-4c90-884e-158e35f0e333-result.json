{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "start": 1753349793794, "stop": 1753349806116}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c4e24887-cdb2-4be9-b484-e664810b2645-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "49872820-cd70-4532-b731-446478b7d67c-attachment.png", "type": "image/png"}], "start": 1753349806116, "stop": 1753349806410}], "start": 1753349793794, "stop": 1753349806412}, {"name": "验证响应包含Done", "status": "passed", "start": 1753349806412, "stop": 1753349806423}, {"name": "验证bluetooth已打开", "status": "passed", "start": 1753349806423, "stop": 1753349806423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1a490e11-2390-45bd-b246-c87216be48f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cb750506-9524-4f23-9768-07cfab8b271a-attachment.png", "type": "image/png"}], "start": 1753349806423, "stop": 1753349806726}], "attachments": [{"name": "stdout", "source": "734b0696-2fb8-41c5-92dd-df63733db274-attachment.txt", "type": "text/plain"}], "start": 1753349793794, "stop": 1753349806727, "uuid": "982c3391-ab12-4773-97d6-f4492f390bfe", "historyId": "6ccf50a90a4364fc2767466e6c0283fc", "testCaseId": "6ccf50a90a4364fc2767466e6c0283fc", "fullName": "testcases.test_ella.open_app.test_open_bluetooth.TestEllaCommandConcise#test_open_bluetooth", "labels": [{"name": "story", "value": "联系人控制命令 - 简洁版本"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_bluetooth"}]}