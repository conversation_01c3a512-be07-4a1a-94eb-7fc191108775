{"name": "测试close bluetooth命令", "status": "passed", "description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "steps": [{"name": "记录测试开始状态", "status": "passed", "attachments": [{"name": "Ella初始状态", "source": "cbfbe994-8d3c-46fb-9fa2-3397c577a421-attachment.png", "type": "image/png"}], "start": 1750679189720, "stop": 1750679190147}, {"name": "输入命令: close bluetooth", "status": "passed", "start": 1750679190147, "stop": 1750679192872}, {"name": "等待AI响应", "status": "passed", "start": 1750679192872, "stop": 1750679199461}, {"name": "获取并验证响应内容", "status": "passed", "attachments": [{"name": "AI响应内容", "source": "036f3be0-7e85-4f2c-84fd-6c602d76e4d3-attachment.txt", "type": "text/plain"}], "start": 1750679199461, "stop": 1750679201384}, {"name": "验证蓝牙状态", "status": "passed", "start": 1750679201384, "stop": 1750679203595}, {"name": "记录测试完成状态", "status": "passed", "attachments": [{"name": "测试完成状态", "source": "12fd13bb-675b-4a15-a5b1-03c950fcebd2-attachment.png", "type": "image/png"}], "start": 1750679203595, "stop": 1750679203912}], "attachments": [{"name": "stdout", "source": "46acd756-dc24-4e1e-ae0d-9a57749bd68f-attachment.txt", "type": "text/plain"}], "start": 1750679189720, "stop": 1750679203912, "uuid": "6d3c4e81-f878-4867-a229-55b34eac8128", "historyId": "dc95ac7c06b3ef70ff231af88ffd03a1", "testCaseId": "dc95ac7c06b3ef70ff231af88ffd03a1", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_close_bluetooth_command", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}]}