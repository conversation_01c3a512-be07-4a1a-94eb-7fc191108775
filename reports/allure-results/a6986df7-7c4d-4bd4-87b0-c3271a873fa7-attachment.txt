2025-07-22 22:06:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:51 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:51 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{182c4d3 #118 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{f32110c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t118}
    * Hist  #0
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone，状态: True
2025-07-22 22:06:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open phone
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open phone
2025-07-22 22:06:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open phone
2025-07-22 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:59 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:59 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open phone
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:07:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 22:07:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 22:07:03 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 22:07:03 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 22:07:03 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 22:07:03 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:04 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{182c4d3 #118 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{f32110c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t118}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:07:04 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:05 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:07:05 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:07:07 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open phone，已达到最大重试次数
2025-07-22 22:07:07 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:648 | 匹配到完成响应模式: done -> Done!
2025-07-22 22:07:07 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:282 | ✅ 从robot_text成功获取响应: Done!
2025-07-22 22:07:08 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-22 22:07:10 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open phone', 'Done!', '', '']'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:07:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open phone 测试完成
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open phone', 'Done!', '', '']
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:07:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:07:11 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:07:11 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
