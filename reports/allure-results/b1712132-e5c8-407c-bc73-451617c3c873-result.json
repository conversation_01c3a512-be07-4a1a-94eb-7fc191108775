{"name": "测试Switch to Low-Temp Charge能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app", "trace": "self = <testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge object at 0x00000228877939D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge object at 0x00000228877939D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9090>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "Switch to Low-Temp Charge", "attachments": [{"name": "stdout", "source": "5653bf8a-9af3-4d72-bf6e-b64b13e17b00-attachment.txt", "type": "text/plain"}], "start": 1753349218468, "stop": 1753349218468, "uuid": "5da833bc-b252-4e26-be9d-b79ea51a8a5a", "historyId": "ee231dd22ce3bf43d8c81633728f58b1", "testCaseId": "ee231dd22ce3bf43d8c81633728f58b1", "fullName": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge"}]}