{"name": "测试Switch to Low-Temp Charge能正常执行", "status": "passed", "description": "Switch to Low-Temp Charge", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Low-Temp Charge", "status": "passed", "start": 1753350174077, "stop": 1753350183030}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "23a0e066-5c0a-40f6-9eb8-bdb9e7ad6667-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "55e6bef3-8cb1-44c6-9ea8-618387dbabb0-attachment.png", "type": "image/png"}], "start": 1753350183030, "stop": 1753350183300}], "start": 1753350174077, "stop": 1753350183300}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350183300, "stop": 1753350183303}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c61dcc9b-6a54-4141-8786-90ef7579511e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e8cc0339-2d06-47c2-beac-eb42b7ec3650-attachment.png", "type": "image/png"}], "start": 1753350183303, "stop": 1753350183596}], "attachments": [{"name": "stdout", "source": "50379b56-c618-43a5-89cf-d0ebfd97d98b-attachment.txt", "type": "text/plain"}], "start": 1753350174077, "stop": 1753350183598, "uuid": "502f4f30-25b0-4e95-8e2b-14baeb91d377", "historyId": "ee231dd22ce3bf43d8c81633728f58b1", "testCaseId": "ee231dd22ce3bf43d8c81633728f58b1", "fullName": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge.TestEllaSwitchToLowtempCharge#test_switch_to_low_temp_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_low_temp_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToLowtempCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_low_temp_charge"}]}