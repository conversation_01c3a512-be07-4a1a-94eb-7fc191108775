{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open wifi", "status": "passed", "steps": [{"name": "执行命令: open wifi", "status": "passed", "start": 1753349990953, "stop": 1753350004443}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7b60dc5d-6483-4584-bff5-2eab5d771638-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a5127d1e-413c-4974-97e8-935f64e3cae2-attachment.png", "type": "image/png"}], "start": 1753350004443, "stop": 1753350004751}], "start": 1753349990953, "stop": 1753350004751}, {"name": "验证响应包含Done", "status": "passed", "start": 1753350004751, "stop": 1753350004756}, {"name": "验证wifi已打开", "status": "passed", "start": 1753350004756, "stop": 1753350004756}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3366e07f-82e6-4f25-8f6d-9c10a17dfdca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b6ffcbb3-79db-4f48-8ed7-5171a32e12db-attachment.png", "type": "image/png"}], "start": 1753350004756, "stop": 1753350005011}], "attachments": [{"name": "stdout", "source": "74d0e7b0-5fb0-4876-bb82-dde941afabe3-attachment.txt", "type": "text/plain"}], "start": 1753349990953, "stop": 1753350005012, "uuid": "308d1763-9b0a-4946-bd12-83ed7e891520", "historyId": "10b1ce60a8d05504eb6d58494ac3ec0c", "testCaseId": "10b1ce60a8d05504eb6d58494ac3ec0c", "fullName": "testcases.test_ella.open_app.test_open_wifi.TestEllaCommandConcise#test_open_wifi", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_wifi"}]}