{"name": "测试open folax能正常执行", "status": "passed", "description": "open folax", "steps": [{"name": "执行命令: open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "status": "passed", "start": 1753349954708, "stop": 1753349965176}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "83f9b842-6ee8-4afc-a6af-b247fdbbee3d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b359d4d3-9e64-4eb9-ac65-d955127ec5c9-attachment.png", "type": "image/png"}], "start": 1753349965176, "stop": 1753349965475}], "start": 1753349954708, "stop": 1753349965476}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753349965476, "stop": 1753349965480}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a1c1e2f2-bb27-46c3-ac5b-6b524f3b8afe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7c506ce1-4406-4ba5-9ce2-f9c0cecd6697-attachment.png", "type": "image/png"}], "start": 1753349965480, "stop": 1753349965809}], "attachments": [{"name": "stdout", "source": "1a645515-0ad9-4230-a55b-777be960091e-attachment.txt", "type": "text/plain"}], "start": 1753349954708, "stop": 1753349965810, "uuid": "3936c73a-b632-43ae-9bd2-eaf1bd201a47", "historyId": "665d2298fe67372a7d3111361b2a1434", "testCaseId": "665d2298fe67372a7d3111361b2a1434", "fullName": "testcases.test_ella.open_app.test_open_folax.TestEllaCommandConcise#test_open_folax", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_folax"}]}