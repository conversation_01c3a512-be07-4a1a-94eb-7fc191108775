{"name": "测试open bluetooth命令", "status": "failed", "statusDetails": {"message": "AssertionError: 未收到AI响应\nassert False", "trace": "self = <test_bluetooth_command.TestEllaBluetoothCommand object at 0x0000018BB8F1F250>, ella_app = <pages.apps.ella.main_page.EllaMainPage object at 0x0000018BB930E510>\n\n    @allure.title(\"测试open bluetooth命令\")\n    @allure.description(\"通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_bluetooth_command(self, ella_app):\n        \"\"\"测试open bluetooth命令\"\"\"\n        command = \"open bluetooth\"\n    \n        with allure.step(\"记录测试开始状态\"):\n            # 截图记录初始状态\n            screenshot_path = ella_app.screenshot(\"ella_initial_state.png\")\n            allure.attach.file(screenshot_path, name=\"Ella初始状态\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            # 记录蓝牙初始状态\n            initial_bluetooth_status = ella_app.check_bluetooth_status()\n            log.info(f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\")\n            allure.attach(\n                f\"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}\",\n                name=\"蓝牙初始状态\",\n                attachment_type=allure.attachment_type.TEXT\n            )\n    \n        with allure.step(f\"输入命令: {command}\"):\n            # 执行文本命令\n            success = ella_app.execute_text_command(command)\n            assert success, f\"执行命令失败: {command}\"\n    \n            # 截图记录命令输入后的状态\n            screenshot_path = ella_app.screenshot(\"ella_command_sent.png\")\n            allure.attach.file(screenshot_path, name=\"命令发送后\",\n                             attachment_type=allure.attachment_type.PNG)\n    \n            log.info(f\"✅ 成功执行命令: {command}\")\n    \n        with allure.step(\"等待AI响应\"):\n            # 等待AI响应\n            response_received = ella_app.wait_for_response(timeout=15)\n>           assert response_received, \"未收到AI响应\"\nE           AssertionError: 未收到AI响应\nE           assert False\n\ntestcases\\test_ella\\test_bluetooth_command.py:106: AssertionError"}, "description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "steps": [{"name": "记录测试开始状态", "status": "passed", "attachments": [{"name": "Ella初始状态", "source": "2152eda3-031b-440c-a0f7-2628cda97e6b-attachment.png", "type": "image/png"}, {"name": "蓝牙初始状态", "source": "35b8ba6d-2c29-45c7-a913-6d8c6e11f134-attachment.txt", "type": "text/plain"}], "start": 1750679165655, "stop": 1750679166183}, {"name": "输入命令: open bluetooth", "status": "passed", "attachments": [{"name": "命令发送后", "source": "c2b8f069-e5b6-4250-93f1-44b2d4eaf6ae-attachment.png", "type": "image/png"}], "start": 1750679166183, "stop": 1750679168720}, {"name": "等待AI响应", "status": "failed", "statusDetails": {"message": "AssertionError: 未收到AI响应\nassert False\n", "trace": "  File \"D:\\PythonProject\\app_test\\testcases\\test_ella\\test_bluetooth_command.py\", line 106, in test_open_bluetooth_command\n    assert response_received, \"未收到AI响应\"\n"}, "start": 1750679168720, "stop": 1750679186239}], "attachments": [{"name": "stdout", "source": "06a7cef5-00f5-44ed-ada3-6b7e3043a51d-attachment.txt", "type": "text/plain"}], "start": 1750679165655, "stop": 1750679186246, "uuid": "d11b037e-9be1-4b87-9763-dcca5a448f46", "historyId": "e25c945dfd6c1c874b0927b961b40416", "testCaseId": "e25c945dfd6c1c874b0927b961b40416", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_open_bluetooth_command", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}]}