{"name": "测试蓝牙状态查询命令", "status": "passed", "description": "通过Ella查询蓝牙状态", "steps": [{"name": "测试命令: bluetooth status", "status": "passed", "start": 1750679206967, "stop": 1750679304884}, {"name": "测试命令: is bluetooth on", "status": "passed", "start": 1750679304884, "stop": 1750679305201}, {"name": "测试命令: check bluetooth", "status": "passed", "start": 1750679305202, "stop": 1750679305490}], "attachments": [{"name": "stdout", "source": "f875a7c7-076c-47c6-a6bd-d644f35f5802-attachment.txt", "type": "text/plain"}], "start": 1750679206967, "stop": 1750679305491, "uuid": "044fafdd-331e-46db-9dd7-9bd51f0840e8", "historyId": "51d696b831178c19578ea68b891a77c8", "testCaseId": "51d696b831178c19578ea68b891a77c8", "fullName": "testcases.test_ella.test_bluetooth_command.TestEllaBluetoothCommand#test_bluetooth_status_query", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "severity", "value": "minor"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "30156-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_bluetooth_command"}]}