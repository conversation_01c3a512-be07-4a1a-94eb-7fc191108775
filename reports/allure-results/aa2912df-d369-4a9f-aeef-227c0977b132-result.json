{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open app", "status": "passed", "steps": [{"name": "执行命令: open app", "status": "passed", "start": 1753349779966, "stop": 1753349788890}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3cb707d4-2aaf-48a2-b30b-c4f28e048fd5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4dda5137-5643-4b8b-b1a6-db6893c64d24-attachment.png", "type": "image/png"}], "start": 1753349788890, "stop": 1753349789648}], "start": 1753349779966, "stop": 1753349789649}, {"name": "验证响应包含Done", "status": "passed", "start": 1753349789649, "stop": 1753349789654}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ba393ad1-701c-4c00-93e9-df936407552a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d660c2b1-5ed5-4d07-8c63-c6f84d2878d4-attachment.png", "type": "image/png"}], "start": 1753349789654, "stop": 1753349789971}], "attachments": [{"name": "stdout", "source": "e7335be5-1c75-4413-9bde-16db795ec6a1-attachment.txt", "type": "text/plain"}], "start": 1753349779966, "stop": 1753349789972, "uuid": "3c07c0d9-ae1f-4619-b1bb-17ff02bbf697", "historyId": "99f21cb6aab54e7c38798c0bd39b97d0", "testCaseId": "99f21cb6aab54e7c38798c0bd39b97d0", "fullName": "testcases.test_ella.open_app.test_open_app.TestEllaCommandConcise#test_open_app", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_app"}]}