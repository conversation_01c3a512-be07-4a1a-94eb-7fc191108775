{"name": "测试order a burger能正常执行", "status": "passed", "description": "order a burger", "steps": [{"name": "执行命令: order a burger", "status": "passed", "steps": [{"name": "执行命令: order a burger", "status": "passed", "start": 1753350056024, "stop": 1753350065348}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "229b976d-020b-4c26-8de4-3935903be62a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "43bc481b-0297-45c1-8f44-fb25900298cf-attachment.png", "type": "image/png"}], "start": 1753350065348, "stop": 1753350065618}], "start": 1753350056024, "stop": 1753350065619}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350065619, "stop": 1753350065622}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a037b5e2-1a12-40e4-81e0-eea28ddfff70-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "55e62c3c-5464-47b7-b464-8cf3beb4a05b-attachment.png", "type": "image/png"}], "start": 1753350065622, "stop": 1753350065896}], "attachments": [{"name": "stdout", "source": "701f4d8d-5637-428e-840d-28ada46d8cc8-attachment.txt", "type": "text/plain"}], "start": 1753350056024, "stop": 1753350065897, "uuid": "b54e51e4-ed1a-487f-bdd7-85166a75f7b0", "historyId": "09f397887d36f3ef1e86e3c78272f1d6", "testCaseId": "09f397887d36f3ef1e86e3c78272f1d6", "fullName": "testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise#test_order_a_burger", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_burger"}]}