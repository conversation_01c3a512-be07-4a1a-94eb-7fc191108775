{"name": "open clock", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "trace": "self = <testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise object at 0x0000028F12381BD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F12383ED0>\n\n    @allure.title(\"open clock\")\n    @allure.description(\"使用open clock命令，验证响应包含Done且实际打开clock命令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_clock(self, ella_app):\n        \"\"\"测试open clock命令 - 简洁版本\"\"\"\n        command = \"open clock\"\n    \n        app_name = 'clock'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            expected_text =[\"done\"]\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\open_app\\test_open_clock.py:31: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise object at 0x0000028F12381BD0>\nexpected_text = ['done'], response_text = ['open clock', '', '', '', '5:37 Stopwatch Alarm World Clock Timer Stopwatch']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:566: AssertionError"}, "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "steps": [{"name": "执行命令: open clock", "status": "passed", "steps": [{"name": "执行命令: open clock", "status": "passed", "start": 1753349829957, "stop": 1753349843016}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e1178801-d302-474d-8075-8fa8de0f7e83-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b0a43cc4-452f-40c2-be2a-4349fd72fe4e-attachment.png", "type": "image/png"}], "start": 1753349843016, "stop": 1753349843240}], "start": 1753349829957, "stop": 1753349843240}, {"name": "验证响应包含Done", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['done']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_clock.py\", line 31, in test_open_clock\n    result = self.verify_expected_in_response(expected_text, response_text)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 566, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1753349843241, "stop": 1753349843247}], "attachments": [{"name": "stdout", "source": "8c9ad64c-2186-4498-82f6-28534ca07559-attachment.txt", "type": "text/plain"}], "start": 1753349829956, "stop": 1753349843254, "uuid": "514e313b-5f6d-4433-a77d-4d1593de3ed5", "historyId": "757ca38b1dbab556e91ff727b60faeb4", "testCaseId": "757ca38b1dbab556e91ff727b60faeb4", "fullName": "testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise#test_open_clock", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "open clock"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_clock"}]}