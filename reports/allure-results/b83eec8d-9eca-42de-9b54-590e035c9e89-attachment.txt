2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:04:21 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:04:21 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:04:21 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-22 22:04:21 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:21 | INFO | core.base_driver:start_app:233 | 启动应用: com.transsion.aivoiceassistant
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-22 22:04:23 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-22 22:04:25 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-22 22:04:26 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:26 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:26 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-07-22 22:04:26 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:04:27 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:28 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:29 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:29 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:29 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:580 | 清空输入框...
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:30 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:586 | 主输入框已清空
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-22 22:04:30 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-22 22:04:31 | INFO | pages.apps.ella.history.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:31 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:32 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-22 22:04:32 | INFO | pages.apps.ella.history.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:send_command:657 | 发送命令
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:34 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:send_command:664 | ✅ 命令发送成功
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-22 22:04:34 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-07-22 22:04:34 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:wait_for_response:2008 | 初始元素数量: 13
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:_check_tts_button_appeared:2323 | 检测到TTS播放按钮
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-22 22:04:40 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_response_received.png
2025-07-22 22:04:40 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:get_response_text_smart:2428 | 智能获取响应文本...
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:get_response_text:2452 | 获取AI响应文本
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2468 | 页面上所有文本元素数量: 13
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:_is_ai_response:2286 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2476 | 找到AI响应: Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2489 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:verify_command_in_response:2605 | 验证响应是否包含命令: open bluetooth
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:verify_command_in_response:2635 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-22 22:04:44 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status_smart:2404 | 智能检查蓝牙状态...
2025-07-22 22:04:44 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-22 22:04:45 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_test_completed.png
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-22 22:04:45 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:45 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
