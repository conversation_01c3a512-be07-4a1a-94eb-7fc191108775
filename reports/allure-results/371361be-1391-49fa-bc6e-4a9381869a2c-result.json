{"name": "测试download basketball能正常执行", "status": "passed", "description": "download basketball", "steps": [{"name": "执行命令: download basketball", "status": "passed", "steps": [{"name": "执行命令: download basketball", "status": "passed", "start": 1753350042366, "stop": 1753350051454}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1fe1b7e3-4ed0-4bde-9925-bd9c6b7e81ab-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fddf019b-58be-48c3-9307-245e6bf7915e-attachment.png", "type": "image/png"}], "start": 1753350051454, "stop": 1753350051777}], "start": 1753350042366, "stop": 1753350051777}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350051777, "stop": 1753350051780}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0ab1d551-54e0-448c-b53c-bcfb468a46f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e74624db-5147-4486-852d-d193d2216da8-attachment.png", "type": "image/png"}], "start": 1753350051780, "stop": 1753350052142}], "attachments": [{"name": "stdout", "source": "af58e9b3-3817-459b-a702-0d8e8fe71732-attachment.txt", "type": "text/plain"}], "start": 1753350042366, "stop": 1753350052142, "uuid": "3cc415d2-6d2d-4caa-80ac-bf5662f12369", "historyId": "6f2c4144233271771cdd01a5c48ea3ca", "testCaseId": "6f2c4144233271771cdd01a5c48ea3ca", "fullName": "testcases.test_ella.third_coupling.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_basketball"}]}