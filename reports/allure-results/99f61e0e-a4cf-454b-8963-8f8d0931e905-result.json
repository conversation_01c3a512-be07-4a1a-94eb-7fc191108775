{"name": "测试download app能正常执行", "status": "passed", "description": "download app", "steps": [{"name": "执行命令: download app", "status": "passed", "steps": [{"name": "执行命令: download app", "status": "passed", "start": 1753350023658, "stop": 1753350037885}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4a58297c-120a-4840-8189-907972df01a6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f5f9051f-f7ad-47d3-a3dd-88a205fb7fb0-attachment.png", "type": "image/png"}], "start": 1753350037885, "stop": 1753350038195}], "start": 1753350023658, "stop": 1753350038196}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350038196, "stop": 1753350038204}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a460f7fd-c2b6-4690-93cc-34f86aee2b6a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a84a3411-c526-4ece-8b45-d79d24dccc99-attachment.png", "type": "image/png"}], "start": 1753350038204, "stop": 1753350038532}], "attachments": [{"name": "stdout", "source": "41506eb4-656f-43ad-9a75-7f7a6b89f846-attachment.txt", "type": "text/plain"}], "start": 1753350023658, "stop": 1753350038533, "uuid": "b904eb4c-6b3e-4643-a0c9-e2ede003a7d0", "historyId": "578e52c6d5e868d5464682b454971c51", "testCaseId": "578e52c6d5e868d5464682b454971c51", "fullName": "testcases.test_ella.third_coupling.test_download_app.TestEllaDownloadApp#test_download_app", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_app"}, {"name": "subSuite", "value": "TestEllaDownloadApp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_app"}]}