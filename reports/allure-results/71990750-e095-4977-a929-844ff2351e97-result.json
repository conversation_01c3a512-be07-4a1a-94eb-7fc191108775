{"name": "测试open facebook能正常执行", "status": "passed", "description": "open facebook", "steps": [{"name": "执行命令: open facebook", "status": "passed", "steps": [{"name": "执行命令: open facebook", "status": "passed", "start": 1753349915484, "stop": 1753349930489}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "37502f35-7cfd-4662-bcf5-66578a58f04b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "89451c99-f436-478e-9c43-ca18e1f1b826-attachment.png", "type": "image/png"}], "start": 1753349930489, "stop": 1753349930799}], "start": 1753349915484, "stop": 1753349930800}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753349930800, "stop": 1753349930808}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "73e04826-d035-45ee-8601-871b4ad91dd9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e8a707ea-2833-4642-a1f2-56694376b0d3-attachment.png", "type": "image/png"}], "start": 1753349930808, "stop": 1753349931110}], "attachments": [{"name": "stdout", "source": "ddbd1fe9-7a33-420f-b77c-9a97f157c7b9-attachment.txt", "type": "text/plain"}], "start": 1753349915484, "stop": 1753349931111, "uuid": "92a5c456-c925-44b2-a9f7-d6b32ce33f4f", "historyId": "60e3b7ad9599b952591bccdea8d4bf1b", "testCaseId": "60e3b7ad9599b952591bccdea8d4bf1b", "fullName": "testcases.test_ella.open_app.test_open_facebook.TestEllaCommandConcise#test_open_facebook", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_facebook"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_facebook"}]}