{"name": "测试smart charge能正常执行", "status": "passed", "description": "smart charge", "steps": [{"name": "执行命令: smart charge", "status": "passed", "steps": [{"name": "执行命令: smart charge", "status": "passed", "start": 1753350084098, "stop": 1753350093709}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "83ea56ee-a9af-474c-b9d3-aa34353c361e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7e6fb9a1-50a9-4894-be3a-7f7e318ca86b-attachment.png", "type": "image/png"}], "start": 1753350093709, "stop": 1753350094033}], "start": 1753350084098, "stop": 1753350094033}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350094033, "stop": 1753350094037}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5851243c-c220-49d0-a5f9-d5124b803a64-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3bb94b36-d41e-4051-aeb6-c366dd11e255-attachment.png", "type": "image/png"}], "start": 1753350094037, "stop": 1753350094351}], "attachments": [{"name": "stdout", "source": "3df7c5e3-6259-45d0-a3ad-d84e9a4f9c58-attachment.txt", "type": "text/plain"}], "start": 1753350084098, "stop": 1753350094352, "uuid": "bd831574-1b3f-4d45-98a3-e291cb6dca50", "historyId": "547e7a68915f4e05c1ef0182ac744dc1", "testCaseId": "547e7a68915f4e05c1ef0182ac744dc1", "fullName": "testcases.test_ella.third_coupling.test_smart_charge.TestEllaSmartCharge#test_smart_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_smart_charge"}, {"name": "subSuite", "value": "TestEllaSmartCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_smart_charge"}]}