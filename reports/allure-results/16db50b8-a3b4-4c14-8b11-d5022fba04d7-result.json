{"name": "测试disable magic voice changer能正常执行", "status": "passed", "description": "disable magic voice changer", "steps": [{"name": "执行命令: disable magic voice changer", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "status": "passed", "start": 1753350008792, "stop": 1753350019047}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ade6169f-c56d-463f-9cc4-6494db537dfe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a24356f0-59d7-40b1-82bb-2b992497bc05-attachment.png", "type": "image/png"}], "start": 1753350019047, "stop": 1753350019431}], "start": 1753350008792, "stop": 1753350019432}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350019432, "stop": 1753350019437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f3bda597-9e38-4df7-accb-15314c2601a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2ac21eec-dc30-46ba-8f43-4cc9d4b8f895-attachment.png", "type": "image/png"}], "start": 1753350019437, "stop": 1753350019776}], "attachments": [{"name": "stdout", "source": "91a5514a-5bdc-48cb-a5b4-386c2d6871e3-attachment.txt", "type": "text/plain"}], "start": 1753350008791, "stop": 1753350019777, "uuid": "743cceb4-71b4-44e8-a157-3161b853397f", "historyId": "8dd76fb2f1440324f5c0805128da5079", "testCaseId": "8dd76fb2f1440324f5c0805128da5079", "fullName": "testcases.test_ella.third_coupling.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_disable_magic_voice_changer"}]}