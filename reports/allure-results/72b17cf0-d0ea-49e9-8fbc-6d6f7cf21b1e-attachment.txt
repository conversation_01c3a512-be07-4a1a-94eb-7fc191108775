2025-07-24 17:37:23 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-07-24 17:37:23 | INFO | pages.apps.ella.dialogue_page:start_app:128 | 启动Ella应用
2025-07-24 17:37:23 | INFO | pages.apps.ella.dialogue_page:start_app:136 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:27 | INFO | pages.apps.ella.dialogue_page:_check_app_started:194 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-24 17:37:27 | INFO | pages.apps.ella.dialogue_page:start_app:141 | ✅ Ella应用启动成功（指定Activity）
2025-07-24 17:37:27 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:215 | 等待Ella页面加载完成 (超时: 15秒)
2025-07-24 17:37:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-24 17:37:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:37:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:37:27 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:219 | ✅ 输入框已出现，页面加载完成
2025-07-24 17:37:27 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-24 17:37:27 | INFO | pages.base.app_detector:check_contacts_app_opened:246 | 检查联系人应用状态
2025-07-24 17:37:28 | INFO | pages.base.app_detector:check_contacts_app_opened:348 | 未检测到活跃的联系人应用
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: False
2025-07-24 17:37:28 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:37:28 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:37:28 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:37:28 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:28 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:37:28 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-24 17:37:28 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:37:29 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:37:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:37:29 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-24 17:37:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-24 17:37:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-24 17:37:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-24 17:37:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:37:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-24 17:37:30 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-24 17:37:30 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-24 17:37:30 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-24 17:37:30 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-24 17:37:33 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-24 17:37:33 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:37:33 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:37:33 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.sh.smart.caller
2025-07-24 17:37:33 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-24 17:37:33 | WARNING | pages.base.system_status_checker:ensure_ella_process:504 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-24 17:37:33 | WARNING | pages.apps.ella.dialogue_page:ensure_on_chat_page:443 | 当前不在Ella进程，尝试返回Ella
2025-07-24 17:37:33 | INFO | pages.apps.ella.dialogue_page:return_to_ella_app:250 | 尝试返回Ella应用
2025-07-24 17:37:33 | INFO | pages.apps.ella.dialogue_page:start_app:128 | 启动Ella应用
2025-07-24 17:37:33 | INFO | pages.apps.ella.dialogue_page:start_app:136 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:_check_app_started:194 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:start_app:141 | ✅ Ella应用启动成功（指定Activity）
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:215 | 等待Ella页面加载完成 (超时: 10秒)
2025-07-24 17:37:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 10秒
2025-07-24 17:37:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-24 17:37:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:219 | ✅ 输入框已出现，页面加载完成
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:return_to_ella_app:255 | ✅ 成功返回Ella应用
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:37:37 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-24 17:37:37 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-24 17:37:37 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:439 | 确保在对话页面...
2025-07-24 17:37:37 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:37:37 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:37:37 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:37 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:37:38 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:450 | ✅ 已在对话页面
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:318 | 从check_area节点获取响应文本
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:330 | 从check_area直接获取文本: Done!
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-24 17:37:38 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-24 17:37:38 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-24 17:37:38 | INFO | pages.base.app_detector:check_contacts_app_opened_smart:669 | 使用智能方法检查联系人应用状态
2025-07-24 17:37:38 | INFO | pages.base.app_detector:check_contacts_app_opened:246 | 检查联系人应用状态
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened:348 | 未检测到活跃的联系人应用
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened_smart:678 | 主要方法未检测到，尝试替代方法
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened_alternative:543 | 使用替代方法检查联系人应用状态
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened_alternative:623 | 所有替代方法都未检测到联系人应用
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened_smart:685 | 尝试简单包名检查
2025-07-24 17:37:39 | INFO | pages.base.app_detector:check_contacts_app_opened_smart:691 | 所有方法都未检测到联系人应用
2025-07-24 17:37:39 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=False, 最终状态=False, 响应='Done!'
2025-07-24 17:37:39 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=False, 最终=False
2025-07-24 17:37:39 | WARNING | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:213 | 自动检测失败，尝试手动验证Dalier应用状态
2025-07-24 17:37:39 | INFO | pages.apps.ella.ella_contact_command_handler:manual_check_dalier_app:120 | 执行手动Dalier应用检查
2025-07-24 17:37:40 | INFO | pages.apps.ella.ella_contact_command_handler:_check_current_app_package:146 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-24 17:37:40 | INFO | pages.apps.ella.ella_contact_command_handler:_check_with_adb:167 | 顶层Activity信息（前200字符）: TASK null id=2 userId=0 displayId=0(type=INTERNAL)
  ACTIVITY com.transsion.hilauncher/com.android.launcher3.uioverrides.QuickstepLauncher 622ea14 pid=2429 userId=0 uid=10153 displayId=0(type=INTERNAL
2025-07-24 17:37:40 | INFO | pages.apps.ella.ella_contact_command_handler:_check_with_adb:170 | ✅ 通过顶层Activity检测到Dalier
2025-07-24 17:37:40 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:217 | 手动检查结果: True
2025-07-24 17:37:40 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:220 | ✅ 手动检查确认Dalier应用已打开
2025-07-24 17:37:40 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:508 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-24 17:37:40 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:552 | ✅ 响应包含期望内容: 'done'
2025-07-24 17:37:40 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:560 | 🎉 所有期望内容都已找到 (1/1)
2025-07-24 17:37:40 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-24 17:37:40 | INFO | pages.apps.ella.dialogue_page:stop_app:235 | 停止Ella应用
2025-07-24 17:37:40 | WARNING | pages.apps.ella.dialogue_page:stop_app:241 | ⚠️ 停止Ella应用可能失败
