{"name": "测试order a burger能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app", "trace": "self = <testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise object at 0x0000022887792FD0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise object at 0x0000022887792FD0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "order a burger", "attachments": [{"name": "stdout", "source": "35e17cb7-5c88-4e5c-b52d-651e262bb3da-attachment.txt", "type": "text/plain"}], "start": 1753349215075, "stop": 1753349215075, "uuid": "7a3576aa-84fe-4352-ad0b-547ec177fa89", "historyId": "09f397887d36f3ef1e86e3c78272f1d6", "testCaseId": "09f397887d36f3ef1e86e3c78272f1d6", "fullName": "testcases.test_ella.third_coupling.test_order_a_burger.TestEllaCommandConcise#test_order_a_burger", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_order_a_burger"}]}