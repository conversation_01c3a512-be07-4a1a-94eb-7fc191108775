{"name": "测试switch charging modes能正常执行", "status": "passed", "description": "switch charging modes", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "steps": [{"name": "执行命令: switch charging modes", "status": "passed", "start": 1753350098134, "stop": 1753350108970}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a862e177-64f2-41a9-aae6-a3540cdd5c11-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "664a82fc-bba2-43b5-bafb-100d144d7c51-attachment.png", "type": "image/png"}], "start": 1753350108970, "stop": 1753350109280}], "start": 1753350098134, "stop": 1753350109281}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350109281, "stop": 1753350109284}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d26d378e-5e33-47db-846b-303387dd984d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b049897a-4790-49eb-a2c1-76f9de48bfe1-attachment.png", "type": "image/png"}], "start": 1753350109284, "stop": 1753350109623}], "attachments": [{"name": "stdout", "source": "718e8fed-c781-4be2-bb59-fac73d4e0465-attachment.txt", "type": "text/plain"}], "start": 1753350098134, "stop": 1753350109624, "uuid": "4471156d-8457-474e-b13d-b8313f0bbafa", "historyId": "eab84c8d9454644261f5e07341993e63", "testCaseId": "eab84c8d9454644261f5e07341993e63", "fullName": "testcases.test_ella.third_coupling.test_switch_charging_modes.TestEllaSwitchChargingModes#test_switch_charging_modes", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_charging_modes"}, {"name": "subSuite", "value": "TestEllaSwitchChargingModes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_charging_modes"}]}