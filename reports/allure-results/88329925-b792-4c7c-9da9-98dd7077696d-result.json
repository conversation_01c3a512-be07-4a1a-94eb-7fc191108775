{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open contact", "status": "passed", "steps": [{"name": "执行联系人命令: open contact", "status": "passed", "start": 1753193187796, "stop": 1753193196526}, {"name": "验证联系人命令结果", "status": "passed", "start": 1753193196526, "stop": 1753193196526}], "start": 1753193187796, "stop": 1753193196526}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193196526, "stop": 1753193196530}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1753193196530, "stop": 1753193196530}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dc6987c0-abf7-4e17-b9d6-051f4a9f3aaf-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "022871d8-1ef9-425f-8791-47ac17346474-attachment.png", "type": "image/png"}], "start": 1753193196530, "stop": 1753193196883}], "attachments": [{"name": "stdout", "source": "a86fcb75-f080-4dbb-8882-a38a70fc6205-attachment.txt", "type": "text/plain"}], "start": 1753193187796, "stop": 1753193196884, "uuid": "2889ed6d-0d06-49e7-9c06-ba7b3505151d", "historyId": "9f28736d78e3cebb5eddb51e5e9dc3c8", "testCaseId": "9f28736d78e3cebb5eddb51e5e9dc3c8", "fullName": "testcases.test_ella.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_contact"}]}