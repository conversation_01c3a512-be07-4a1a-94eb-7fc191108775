{"name": "测试open contact命令", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app", "trace": "self = <testcases.test_ella.open_app.test_open_contact.TestEllaContactCommandConcise object at 0x0000022887792210>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.open_app.test_open_contact.TestEllaContactCommandConcise object at 0x0000022887792210>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887790CD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "attachments": [{"name": "stdout", "source": "63d3bddd-d709-4cc8-80da-0a3c03698188-attachment.txt", "type": "text/plain"}], "start": 1753349210144, "stop": 1753349210144, "uuid": "4aa02582-4a16-44df-a4ea-35110fd728dd", "historyId": "89f51b14bb23a377ba1e71ef94b4b445", "testCaseId": "89f51b14bb23a377ba1e71ef94b4b445", "fullName": "testcases.test_ella.open_app.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_contact"}]}