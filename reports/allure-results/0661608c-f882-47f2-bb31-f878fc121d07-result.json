{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open phone", "status": "passed", "steps": [{"name": "执行命令: open phone", "status": "passed", "start": 1753193217024, "stop": 1753193230197}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dff4434d-4616-4d47-aa2f-146c8433ac01-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8c247015-6b63-4b29-8da1-ebaabc13234c-attachment.png", "type": "image/png"}], "start": 1753193230197, "stop": 1753193230658}], "start": 1753193217024, "stop": 1753193230659}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193230659, "stop": 1753193230662}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1753193230662, "stop": 1753193230662}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c685d80b-08fa-4c88-b8bb-d35da9d3be50-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3658f444-b416-48d4-ae41-a34914c46267-attachment.png", "type": "image/png"}], "start": 1753193230662, "stop": 1753193231001}], "attachments": [{"name": "stdout", "source": "a6986df7-7c4d-4bd4-87b0-c3271a873fa7-attachment.txt", "type": "text/plain"}], "start": 1753193217024, "stop": 1753193231002, "uuid": "8f1d8d10-38bd-4b00-9657-394e46406708", "historyId": "93f6dcb971c765f4ba78d47e1e20e8f1", "testCaseId": "93f6dcb971c765f4ba78d47e1e20e8f1", "fullName": "testcases.test_ella.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_phone"}]}