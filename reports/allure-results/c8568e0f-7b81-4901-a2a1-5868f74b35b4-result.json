{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "status": "passed", "start": 1753193168740, "stop": 1753193181336}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b30c603d-0846-4612-a737-16dc53826a29-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a338a9ba-2505-4a99-b9c4-399b0826ac0b-attachment.png", "type": "image/png"}], "start": 1753193181336, "stop": 1753193181661}], "start": 1753193168740, "stop": 1753193181662}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193181662, "stop": 1753193181669}, {"name": "验证bluetooth已打开", "status": "passed", "start": 1753193181669, "stop": 1753193181669}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da03fa1d-2130-4197-b1a7-bd7eb45d7068-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be04b678-8a3b-4200-816b-19fdb7691dc9-attachment.png", "type": "image/png"}], "start": 1753193181669, "stop": 1753193181971}], "attachments": [{"name": "stdout", "source": "7c8d2122-dbce-4b59-8628-0ec4d419d6d8-attachment.txt", "type": "text/plain"}], "start": 1753193168740, "stop": 1753193181972, "uuid": "e2108d70-0818-4953-a1fc-5b8e34dca86b", "historyId": "e370b52aee9ba35516a7fb36220c8d53", "testCaseId": "e370b52aee9ba35516a7fb36220c8d53", "fullName": "testcases.test_ella.test_open_bluetooth.TestEllaCommandConcise#test_open_bluetooth", "labels": [{"name": "story", "value": "联系人控制命令 - 简洁版本"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_bluetooth"}]}