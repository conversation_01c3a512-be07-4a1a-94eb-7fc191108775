{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open contact", "status": "passed", "steps": [{"name": "执行联系人命令: open contact", "status": "passed", "start": 1753349847621, "stop": 1753349859803}, {"name": "验证联系人命令结果", "status": "passed", "start": 1753349859803, "stop": 1753349860234}], "start": 1753349847621, "stop": 1753349860234}, {"name": "验证响应包含Done", "status": "passed", "start": 1753349860234, "stop": 1753349860238}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1753349860238, "stop": 1753349860238}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48e8314a-afbb-4eb9-aa72-683a7927a262-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "67c5fa7d-77f0-4196-a488-ad1bf9c21370-attachment.png", "type": "image/png"}], "start": 1753349860239, "stop": 1753349860614}], "attachments": [{"name": "stdout", "source": "72b17cf0-d0ea-49e9-8fbc-6d6f7cf21b1e-attachment.txt", "type": "text/plain"}], "start": 1753349847621, "stop": 1753349860616, "uuid": "be0e38a0-0c07-4095-9a1c-c3a56be10d73", "historyId": "89f51b14bb23a377ba1e71ef94b4b445", "testCaseId": "89f51b14bb23a377ba1e71ef94b4b445", "fullName": "testcases.test_ella.open_app.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "labels": [{"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_contact"}]}