{"name": "测试switch to flash notification能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "trace": "self = <testcases.test_ella.third_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification object at 0x0000028F12383610>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F127BB9D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_switch_to_flash_notification(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含在期望中\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\third_coupling\\test_switch_to_flash_notification.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.third_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification object at 0x0000028F12383610>\nexpected_text = ['Done']\nresponse_text = ['', '', '', '', '5:42 Digital Assistant App Default digital assistant app Ella Use text from screen Allow the assist ...he screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:566: AssertionError"}, "description": "switch to flash notification", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "steps": [{"name": "执行命令: switch to flash notification", "status": "passed", "start": 1753350141136, "stop": 1753350155292}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3a45ece4-593c-4dd7-b6b7-77c3c5d87458-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "13b2da4f-1ca0-4b56-9598-e6611131f724-attachment.png", "type": "image/png"}], "start": 1753350155293, "stop": 1753350155613}], "start": 1753350141136, "stop": 1753350155614}, {"name": "验证响应包含在期望中", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\third_coupling\\test_switch_to_flash_notification.py\", line 32, in test_switch_to_flash_notification\n    result = self.verify_expected_in_response(expected_text, response_text)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 566, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1753350155614, "stop": 1753350155620}], "attachments": [{"name": "stdout", "source": "8cc65cd6-8a48-430b-b5cc-5efbcf48480c-attachment.txt", "type": "text/plain"}], "start": 1753350141136, "stop": 1753350155622, "uuid": "3bff8554-7c70-434c-a5d1-027b1fc26270", "historyId": "ed459b5d14edcd2b6a73764bc41c7e2d", "testCaseId": "ed459b5d14edcd2b6a73764bc41c7e2d", "fullName": "testcases.test_ella.third_coupling.test_switch_to_flash_notification.TestEllaSwitchToFlashNotification#test_switch_to_flash_notification", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_flash_notification"}, {"name": "subSuite", "value": "TestEllaSwitchToFlashNotification"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_flash_notification"}]}