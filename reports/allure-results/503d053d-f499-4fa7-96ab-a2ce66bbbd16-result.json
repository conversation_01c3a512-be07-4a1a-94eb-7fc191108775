{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open app", "status": "passed", "steps": [{"name": "执行命令: open app", "status": "passed", "start": 1753193150076, "stop": 1753193161651}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "863eec4a-d602-4d44-b3b8-112e5d2773f4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1f4e71c1-17af-4d49-a17c-fc10c50e703b-attachment.png", "type": "image/png"}], "start": 1753193161651, "stop": 1753193162544}], "start": 1753193150076, "stop": 1753193162545}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193162545, "stop": 1753193162548}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "07380264-5c09-4abc-8907-5ce42eb40d6f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c0dd605a-8f8b-4131-bfbc-1447047f8de6-attachment.png", "type": "image/png"}], "start": 1753193162548, "stop": 1753193162898}], "attachments": [{"name": "stdout", "source": "c55300c5-e762-4e74-ad68-************-attachment.txt", "type": "text/plain"}], "start": 1753193150076, "stop": 1753193162899, "uuid": "f8904593-b095-4676-aa4a-4323407bcefb", "historyId": "a972283fef29b51b6464e93f4a297bde", "testCaseId": "a972283fef29b51b6464e93f4a297bde", "fullName": "testcases.test_ella.test_open_app.TestEllaCommandConcise#test_open_app", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_app"}]}