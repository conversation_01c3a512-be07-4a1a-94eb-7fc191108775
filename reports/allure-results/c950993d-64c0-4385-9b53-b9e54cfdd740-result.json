{"name": "测试open flashlight", "status": "failed", "statusDetails": {"message": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False", "trace": "self = <testcases.test_ella.open_app.test_open_flashlight.TestEllaCommandConcise object at 0x0000028F123825D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F12380F50>\n\n    @allure.title(\"测试open flashlight\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_flashlight(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open flashlight\"\n    \n        app_name = 'flashlight'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            expected_text =[\"flashlight\"]\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_flashlight.py:35: AssertionError"}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "start": 1753349935010, "stop": 1753349950183}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7cc1d61e-35a3-4a32-8627-623902290e4a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a0e68963-0076-4625-9e14-ef9c5d9c9060-attachment.png", "type": "image/png"}], "start": 1753349950183, "stop": 1753349950472}], "start": 1753349935010, "stop": 1753349950473}, {"name": "验证响应包含Done", "status": "passed", "start": 1753349950473, "stop": 1753349950477}, {"name": "验证flashlight已打开", "status": "failed", "statusDetails": {"message": "AssertionError: flashlight: 初始=False, 最终=False, 响应='['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_flashlight.py\", line 35, in test_open_flashlight\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1753349950477, "stop": 1753349950477}], "attachments": [{"name": "stdout", "source": "361b6a02-c839-432a-b381-54d6efdc1290-attachment.txt", "type": "text/plain"}], "start": 1753349935010, "stop": 1753349950479, "uuid": "ab3fa5fa-62ae-4afc-a3dc-e0a416b2af70", "historyId": "4c3ac983971198f129e754475a2ddba8", "testCaseId": "4c3ac983971198f129e754475a2ddba8", "fullName": "testcases.test_ella.open_app.test_open_flashlight.TestEllaCommandConcise#test_open_flashlight", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_flashlight"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_flashlight"}]}