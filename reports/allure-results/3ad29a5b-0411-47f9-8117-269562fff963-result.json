{"name": "测试Switch to Hyper Charge能正常执行", "status": "passed", "description": "Switch to Hyper Charge", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "steps": [{"name": "执行命令: Switch to Hyper Charge", "status": "passed", "start": 1753350159949, "stop": 1753350169540}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a79a1f24-4d08-4a03-9ddd-6369852db096-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a22d5910-9ae0-4a6b-9602-bba5c73aed8c-attachment.png", "type": "image/png"}], "start": 1753350169540, "stop": 1753350169918}], "start": 1753350159949, "stop": 1753350169919}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350169919, "stop": 1753350169925}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4fcc70a7-79b9-409a-a01b-306e64ab5934-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bf0f85bb-a014-47a1-8b52-80e4f2d28d2e-attachment.png", "type": "image/png"}], "start": 1753350169925, "stop": 1753350170282}], "attachments": [{"name": "stdout", "source": "7f1f6c06-1cf7-42f0-9c32-bc4a57547525-attachment.txt", "type": "text/plain"}], "start": 1753350159949, "stop": 1753350170283, "uuid": "7913fbe1-1810-4aee-8ba6-c1325b5c6bd6", "historyId": "cc16e6af6c17da447c32eeb71c66d71c", "testCaseId": "cc16e6af6c17da447c32eeb71c66d71c", "fullName": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge.TestEllaSwitchToHyperCharge#test_switch_to_hyper_charge", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_hyper_charge"}, {"name": "subSuite", "value": "TestEllaSwitchToHyperCharge"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_hyper_charge"}]}