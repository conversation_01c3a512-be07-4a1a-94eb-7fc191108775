{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDAAD0>.start_app", "trace": "self = <testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango object at 0x00000228877934D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDAAD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango object at 0x00000228877934D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDAAD0>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "switch magic voice to Mango", "attachments": [{"name": "stdout", "source": "769d1104-ac55-4d71-8dfa-fb33b9d5f9a2-attachment.txt", "type": "text/plain"}], "start": 1753349216753, "stop": 1753349216753, "uuid": "fef84704-2e89-4e43-83ee-eeeb04e39786", "historyId": "77f7a75d1fb0221269048ba90dc85f58", "testCaseId": "77f7a75d1fb0221269048ba90dc85f58", "fullName": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango#test_switch_magic_voice_to_mango", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_mango"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceToMango"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_magic_voice_to_mango"}]}