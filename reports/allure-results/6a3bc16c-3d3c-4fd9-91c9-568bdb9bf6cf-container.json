{"uuid": "1e02875b-7a1f-440f-a313-2c8f02ab50c4", "children": ["9451b9d5-91b4-49b1-a47f-500ded38bc50"], "befores": [{"name": "take_screenshot_on_failure", "status": "passed", "start": 1753193090094, "stop": 1753193090094}], "afters": [{"name": "take_screenshot_on_failure::0", "status": "broken", "statusDetails": {"message": "AttributeError: 'Function' object has no attribute 'rep_call'\n", "trace": "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\allure_commons\\_allure.py\", line 231, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pytest\\fixtures.py\", line 938, in _teardown_yield_fixture\n    next(it)\n    ~~~~^^^^\n  File \"D:\\aigc\\app_test\\testcases\\conftest.py\", line 74, in take_screenshot_on_failure\n    if request.node.rep_call.failed:\n       ^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1753193098901, "stop": 1753193098914}], "start": 1753193090094, "stop": 1753193098914}