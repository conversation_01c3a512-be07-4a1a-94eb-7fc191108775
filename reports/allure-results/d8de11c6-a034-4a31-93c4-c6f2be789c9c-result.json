{"name": "测试switch to power saving mode能正常执行", "status": "passed", "description": "switch to power saving mode", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "start": 1753350202834, "stop": 1753350212045}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7283ad72-9997-4bc7-b4d4-2207e00379b7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7ba1b385-3dcf-4056-81fb-dc562185fe19-attachment.png", "type": "image/png"}], "start": 1753350212045, "stop": 1753350212373}], "start": 1753350202834, "stop": 1753350212373}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753350212373, "stop": 1753350212376}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1dca6b1f-1e3f-4276-ad7f-20b8b78c46b3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c784e86d-974f-4508-83c8-b997151a4f0f-attachment.png", "type": "image/png"}], "start": 1753350212376, "stop": 1753350212740}], "attachments": [{"name": "stdout", "source": "284ef9d4-1054-4a9c-a9d2-83987fb94da0-attachment.txt", "type": "text/plain"}], "start": 1753350202833, "stop": 1753350212741, "uuid": "e31cf4e8-b631-45fc-a566-08d9bf7f1146", "historyId": "8fd5e40ff38046a9fe7fe83ba6136249", "testCaseId": "8fd5e40ff38046a9fe7fe83ba6136249", "fullName": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_power_saving_mode"}]}