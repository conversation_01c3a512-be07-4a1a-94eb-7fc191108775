{"name": "测试open contact命令", "status": "failed", "statusDetails": {"message": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False", "trace": "self = <testcases.test_ella.open_app.test_open_phone.TestEllaContactCommandConcise object at 0x0000028F12382850>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000028F127B87D0>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_phone(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open phone\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n            result = self.verify_expected_in_response(\"done\", response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(\"验证Dalier应用已打开\"):\n>           assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nE           assert False\n\ntestcases\\test_ella\\open_app\\test_open_phone.py:35: AssertionError"}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open phone", "status": "passed", "steps": [{"name": "执行命令: open phone", "status": "passed", "start": 1753349969590, "stop": 1753349986590}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3a5f3a5a-2c58-4218-8b09-714968ef42b3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "95299ed4-e7c0-4a80-9ce6-56ea8f553a27-attachment.png", "type": "image/png"}], "start": 1753349986591, "stop": 1753349986899}], "start": 1753349969590, "stop": 1753349986900}, {"name": "验证响应包含Done", "status": "passed", "start": 1753349986900, "stop": 1753349986903}, {"name": "验证Dalier应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: Dalier应用未打开: 初始=False, 最终=False, 响应='['open phone', 'Done!', '', '']'\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_phone.py\", line 35, in test_open_phone\n    assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1753349986903, "stop": 1753349986903}], "attachments": [{"name": "stdout", "source": "2a84138d-2995-42c0-a746-00015bf68937-attachment.txt", "type": "text/plain"}], "start": 1753349969590, "stop": 1753349986905, "uuid": "84f414b1-cdda-45ef-a3a4-ca269941a768", "historyId": "47f6f542e363a70ee1e571c7805a9526", "testCaseId": "47f6f542e363a70ee1e571c7805a9526", "fullName": "testcases.test_ella.open_app.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "labels": [{"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_phone"}]}