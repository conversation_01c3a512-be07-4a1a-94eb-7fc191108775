{"name": "测试open bluetooth命令", "status": "passed", "description": "通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态", "steps": [{"name": "记录测试开始状态", "status": "passed", "attachments": [{"name": "Ella初始状态", "source": "78a7bb67-a302-4e41-a184-b2d36a7d811f-attachment.png", "type": "image/png"}, {"name": "蓝牙初始状态", "source": "0fdbd639-c9e4-4140-b90b-50263ede6eda-attachment.txt", "type": "text/plain"}], "start": 1753193066669, "stop": 1753193067089}, {"name": "确保在对话页面并准备输入", "status": "passed", "attachments": [{"name": "预备状态检查", "source": "9b6579e6-500f-4e90-a0c6-019a54dd4d42-attachment.txt", "type": "text/plain"}], "start": 1753193067089, "stop": 1753193068635}, {"name": "输入命令: open bluetooth", "status": "passed", "attachments": [{"name": "命令发送后", "source": "591a3fc2-3689-45e1-993e-bc7ea4f01d4e-attachment.png", "type": "image/png"}], "start": 1753193068635, "stop": 1753193074376}, {"name": "等待AI响应", "status": "passed", "attachments": [{"name": "收到AI响应", "source": "44a3648f-e3eb-42dd-b432-bb81ae46b52d-attachment.png", "type": "image/png"}], "start": 1753193074376, "stop": 1753193080032}, {"name": "获取并验证响应内容", "status": "passed", "attachments": [{"name": "AI响应内容", "source": "5701d869-7438-4fe2-be72-aa817f45d875-attachment.txt", "type": "text/plain"}], "start": 1753193080032, "stop": 1753193082892}, {"name": "验证蓝牙状态", "status": "passed", "attachments": [{"name": "蓝牙最终状态", "source": "7cdea2db-c33f-45dc-94ec-3720c7cff880-attachment.txt", "type": "text/plain"}], "start": 1753193082892, "stop": 1753193085397}, {"name": "记录测试完成状态", "status": "passed", "attachments": [{"name": "测试完成状态", "source": "84ee4ab7-4341-4334-a4fe-b1365181e9ca-attachment.png", "type": "image/png"}, {"name": "测试总结", "source": "7a90af23-ca4a-4284-8b05-08fa9607a6ca-attachment.txt", "type": "text/plain"}], "start": 1753193085397, "stop": 1753193085674}], "attachments": [{"name": "stdout", "source": "b83eec8d-9eca-42de-9b54-590e035c9e89-attachment.txt", "type": "text/plain"}], "start": 1753193066669, "stop": 1753193085675, "uuid": "2ed7d0a9-9c7e-4109-87c7-14e9486ac66e", "historyId": "e45073f4c99863397022493478030cd3", "testCaseId": "e45073f4c99863397022493478030cd3", "fullName": "testcases.test_ella.history.test_bluetooth_command.TestEllaBluetoothCommand#test_open_bluetooth_command", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "story", "value": "蓝牙控制命令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.history"}, {"name": "suite", "value": "test_bluetooth_command"}, {"name": "subSuite", "value": "TestEllaBluetoothCommand"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "21828-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.history.test_bluetooth_command"}]}