2025-06-23 19:46:44 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 19:46:44 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-23 19:46:44 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:46:44 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-23 19:46:46 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-23 19:46:46 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-23 19:46:46 | INFO | pages.apps.ella.main_page:wait_for_page_load:138 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 19:46:46 | INFO | pages.apps.ella.main_page:wait_for_page_load:143 | ✅ Ella应用包已加载
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:46 | INFO | pages.apps.ella.main_page:wait_for_page_load:147 | ✅ Ella输入框已加载
2025-06-23 19:46:46 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-23 19:46:46 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:46 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 19:46:46 | INFO | pages.apps.ella.main_page:execute_text_command:254 | 执行文本命令: bluetooth status
2025-06-23 19:46:46 | INFO | pages.apps.ella.main_page:input_text_command:175 | 输入文本命令: bluetooth status
2025-06-23 19:46:47 | INFO | pages.apps.ella.main_page:input_text_command:179 | 找到输入框(et_input)，点击激活
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:47 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 3秒
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 19:46:48 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: bluetooth status
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:input_text_command:188 | ✅ 成功输入文本: bluetooth status
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:send_command:223 | 发送命令
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 19:46:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 19:46:48 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:send_command:228 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:send_command:230 | ✅ 命令发送成功
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:execute_text_command:264 | ✅ 文本命令执行完成
2025-06-23 19:46:48 | INFO | pages.apps.ella.main_page:wait_for_response:314 | 等待AI响应，超时时间: 10秒
2025-06-23 19:46:50 | INFO | pages.apps.ella.main_page:wait_for_response:327 | 初始文本内容数量: 13
2025-06-23 19:48:20 | INFO | pages.apps.ella.main_page:wait_for_response:346 | 检测到新文本: ['设备名称', 'TECNO CAMON 40 Premier 5G', '更多设置']
2025-06-23 19:48:20 | INFO | pages.apps.ella.main_page:wait_for_response:376 | 检测到AI响应: 更多设置...
2025-06-23 19:48:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [TTS播放按钮], 超时时间: 3秒
2025-06-23 19:48:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-06-23 19:48:24 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [TTS播放按钮]
2025-06-23 19:48:24 | INFO | pages.apps.ella.main_page:get_response_text:406 | 获取AI响应文本
2025-06-23 19:48:24 | INFO | pages.apps.ella.main_page:get_response_text:421 | 页面上所有文本元素: ['蓝牙', '蓝牙', '设备名称', 'TECNO CAMON 40 Premier 5G', '更多设置']
2025-06-23 19:48:24 | INFO | pages.apps.ella.main_page:get_response_text:499 | 备选响应文本: TECNO CAMON 40 Premier 5G
2025-06-23 19:48:24 | INFO | test_bluetooth_command:test_bluetooth_status_query:266 | 命令 'bluetooth status' 响应: TECNO CAMON 40 Premier 5G
2025-06-23 19:48:24 | INFO | test_bluetooth_command:test_bluetooth_status_query:274 | ✅ 命令 'bluetooth status' 响应包含蓝牙相关内容
2025-06-23 19:48:24 | INFO | pages.apps.ella.main_page:execute_text_command:254 | 执行文本命令: is bluetooth on
2025-06-23 19:48:24 | INFO | pages.apps.ella.main_page:input_text_command:175 | 输入文本命令: is bluetooth on
2025-06-23 19:48:25 | ERROR | pages.apps.ella.main_page:input_text_command:208 | 未找到任何输入框
2025-06-23 19:48:25 | WARNING | test_bluetooth_command:test_bluetooth_status_query:260 | 命令执行失败: is bluetooth on
2025-06-23 19:48:25 | INFO | pages.apps.ella.main_page:execute_text_command:254 | 执行文本命令: check bluetooth
2025-06-23 19:48:25 | INFO | pages.apps.ella.main_page:input_text_command:175 | 输入文本命令: check bluetooth
2025-06-23 19:48:25 | ERROR | pages.apps.ella.main_page:input_text_command:208 | 未找到任何输入框
2025-06-23 19:48:25 | WARNING | test_bluetooth_command:test_bluetooth_status_query:260 | 命令执行失败: check bluetooth
2025-06-23 19:48:25 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-23 19:48:25 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 19:48:25 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 19:48:25 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-06-23 19:48:25 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 19:48:25 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 19:48:25 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
