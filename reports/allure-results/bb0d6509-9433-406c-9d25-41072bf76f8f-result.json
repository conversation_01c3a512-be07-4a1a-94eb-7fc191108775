{"name": "测试switch to performance mode能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9F90>.start_app", "trace": "self = <testcases.test_ella.third_coupling.test_switch_to_performance_mode.TestEllaSwitchToPerformanceMode object at 0x0000022887793B10>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9F90>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.third_coupling.test_switch_to_performance_mode.TestEllaSwitchToPerformanceMode object at 0x0000022887793B10>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BD9F90>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "switch to performance mode", "attachments": [{"name": "stdout", "source": "39985ac6-c5c4-4249-96b2-2d386cee10b3-attachment.txt", "type": "text/plain"}], "start": 1753349218901, "stop": 1753349218901, "uuid": "eaee2d94-03ce-4257-960f-dbc69b658bcc", "historyId": "d74d40d0bb79edc1bd2153fdf46aeafb", "testCaseId": "d74d40d0bb79edc1bd2153fdf46aeafb", "fullName": "testcases.test_ella.third_coupling.test_switch_to_performance_mode.TestEllaSwitchToPerformanceMode#test_switch_to_performance_mode", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_switch_to_performance_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_switch_to_performance_mode"}]}