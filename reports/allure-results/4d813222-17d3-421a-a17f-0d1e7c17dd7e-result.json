{"name": "open clock", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDA490>.start_app", "trace": "self = <testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise object at 0x0000022887791E50>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n>           assert ella_page.start_app(), \"Ella应用启动失败\"\nE           AssertionError: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDA490>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:23: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise object at 0x0000022887791E50>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella应用启动失败\nE           assert False\nE            +  where False = start_app()\nE            +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000022887BDA490>.start_app\n\ntestcases\\test_ella\\base_ella_test.py:31: Failed"}, "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "attachments": [{"name": "stdout", "source": "2d6ba13b-b1ad-4e25-a75f-981fa4b2e3be-attachment.txt", "type": "text/plain"}], "start": 1753349209733, "stop": 1753349209733, "uuid": "76f3f108-4e80-4053-8f2f-5e8d941478c1", "historyId": "757ca38b1dbab556e91ff727b60faeb4", "testCaseId": "757ca38b1dbab556e91ff727b60faeb4", "fullName": "testcases.test_ella.open_app.test_open_clock.TestEllaCommandConcise#test_open_clock", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "open clock"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "58380-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_clock"}]}