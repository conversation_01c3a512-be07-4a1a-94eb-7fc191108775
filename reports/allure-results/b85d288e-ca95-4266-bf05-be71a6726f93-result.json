{"name": "测试open countdown能正常执行", "status": "passed", "description": "open countdown", "steps": [{"name": "执行命令: open countdown", "status": "passed", "steps": [{"name": "执行命令: open countdown", "status": "passed", "start": 1753349864535, "stop": 1753349875041}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5fb623d6-a9f2-47d3-a27a-f579ad353193-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "09de4712-cccf-4062-80e1-8465ec7fd9f2-attachment.png", "type": "image/png"}], "start": 1753349875041, "stop": 1753349875347}], "start": 1753349864535, "stop": 1753349875347}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1753349875347, "stop": 1753349875351}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0f451163-f649-466e-84d9-2240598aeaba-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "81761b21-4e9e-4e24-81d8-507c6f767c2c-attachment.png", "type": "image/png"}], "start": 1753349875351, "stop": 1753349875674}], "attachments": [{"name": "stdout", "source": "994e716a-cc94-48db-97ca-27dfc4b39785-attachment.txt", "type": "text/plain"}], "start": 1753349864535, "stop": 1753349875675, "uuid": "cfe08833-b65d-4849-8ccd-6317810b0ccb", "historyId": "4435e72d0d42f3a5a2ebd4b283ecb50b", "testCaseId": "4435e72d0d42f3a5a2ebd4b283ecb50b", "fullName": "testcases.test_ella.open_app.test_open_countdown.TestEllaCommandConcise#test_open_countdown", "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_countdown"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "60304-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_countdown"}]}