{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open wifi", "status": "passed", "steps": [{"name": "执行命令: open wifi", "status": "passed", "start": 1753193236966, "stop": 1753193251066}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4e9931b7-f5a0-4b8a-8e58-674359affb01-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "83d7449e-7e3b-438d-bd40-1b1694914192-attachment.png", "type": "image/png"}], "start": 1753193251066, "stop": 1753193251376}], "start": 1753193236966, "stop": 1753193251377}, {"name": "验证响应包含Done", "status": "passed", "start": 1753193251377, "stop": 1753193251380}, {"name": "验证wifi已打开", "status": "passed", "start": 1753193251380, "stop": 1753193251380}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "df55ec2c-e5af-491f-9f8b-d12f23c1dd6f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b77082e6-2489-4b05-9958-0d35d553ed12-attachment.png", "type": "image/png"}], "start": 1753193251380, "stop": 1753193251815}], "attachments": [{"name": "stdout", "source": "9d6bb85a-71f7-4d7b-a6ec-9a04eb99f905-attachment.txt", "type": "text/plain"}], "start": 1753193236966, "stop": 1753193251816, "uuid": "8b7f118f-5cb2-4c80-86a9-b616b3b1ceff", "historyId": "3c3847508282ec757756bf34e51855d9", "testCaseId": "3c3847508282ec757756bf34e51855d9", "fullName": "testcases.test_ella.test_open_wifi.TestEllaCommandConcise#test_open_wifi", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_wifi"}]}