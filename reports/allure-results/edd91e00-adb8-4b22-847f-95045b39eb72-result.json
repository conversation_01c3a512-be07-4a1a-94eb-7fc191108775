{"name": "测试close bluetooth命令", "status": "passed", "description": "通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态", "steps": [{"name": "记录测试开始状态", "status": "passed", "attachments": [{"name": "Ella初始状态", "source": "1e402451-552d-43f2-a0dc-fa7433b288ef-attachment.png", "type": "image/png"}], "start": 1753193090096, "stop": 1753193090482}, {"name": "确保在对话页面并准备输入", "status": "passed", "attachments": [{"name": "预备状态检查", "source": "25388d31-c557-4ac6-9840-3b3a1d451534-attachment.txt", "type": "text/plain"}], "start": 1753193090482, "stop": 1753193092121}, {"name": "输入命令: close bluetooth", "status": "passed", "start": 1753193092122, "stop": 1753193097586}, {"name": "等待AI响应", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\history\\test_bluetooth_command.py\", line 261, in test_close_bluetooth_command\n    response_received = ella_app.wait_for_response(timeout=20)\n  File \"D:\\aigc\\app_test\\pages\\apps\\ella\\history\\main_page.py\", line 2007, in wait_for_response\n    initial_element_count = self._get_quick_element_count()\n  File \"D:\\aigc\\app_test\\pages\\apps\\ella\\history\\main_page.py\", line 2048, in _get_quick_element_count\n    if text_views.exists():\n       ~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\utils.py\", line 127, in __call__\n    return bool(self)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\utils.py\", line 117, in __bool__\n    return self.__nonzero__()\n           ~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\utils.py\", line 113, in __nonzero__\n    return self.uiobject.jsonrpc.exist(self.uiobject.selector)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\base.py\", line 158, in __call__\n    return self.server.jsonrpc_call(self.method, params, http_timeout)\n           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 311, in jsonrpc_call\n    return _jsonrpc_call(self._dev, self._device_server_port, method, params, timeout, self._debug)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 173, in _jsonrpc_call\n    r = _http_request(dev, device_port, \"POST\", \"/jsonrpc/0\", payload, timeout=timeout, print_request=print_request)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 139, in _http_request\n    conn.request(method, path, json.dumps(data), headers=headers)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\http\\client.py\", line 1338, in request\n    self._send_request(method, url, body, headers, encode_chunked)\n    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\http\\client.py\", line 1384, in _send_request\n    self.endheaders(body, encode_chunked=encode_chunked)\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\http\\client.py\", line 1333, in endheaders\n    self._send_output(message_body, encode_chunked=encode_chunked)\n    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\http\\client.py\", line 1093, in _send_output\n    self.send(msg)\n    ~~~~~~~~~^^^^^\n  File \"C:\\Python313\\Lib\\http\\client.py\", line 1037, in send\n    self.connect()\n    ~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uiautomator2\\core.py\", line 97, in connect\n    self.sock = self.__device.create_connection(adbutils.Network.TCP, self.__port)\n                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 457, in create_connection\n    c = self.open_transport()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_device_base.py\", line 67, in open_transport\n    c = self._client.make_connection(timeout=timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 234, in make_connection\n    _conn = AdbConnection(self.__host, self.__port)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 47, in __init__\n    self.__conn = self._safe_connect()\n                  ~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 73, in _safe_connect\n    return self._create_socket()\n           ~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\adbutils\\_adb.py\", line 63, in _create_socket\n    s.connect((adb_host, adb_port))\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1753193097586, "stop": 1753193097857}], "start": 1753193090096, "stop": 1753193097960, "uuid": "9451b9d5-91b4-49b1-a47f-500ded38bc50", "testCaseId": "ae84edbd370f3b86dd7abce07349301a", "fullName": "testcases.test_ella.history.test_bluetooth_command.TestEllaBluetoothCommand#test_close_bluetooth_command"}