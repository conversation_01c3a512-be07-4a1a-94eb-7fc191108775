<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,143]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,143]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,143]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,143]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[39,27][1221,143]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[49,27][582,143]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[49,27][356,143]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[49,27][356,143]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="13:31" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="13:31" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[49,27][180,143]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[180,27][356,143]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[180,27][356,143]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[180,27][356,143]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[191,63][235,107]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="电池与省电通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[246,63][290,107]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="My Health通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[301,63][345,107]" drawing-order="3" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[582,27][678,143]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[678,27][1211,143]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[886,27][1211,143]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[886,46][1211,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,46][1088,123]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,46][1072,123]" drawing-order="2" hint="" display-id="0" />

                  <node index="1" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,46][1072,123]" drawing-order="1" hint="" display-id="0">

                    <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[902,48][930,120]" drawing-order="13" hint="" display-id="0" />

                    <node index="3" text="" resource-id="com.android.systemui:id/network_rate" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[946,48][990,120]" drawing-order="18" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[946,64][990,103]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="KB/S" resource-id="com.android.systemui:id/network_rate_unit_view" class="android.widget.TextView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[946,84][990,103]" drawing-order="2" hint="" display-id="0" />

                        <node index="1" text="1.20" resource-id="com.android.systemui:id/network_rate_view" class="android.widget.TextView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,64][988,86]" drawing-order="1" hint="" display-id="0" />

                      </node>

                    </node>

                    <node index="4" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,48][1072,120]" drawing-order="22" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,48][1072,120]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,58][1072,110]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,58][1072,110]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,58][1072,110]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1006,58][1020,110]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1023,58][1072,110]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1020,58][1072,110]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="正在充电，已完成百分之 66。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1088,46][1195,123]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1088,65][1165,104]" drawing-order="1" hint="" display-id="0" />

                  <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1172,60][1195,109]" drawing-order="2" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,143]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,2800]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1260,2670]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,143][1260,2670]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.calculator:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,143][1260,2670]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,143][1260,2670]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.calculator:id/calculator_root" class="android.view.ViewGroup" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,143][1260,2670]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.calculator:id/toolbar" class="android.view.ViewGroup" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,143][1260,339]" drawing-order="1" hint="" display-id="0">

                <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[14,146][1232,335]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.calculator:id/img_invoke_thunderwindow" class="android.widget.ImageView" package="com.transsion.calculator" content-desc="小窗" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[70,198][154,282]" drawing-order="1" hint="" display-id="0" />

                  <node index="1" text="" resource-id="com.transsion.calculator:id/img_history" class="android.widget.ImageView" package="com.transsion.calculator" content-desc="历史记录" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[770,198][854,282]" drawing-order="2" hint="" display-id="0" />

                  <node index="2" text="" resource-id="com.transsion.calculator:id/img_currency" class="android.widget.ImageView" package="com.transsion.calculator" content-desc="在线汇率" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[938,198][1022,282]" drawing-order="3" hint="" display-id="0" />

                  <node index="3" text="" resource-id="com.transsion.calculator:id/img_unitconvert" class="android.widget.ImageView" package="com.transsion.calculator" content-desc="单位换算" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1106,198][1190,282]" drawing-order="4" hint="" display-id="0" />

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.calculator:id/formula_result_set" class="android.widget.LinearLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[70,477][1260,894]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.calculator:id/formula_container" class="android.widget.HorizontalScrollView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[70,477][1176,721]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.calculator:id/formula" class="android.widget.EditText" package="com.transsion.calculator" content-desc="无任何公式" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="true" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[70,477][1176,721]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.calculator:id/result" class="android.widget.TextView" package="com.transsion.calculator" content-desc="没有任何结果" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[70,721][1260,894]" drawing-order="2" hint="" display-id="0" />

              </node>

              <node index="2" text="" resource-id="com.transsion.calculator:id/main_calculator_set" class="android.widget.LinearLayout" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,1199][1197,2616]" drawing-order="6" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.calculator:id/pad_numeric_number" class="android.view.ViewGroup" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,1199][914,2616]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.calculator:id/clr" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="清除" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,1199][346,1482]" drawing-order="1" hint="" display-id="0" />

                  <node index="1" text="" resource-id="com.transsion.calculator:id/op_pct" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="百分比符号" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[346,1199][630,1482]" drawing-order="2" hint="" display-id="0" />

                  <node index="2" text="" resource-id="com.transsion.calculator:id/del" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="删除" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[630,1199][914,1482]" drawing-order="3" hint="" display-id="0" />

                  <node index="3" text="7" resource-id="com.transsion.calculator:id/digit_7" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,1482][346,1765]" drawing-order="4" hint="" display-id="0" />

                  <node index="4" text="8" resource-id="com.transsion.calculator:id/digit_8" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[346,1482][630,1765]" drawing-order="5" hint="" display-id="0" />

                  <node index="5" text="9" resource-id="com.transsion.calculator:id/digit_9" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[630,1482][914,1765]" drawing-order="6" hint="" display-id="0" />

                  <node index="6" text="4" resource-id="com.transsion.calculator:id/digit_4" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,1765][346,2048]" drawing-order="7" hint="" display-id="0" />

                  <node index="7" text="5" resource-id="com.transsion.calculator:id/digit_5" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[346,1765][630,2048]" drawing-order="8" hint="" display-id="0" />

                  <node index="8" text="6" resource-id="com.transsion.calculator:id/digit_6" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[630,1765][914,2048]" drawing-order="9" hint="" display-id="0" />

                  <node index="9" text="1" resource-id="com.transsion.calculator:id/digit_1" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,2048][346,2332]" drawing-order="10" hint="" display-id="0" />

                  <node index="10" text="2" resource-id="com.transsion.calculator:id/digit_2" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[346,2048][630,2332]" drawing-order="11" hint="" display-id="0" />

                  <node index="11" text="3" resource-id="com.transsion.calculator:id/digit_3" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[630,2048][914,2332]" drawing-order="12" hint="" display-id="0" />

                  <node index="12" text="" resource-id="com.transsion.calculator:id/op_ori_vert" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="科学计算器" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[63,2332][346,2616]" drawing-order="14" hint="" display-id="0" />

                  <node index="13" text="0" resource-id="com.transsion.calculator:id/digit_0" class="android.widget.TextView" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[346,2332][630,2616]" drawing-order="15" hint="" display-id="0" />

                  <node index="14" text="." resource-id="com.transsion.calculator:id/dec_point" class="android.widget.TextView" package="com.transsion.calculator" content-desc="小数点" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[630,2332][914,2616]" drawing-order="16" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.calculator:id/pad_numeric" class="android.view.ViewGroup" package="com.transsion.calculator" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,1199][1197,2616]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.calculator:id/op_div" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="除" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,1199][1197,1482]" drawing-order="1" hint="" display-id="0" />

                  <node index="1" text="" resource-id="com.transsion.calculator:id/op_mul" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="乘" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,1482][1197,1766]" drawing-order="2" hint="" display-id="0" />

                  <node index="2" text="" resource-id="com.transsion.calculator:id/op_sub" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="减" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,1766][1197,2049]" drawing-order="3" hint="" display-id="0" />

                  <node index="3" text="" resource-id="com.transsion.calculator:id/op_add" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="加" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,2049][1197,2333]" drawing-order="4" hint="" display-id="0" />

                  <node index="4" text="" resource-id="com.transsion.calculator:id/eq" class="android.widget.ImageButton" package="com.transsion.calculator" content-desc="等于" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[914,2333][1197,2616]" drawing-order="5" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1222,347][1260,767]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1222,347][1260,767]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1222,347][1260,767]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1236,403][1260,711]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="com.android.systemui:id/navigation_bar_frame" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2670][1260,2800]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/navigation_bar_view" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2670][1260,2800]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/navigation_inflater" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2670][1260,2800]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/horizontal" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2670][1260,2800]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/nav_buttons" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[26,2670][1234,2800]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/ends_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[26,2670][1234,2800]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[26,2670][146,2800]" drawing-order="1" hint="" display-id="0" />

              <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[146,2670][387,2800]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/recent_apps" class="android.widget.ImageView" package="com.android.systemui" content-desc="概览" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[146,2670][387,2800]" drawing-order="1" hint="" display-id="0" />

              </node>

              <node index="2" text="" resource-id="" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[871,2670][1113,2800]" drawing-order="4" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/back" class="android.widget.ImageView" package="com.android.systemui" content-desc="返回" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[871,2670][1113,2800]" drawing-order="1" hint="" display-id="0" />

              </node>

              <node index="3" text="" resource-id="" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1113,2670][1234,2800]" drawing-order="5" hint="" display-id="0" />

            </node>

            <node index="1" text="" resource-id="com.android.systemui:id/center_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[500,2670][760,2800]" drawing-order="2" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/home" class="android.widget.ImageView" package="com.android.systemui" content-desc="主屏幕" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[500,2670][760,2800]" drawing-order="1" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

</hierarchy>