#!/usr/bin/env python3
"""
调试手电筒检测问题
"""
import subprocess

def debug_flashlight_detection():
    """调试手电筒检测"""
    print('=== 调试手电筒检测问题 ===')
    
    # 1. 检查原始日志
    print('\n1. 检查原始torch日志:')
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "media.camera"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            torch_operation_lines = [
                line.strip() for line in lines 
                if 'torch for camera' in line.lower() and ('turned on' in line.lower() or 'turned off' in line.lower())
            ]
            
            print(f'   找到 {len(torch_operation_lines)} 条torch操作日志:')
            for i, line in enumerate(torch_operation_lines[:8]):
                print(f'     [{i+1}] {line}')
            
            if torch_operation_lines:
                latest = torch_operation_lines[0]
                print(f'\n   最新操作: {latest}')
                if 'turned on' in latest.lower():
                    print('   -> 应该返回: True (开启)')
                elif 'turned off' in latest.lower():
                    print('   -> 应该返回: False (关闭)')
        else:
            print(f'   获取日志失败: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')
    
    # 2. 测试实际检测函数
    print('\n2. 测试实际检测函数:')
    try:
        from pages.apps.ella.ella_status_checker import EllaStatusChecker
        from core.base_driver import driver_manager

        checker = EllaStatusChecker(driver_manager.driver)
        status = checker.check_flashlight_status()
        print(f'   实际返回结果: {status}')
        
        # 3. 获取详细状态
        print('\n3. 详细状态信息:')
        detailed = checker.get_flashlight_detailed_status()
        for key, value in detailed.items():
            if key == 'torch_logs':
                print(f'   {key}: {len(value) if value else 0} 条')
                for i, log in enumerate(value[:3] if value else []):
                    print(f'     [{i+1}] {log}')
            else:
                print(f'   {key}: {value}')
                
    except Exception as e:
        print(f'   测试失败: {e}')

if __name__ == '__main__':
    debug_flashlight_detection()
