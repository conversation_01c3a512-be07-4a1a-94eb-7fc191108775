#!/usr/bin/env python3
"""
测试Recent页面清理按钮点击功能
专门验证优化后的删除按钮点击成功率
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def start_test_apps():
    """启动一些测试应用"""
    test_apps = [
        ("com.google.android.apps.maps", "Google Maps"),
        ("com.android.chrome", "Chrome"),
        ("com.android.settings", "Settings"),
        ("com.android.calculator2", "Calculator")
    ]
    
    started_apps = []
    for package, name in test_apps:
        try:
            result = subprocess.run(
                ["adb", "shell", "monkey", "-p", package, "-c", "android.intent.category.LAUNCHER", "1"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                started_apps.append((package, name))
                log.info(f"✅ 启动应用: {name}")
                time.sleep(1)
            else:
                log.warning(f"⚠️ 启动应用失败: {name}")
        except Exception as e:
            log.warning(f"⚠️ 启动 {name} 异常: {e}")
    
    return started_apps


def check_recent_apps():
    """检查Recent页面的应用数量"""
    try:
        # 打开Recent页面
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(2)
        
        # 获取Recent应用信息
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "recents"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            lines = [line for line in result.stdout.split('\n') if line.strip()]
            app_count = len([line for line in lines if 'Task' in line or 'Activity' in line])
            log.info(f"Recent页面应用数量: {app_count}")
            return app_count
        else:
            log.warning("无法获取Recent应用信息")
            return -1
            
    except Exception as e:
        log.error(f"检查Recent应用异常: {e}")
        return -1
    finally:
        # 返回主屏幕
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )


def test_ui_detection():
    """测试UI元素检测功能"""
    log.info("🔍 测试UI元素检测功能...")
    
    try:
        # 打开Recent页面
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(2)
        
        # 创建BaseEllaTest实例并测试UI检测
        base_test = BaseEllaTest()
        success = base_test._find_clear_button_by_ui()
        
        if success:
            log.info("✅ UI元素检测成功")
        else:
            log.warning("⚠️ UI元素检测失败")
        
        return success
        
    except Exception as e:
        log.error(f"UI元素检测测试异常: {e}")
        return False
    finally:
        # 返回主屏幕
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )


def test_enhanced_tap():
    """测试增强点击功能"""
    log.info("👆 测试增强点击功能...")
    
    try:
        # 打开Recent页面
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(2)
        
        # 创建BaseEllaTest实例并测试增强点击
        base_test = BaseEllaTest()
        
        # 测试不同的点击位置
        test_positions = [
            (540, 1800, "底部中央"),
            (1000, 1800, "底部右侧"),
            (200, 1800, "底部左侧")
        ]
        
        for x, y, desc in test_positions:
            log.info(f"测试点击位置: {desc} ({x}, {y})")
            success = base_test._enhanced_tap(x, y, desc, 0)
            if success:
                log.info(f"✅ {desc} 点击成功")
                return True
            else:
                log.warning(f"⚠️ {desc} 点击失败")
                time.sleep(1)
        
        return False
        
    except Exception as e:
        log.error(f"增强点击测试异常: {e}")
        return False
    finally:
        # 返回主屏幕
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )


def test_complete_clear_process():
    """测试完整的清理流程"""
    log.info("🧹 测试完整的清理流程...")
    
    try:
        # 1. 启动测试应用
        log.info("步骤1: 启动测试应用...")
        started_apps = start_test_apps()
        if not started_apps:
            log.warning("没有成功启动任何测试应用")
            return False
        
        time.sleep(3)
        
        # 2. 检查Recent页面应用数量（清理前）
        log.info("步骤2: 检查清理前的应用数量...")
        before_count = check_recent_apps()
        log.info(f"清理前应用数量: {before_count}")
        
        time.sleep(2)
        
        # 3. 执行清理
        log.info("步骤3: 执行Recent页面清理...")
        base_test = BaseEllaTest()
        cleared_count = base_test._clear_via_recent_apps()
        log.info(f"清理操作返回值: {cleared_count}")
        
        time.sleep(3)
        
        # 4. 检查Recent页面应用数量（清理后）
        log.info("步骤4: 检查清理后的应用数量...")
        after_count = check_recent_apps()
        log.info(f"清理后应用数量: {after_count}")
        
        # 5. 评估清理效果
        if before_count > 0 and after_count >= 0:
            if after_count < before_count:
                log.info(f"✅ 清理成功! 清理了 {before_count - after_count} 个应用")
                return True
            elif after_count == 0:
                log.info("✅ 清理成功! 所有应用都被清理")
                return True
            else:
                log.warning(f"⚠️ 清理效果不明显，应用数量变化: {before_count} -> {after_count}")
                return False
        else:
            log.warning("⚠️ 无法准确评估清理效果")
            return cleared_count > 0
        
    except Exception as e:
        log.error(f"完整清理流程测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试Recent页面清理按钮点击功能...")
    
    # 检查ADB连接
    try:
        result = subprocess.run(
            ["adb", "devices"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if "device" not in result.stdout:
            log.error("❌ 未检测到ADB设备连接")
            return
        else:
            log.info("✅ ADB设备连接正常")
            
    except Exception as e:
        log.error(f"❌ ADB连接检查失败: {e}")
        return
    
    # 测试序列
    tests = [
        ("UI元素检测", test_ui_detection),
        ("增强点击功能", test_enhanced_tap),
        ("完整清理流程", test_complete_clear_process)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        log.info(f"\n{'='*50}")
        log.info(f"测试: {test_name}")
        log.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                log.info(f"✅ {test_name} 测试通过")
            else:
                log.warning(f"⚠️ {test_name} 测试失败")
        except Exception as e:
            log.error(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
        
        time.sleep(3)
    
    # 输出测试总结
    log.info(f"\n{'='*50}")
    log.info("测试总结")
    log.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    log.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        log.info("🎉 所有测试都通过了！Recent页面清理功能工作正常")
    elif passed > 0:
        log.info("⚠️ 部分测试通过，Recent页面清理功能部分可用")
    else:
        log.error("❌ 所有测试都失败了，需要进一步优化")


if __name__ == "__main__":
    main()
