"""
Ella语音助手拍照测试 - 优化版本
测试通过Ella输入"take a photo"命令并验证结果
使用重构后的页面类，提供更好的模块化和可维护性
"""
import pytest
import allure
import time
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("拍照控制命令 - 优化版本")
class TestEllaTakePhotoCommandOptimized:
    """Ella拍照命令测试类 - 优化版本"""

    @pytest.fixture(scope="function")
    def ella_app(self):
        """Ella应用fixture - 使用重构后的页面类"""
        ella_page = EllaDialoguePage()

        try:
            log.info("🚀 开始启动Ella应用...")

            # 启动应用
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")

                # 等待页面加载，增加超时时间
                log.info("⏳ 等待Ella页面加载...")
                if ella_page.wait_for_page_load(timeout=15):
                    log.info("✅ Ella页面加载完成")

                    # 截图记录启动成功状态
                    screenshot_path = ella_page.screenshot("ella_photo_app_started.png")
                    log.info(f"📸 启动成功截图: {screenshot_path}")

                    yield ella_page
                else:
                    log.error("❌ Ella页面加载失败")
                    # 截图记录失败状态
                    screenshot_path = ella_page.screenshot("ella_photo_page_load_failed.png")
                    log.error(f"📸 页面加载失败截图: {screenshot_path}")
                    pytest.fail("Ella页面加载失败")
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")

        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            # 截图记录异常状态
            try:
                screenshot_path = ella_page.screenshot("ella_photo_fixture_error.png")
                log.error(f"📸 异常状态截图: {screenshot_path}")
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")

        finally:
            # 清理：停止应用
            try:
                log.info("🧹 清理Ella应用...")
                ella_page.stop_app()
                log.info("✅ Ella应用已停止")
            except Exception as e:
                log.warning(f"⚠️ 停止Ella应用时出现异常: {e}")
    
    @allure.title("测试take a photo命令")
    @allure.description("通过Ella输入'take a photo'命令，验证响应和拍照功能")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_take_photo_command(self, ella_app):
        """测试take a photo命令"""
        command = "take a photo"
        
        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_photo_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 记录相机应用初始状态
            initial_camera_status = ella_app.check_camera_app_opened()
            log.info(f"相机应用初始状态: {'已打开' if initial_camera_status else '未打开'}")
            
            # 记录相机权限状态
            camera_permission = ella_app.check_camera_permission()
            log.info(f"相机权限状态: {'已授权' if camera_permission else '未授权'}")
            
            allure.attach(
                f"相机应用初始状态: {'已打开' if initial_camera_status else '未打开'}\n"
                f"相机权限状态: {'已授权' if camera_permission else '未授权'}",
                name="相机初始状态",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"输入命令: {command}"):
            # 执行文本命令（现在包含了确保在对话页面的逻辑）
            success = ella_app.execute_text_command(command)
            assert success, f"执行命令失败: {command}"
            
            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_photo_command_sent.png")
            allure.attach.file(screenshot_path, name="命令发送后", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info(f"✅ 成功执行命令: {command}")
        
        with allure.step("等待AI响应"):
            # 等待AI响应，拍照可能需要更长时间
            response_received = ella_app.wait_for_response(timeout=12)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                # 等待额外时间确保响应完整
                time.sleep(3)

                # 截图记录响应状态
                screenshot_path = ella_app.screenshot("ella_photo_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)

                log.info("✅ 收到AI响应")
            else:
                # 截图记录无响应状态
                screenshot_path = ella_app.screenshot("ella_photo_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)

                # 获取页面文本快照用于调试
                debug_snapshot = ella_app._get_page_text_snapshot()
                allure.attach(debug_snapshot, name="页面文本快照",
                             attachment_type=allure.attachment_type.TEXT)

                log.warning("⚠️ 未检测到AI响应，继续测试以验证拍照功能")
        
        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")
        
        with allure.step("验证拍照功能"):
            # 等待拍照功能可能的变化
            time.sleep(3)
            
            # 综合验证拍照结果
            photo_verification = ella_app.verify_photo_taken_success(response_text)
            
            # 记录详细的验证信息
            verification_details = "\n".join(photo_verification["details"])
            allure.attach(
                f"拍照功能验证结果:\n"
                f"响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}\n"
                f"拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}\n"
                f"相机应用: {'✅' if photo_verification['camera_app_opened'] else '❌'}\n"
                f"相机权限: {'✅' if photo_verification['camera_permission'] else '❌'}\n"
                f"综合结果: {'✅' if photo_verification['overall_success'] else '❌'}\n\n"
                f"详细信息:\n{verification_details}",
                name="拍照功能验证",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 检查最终相机应用状态
            final_camera_status = ella_app.check_camera_app_opened()
            log.info(f"相机应用最终状态: {'已打开' if final_camera_status else '未打开'}")
            
            # 验证拍照是否成功
            if photo_verification["overall_success"]:
                log.info("✅ 拍照功能验证通过")
            else:
                log.warning("⚠️ 拍照功能验证未完全通过，但测试继续")
                # 注意：拍照功能可能需要权限或特定配置，这里不强制失败
        
        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_photo_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 总结测试结果
            test_summary = f"""
测试命令: {command}
响应内容: {response_text}
响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}
拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}
相机应用初始状态: {'已打开' if initial_camera_status else '未打开'}
相机应用最终状态: {'已打开' if final_camera_status else '未打开'}
相机权限: {'✅' if photo_verification['camera_permission'] else '❌'}
综合验证结果: {'✅' if photo_verification['overall_success'] else '❌'}
测试结果: 完成
"""
            allure.attach(test_summary, name="测试总结", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("🎉 拍照命令测试完成")

    @allure.title("测试语音输入take a photo命令")
    @allure.description("通过Ella语音输入'take a photo'命令，验证响应和拍照功能")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.voice
    def test_voice_take_photo_command(self, ella_app):
        """测试语音输入take a photo命令"""
        command = "take a photo"

        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_voice_photo_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态",
                             attachment_type=allure.attachment_type.PNG)

            # 记录相机应用初始状态
            initial_camera_status = ella_app.check_camera_app_opened()
            log.info(f"相机应用初始状态: {'已打开' if initial_camera_status else '未打开'}")

        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"语音输入命令: {command}"):
            # 执行语音命令（包含回退到文本输入的逻辑）
            success = ella_app.execute_voice_command(command, duration=3.0)
            assert success, f"执行语音命令失败: {command}"

            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_voice_photo_command_sent.png")
            allure.attach.file(screenshot_path, name="语音命令发送后",
                             attachment_type=allure.attachment_type.PNG)

            log.info(f"✅ 成功执行语音命令: {command}")

        with allure.step("等待AI响应"):
            # 等待AI响应，拍照可能需要更长时间
            response_received = ella_app.wait_for_response(timeout=15)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                time.sleep(5)
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                time.sleep(3)
                screenshot_path = ella_app.screenshot("ella_voice_photo_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)
                log.info("✅ 收到AI响应")
            else:
                screenshot_path = ella_app.screenshot("ella_voice_photo_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.warning("⚠️ 未检测到AI响应，继续测试以验证拍照功能")

        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")

        with allure.step("验证拍照功能"):
            # 等待拍照功能可能的变化
            time.sleep(3)

            # 综合验证拍照结果
            photo_verification = ella_app.verify_photo_taken_success(response_text)

            # 记录详细的验证信息
            verification_details = "\n".join(photo_verification["details"])
            allure.attach(
                f"拍照功能验证结果:\n"
                f"响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}\n"
                f"拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}\n"
                f"相机应用: {'✅' if photo_verification['camera_app_opened'] else '❌'}\n"
                f"综合结果: {'✅' if photo_verification['overall_success'] else '❌'}\n\n"
                f"详细信息:\n{verification_details}",
                name="拍照功能验证",
                attachment_type=allure.attachment_type.TEXT
            )

            # 验证拍照是否成功
            if photo_verification["overall_success"]:
                log.info("✅ 拍照功能验证通过")
            else:
                log.warning("⚠️ 拍照功能验证未完全通过，但测试继续")

        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_voice_photo_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态",
                             attachment_type=allure.attachment_type.PNG)

            # 总结测试结果
            test_summary = f"""
测试类型: 语音输入
测试命令: {command}
响应内容: {response_text}
响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}
拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}
相机应用: {'✅' if photo_verification['camera_app_opened'] else '❌'}
综合验证结果: {'✅' if photo_verification['overall_success'] else '❌'}
测试结果: 完成
"""
            allure.attach(test_summary, name="语音测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 语音输入拍照命令测试完成")

    @allure.title("测试真实TTS语音输入take a photo命令")
    @allure.description("通过TTS将'take a photo'转换为语音，通过麦克风播放给手机")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.tts_voice
    def test_real_tts_voice_take_photo_command(self, ella_app):
        """测试真实TTS语音输入take a photo命令"""
        command = "take a photo"

        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_tts_photo_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态",
                             attachment_type=allure.attachment_type.PNG)

            # 记录相机应用初始状态
            initial_camera_status = ella_app.check_camera_app_opened()
            log.info(f"相机应用初始状态: {'已打开' if initial_camera_status else '未打开'}")

        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"TTS语音输入命令: {command}"):
            # 执行真实TTS语音命令
            success = ella_app.execute_real_voice_command(
                command,
                language='en-US',  # 英文
                volume=0.8,        # 音量80%
                tts_delay=1.5      # TTS播放前延迟1.5秒
            )
            assert success, f"执行TTS语音命令失败: {command}"

            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_tts_photo_command_sent.png")
            allure.attach.file(screenshot_path, name="TTS语音命令发送后",
                             attachment_type=allure.attachment_type.PNG)

            log.info(f"✅ 成功执行TTS语音命令: {command}")

        with allure.step("等待AI响应"):
            # 等待AI响应，拍照可能需要更长时间
            response_received = ella_app.wait_for_response(timeout=18)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                time.sleep(5)
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                time.sleep(3)
                screenshot_path = ella_app.screenshot("ella_tts_photo_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)
                log.info("✅ 收到AI响应")
            else:
                screenshot_path = ella_app.screenshot("ella_tts_photo_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.warning("⚠️ 未检测到AI响应，继续测试以验证拍照功能")

        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")

        with allure.step("验证拍照功能"):
            # 等待拍照功能可能的变化
            time.sleep(3)

            # 综合验证拍照结果
            photo_verification = ella_app.verify_photo_taken_success(response_text)

            # 记录详细的验证信息
            verification_details = "\n".join(photo_verification["details"])
            allure.attach(
                f"拍照功能验证结果:\n"
                f"响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}\n"
                f"拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}\n"
                f"相机应用: {'✅' if photo_verification['camera_app_opened'] else '❌'}\n"
                f"综合结果: {'✅' if photo_verification['overall_success'] else '❌'}\n\n"
                f"详细信息:\n{verification_details}",
                name="拍照功能验证",
                attachment_type=allure.attachment_type.TEXT
            )

            # 验证拍照是否成功
            if photo_verification["overall_success"]:
                log.info("✅ 拍照功能验证通过")
            else:
                log.warning("⚠️ 拍照功能验证未完全通过，但测试继续")

        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_tts_photo_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态",
                             attachment_type=allure.attachment_type.PNG)

            # 总结测试结果
            test_summary = f"""
测试类型: TTS真实语音输入
测试命令: {command}
TTS语言: en-US
TTS音量: 80%
响应内容: {response_text}
响应有效性: {'✅' if photo_verification['response_valid'] else '❌'}
拍照信息: {'✅' if photo_verification['photo_info_found'] else '❌'}
相机应用: {'✅' if photo_verification['camera_app_opened'] else '❌'}
综合验证结果: {'✅' if photo_verification['overall_success'] else '❌'}
测试结果: 完成
"""
            allure.attach(test_summary, name="TTS语音测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 TTS真实语音输入拍照命令测试完成")

    @allure.title("测试多种拍照命令")
    @allure.description("通过Ella测试多种拍照相关命令")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.regression
    def test_various_photo_commands(self, ella_app):
        """测试多种拍照命令"""
        commands = [
            "take a picture",
            "capture a photo",
            "snap a photo",
            "open camera",
            "start camera app"
        ]

        for command in commands:
            with allure.step(f"测试命令: {command}"):
                # 确保在对话页面并准备输入
                chat_page_ready = ella_app.ensure_on_chat_page()
                if not chat_page_ready:
                    log.warning(f"无法确保在对话页面，跳过命令: {command}")
                    continue

                input_ready = ella_app.ensure_input_box_ready()
                if not input_ready:
                    log.warning(f"输入框未就绪，跳过命令: {command}")
                    continue

                # 执行命令
                success = ella_app.execute_text_command(command)
                if not success:
                    log.warning(f"命令执行失败: {command}")
                    continue

                # 等待响应
                if ella_app.wait_for_response(timeout=8):
                    response_text = ella_app.get_response_text()
                    log.info(f"命令 '{command}' 响应: {response_text}")

                    # 验证响应包含拍照相关内容
                    photo_keywords = ["photo", "拍照", "picture", "照片", "camera", "相机", "capture", "捕获", "snap", "快照"]
                    response_lower = response_text.lower() if response_text else ""

                    has_photo_content = any(keyword in response_lower for keyword in photo_keywords)
                    if has_photo_content:
                        log.info(f"✅ 命令 '{command}' 响应包含拍照相关内容")
                    else:
                        log.warning(f"⚠️ 命令 '{command}' 响应未包含拍照相关内容")

                    # 检查是否打开了相机应用
                    camera_opened = ella_app.check_camera_app_opened()
                    if camera_opened:
                        log.info(f"✅ 命令 '{command}' 成功打开相机应用")
                    else:
                        log.info(f"ℹ️ 命令 '{command}' 未检测到相机应用打开")

                # 短暂等待
                time.sleep(2)
