"""
Ella语音助手探索页面测试用例
测试探索页面的各种功能和交互
"""
import pytest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from pages.apps.ella.explore_page import EllaExplorePage
from pages.apps.ella.history.main_page import EllaMainPage
from core.logger import log
import time


class TestEllaExplorePage:
    """Ella探索页面测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        log.info("🚀 开始Ella探索页面测试")
        cls.main_page = EllaMainPage()
        cls.explore_page = EllaExplorePage()
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        log.info("🏁 Ella探索页面测试完成")
    
    def setup_method(self):
        """每个测试方法前的准备"""
        log.info("📋 准备测试环境")
        
        # 确保Ella应用已启动
        if not self.main_page.start_app_with_activity():
            pytest.fail("Ella应用启动失败")
        
        # 等待主页加载
        if not self.main_page.wait_for_page_load():
            pytest.fail("Ella主页加载失败")
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        log.info("🧹 清理测试环境")
        
        # 返回主页并清理状态
        try:
            self.explore_page.cleanup_and_return_home()
        except:
            # 如果清理失败，尝试重新启动应用
            self.main_page.start_app_with_activity()
    
    def test_navigate_to_explore_page(self):
        """测试导航到探索页面"""
        log.info("🧪 测试导航到探索页面")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        
        # 验证页面加载
        assert self.explore_page.wait_for_page_load(), "探索页面加载失败"
        
        # 验证页面元素
        element_results = self.explore_page.verify_page_elements()
        assert element_results['page_loaded'], "探索页面未正确加载"
        
        log.info("✅ 导航到探索页面测试通过")
    
    def test_search_functionality(self):
        """测试搜索功能"""
        log.info("🧪 测试探索页面搜索功能")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        assert self.explore_page.wait_for_page_load(), "探索页面加载失败"
        
        # 测试文本搜索
        search_query = "人工智能"
        assert self.explore_page.search_content(search_query), f"搜索'{search_query}'失败"
        
        # 等待搜索结果
        time.sleep(3)
        
        # 验证搜索后页面状态
        content_count = self.explore_page.get_content_count()
        assert content_count >= 0, "获取搜索结果数量失败"
        
        log.info(f"✅ 搜索功能测试通过，搜索结果数量: {content_count}")
    
    def test_content_interaction(self):
        """测试内容交互功能"""
        log.info("🧪 测试内容交互功能")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        assert self.explore_page.wait_for_content_load(), "内容加载失败"
        
        # 获取内容数量
        initial_count = self.explore_page.get_content_count()
        log.info(f"初始内容数量: {initial_count}")
        
        if initial_count > 0:
            # 测试点击内容项
            assert self.explore_page.click_content_item(0), "点击内容项失败"
            time.sleep(2)
            
            # 返回探索页面
            self.explore_page.navigate_to_explore()
        
        # 测试刷新功能
        assert self.explore_page.refresh_content(), "刷新内容失败"
        
        # 测试滚动功能
        assert self.explore_page.scroll_to_bottom(), "滚动到底部失败"
        assert self.explore_page.scroll_to_top(), "滚动到顶部失败"
        
        log.info("✅ 内容交互功能测试通过")
    
    def test_hot_topics(self):
        """测试热门话题功能"""
        log.info("🧪 测试热门话题功能")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        assert self.explore_page.wait_for_content_load(), "内容加载失败"
        
        # 获取热门话题
        topics = self.explore_page.get_hot_topics()
        log.info(f"获取到热门话题数量: {len(topics)}")
        
        if len(topics) > 0:
            # 测试点击第一个话题
            first_topic = topics[0]
            log.info(f"测试点击话题: {first_topic}")
            
            # 注意：这里可能会跳转到其他页面，所以要小心处理
            if self.explore_page.click_hot_topic(first_topic):
                log.info(f"✅ 成功点击话题: {first_topic}")
                time.sleep(2)
                
                # 尝试返回探索页面
                self.explore_page.navigate_to_explore()
            else:
                log.warning(f"点击话题失败: {first_topic}")
        
        log.info("✅ 热门话题功能测试完成")
    
    def test_page_performance(self):
        """测试页面性能"""
        log.info("🧪 测试探索页面性能")
        
        # 获取性能信息
        performance_info = self.explore_page.get_page_performance_info()
        
        # 验证性能指标
        assert performance_info['load_time'] > 0, "页面加载时间获取失败"
        assert performance_info['load_time'] < 30, f"页面加载时间过长: {performance_info['load_time']}秒"
        
        assert performance_info['content_count'] >= 0, "内容数量获取失败"
        
        # 记录性能信息
        log.info(f"页面性能信息: {performance_info}")
        
        # 性能评估
        if performance_info['load_time'] < 5:
            log.info("✅ 页面加载性能优秀")
        elif performance_info['load_time'] < 10:
            log.info("✅ 页面加载性能良好")
        else:
            log.warning("⚠️ 页面加载性能需要优化")
        
        log.info("✅ 页面性能测试完成")
    
    def test_comprehensive_functionality(self):
        """测试综合功能"""
        log.info("🧪 测试探索页面综合功能")
        
        # 执行综合测试
        assert self.explore_page.perform_comprehensive_test(), "综合功能测试失败"
        
        log.info("✅ 综合功能测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        log.info("🧪 测试错误处理功能")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        
        # 测试网络错误处理
        network_handled = self.explore_page.handle_network_error()
        log.info(f"网络错误处理结果: {network_handled}")
        
        # 测试空状态检查
        empty_state = self.explore_page.check_empty_state()
        log.info(f"空状态检查结果: {empty_state}")
        
        # 测试智能滚动查找不存在的元素
        found = self.explore_page.smart_scroll_to_element("不存在的元素文本")
        assert not found, "不应该找到不存在的元素"
        
        log.info("✅ 错误处理功能测试完成")
    
    def test_screenshot_functionality(self):
        """测试截图功能"""
        log.info("🧪 测试截图功能")
        
        # 导航到探索页面
        assert self.explore_page.navigate_to_explore(), "导航到探索页面失败"
        assert self.explore_page.wait_for_page_load(), "探索页面加载失败"
        
        # 获取带信息的截图
        screenshot_path = self.explore_page.get_page_screenshot_with_info()
        assert screenshot_path, "截图获取失败"
        assert os.path.exists(screenshot_path), f"截图文件不存在: {screenshot_path}"
        
        log.info(f"✅ 截图功能测试通过，截图路径: {screenshot_path}")


if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v", "-s"])
