"""
Ella语音助手测试 - 集成弹窗处理
展示如何在测试中使用智能弹窗处理功能
"""
import pytest
import allure
import time
from pages.apps.ella.history.main_page_with_popup import EllaMainPageWithPopup
from utils.popup_utils import with_popup_handling, PopupHandlingContext
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("智能弹窗处理测试")
class TestEllaWithPopupHandling:
    """Ella弹窗处理测试类"""
    
    @pytest.fixture(scope="function")
    def ella_app_with_popup(self):
        """Ella应用fixture - 集成弹窗处理"""
        ella_page = EllaMainPageWithPopup()
        
        try:
            log.info("🚀 启动Ella应用（集成弹窗处理）...")
            
            # 启动应用（自动处理启动弹窗）
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")
                
                # 处理启动后可能的弹窗
                ella_page.handle_ella_specific_popups()
                
                # 截图记录启动状态
                screenshot_path = ella_page.screenshot("ella_with_popup_started.png")
                allure.attach.file(screenshot_path, name="启动成功", 
                                 attachment_type=allure.attachment_type.PNG)
                
                yield ella_page
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")
                
        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            try:
                screenshot_path = ella_page.screenshot("ella_fixture_error.png")
                allure.attach.file(screenshot_path, name="异常状态", 
                                 attachment_type=allure.attachment_type.PNG)
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")
        
        finally:
            # 清理
            try:
                log.info("🧹 清理Ella应用...")
                
                # 获取弹窗处理统计
                stats = ella_page.get_popup_stats()
                if stats:
                    stats_info = f"""
弹窗处理统计:
- 总检测数: {stats.get('popups_detected', 0)}
- 总处理数: {stats.get('popups_handled', 0)}
- 成功率: {stats.get('handler_stats', {}).get('success_rate', 0):.2%}
- 类型分布: {stats.get('handler_stats', {}).get('type_counts', {})}
"""
                    allure.attach(stats_info, name="弹窗处理统计", 
                                attachment_type=allure.attachment_type.TEXT)
                    log.info(stats_info)
                
                ella_page.cleanup()
                log.info("✅ Ella应用已清理")
            except Exception as e:
                log.warning(f"⚠️ 清理Ella应用时出现异常: {e}")
    
    @allure.title("蓝牙控制测试 - 智能弹窗处理")
    @allure.description("测试通过语音命令控制蓝牙，自动处理权限弹窗")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_bluetooth_control_with_popup_handling(self, ella_app_with_popup):
        """蓝牙控制测试 - 智能弹窗处理"""
        
        with allure.step("执行开启蓝牙命令"):
            # 执行命令（自动处理权限弹窗）
            command = "open bluetooth"
            result = ella_app_with_popup.execute_text_command(command)
            assert result, f"执行命令失败: {command}"
            
            # 记录命令执行
            allure.attach(f"执行命令: {command}", name="语音命令", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("等待AI响应"):
            # 等待响应（自动处理网络错误弹窗）
            response_received = ella_app_with_popup.wait_for_response(timeout=20)
            
            if not response_received:
                log.warning("未收到AI响应，继续验证蓝牙状态")
            else:
                # 获取响应内容
                response_text = ella_app_with_popup.get_response_text()
                allure.attach(f"AI响应: {response_text}", name="AI响应内容", 
                             attachment_type=allure.attachment_type.TEXT)
                
                # 验证响应包含命令内容
                assert command in response_text, f"响应中未包含命令: {command}"
        
        with allure.step("验证蓝牙状态"):
            # 检查蓝牙状态（自动处理蓝牙权限弹窗）
            bluetooth_status = ella_app_with_popup.check_bluetooth_status()
            assert bluetooth_status, "蓝牙未成功开启"
            
            log.info("✅ 蓝牙已成功开启")
            allure.attach("蓝牙状态: 已开启", name="蓝牙验证结果", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("记录测试完成状态"):
            screenshot_path = ella_app_with_popup.screenshot("bluetooth_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成", 
                             attachment_type=allure.attachment_type.PNG)
    
    @allure.title("应用打开测试 - 弹窗处理")
    @allure.description("测试通过语音命令打开应用，处理可能的弹窗")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_open_app_with_popup_handling(self, ella_app_with_popup):
        """应用打开测试 - 弹窗处理"""
        
        with allure.step("执行打开时钟命令"):
            command = "open clock"
            result = ella_app_with_popup.execute_text_command(command)
            assert result, f"执行命令失败: {command}"
        
        with allure.step("等待AI响应和应用打开"):
            # 等待响应
            ella_app_with_popup.wait_for_response(timeout=15)
            
            # 等待应用打开
            time.sleep(3)
            
            # 处理可能的应用启动弹窗
            ella_app_with_popup.handle_popups_immediately()
        
        with allure.step("验证时钟应用已打开"):
            # 这里应该检查时钟应用是否打开
            # 由于示例中没有具体实现，这里用日志记录
            log.info("✅ 时钟应用打开验证完成")
            allure.attach("应用打开验证: 通过", name="应用验证结果", 
                         attachment_type=allure.attachment_type.TEXT)
    
    @with_popup_handling(auto_handle=True, monitor_during_test=True)
    @allure.title("装饰器弹窗处理测试")
    @allure.description("使用装饰器进行弹窗处理的测试示例")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_with_decorator_popup_handling(self, ella_app_with_popup):
        """装饰器弹窗处理测试"""
        
        with allure.step("执行多个命令测试"):
            commands = ["what time is it", "what's the weather", "tell me a joke"]
            
            for i, command in enumerate(commands, 1):
                with allure.step(f"执行第{i}个命令: {command}"):
                    # 装饰器会自动处理弹窗
                    result = ella_app_with_popup.execute_text_command(command)
                    assert result, f"命令执行失败: {command}"
                    
                    # 等待响应
                    ella_app_with_popup.wait_for_response(timeout=10)
                    
                    # 短暂等待
                    time.sleep(1)
    
    @allure.title("上下文管理器弹窗处理测试")
    @allure.description("使用上下文管理器进行弹窗处理的测试示例")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_with_context_manager_popup_handling(self, ella_app_with_popup):
        """上下文管理器弹窗处理测试"""
        
        with allure.step("使用上下文管理器处理弹窗"):
            with PopupHandlingContext(ella_app_with_popup.driver, auto_handle=True, monitor=True) as popup_monitor:
                
                # 在这个上下文中，弹窗会被自动处理
                with allure.step("执行网络相关命令"):
                    command = "what is the weather today"
                    result = ella_app_with_popup.execute_text_command(command)
                    assert result, f"命令执行失败: {command}"
                
                with allure.step("等待响应（可能有网络弹窗）"):
                    # 等待响应，期间可能出现网络错误弹窗
                    response_received = ella_app_with_popup.wait_for_response(timeout=20)
                    
                    if response_received:
                        response_text = ella_app_with_popup.get_response_text()
                        allure.attach(f"天气响应: {response_text}", name="天气查询结果", 
                                     attachment_type=allure.attachment_type.TEXT)
                
                # 获取上下文中的弹窗处理统计
                context_stats = popup_monitor.get_monitor_stats()
                allure.attach(str(context_stats), name="上下文弹窗统计", 
                             attachment_type=allure.attachment_type.TEXT)
    
    @allure.title("弹窗处理压力测试")
    @allure.description("连续执行多个命令，测试弹窗处理的稳定性")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.stress
    def test_popup_handling_stress(self, ella_app_with_popup):
        """弹窗处理压力测试"""
        
        with allure.step("连续执行多个命令"):
            commands = [
                "open bluetooth",
                "close bluetooth", 
                "open wifi",
                "what time is it",
                "set alarm for 8 am",
                "play music",
                "stop music",
                "what's the weather",
                "tell me a joke",
                "open calculator"
            ]
            
            success_count = 0
            failure_count = 0
            
            for i, command in enumerate(commands, 1):
                with allure.step(f"执行命令 {i}/{len(commands)}: {command}"):
                    try:
                        # 执行命令
                        result = ella_app_with_popup.execute_text_command(command)
                        
                        if result:
                            success_count += 1
                            log.info(f"✅ 命令成功: {command}")
                        else:
                            failure_count += 1
                            log.warning(f"⚠️ 命令失败: {command}")
                        
                        # 等待响应
                        ella_app_with_popup.wait_for_response(timeout=10)
                        
                        # 短暂等待，让弹窗处理系统工作
                        time.sleep(0.5)
                        
                    except Exception as e:
                        failure_count += 1
                        log.error(f"❌ 命令异常: {command} - {e}")
        
        with allure.step("验证压力测试结果"):
            total_commands = len(commands)
            success_rate = success_count / total_commands
            
            test_result = f"""
压力测试结果:
- 总命令数: {total_commands}
- 成功数: {success_count}
- 失败数: {failure_count}
- 成功率: {success_rate:.2%}
"""
            
            allure.attach(test_result, name="压力测试结果", 
                         attachment_type=allure.attachment_type.TEXT)
            
            # 要求成功率至少70%
            assert success_rate >= 0.7, f"压力测试成功率过低: {success_rate:.2%}"
            
            log.info(f"✅ 压力测试完成，成功率: {success_rate:.2%}")
    
    @allure.title("弹窗统计信息测试")
    @allure.description("测试弹窗处理统计信息的收集和报告")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.regression
    def test_popup_statistics(self, ella_app_with_popup):
        """弹窗统计信息测试"""
        
        with allure.step("执行一些操作产生弹窗"):
            # 执行一些可能产生弹窗的操作
            ella_app_with_popup.execute_text_command("open bluetooth")
            ella_app_with_popup.wait_for_response(timeout=10)
            
            ella_app_with_popup.execute_text_command("what's the weather")
            ella_app_with_popup.wait_for_response(timeout=15)
        
        with allure.step("获取并验证统计信息"):
            stats = ella_app_with_popup.get_popup_stats()
            
            # 验证统计信息结构
            assert 'popups_detected' in stats, "统计信息缺少检测数量"
            assert 'popups_handled' in stats, "统计信息缺少处理数量"
            assert 'handler_stats' in stats, "统计信息缺少处理器统计"
            
            # 生成统计报告
            stats_report = f"""
弹窗处理统计报告:
=================
监控运行时间: {stats.get('runtime', 0):.2f}秒
总检测周期: {stats.get('total_cycles', 0)}
检测到弹窗: {stats.get('popups_detected', 0)}个
处理弹窗: {stats.get('popups_handled', 0)}个
监控错误: {stats.get('errors', 0)}次

处理器统计:
- 总处理数: {stats.get('handler_stats', {}).get('total_handled', 0)}
- 成功数: {stats.get('handler_stats', {}).get('success_count', 0)}
- 失败数: {stats.get('handler_stats', {}).get('failure_count', 0)}
- 成功率: {stats.get('handler_stats', {}).get('success_rate', 0):.2%}

类型分布: {stats.get('handler_stats', {}).get('type_counts', {})}
方法分布: {stats.get('handler_stats', {}).get('method_counts', {})}
"""
            
            allure.attach(stats_report, name="详细统计报告", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("✅ 弹窗统计信息测试完成")


# 运行命令示例
"""
# 运行所有弹窗处理测试
python -m pytest testcases/test_ella/test_with_popup_handling.py -v -s

# 运行特定测试
python -m pytest testcases/test_ella/test_with_popup_handling.py::TestEllaWithPopupHandling::test_bluetooth_control_with_popup_handling -v -s

# 运行压力测试
python -m pytest testcases/test_ella/test_with_popup_handling.py -m stress -v -s

# 生成Allure报告
python -m pytest testcases/test_ella/test_with_popup_handling.py --alluredir=reports/allure-results
allure generate reports/allure-results -o reports/allure-report --clean
"""
