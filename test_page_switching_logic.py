#!/usr/bin/env python3
"""
测试页面切换逻辑优化
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


class MockEllaApp:
    """模拟Ella应用，用于测试页面切换逻辑"""
    
    def __init__(self):
        self.current_page = "ella"  # 当前页面: "ella", "contacts", "camera", etc.
        self.response_ready = True
        self.return_attempts = 0
        self.max_return_attempts = 2  # 模拟返回失败次数
        
    def wait_for_response(self, timeout=8):
        """模拟等待响应"""
        log.info(f"模拟等待响应 {timeout}秒")
        return self.response_ready
    
    def ensure_on_chat_page(self):
        """模拟检查是否在Ella对话页面"""
        is_on_ella = self.current_page == "ella"
        log.info(f"检查是否在Ella页面: {is_on_ella} (当前页面: {self.current_page})")
        return is_on_ella
    
    def return_to_ella_app(self):
        """模拟返回Ella应用"""
        self.return_attempts += 1
        log.info(f"模拟返回Ella应用 (第{self.return_attempts}次尝试)")
        
        # 模拟前几次返回失败，最后一次成功
        if self.return_attempts <= self.max_return_attempts:
            log.warning(f"模拟返回失败 (第{self.return_attempts}次)")
            return False
        else:
            log.info(f"模拟返回成功 (第{self.return_attempts}次)")
            self.current_page = "ella"
            return True
    
    def get_response_all_text(self):
        """模拟获取所有响应文本"""
        if self.current_page == "ella":
            return ["蓝牙已打开", "设备可发现"]
        else:
            log.warning(f"不在Ella页面，无法获取响应文本 (当前页面: {self.current_page})")
            return []
    
    def get_response_text(self):
        """模拟获取响应文本"""
        if self.current_page == "ella":
            return "蓝牙已打开"
        else:
            log.warning(f"不在Ella页面，无法获取响应文本 (当前页面: {self.current_page})")
            return ""
    
    def execute_text_command(self, command):
        """模拟执行命令"""
        log.info(f"模拟执行命令: {command}")
        
        # 模拟执行命令后可能跳转到其他应用
        if "contact" in command.lower():
            self.current_page = "contacts"
            log.info("模拟跳转到联系人应用")
        elif "camera" in command.lower():
            self.current_page = "camera"
            log.info("模拟跳转到相机应用")
        elif "bluetooth" in command.lower():
            # 蓝牙命令通常不跳转
            log.info("蓝牙命令，保持在Ella页面")
        
        return True
    
    def check_bluetooth_status(self):
        """模拟检查蓝牙状态"""
        return False
    
    def check_bluetooth_status_smart(self):
        """模拟智能检查蓝牙状态"""
        # 智能方法会自动返回Ella页面
        if self.current_page != "ella":
            log.info("智能方法检测到不在Ella页面，自动返回")
            self.current_page = "ella"
        return True
    
    def check_contacts_app_opened(self):
        """模拟检查联系人应用状态"""
        return self.current_page == "contacts"
    
    def check_contacts_app_opened_smart(self):
        """模拟智能检查联系人应用状态"""
        # 智能方法会自动返回Ella页面
        was_contacts_open = self.current_page == "contacts"
        if self.current_page != "ella":
            log.info("智能方法检测到不在Ella页面，自动返回")
            self.current_page = "ella"
        return was_contacts_open
    
    def set_scenario(self, scenario):
        """设置测试场景"""
        if scenario == "bluetooth_command":
            self.current_page = "ella"
            self.response_ready = True
            self.return_attempts = 0
        elif scenario == "contacts_command":
            self.current_page = "ella"
            self.response_ready = True
            self.return_attempts = 0
        elif scenario == "response_timeout":
            self.current_page = "ella"
            self.response_ready = False
            self.return_attempts = 0
        elif scenario == "return_failure":
            self.current_page = "contacts"
            self.response_ready = True
            self.return_attempts = 0
            self.max_return_attempts = 5  # 模拟多次返回失败


class TestPageSwitchingLogic:
    """测试页面切换逻辑"""
    
    def __init__(self):
        self.test_instance = BaseEllaTest()
        self.mock_app = MockEllaApp()
    
    def test_bluetooth_command_flow(self):
        """测试蓝牙命令流程（不跳转应用）"""
        print("\n📋 测试1: 蓝牙命令流程（不跳转应用）")
        
        self.mock_app.set_scenario("bluetooth_command")
        
        try:
            initial_status, final_status, response_text = self.test_instance.execute_command_and_verify(
                self.mock_app, "open bluetooth", expected_status_change=True
            )
            
            print(f"  初始状态: {initial_status}")
            print(f"  最终状态: {final_status}")
            print(f"  响应文本: {response_text}")
            print("  ✅ 蓝牙命令流程测试通过")
            
        except Exception as e:
            print(f"  ❌ 蓝牙命令流程测试失败: {e}")
    
    def test_contacts_command_flow(self):
        """测试联系人命令流程（会跳转应用）"""
        print("\n📋 测试2: 联系人命令流程（会跳转应用）")
        
        self.mock_app.set_scenario("contacts_command")
        
        try:
            initial_status, final_status, response_text = self.test_instance.execute_command_and_verify(
                self.mock_app, "open contacts", expected_status_change=True
            )
            
            print(f"  初始状态: {initial_status}")
            print(f"  最终状态: {final_status}")
            print(f"  响应文本: {response_text}")
            print("  ✅ 联系人命令流程测试通过")
            
        except Exception as e:
            print(f"  ❌ 联系人命令流程测试失败: {e}")
    
    def test_response_timeout_scenario(self):
        """测试响应超时场景"""
        print("\n📋 测试3: 响应超时场景")
        
        self.mock_app.set_scenario("response_timeout")
        
        try:
            initial_status, final_status, response_text = self.test_instance.execute_command_and_verify(
                self.mock_app, "open bluetooth", expected_status_change=True
            )
            
            print(f"  初始状态: {initial_status}")
            print(f"  最终状态: {final_status}")
            print(f"  响应文本: {response_text}")
            print("  ✅ 响应超时场景测试通过")
            
        except Exception as e:
            print(f"  ❌ 响应超时场景测试失败: {e}")
    
    def test_return_failure_scenario(self):
        """测试返回Ella失败场景"""
        print("\n📋 测试4: 返回Ella失败场景")
        
        self.mock_app.set_scenario("return_failure")
        
        try:
            # 测试新的响应获取方法
            response_text = self.test_instance._wait_and_get_response_after_status_check(self.mock_app)
            
            print(f"  响应文本: {response_text}")
            print("  ✅ 返回失败场景测试通过")
            
        except Exception as e:
            print(f"  ❌ 返回失败场景测试失败: {e}")
    
    def test_safe_get_response_text(self):
        """测试安全获取响应文本方法"""
        print("\n📋 测试5: 安全获取响应文本方法")
        
        # 测试在Ella页面获取响应
        self.mock_app.current_page = "ella"
        response_text = self.test_instance._safe_get_response_text(self.mock_app)
        print(f"  在Ella页面获取响应: {response_text}")
        
        # 测试在其他页面获取响应
        self.mock_app.current_page = "contacts"
        response_text = self.test_instance._safe_get_response_text(self.mock_app)
        print(f"  在联系人页面获取响应: {response_text}")
        
        print("  ✅ 安全获取响应文本测试通过")
    
    def test_execution_order_verification(self):
        """测试执行顺序验证"""
        print("\n📋 测试6: 执行顺序验证")
        
        self.mock_app.set_scenario("contacts_command")
        
        # 记录执行步骤
        execution_steps = []
        
        # 重写mock方法来记录执行顺序
        original_execute = self.mock_app.execute_text_command
        original_wait = self.mock_app.wait_for_response
        original_smart_check = self.mock_app.check_contacts_app_opened_smart
        original_get_response = self.mock_app.get_response_all_text
        
        def track_execute(command):
            execution_steps.append(f"1. 执行命令: {command}")
            return original_execute(command)
        
        def track_wait(timeout=8):
            execution_steps.append(f"2. 等待响应: {timeout}秒")
            return original_wait(timeout)
        
        def track_smart_check():
            execution_steps.append("3. 智能状态检查（会返回Ella页面）")
            return original_smart_check()
        
        def track_get_response():
            execution_steps.append("4. 获取响应文本")
            return original_get_response()
        
        self.mock_app.execute_text_command = track_execute
        self.mock_app.wait_for_response = track_wait
        self.mock_app.check_contacts_app_opened_smart = track_smart_check
        self.mock_app.get_response_all_text = track_get_response
        
        try:
            initial_status, final_status, response_text = self.test_instance.execute_command_and_verify(
                self.mock_app, "open contacts", expected_status_change=True
            )
            
            print("  执行顺序:")
            for step in execution_steps:
                print(f"    {step}")
            
            print("  ✅ 执行顺序验证通过")
            
        except Exception as e:
            print(f"  ❌ 执行顺序验证失败: {e}")
        
        # 恢复原始方法
        self.mock_app.execute_text_command = original_execute
        self.mock_app.wait_for_response = original_wait
        self.mock_app.check_contacts_app_opened_smart = original_smart_check
        self.mock_app.get_response_all_text = original_get_response
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始测试页面切换逻辑优化...")
        
        test_methods = [
            self.test_bluetooth_command_flow,
            self.test_contacts_command_flow,
            self.test_response_timeout_scenario,
            self.test_return_failure_scenario,
            self.test_safe_get_response_text,
            self.test_execution_order_verification
        ]
        
        passed = 0
        total = len(test_methods)
        
        for test_method in test_methods:
            try:
                test_method()
                passed += 1
            except Exception as e:
                print(f"❌ 测试方法 {test_method.__name__} 执行异常: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n🎉 测试完成: {passed}/{total} 个测试通过")


if __name__ == "__main__":
    tester = TestPageSwitchingLogic()
    tester.run_all_tests()
