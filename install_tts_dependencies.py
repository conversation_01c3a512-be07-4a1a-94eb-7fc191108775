"""
TTS依赖安装脚本
解决SSL证书问题，自动安装所有TTS相关依赖
"""
import subprocess
import sys
import os


def install_package(package_name, use_official_pypi=False):
    """
    安装Python包，处理SSL证书问题
    
    Args:
        package_name: 包名
        use_official_pypi: 是否使用官方PyPI源
    """
    print(f"📦 正在安装 {package_name}...")
    
    # 尝试多种安装方式
    install_commands = []
    
    if use_official_pypi:
        # 使用官方PyPI源
        install_commands.append([
            sys.executable, '-m', 'pip', 'install', package_name,
            '-i', 'https://pypi.org/simple/'
        ])
    
    # 使用国内镜像源（跳过SSL验证）
    install_commands.extend([
        # 阿里云镜像
        [sys.executable, '-m', 'pip', 'install', package_name,
         '-i', 'https://mirrors.aliyun.com/pypi/simple/',
         '--trusted-host', 'mirrors.aliyun.com'],
        
        # 豆瓣镜像
        [sys.executable, '-m', 'pip', 'install', package_name,
         '-i', 'https://pypi.douban.com/simple/',
         '--trusted-host', 'pypi.douban.com'],
        
        # 中科大镜像
        [sys.executable, '-m', 'pip', 'install', package_name,
         '-i', 'https://pypi.mirrors.ustc.edu.cn/simple/',
         '--trusted-host', 'pypi.mirrors.ustc.edu.cn'],
        
        # 清华镜像（跳过SSL验证）
        [sys.executable, '-m', 'pip', 'install', package_name,
         '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/',
         '--trusted-host', 'pypi.tuna.tsinghua.edu.cn'],
        
        # 官方源（跳过SSL验证）
        [sys.executable, '-m', 'pip', 'install', package_name,
         '--trusted-host', 'pypi.org',
         '--trusted-host', 'pypi.python.org',
         '--trusted-host', 'files.pythonhosted.org']
    ])
    
    for i, cmd in enumerate(install_commands, 1):
        try:
            print(f"  尝试方法 {i}: {' '.join(cmd[4:])}")  # 只显示关键参数
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=120  # 2分钟超时
            )
            
            if result.returncode == 0:
                print(f"  ✅ {package_name} 安装成功！")
                return True
            else:
                print(f"  ❌ 方法 {i} 失败: {result.stderr.strip()}")
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 方法 {i} 超时")
        except Exception as e:
            print(f"  ❌ 方法 {i} 异常: {e}")
    
    print(f"  ❌ {package_name} 安装失败，已尝试所有方法")
    return False


def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🎤 TTS依赖自动安装脚本")
    print("=" * 60)
    
    # TTS相关包列表
    packages = [
        ('edge-tts', 'edge_tts', 'Microsoft Edge TTS'),
        ('gtts', 'gtts', 'Google Text-to-Speech'),
        ('pyttsx3', 'pyttsx3', '离线TTS引擎'),
        ('pygame', 'pygame', '音频播放库'),
        ('playsound', 'playsound', '简单音频播放'),
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for package_name, import_name, description in packages:
        print(f"\n📋 检查 {description} ({package_name})")
        
        if check_package_installed(import_name):
            print(f"  ✅ {package_name} 已安装")
            installed_count += 1
        else:
            print(f"  📦 {package_name} 未安装，开始安装...")
            
            # 先尝试官方源，再尝试镜像源
            success = (install_package(package_name, use_official_pypi=True) or
                      install_package(package_name, use_official_pypi=False))
            
            if success:
                installed_count += 1
            else:
                print(f"  ⚠️ {package_name} 安装失败，可能需要手动安装")
    
    print("\n" + "=" * 60)
    print("📊 安装结果总结")
    print("=" * 60)
    
    for package_name, import_name, description in packages:
        status = "✅ 已安装" if check_package_installed(import_name) else "❌ 未安装"
        print(f"{description:20} : {status}")
    
    print("-" * 60)
    print(f"📋 总计: {installed_count}/{total_count} 个包安装成功")
    
    if installed_count == total_count:
        print("🎉 所有TTS依赖安装完成！")
        print("💡 现在可以使用完整的TTS语音功能")
    elif installed_count >= 2:
        print("⚠️ 部分依赖安装成功，TTS功能基本可用")
        print("💡 建议手动安装失败的包以获得最佳体验")
    else:
        print("❌ 大部分依赖安装失败")
        print("💡 建议检查网络连接或手动安装")
    
    print("\n🔧 手动安装命令（如果自动安装失败）:")
    print("pip install gtts -i https://pypi.org/simple/")
    print("pip install edge-tts --trusted-host pypi.org")
    print("pip install pyttsx3 --trusted-host pypi.org")
    print("pip install pygame --trusted-host pypi.org")
    
    print("=" * 60)
    
    return installed_count >= 2


def install_specific_package():
    """安装特定包的交互式函数"""
    packages = {
        '1': ('gtts', 'Google Text-to-Speech'),
        '2': ('edge-tts', 'Microsoft Edge TTS'),
        '3': ('pyttsx3', '离线TTS引擎'),
        '4': ('pygame', '音频播放库'),
        '5': ('playsound', '简单音频播放')
    }
    
    print("\n🎯 选择要安装的包:")
    for key, (package, desc) in packages.items():
        print(f"  {key}. {desc} ({package})")
    
    choice = input("\n请输入选项 (1-5): ").strip()
    
    if choice in packages:
        package_name, description = packages[choice]
        print(f"\n📦 安装 {description} ({package_name})")
        
        success = (install_package(package_name, use_official_pypi=True) or
                  install_package(package_name, use_official_pypi=False))
        
        if success:
            print(f"✅ {package_name} 安装成功！")
        else:
            print(f"❌ {package_name} 安装失败")
            print(f"💡 尝试手动安装: pip install {package_name} -i https://pypi.org/simple/")
    else:
        print("❌ 无效选项")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='TTS依赖安装脚本')
    parser.add_argument('--interactive', '-i', action='store_true', 
                       help='交互式安装特定包')
    parser.add_argument('--package', '-p', type=str,
                       help='安装指定的包')
    
    args = parser.parse_args()
    
    if args.interactive:
        install_specific_package()
    elif args.package:
        success = install_package(args.package, use_official_pypi=True)
        if not success:
            install_package(args.package, use_official_pypi=False)
    else:
        main()
