"""
环境设置脚本
帮助用户快速设置测试环境
"""
import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("请安装Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True


def install_dependencies():
    """安装Python依赖"""
    print("\n安装Python依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Python依赖安装失败: {e}")
        return False


def check_adb():
    """检查ADB是否可用"""
    print("\n检查ADB...")
    try:
        result = subprocess.run(["adb", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ADB可用")
            print(f"ADB版本: {result.stdout.split()[4]}")
            return True
        else:
            print("❌ ADB不可用")
            return False
    except FileNotFoundError:
        print("❌ ADB未找到")
        print("请安装Android SDK Platform Tools并添加到PATH")
        return False


def check_devices():
    """检查连接的设备"""
    print("\n检查连接的设备...")
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
        devices = [line.split()[0] for line in lines if line.strip() and 'device' in line]
        
        if devices:
            print(f"✅ 找到 {len(devices)} 个设备:")
            for device in devices:
                print(f"  - {device}")
            return True
        else:
            print("⚠️ 未找到连接的设备")
            print("请确保:")
            print("1. 设备已连接并开启USB调试")
            print("2. 或者启动了Android模拟器")
            return False
    except Exception as e:
        print(f"❌ 检查设备失败: {e}")
        return False


def check_allure():
    """检查Allure是否安装"""
    print("\n检查Allure...")
    try:
        result = subprocess.run(["allure", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Allure已安装")
            print(f"Allure版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Allure不可用")
            return False
    except FileNotFoundError:
        print("⚠️ Allure未安装")
        print("安装方法:")
        if platform.system() == "Windows":
            print("1. 使用Scoop: scoop install allure")
            print("2. 或下载二进制文件: https://github.com/allure-framework/allure2/releases")
        elif platform.system() == "Darwin":
            print("使用Homebrew: brew install allure")
        else:
            print("下载二进制文件: https://github.com/allure-framework/allure2/releases")
        return False


def create_directories():
    """创建必要的目录"""
    print("\n创建必要的目录...")
    directories = [
        "reports/screenshots",
        "reports/allure-results", 
        "reports/allure-report",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def run_quick_test():
    """运行快速测试"""
    print("\n是否运行快速测试? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes']:
        print("\n运行快速测试...")
        try:
            subprocess.run([sys.executable, "quick_start.py"], check=True)
            print("✅ 快速测试完成")
        except subprocess.CalledProcessError:
            print("❌ 快速测试失败")
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")


def main():
    """主函数"""
    print("🚀 Android自动化测试框架 - 环境设置")
    print("=" * 50)
    
    # 检查列表
    checks = [
        ("Python版本", check_python_version),
        ("安装依赖", install_dependencies),
        ("ADB工具", check_adb),
        ("连接设备", check_devices),
        ("Allure工具", check_allure),
    ]
    
    results = []
    
    # 执行检查
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            results.append((name, False))
    
    # 创建目录
    create_directories()
    
    # 总结
    print("\n" + "=" * 50)
    print("环境检查总结:")
    print("=" * 50)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    # 统计
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n通过: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 环境设置完成！所有检查都通过了")
        print("\n下一步:")
        print("1. 运行 'python quick_start.py' 进行快速测试")
        print("2. 运行 'python run_tests.py --smoke' 执行冒烟测试")
        print("3. 查看 README.md 了解更多使用方法")
        
        # 询问是否运行快速测试
        run_quick_test()
        
    else:
        print("\n⚠️ 部分检查未通过，请根据上述提示解决问题")
        print("\n常见问题解决:")
        print("1. Python版本: 安装Python 3.7+")
        print("2. ADB工具: 安装Android SDK Platform Tools")
        print("3. 设备连接: 开启USB调试，信任计算机")
        print("4. Allure工具: 按照提示安装Allure")


if __name__ == "__main__":
    main()
