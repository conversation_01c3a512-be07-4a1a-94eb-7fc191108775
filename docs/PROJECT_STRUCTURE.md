# 项目目录结构

本文档描述了Android自动化测试项目的目录结构和文件组织方式。

## 📁 根目录结构

```
app_test/
├── config/                  # 配置文件
├── core/                    # 核心框架代码
├── debug/                   # 调试相关文件
├── docs/                    # 项目文档
├── logs/                    # 日志文件
├── pages/                   # 页面对象模型
├── reports/                 # 测试报告和结果
├── testcases/              # 测试用例
├── tools/                   # 工具脚本
├── utils/                   # 工具函数
├── README.md               # 项目说明
├── requirements.txt        # Python依赖
├── pytest.ini            # pytest配置
├── setup.py               # 项目安装配置
├── start.bat              # 快速启动脚本
├── run_tests.py           # 测试运行器 🆕
├── screenshot_manager.py  # 截图管理工具 🆕
└── cleanup_manager.py     # 项目清理工具 🆕
```

## 📂 详细目录说明

### config/ - 配置文件
```
config/
├── __init__.py
├── backups/               # 配置备份
├── config.yaml           # 主配置文件
├── devices.yaml          # 设备配置
└── popup_config.yaml     # 弹窗配置
```

### core/ - 核心框架
```
core/
├── __init__.py
├── base_driver.py         # 基础驱动类
├── base_element.py        # 基础元素类
├── base_page.py          # 基础页面类
├── data_driven_test.py   # 数据驱动测试
├── logger.py             # 日志模块
├── popup_handler.py      # 弹窗处理器
└── popup_monitor.py      # 弹窗监控器
```

### debug/ - 调试文件 🆕
```
debug/
├── README.md
├── temp_tests/           # 临时测试文件
├── response_tests/       # 响应检测调试
├── navigation_tests/     # 页面导航调试
├── popup_tests/         # 弹窗处理调试
├── screenshot_tests/    # 截图功能调试
└── optimization_tests/  # 性能优化调试
```

### docs/ - 项目文档
```
docs/
├── README.md
├── reports/             # 文档报告
├── summaries/          # 功能总结文档 🆕
├── Excel_Driven_Test_Design.md
├── Popup_Handler_Design.md
└── PROJECT_STRUCTURE.md  # 本文档
```

### pages/ - 页面对象模型
```
pages/
├── __init__.py
├── apps/                # 应用页面
│   ├── calculator/      # 计算器应用
│   ├── ella/           # Ella语音助手
│   └── settings/       # 设置应用
├── base/               # 基础页面类
└── base_page_with_popup.py
```

### reports/ - 测试报告
```
reports/
├── allure-report/       # Allure HTML报告
├── allure-results/      # Allure原始数据
├── screenshots/         # 测试截图
├── debug_reports/      # 调试报告 🆕
├── improvement_reports/ # 改进报告 🆕
├── test_reports/       # 测试报告 🆕
└── *.json/*.xml        # 元素结构文件
```

### testcases/ - 测试用例
```
testcases/
├── __init__.py
├── conftest.py         # pytest配置
├── test_calculator/    # 计算器测试
├── test_ella/         # Ella测试
└── test_settings/     # 设置测试
```

### tools/ - 工具脚本 🆕
```
tools/
├── README.md
├── debug_tools/        # 调试工具 🆕
│   ├── element_inspector.py
│   ├── device_manager.py
│   ├── fix_version_issue.py
│   └── install_allure.py
├── optimization_tools/ # 优化工具 🆕
│   ├── screenshot_manager_tool.py
│   └── update_locators.py
├── examples/          # 示例代码
├── exploration/       # 探索脚本
├── temp_tests/       # 临时测试
├── quick_start.py    # 快速启动
└── run_tests.py      # 测试运行器
```

### utils/ - 工具函数
```
utils/
├── __init__.py
├── allure-2.34.0/     # Allure工具
├── device_config_manager.py
├── device_discovery.py
├── excel_utils.py
├── file_utils.py
├── popup_utils.py
├── screenshot_utils.py
├── uiautomator2_manager.py
└── yaml_utils.py
```

## 🔄 目录整理说明

### 整理前的问题
- 根目录下有大量临时调试文件
- 测试脚本和工具脚本混杂
- 报告文件散落在各处
- 缺乏清晰的文件分类

### 整理后的改进
1. **📁 新增debug目录** - 统一管理所有调试相关文件
2. **🔧 重组tools目录** - 按功能分类工具脚本
3. **📊 规范reports目录** - 按类型组织报告文件
4. **📚 完善docs目录** - 集中管理项目文档
5. **🧹 清理缓存文件** - 删除所有__pycache__目录

### 文件移动映射

#### 调试文件 → debug/
- `debug_response.py` → `debug/response_tests/`
- `test_*_response.py` → `debug/response_tests/`
- `test_ella_navigation.py` → `debug/navigation_tests/`
- `test_popup_*.py` → `debug/popup_tests/`
- `test_screenshot_*.py` → `debug/screenshot_tests/`
- `test_*_improved.py` → `debug/optimization_tests/`

#### 工具脚本 → tools/
- `element_inspector.py` → `tools/debug_tools/`
- `device_manager.py` → `tools/debug_tools/`
- `screenshot_manager_tool.py` → `tools/optimization_tools/`
- `update_locators.py` → `tools/optimization_tools/`

#### 报告文件 → reports/
- `*_report_*.txt` → `reports/improvement_reports/`
- `popup_*_test_report_*.txt` → `reports/test_reports/`

#### 文档总结 → docs/summaries/
- `*_SUMMARY.md` → `docs/summaries/`

## 📋 使用指南

### 开发调试
- 调试脚本放在 `debug/` 对应子目录
- 工具脚本放在 `tools/` 对应子目录
- 临时文件使用 `debug/temp_tests/`

### 测试执行
- 正式测试用例在 `testcases/`
- 使用 `run_tests.py` 运行测试（根目录）
- 查看 `reports/` 目录获取结果

### 文档维护
- 功能总结放在 `docs/summaries/`
- 设计文档放在 `docs/`
- 报告文档放在 `docs/reports/`

### 项目维护 🆕
- 使用 `screenshot_manager.py` 管理截图文件
- 使用 `cleanup_manager.py` 清理过期文件
- 定期运行清理工具保持项目整洁

## 🎯 最佳实践

1. **文件命名规范**
   - 测试文件：`test_*.py`
   - 工具文件：`*_tool.py` 或 `*_manager.py`
   - 配置文件：`*.yaml` 或 `*.json`

2. **目录使用原则**
   - 临时文件 → `debug/temp_tests/`
   - 调试脚本 → `debug/` 对应子目录
   - 正式代码 → 对应功能目录
   - 工具脚本 → `tools/` 对应子目录

3. **清理维护**
   - 定期清理 `debug/temp_tests/`
   - 及时移动调试文件到正确位置
   - 保持根目录整洁

## 📝 更新记录

- **2025-06-23**: 完成项目目录整理
  - 新增debug、tools子目录分类
  - 移动33个文件到合适位置
  - 创建README文档
  - 清理缓存文件
  - 更新.gitignore
