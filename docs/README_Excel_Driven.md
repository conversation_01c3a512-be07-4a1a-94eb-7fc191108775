# Ella Excel数据驱动测试说明

本文档介绍如何使用Excel文件进行Ella语音助手的数据驱动测试。

## 📋 功能特性

- ✅ 支持从Excel文件读取测试数据
- ✅ 支持step（测试步骤）和except（期望结果）字段
- ✅ 支持多种断言类型（蓝牙状态、应用打开、响应内容等）
- ✅ 支持测试用例优先级和标签过滤
- ✅ 支持参数化测试和批量测试
- ✅ 自动生成Allure测试报告
- ✅ 支持截图和错误记录

## 📁 文件结构

```
testcases/test_ella/
├── Ella_Test_Cases.xlsx          # Excel测试数据文件
├── test_excel_driven.py          # Excel数据驱动测试脚本
├── test_bluetooth_command.py     # 原始蓝牙测试脚本
├── test_open_clock_command.py    # 原始时钟测试脚本
└── README_Excel_Driven.md        # 本说明文档
```

## 📊 Excel文件格式

Excel文件需要包含以下列：

| 列名 | 必需 | 说明 | 示例 |
|------|------|------|------|
| case_id | 否 | 用例ID | ELLA_001 |
| case_name | 否 | 用例名称 | 测试开启蓝牙 |
| step | ✅ | 测试步骤 | open bluetooth |
| except | ✅ | 期望结果 | 蓝牙已开启 |
| priority | 否 | 优先级 | high/normal/low |
| enabled | 否 | 是否启用 | TRUE/FALSE |
| description | 否 | 用例描述 | 通过语音命令开启蓝牙功能 |
| tags | 否 | 标签 | bluetooth,voice |
| timeout | 否 | 超时时间(秒) | 15 |

### 示例Excel数据

| case_id | case_name | step | except | priority | enabled | description | tags | timeout |
|---------|-----------|------|--------|----------|---------|-------------|------|---------|
| ELLA_001 | 测试开启蓝牙 | open bluetooth | 蓝牙已开启 | high | TRUE | 通过语音命令开启蓝牙功能 | bluetooth,voice | 15 |
| ELLA_002 | 测试关闭蓝牙 | close bluetooth | 蓝牙已关闭 | high | TRUE | 通过语音命令关闭蓝牙功能 | bluetooth,voice | 15 |
| ELLA_003 | 测试打开时钟 | open clock | com.android.deskclock | normal | TRUE | 通过语音命令打开时钟应用 | app,voice | 10 |
| ELLA_004 | 测试查询天气 | what is the weather today | 天气 | normal | TRUE | 查询今天的天气情况 | weather,voice | 20 |

## 🎯 断言类型说明

### 1. 蓝牙状态断言
- **期望值**: `蓝牙已开启`、`蓝牙已关闭`、`bluetooth_on`、`bluetooth_off`
- **验证方式**: 通过ADB命令检查系统蓝牙状态
- **示例**: `open bluetooth` -> `蓝牙已开启`

### 2. 应用打开断言
- **期望值**: 应用包名（如 `com.android.deskclock`）
- **验证方式**: 检查当前前台应用是否为指定包名
- **示例**: `open clock` -> `com.android.deskclock`

### 3. 响应内容断言
- **期望值**: 响应中应包含的关键词
- **验证方式**: 检查AI响应文本是否包含期望内容
- **示例**: `what is the weather today` -> `天气`

### 4. 计算结果断言
- **期望值**: 数字结果
- **验证方式**: 检查计算器返回的结果
- **示例**: `2+3` -> `5`

## 🚀 使用方法

### 1. 准备Excel文件

创建或编辑 `testcases/test_ella/Ella_Test_Cases.xlsx` 文件，按照上述格式填写测试数据。

### 2. 运行完整数据驱动测试

```bash
# 运行所有Excel测试用例
python -m pytest testcases/test_ella/test_excel_driven.py::TestEllaExcelDriven::test_excel_driven_complete -v -s

# 生成Allure报告
python -m pytest testcases/test_ella/test_excel_driven.py::TestEllaExcelDriven::test_excel_driven_complete -v -s --alluredir=reports/allure-results
```

### 3. 运行参数化测试

```bash
# 运行参数化测试（每个Excel行生成一个独立测试用例）
python -m pytest testcases/test_ella/test_excel_driven.py::TestEllaExcelDriven::test_excel_parametrized -v -s
```

### 4. 运行高优先级测试

```bash
# 只运行高优先级测试用例
python -m pytest testcases/test_ella/test_excel_driven.py::TestEllaExcelDriven::test_excel_high_priority -v -s
```

### 5. 运行特定标签测试

```bash
# 只运行蓝牙相关测试用例
python -m pytest testcases/test_ella/test_excel_driven.py::TestEllaExcelDriven::test_excel_bluetooth_only -v -s
```

### 6. 运行所有Excel驱动测试

```bash
# 运行整个Excel驱动测试类
python -m pytest testcases/test_ella/test_excel_driven.py -v -s
```

## 🔧 高级用法

### 1. 自定义Excel文件路径

```python
from core.data_driven_test import DataDrivenTest

# 使用自定义Excel文件
data_driven = DataDrivenTest("path/to/your/excel_file.xlsx")
test_data = data_driven.get_test_data()
```

### 2. 过滤测试数据

```python
from utils.excel_utils import ExcelUtils

# 读取测试数据
test_data = ExcelUtils.read_test_data("../testcases/test_ella/Ella_Test_Cases.xlsx")

# 按优先级过滤
high_priority_data = ExcelUtils.filter_test_data(test_data, priority='high')

# 按标签过滤
bluetooth_data = ExcelUtils.filter_test_data(test_data, tags='bluetooth')
```

### 3. 创建新的Excel模板

```python
from utils.excel_utils import ExcelUtils

# 创建示例Excel文件
ExcelUtils.create_sample_excel("new_test_cases.xlsx")
```

## 📸 截图管理

测试过程中会自动截图，截图按测试类名分文件夹保存：

```
reports/screenshots/
├── TestEllaExcelDriven/
│   ├── excel_test_测试开启蓝牙_initial.png
│   ├── excel_test_测试开启蓝牙_step_executed.png
│   ├── excel_test_测试开启蓝牙_response.png
│   └── excel_test_测试开启蓝牙_completed.png
└── ...
```

## 🐛 错误处理

### 常见问题及解决方案

1. **Excel文件不存在**
   - 确保Excel文件路径正确
   - 运行测试前会自动创建示例文件

2. **缺少必要列**
   - 确保Excel文件包含 `step` 和 `except` 列
   - 检查列名拼写是否正确

3. **测试数据为空**
   - 检查 `enabled` 列是否为 `TRUE`
   - 确保 `step` 列不为空

4. **断言失败**
   - 检查期望结果格式是否正确
   - 查看测试日志了解具体失败原因

## 📈 测试报告

使用Allure生成详细的测试报告：

```bash
# 生成报告
allure generate reports/allure-results -o reports/allure-report --clean

# 打开报告
allure open reports/allure-report
```

报告包含：
- 测试用例执行结果
- 测试步骤详情
- 截图附件
- 测试数据概览
- 失败原因分析

## 🎉 优势

1. **数据与代码分离**: 测试数据存储在Excel中，便于维护
2. **批量测试**: 一次运行多个测试用例
3. **灵活过滤**: 支持按优先级、标签等条件过滤
4. **详细报告**: 自动生成包含截图的详细测试报告
5. **易于扩展**: 支持多种断言类型，可轻松添加新的验证方式
6. **非技术人员友好**: 测试人员可直接编辑Excel文件添加测试用例

## 📝 注意事项

1. 确保设备已连接且Ella应用可正常启动
2. 测试前建议清理应用状态，避免干扰
3. 蓝牙测试可能需要设备权限，确保已授权
4. 网络相关测试（如天气查询）需要网络连接
5. 建议在稳定的测试环境中运行，避免外部干扰
