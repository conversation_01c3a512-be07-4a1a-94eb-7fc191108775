# 项目清理工具使用指南

本指南介绍如何使用项目根目录下的清理和管理工具。

## 🧹 工具概览

项目提供了两个主要的清理管理工具：

1. **`screenshot_manager.py`** - 专门管理测试截图
2. **`cleanup_manager.py`** - 综合项目文件清理工具

## 📸 截图管理工具 (screenshot_manager.py)

### 基本功能

**查看截图统计**
```bash
python screenshot_manager.py --summary
```
输出示例：
```
📊 截图统计信息
总文件数: 77
总大小: 45.30 MB
测试类数量: 15

📂 按测试类分组:
  TestEllaBluetoothCommand: 25个文件, 15.2MB
  TestCalculatorBasic: 12个文件, 8.5MB
  TestSettingsBasic: 8个文件, 4.1MB
```

**查看目录结构**
```bash
python screenshot_manager.py --structure
```

**清理过期截图**
```bash
# 预览7天前的截图（不实际删除）
python screenshot_manager.py --cleanup 7

# 实际清理7天前的截图
python screenshot_manager.py --cleanup 7 --confirm

# 清理所有截图
python screenshot_manager.py --cleanup 0 --confirm
```

**备份截图**
```bash
python screenshot_manager.py --backup
```

### 高级功能

**查看完整信息**
```bash
python screenshot_manager.py --all
```

**自定义清理天数**
```bash
# 清理3天前的截图
python screenshot_manager.py --cleanup 3 --confirm

# 清理30天前的截图
python screenshot_manager.py --cleanup 30 --confirm
```

## 🗂️ 项目清理工具 (cleanup_manager.py)

### 支持的清理类型

| 类型 | 描述 | 默认保留天数 | 文件类型 |
|------|------|-------------|----------|
| `screenshots` | 测试截图 | 7天 | .png, .jpg, .jpeg |
| `logs` | 日志文件 | 14天 | .log, .txt |
| `allure_results` | Allure测试结果 | 30天 | .json, .txt, .properties |
| `debug_reports` | 调试报告 | 14天 | .txt, .log, .json |
| `test_reports` | 测试报告 | 30天 | .txt, .log, .html |
| `temp_files` | 临时测试文件 | 3天 | .py, .txt, .log |

### 基本使用

**查看项目文件统计**
```bash
python cleanup_manager.py --stats
```
输出示例：
```
📊 项目文件统计
============================================================

📂 测试截图 (reports/screenshots)
   文件数量: 77
   总大小: 45.30MB
   平均年龄: 8.2天
   过期文件: 60 个 (>7天)

📂 日志文件 (logs)
   文件数量: 1
   总大小: 0.01MB
   平均年龄: 0.0天
   过期文件: 0 个 (>14天)
```

**预览清理操作**
```bash
# 预览所有过期文件
python cleanup_manager.py

# 预览特定类型的过期文件
python cleanup_manager.py --type screenshots --days 7
```

**执行清理操作**
```bash
# 清理所有过期文件
python cleanup_manager.py --confirm

# 清理特定类型文件
python cleanup_manager.py --type logs --days 14 --confirm

# 清理临时文件（3天前）
python cleanup_manager.py --type temp_files --confirm
```

### 高级使用

**自定义清理天数**
```bash
# 清理1天前的截图
python cleanup_manager.py --type screenshots --days 1 --confirm

# 清理7天前的日志
python cleanup_manager.py --type logs --days 7 --confirm
```

**显示完整信息**
```bash
python cleanup_manager.py --all
```

## 🔄 日常维护建议

### 每日维护
```bash
# 查看项目文件统计
python cleanup_manager.py --stats
```

### 每周维护
```bash
# 清理过期截图
python screenshot_manager.py --cleanup 7 --confirm

# 清理临时文件
python cleanup_manager.py --type temp_files --confirm
```

### 每月维护
```bash
# 清理所有过期文件
python cleanup_manager.py --confirm

# 备份重要截图
python screenshot_manager.py --backup
```

## 🛡️ 安全提示

### 预览模式
- 所有清理命令默认为预览模式
- 只有添加 `--confirm` 参数才会实际删除文件
- 建议先预览，确认无误后再执行

### 备份建议
- 重要截图建议先备份：`python screenshot_manager.py --backup`
- 重要日志文件手动备份到其他位置
- 定期检查清理结果，确保没有误删

### 恢复方法
- 如果误删文件，检查系统回收站
- 重要文件建议使用版本控制系统管理
- 定期备份整个项目目录

## 📋 常用命令速查

```bash
# 快速查看项目状态
python cleanup_manager.py --stats

# 快速清理截图
python screenshot_manager.py --cleanup 7 --confirm

# 快速清理临时文件
python cleanup_manager.py --type temp_files --confirm

# 完整项目清理
python cleanup_manager.py --all
python cleanup_manager.py --confirm

# 备份重要文件
python screenshot_manager.py --backup
```

## 🔧 自定义配置

### 修改默认保留天数
编辑 `cleanup_manager.py` 中的 `cleanup_targets` 配置：

```python
'screenshots': {
    'path': 'reports/screenshots',
    'description': '测试截图',
    'extensions': ['.png', '.jpg', '.jpeg'],
    'default_days': 7  # 修改这里
}
```

### 添加新的清理类型
在 `cleanup_targets` 中添加新的配置项：

```python
'my_files': {
    'path': 'my_directory',
    'description': '我的文件',
    'extensions': ['.txt', '.log'],
    'default_days': 10
}
```

## ❓ 故障排除

### 常见问题

**Q: 工具运行报错找不到模块**
A: 确保在项目根目录下运行命令

**Q: 清理后文件还在**
A: 检查是否使用了 `--confirm` 参数

**Q: 想恢复被清理的文件**
A: 检查系统回收站，或从备份中恢复

**Q: 清理速度很慢**
A: 大量文件时清理需要时间，请耐心等待

### 获取帮助
```bash
# 查看截图管理工具帮助
python screenshot_manager.py --help

# 查看项目清理工具帮助
python cleanup_manager.py --help
```
