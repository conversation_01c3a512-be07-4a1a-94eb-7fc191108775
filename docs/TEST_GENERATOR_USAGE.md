# Ella测试用例生成工具使用说明 🛠️

## 🎯 工具概述

`tools/test_generator.py` 是一个智能的测试用例生成工具，可以根据Ella指令自动生成标准化的测试脚本，大大提高测试开发效率。

## 🚀 快速使用

### 方法1: 交互式生成（推荐）
```bash
cd tools
python ella_test_generator_v2.py
```

交互式生成会要求您输入期望响应，确保测试用例准确：
```
🚀 Ella测试用例生成工具
==================================================
💡 提示: 期望响应将通过交互式方式获取，确保响应内容准确

请输入要测试的命令 (输入 'quit' 退出):
> open bluetooth

📝 为命令 'open bluetooth' 设置期望响应:
💡 建议的期望响应: ['Done', 'bluetooth']

请选择:
1. 使用建议的响应
2. 自定义响应
3. 输入多个响应项
请选择 (1/2/3): 3

请输入期望响应项（每行一个，输入空行结束）:
响应项 1: Done
响应项 2: 蓝牙已成功开启
响应项 3: bluetooth
响应项 4:

✅ 测试用例已生成: testcases/test_ella/open_app/test_open_bluetooth.py
```

### 方法2: 编程式生成（指定期望响应）

```python
from tools.ella_test_generator_v2 import generate_with_custom_response, batch_generate_with_responses

# 生成单个测试用例，指定期望响应
file_path = generate_with_custom_response(
    "open bluetooth",
    ["Done", "蓝牙已成功开启", "bluetooth"]
)
print(f"生成的文件: {file_path}")

# 批量生成，每个命令都指定期望响应
commands_and_responses = [
    ("open bluetooth", ["Done", "蓝牙已开启"]),
    ("open camera", ["Done", "相机已打开"]),
    ("navigate to disneyland", ["Done", "正在为您导航"]),
    ("order a burger", ["Sorry", "暂不支持订餐功能"])
]

generated_files = batch_generate_with_responses(commands_and_responses)
print(f"批量生成完成，共 {len(generated_files)} 个文件")
```

### 方法3: 自动建议模式（不推荐）

```python
from tools.ella_test_generator_v2 import EllaTestGenerator

generator = EllaTestGenerator()

# 使用自动建议的期望响应（可能不准确）
file_path = generator.generate_test_case("open camera")
print(f"生成的文件: {file_path}")
```

## 🧠 智能特性

### 1. 自动命令类型检测
工具会自动分析命令并分类：

```python
# 应用打开类 -> 需要状态验证
"open bluetooth" -> app_open (True)
"open camera" -> app_open (True)
"打开联系人" -> app_open (True)

# 第三方集成类 -> 只验证响应
"navigate to disneyland" -> third_party (False)
"order a burger" -> third_party (False)
"download app" -> third_party (False)

# 系统设置类 -> 需要状态验证
"switch to performance mode" -> system_setting (True)
"设置闪光通知" -> system_setting (True)
```

### 2. 智能命名生成
```python
# 输入命令 -> 自动生成的名称
"open bluetooth" -> {
    "class_name": "TestEllaOpenBluetooth",
    "method_name": "test_open_bluetooth", 
    "file_name": "test_open_bluetooth.py"
}

"navigate to shanghai disneyland" -> {
    "class_name": "TestEllaNavigateShanghaiDisneyland",
    "method_name": "test_navigate_to_shanghai_disneyland",
    "file_name": "test_navigate_to_shanghai_disneyland.py"
}
```

### 3. 智能期望响应处理 ⭐
工具提供三种期望响应处理方式：

#### 交互式输入（推荐）
```python
# 工具会显示建议响应，用户可以选择：
📝 为命令 'open bluetooth' 设置期望响应:
💡 建议的期望响应: ['Done', 'bluetooth']

请选择:
1. 使用建议的响应          # 快速选择
2. 自定义响应              # 输入单个响应
3. 输入多个响应项          # 输入多个响应项

# 用户输入示例：
响应项 1: Done
响应项 2: 蓝牙已成功开启
响应项 3: bluetooth
```

#### 编程式指定（精确）
```python
# 直接指定期望响应，确保准确性
generate_with_custom_response(
    "open bluetooth",
    ["Done", "蓝牙已成功开启", "bluetooth"]
)
```

#### 自动建议（仅供参考）
```python
# 基于命令内容自动建议期望响应（可能不准确）
"open bluetooth" -> ["Done", "bluetooth"]
"open camera" -> ["Done", "camera"]
"navigate to xxx" -> ["Done", ""]
"switch to performance mode" -> ["Sorry"]  # 某些功能可能不支持
```

> ⚠️ **重要提醒**: 期望响应必须基于实际测试结果，自动建议仅供参考！

### 4. 自动文件分类
```python
# 应用打开类 -> testcases/test_ella/open_app/
"open bluetooth" -> testcases/test_ella/open_app/test_open_bluetooth.py

# 第三方集成类 -> testcases/test_ella/third_coupling/  
"navigate to xxx" -> testcases/test_ella/third_coupling/test_navigate_to_xxx.py
```

## 📋 支持的命令模式

### 应用打开类
```python
✅ "open bluetooth"
✅ "open camera" 
✅ "open contacts"
✅ "open weather"
✅ "open flashlight"
✅ "open settings"
✅ "打开蓝牙"
✅ "打开相机"
```

### 第三方集成类
```python
✅ "navigate to shanghai disneyland"
✅ "order a burger"
✅ "download app"
✅ "search for restaurants"
✅ "play music"
✅ "call john"
```

### 系统设置类
```python
✅ "switch to performance mode"
✅ "switch to power saving mode"
✅ "switch to flash notification"
✅ "设置性能模式"
✅ "切换到省电模式"
```

## 🎨 生成的代码特性

### 应用打开类模板特性
- ✅ 完整的状态验证逻辑
- ✅ 智能选择验证方法
- ✅ 标准化的断言消息
- ✅ 完整的Allure报告集成
- ✅ 截图和测试摘要

### 第三方集成类模板特性
- ✅ 跳过状态验证 (`verify_status=False`)
- ✅ 类属性方式定义命令和期望
- ✅ 简化的验证流程
- ✅ 标准化的错误消息

## 🔧 自定义配置

### 1. 添加新的应用类型
编辑 `config/status_check_config.json`:
```json
{
  "status_check_config": {
    "new_app": {
      "keywords": ["new_app", "新应用"],
      "initial_method": "check_new_app_status",
      "final_method": "check_new_app_status_smart",
      "description": "新应用状态"
    }
  }
}
```

### 2. 自定义输出路径
```python
generator = EllaTestGenerator()
file_path = generator.generate_test_case(
    "open calculator", 
    output_path="/custom/path/test_calculator.py"
)
```

## 📊 生成统计示例

```
🧪 测试生成蓝牙指令...
检测到命令类型: app_open, 需要状态验证: True
类名: TestEllaOpenBluetooth
方法名: test_open_bluetooth
文件名: test_open_bluetooth.py
期望响应: ['Done', 'bluetooth']
验证方法: check_bluetooth_status_smart
✅ 测试用例已生成: testcases/test_ella/open_app/test_open_bluetooth.py

🧪 测试生成导航指令...
检测到命令类型: third_party, 需要状态验证: False
类名: TestEllaNavigateShanghaiDisneyland
方法名: test_navigate_to_shanghai_disneyland
文件名: test_navigate_to_shanghai_disneyland.py
期望响应: ['Done', '']
验证方法: check_google_map_app_opened
✅ 测试用例已生成: testcases/test_ella/third_coupling/test_navigate_to_shanghai_disneyland.py
```

## 🎯 最佳实践

### 1. 期望响应设置（最重要）⭐
```python
✅ 推荐做法:
# 1. 先手动测试命令，记录实际响应
# 2. 基于实际响应设置期望内容
# 3. 包含关键信息，避免过于宽泛

# 示例：实际测试后发现响应为 "蓝牙已成功开启，设备可被发现"
expected_text = ["Done", "蓝牙已成功开启", "设备可被发现"]

❌ 避免做法:
# 1. 完全依赖自动建议
# 2. 设置过于具体的响应（容易失败）
# 3. 设置过于宽泛的响应（验证不充分）

# 示例：过于具体
expected_text = ["蓝牙已成功开启，设备可被发现，当前连接设备数量：0"]

# 示例：过于宽泛
expected_text = ["Done"]  # 验证不充分
```

### 2. 命令输入规范
```python
✅ 推荐: "open bluetooth"
✅ 推荐: "navigate to shanghai disneyland"
✅ 推荐: "switch to performance mode"

❌ 避免: "bluetooth"  # 不够明确
❌ 避免: "go to disneyland"  # 使用标准动词
```

### 3. 生成后必检项
生成代码后**必须**检查：
- [ ] **期望响应是否基于实际测试结果**（最重要）
- [ ] 验证方法是否正确
- [ ] 类名和方法名是否符合规范
- [ ] 文件路径是否正确
- [ ] 是否需要状态验证

### 4. 测试验证流程
```python
# 推荐的测试开发流程：
1. 手动执行命令，记录实际响应
2. 使用工具生成测试用例，输入实际响应
3. 运行生成的测试用例
4. 根据测试结果调整期望响应
5. 确认测试稳定通过
```

### 5. 手动调整指南
生成的代码是基础模板，常见调整项：
- **期望响应内容**（最常调整）
- 验证逻辑（特殊场景）
- 错误消息（提高可读性）
- 测试步骤（复杂场景）

## ⚠️ 注意事项

1. **覆盖文件**: 如果目标文件已存在，工具会直接覆盖
2. **配置依赖**: 工具依赖 `config/status_check_config.json` 配置文件
3. **路径要求**: 需要在项目根目录或正确设置Python路径
4. **编码格式**: 生成的文件使用UTF-8编码

## 🔍 故障排除

### 问题1: 找不到配置文件
```
❌ 错误: 加载配置失败: [Errno 2] No such file or directory: 'config/status_check_config.json'

✅ 解决: 确保在项目根目录运行，或检查配置文件是否存在
```

### 问题2: 生成的验证方法不存在
```
❌ 错误: AttributeError: 'EllaDialoguePage' object has no attribute 'check_xxx_status'

✅ 解决: 在页面类中添加对应的验证方法，或修改配置文件
```

### 问题3: 类名冲突
```
❌ 错误: 生成的类名与现有类重复

✅ 解决: 手动修改生成的类名，或删除现有文件
```

---

## 📚 相关文档
- [测试脚本编写指南](ELLA_TEST_SCRIPT_GUIDE.md)
- [快速入门指南](ELLA_QUICK_START.md)
- [配置文件说明](../config/)
