# Ella测试指令脚本编写指南

## 📋 目录
- [框架概述](#框架概述)
- [测试类型分类](#测试类型分类)
- [基础测试模板](#基础测试模板)
- [验证方法详解](#验证方法详解)
- [配置文件说明](#配置文件说明)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 🏗️ 框架概述

### 核心组件
```
testcases/test_ella/
├── base_ella_test.py          # 基础测试类
├── open_app/                  # 应用打开类指令
├── third_coupling/            # 第三方集成类指令
└── config/
    ├── status_check_config.json    # 状态检查配置
    └── process_cleanup_config.json # 进程清理配置
```

### 测试基类层次
```python
BaseEllaTest                   # 完整功能基类
└── SimpleEllaTest            # 简化版基类（推荐）
```

## 🎯 测试类型分类

### 1. 应用打开类指令 (open_app/)
**特征**: 打开系统应用或第三方应用
**验证点**:
- ✅ 响应内容验证
- ✅ 应用状态验证 
- ✅ 进程检查验证

**支持的应用类型**:
```json
{
  "bluetooth": ["bluetooth", "蓝牙"],
  "contacts": ["contact", "contacts", "联系人", "通讯录", "phone", "dialer"],
  "weather": ["weather", "天气"],
  "camera": ["camera", "photo", "相机", "拍照"],
  "wifi": ["wifi", "wi-fi", "无线网络"],
  "flashlight": ["flashlight", "手电筒", "闪光灯"],
  "clock": ["clock", "alarm", "timer", "时钟", "闹钟", "定时器"],
  "facebook": ["facebook", "fb"],
  "settings": ["settings", "setting", "设置"],
  "music": ["music", "音乐", "播放器"],
  "gallery": ["gallery", "photos", "相册", "图片"],
  "calculator": ["calculator", "计算器"],
  "google_maps": ["disneyland", "迪士尼", "地图", "google maps", "导航"],
  "browser": ["browser", "chrome", "浏览器"]
}
```

### 2. 第三方集成类指令 (third_coupling/)
**特征**: 调用第三方服务或复杂系统功能
**验证点**:
- ✅ 响应内容验证
- ❌ 应用状态验证（通常不适用）
- ❌ 进程检查验证（通常不适用）

**常见类型**:
- 导航指令: `navigate to shanghai disneyland`
- 订购指令: `order a burger`, `order a takeaway`
- 下载指令: `download app`, `download basketball`
- 系统设置: `switch to performance mode`, `switch to flash notification`
- 充电模式: `switch to hyper charge`, `switch to smart charge`

### 3. 系统状态类指令
**特征**: 改变系统设置或状态
**验证点**:
- ✅ 响应内容验证
- ✅ 系统状态验证
- ❌ 应用打开验证（通常不适用）

## 📝 基础测试模板

### 模板1: 应用打开类指令
```python
"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenXXX(SimpleEllaTest):
    """Ella打开XXX应用测试类"""

    @allure.title("测试open xxx")
    @allure.description("测试打开XXX应用的指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_xxx(self, ella_app):
        """测试open xxx命令"""
        command = "open xxx"
        app_name = 'xxx'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = ["Done", "xxx"]  # 根据实际情况调整
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

### 模板2: 第三方集成类指令
```python
"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaXXXCommand(SimpleEllaTest):
    """Ella XXX命令测试类"""
    command = "your command here"
    expected_text = ["Done", ""]  # 根据实际情况调整

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_xxx_command(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

## 🔍 验证方法详解

### 1. 响应内容验证
```python
# 基础验证方法
def verify_expected_in_response(self, expected_text, response_text):
    """
    验证期望内容是否在响应中
    
    Args:
        expected_text: 期望的文本内容，可以是字符串或字符串列表
        response_text: 实际响应文本，可以是字符串或字符串列表
    
    Returns:
        bool: 验证是否通过
    """

# 高级验证方法
def verify_expected_in_response_advanced(self, expected_text, response_text,
                                       search_mode: str = "combined",
                                       match_any: bool = False):
    """
    高级版本的响应验证方法，支持多种搜索模式
    
    Args:
        expected_text: 期望的文本内容
        response_text: 实际响应文本
        search_mode: 搜索模式 ("combined", "individual", "any_match")
        match_any: 是否只需匹配任意一个期望内容
    """
```

### 2. 状态验证方法

#### 系统状态验证
```python
# 蓝牙状态
ella_app.check_bluetooth_status()          # 基础检查
ella_app.check_bluetooth_status_smart()    # 智能检查（推荐）

# WiFi状态
ella_app.check_wifi_status()

# 手电筒状态
ella_app.check_flashlight_status()
```

#### 应用状态验证
```python
# 联系人应用
ella_app.check_contacts_app_opened()       # 基础检查
ella_app.check_contacts_app_opened_smart() # 智能检查（推荐）

# 相机应用
ella_app.check_camera_app_opened()

# 天气应用
ella_app.check_weather_app_opened()

# Facebook应用
ella_app.check_facebook_app_opened()

# 设置应用
ella_app.check_settings_opened()

# 时钟应用
ella_app.check_alarm_status()

# Google地图应用
ella_app.check_google_map_app_opened()

# 浏览器应用
ella_app.check_browser_app_opened()

# 音乐应用
ella_app.check_music_app_opened()

# 相册应用
ella_app.check_gallery_app_opened()

# 计算器应用
ella_app.check_calculator_app_opened()
```

### 3. 智能状态检查机制
框架会根据命令内容自动选择合适的验证方法：

```python
# 自动检测命令类型并选择验证方法
def simple_command_test(self, ella_app, command: str, verify_status: bool = True):
    """
    极简命令测试方法
    
    Args:
        ella_app: Ella应用实例
        command: 要测试的命令
        verify_status: 是否验证状态变化（默认True）
    
    Returns:
        tuple: (初始状态, 最终状态, 响应文本)
    """
```

## ⚙️ 配置文件说明

### 状态检查配置 (config/status_check_config.json)
```json
{
  "status_check_config": {
    "bluetooth": {
      "keywords": ["bluetooth", "蓝牙"],
      "initial_method": "check_bluetooth_status",
      "final_method": "check_bluetooth_status_smart",
      "description": "蓝牙状态"
    }
  }
}
```

### 进程清理配置 (config/process_cleanup_config.json)
```json
{
  "process_cleanup_config": {
    "common_user_apps": [
      {
        "package": "com.transsion.aivoiceassistant",
        "description": "Ella语音助手",
        "category": "system_app"
      }
    ],
    "cleanup_settings": {
      "gentle_cleanup_enabled": true,
      "recent_apps_fallback_enabled": true,
      "min_apps_for_fallback": 5
    }
  }
}
```

## 💡 最佳实践

### 1. 命名规范
```python
# 文件命名
test_open_bluetooth.py          # 应用打开类
test_navigate_to_disneyland.py  # 第三方集成类

# 类命名
class TestEllaOpenBluetooth(SimpleEllaTest):
class TestEllaNavigateToDisneyland(SimpleEllaTest):

# 方法命名
def test_open_bluetooth(self, ella_app):
def test_navigate_to_disneyland(self, ella_app):
```

### 2. 期望响应设置
```python
# 成功响应
expected_text = ["Done", "已打开", "成功"]

# 失败响应
expected_text = ["Sorry", "无法", "失败"]

# 特定功能响应
expected_text = ["bluetooth", "蓝牙"]  # 功能相关关键词
```

### 3. 验证策略选择

#### 应用打开类指令
```python
# 必须验证
✅ 响应内容验证
✅ 应用状态验证
✅ 截图记录

# 示例
with allure.step("验证响应包含Done"):
    expected_text = ["Done"]
    result = self.verify_expected_in_response(expected_text, response_text)
    assert result

with allure.step("验证蓝牙已打开"):
    assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"
```

#### 第三方集成类指令
```python
# 必须验证
✅ 响应内容验证
✅ 截图记录

# 可选验证
❓ 应用状态验证（根据具体功能决定）

# 示例
with allure.step(f"执行命令: {command}"):
    initial_status, final_status, response_text = self.simple_command_test(
        ella_app, command, verify_status=False  # 不验证状态
    )
```

### 4. 错误处理
```python
# 响应验证失败
assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

# 状态验证失败
assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

# 自定义验证失败
assert custom_check(), f"自定义验证失败: 详细信息"
```

## 📋 快速参考表

### 指令类型判断表
| 指令特征 | 类型 | 验证要求 | 模板选择 |
|---------|------|----------|----------|
| `open xxx`, `打开xxx` | 应用打开类 | 响应+状态+截图 | 模板1 |
| `navigate to`, `order`, `download` | 第三方集成类 | 响应+截图 | 模板2 |
| `switch to`, `设置xxx` | 系统设置类 | 响应+状态+截图 | 模板1 |

### 验证方法速查表
| 功能类型 | 基础方法 | 智能方法 | 推荐使用 |
|---------|----------|----------|----------|
| 蓝牙 | `check_bluetooth_status()` | `check_bluetooth_status_smart()` | 智能方法 |
| WiFi | `check_wifi_status()` | - | 基础方法 |
| 手电筒 | `check_flashlight_status()` | - | 基础方法 |
| 联系人 | `check_contacts_app_opened()` | `check_contacts_app_opened_smart()` | 智能方法 |
| 相机 | `check_camera_app_opened()` | - | 基础方法 |
| 天气 | `check_weather_app_opened()` | - | 基础方法 |
| Facebook | `check_facebook_app_opened()` | - | 基础方法 |
| 设置 | `check_settings_opened()` | - | 基础方法 |
| 时钟 | `check_alarm_status()` | - | 基础方法 |
| 地图 | `check_google_map_app_opened()` | - | 基础方法 |
| 浏览器 | `check_browser_app_opened()` | - | 基础方法 |
| 音乐 | `check_music_app_opened()` | - | 基础方法 |
| 相册 | `check_gallery_app_opened()` | - | 基础方法 |
| 计算器 | `check_calculator_app_opened()` | - | 基础方法 |

### 期望响应速查表
| 响应类型 | 推荐配置 | 使用场景 |
|---------|----------|----------|
| 成功响应 | `["Done", "已完成", "成功"]` | 大部分成功指令 |
| 失败响应 | `["Sorry", "无法", "失败"]` | 预期失败的指令 |
| 功能响应 | `["bluetooth", "蓝牙"]` | 特定功能关键词 |
| 空响应 | `["", "Done"]` | 某些系统指令 |

### 测试标记速查表
| 标记 | 用途 | 示例 |
|------|------|------|
| `@pytest.mark.smoke` | 冒烟测试 | 核心功能测试 |
| `@pytest.mark.regression` | 回归测试 | 完整功能测试 |
| `@allure.severity_level.CRITICAL` | 严重级别 | 核心功能 |
| `@allure.severity_level.NORMAL` | 普通级别 | 一般功能 |

## ❓ 常见问题

### Q1: 如何判断指令类型？
**A**: 参考上方"指令类型判断表"，根据指令特征快速分类。

### Q2: 什么时候需要验证状态？
**A**:
- ✅ 应用打开类指令：必须验证
- ❌ 第三方集成类指令：通常不验证
- ✅ 系统设置类指令：必须验证

### Q3: 如何选择期望响应？
**A**: 参考上方"期望响应速查表"，根据指令预期结果选择。

### Q4: 智能检查和基础检查的区别？
**A**:
- **基础检查**: 直接检查状态，速度快
- **智能检查**: 包含进程检查、自动返回等，更可靠（推荐）

### Q5: 如何添加新的状态检查？
**A**:
```python
# 方法1: 在配置文件中添加 (推荐)
# 编辑 config/status_check_config.json

# 方法2: 使用代码动态添加
self.add_custom_status_check(
    command_type="new_app",
    keywords=["new", "新应用"],
    initial_method="check_new_app_status",
    final_method="check_new_app_status_smart"
)
```

### Q6: 测试失败时如何调试？
**A**:
1. 查看Allure报告中的截图
2. 检查日志文件中的详细信息
3. 使用调试模式运行单个测试
4. 验证设备连接和应用状态

### Q7: 如何处理不稳定的测试？
**A**:
1. 增加等待时间
2. 使用智能检查方法
3. 添加重试机制
4. 检查测试环境是否干净

## 🚀 高级功能

### 1. 自定义验证方法
```python
class TestCustomVerification(SimpleEllaTest):
    def test_custom_command(self, ella_app):
        command = "custom command"

        # 执行命令
        initial_status, final_status, response_text = self.simple_command_test(
            ella_app, command, verify_status=False
        )

        # 自定义验证逻辑
        with allure.step("自定义验证"):
            # 例：验证特定UI元素存在
            element_exists = ella_app.driver.find_element_by_text("特定文本")
            assert element_exists, "未找到期望的UI元素"

            # 例：验证系统设置
            setting_value = self.get_system_setting("setting_key")
            assert setting_value == "expected_value", f"设置值不正确: {setting_value}"
```

### 2. 数据驱动测试
```python
import pytest

class TestDataDriven(SimpleEllaTest):
    @pytest.mark.parametrize("command,expected", [
        ("open bluetooth", ["Done", "蓝牙"]),
        ("open camera", ["Done", "相机"]),
        ("open contacts", ["Done", "联系人"]),
    ])
    def test_multiple_commands(self, ella_app, command, expected):
        """数据驱动的多命令测试"""
        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应"):
            result = self.verify_expected_in_response(expected, response_text)
            assert result, f"命令 '{command}' 验证失败"
```

### 3. 条件验证
```python
def test_conditional_verification(self, ella_app):
    command = "open bluetooth"

    initial_status, final_status, response_text = self.simple_command_test(
        ella_app, command
    )

    # 根据响应内容进行不同验证
    if "Done" in str(response_text):
        # 成功情况的验证
        assert final_status, "蓝牙应该已开启"
    elif "Sorry" in str(response_text):
        # 失败情况的验证
        with allure.step("验证失败原因"):
            # 检查是否是权限问题
            # 检查是否是硬件问题
            pass
    else:
        pytest.fail(f"未知响应类型: {response_text}")
```

### 4. 多步骤验证
```python
def test_multi_step_verification(self, ella_app):
    """多步骤验证示例"""

    # 步骤1: 检查初始状态
    with allure.step("检查初始蓝牙状态"):
        initial_bluetooth = ella_app.check_bluetooth_status()

    # 步骤2: 执行命令
    with allure.step("执行开启蓝牙命令"):
        ella_app.execute_text_command("open bluetooth")
        ella_app.wait_for_response()

    # 步骤3: 验证中间状态
    with allure.step("验证命令响应"):
        response_text = ella_app.get_response_text()
        assert "Done" in str(response_text), f"响应异常: {response_text}"

    # 步骤4: 验证最终状态
    with allure.step("验证最终蓝牙状态"):
        final_bluetooth = ella_app.check_bluetooth_status_smart()
        assert final_bluetooth, f"蓝牙未开启: 初始={initial_bluetooth}, 最终={final_bluetooth}"

    # 步骤5: 验证副作用
    with allure.step("验证系统副作用"):
        # 检查是否有相关通知
        # 检查是否影响其他功能
        pass
```

## 🔧 调试技巧

### 1. 日志分析
```python
from core.logger import log

def test_with_detailed_logging(self, ella_app):
    log.info("开始测试蓝牙功能")

    # 记录详细的执行过程
    log.debug(f"当前应用状态: {ella_app.get_current_activity()}")

    command = "open bluetooth"
    log.info(f"执行命令: {command}")

    initial_status, final_status, response_text = self.simple_command_test(
        ella_app, command
    )

    # 记录关键信息
    log.info(f"初始状态: {initial_status}")
    log.info(f"最终状态: {final_status}")
    log.info(f"响应内容: {response_text}")

    # 记录系统信息
    log.debug(f"当前运行的应用: {ella_app.get_running_apps()}")
```

### 2. 截图调试
```python
def test_with_screenshots(self, ella_app):
    # 测试开始截图
    self.take_screenshot(ella_app, "test_start")

    # 执行命令前截图
    self.take_screenshot(ella_app, "before_command")

    # 执行命令
    ella_app.execute_text_command("open bluetooth")

    # 命令执行后截图
    self.take_screenshot(ella_app, "after_command")

    # 等待响应
    ella_app.wait_for_response()

    # 响应完成后截图
    self.take_screenshot(ella_app, "response_complete")

    # 验证完成后截图
    self.take_screenshot(ella_app, "verification_complete")
```

### 3. 错误恢复
```python
def test_with_error_recovery(self, ella_app):
    try:
        # 正常测试流程
        initial_status, final_status, response_text = self.simple_command_test(
            ella_app, "open bluetooth"
        )

    except Exception as e:
        log.error(f"测试执行异常: {e}")

        # 错误恢复操作
        with allure.step("错误恢复"):
            # 截图记录错误状态
            self.take_screenshot(ella_app, "error_state")

            # 尝试返回主页
            ella_app.return_to_main_page()

            # 重新启动应用
            ella_app.restart_app()

        # 重新抛出异常
        raise
```

## 📊 性能优化

### 1. 测试执行优化
```python
class TestOptimized(SimpleEllaTest):
    @classmethod
    def setup_class(cls):
        """类级别的设置，减少重复初始化"""
        cls.test_data = cls.load_test_data()

    def test_optimized_execution(self, ella_app):
        # 使用缓存的测试数据
        command = self.test_data.get("bluetooth_command")

        # 跳过不必要的验证
        initial_status, final_status, response_text = self.simple_command_test(
            ella_app, command, verify_status=True
        )

        # 批量验证
        verifications = [
            (["Done"], response_text, "响应验证"),
            (final_status, True, "状态验证")
        ]

        for expected, actual, desc in verifications:
            with allure.step(desc):
                if isinstance(expected, list):
                    assert self.verify_expected_in_response(expected, actual)
                else:
                    assert actual == expected
```

### 2. 并发测试注意事项
```python
# 避免并发冲突的测试设计
class TestConcurrentSafe(SimpleEllaTest):
    def test_isolated_bluetooth(self, ella_app):
        """隔离的蓝牙测试，避免与其他测试冲突"""

        # 确保测试环境干净
        with allure.step("清理测试环境"):
            self.clear_all_running_processes()  # 自动调用

        # 执行测试
        command = "open bluetooth"
        initial_status, final_status, response_text = self.simple_command_test(
            ella_app, command
        )

        # 清理测试后状态
        with allure.step("清理测试后状态"):
            if final_status:
                ella_app.execute_text_command("close bluetooth")
```

---

## 📚 参考资源
- [BaseEllaTest API文档](testcases/test_ella/base_ella_test.py)
- [配置文件示例](config/)
- [测试用例示例](testcases/test_ella/)
- [页面对象模型](pages/apps/ella/)
- [状态检查器](pages/base/system_status_checker.py)
- [应用检测器](pages/base/app_detector.py)
