"""
项目清理管理工具
用于清理过期的日志、截图、报告等文件
"""
import os
import sys
import time
import shutil
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.logger import log


class CleanupManager:
    """项目清理管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.cleanup_targets = {
            'screenshots': {
                'path': 'reports/screenshots',
                'description': '测试截图',
                'extensions': ['.png', '.jpg', '.jpeg'],
                'default_days': 7
            },
            'logs': {
                'path': 'logs',
                'description': '日志文件',
                'extensions': ['.log', '.txt'],
                'default_days': 14
            },
            'allure_results': {
                'path': 'reports/allure-results',
                'description': 'Allure测试结果',
                'extensions': ['.json', '.txt', '.properties'],
                'default_days': 30
            },
            'debug_reports': {
                'path': 'reports/debug_reports',
                'description': '调试报告',
                'extensions': ['.txt', '.log', '.json'],
                'default_days': 14
            },
            'test_reports': {
                'path': 'reports/test_reports',
                'description': '测试报告',
                'extensions': ['.txt', '.log', '.html'],
                'default_days': 30
            },
            'temp_files': {
                'path': 'debug/temp_tests',
                'description': '临时测试文件',
                'extensions': ['.py', '.txt', '.log'],
                'default_days': 3
            }
        }
    
    def get_file_age_days(self, file_path: Path) -> int:
        """获取文件的天数"""
        try:
            file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            age = datetime.now() - file_time
            return age.days
        except Exception as e:
            log.debug(f"获取文件时间失败: {file_path}, 错误: {e}")
            return 0
    
    def scan_files(self, target_type: str, days: int = None) -> list:
        """扫描指定类型的过期文件"""
        if target_type not in self.cleanup_targets:
            log.error(f"不支持的清理类型: {target_type}")
            return []
        
        target = self.cleanup_targets[target_type]
        target_path = self.project_root / target['path']
        
        if not target_path.exists():
            log.info(f"目录不存在: {target_path}")
            return []
        
        days = days or target['default_days']
        expired_files = []
        
        log.info(f"扫描 {target['description']} 目录: {target_path}")
        log.info(f"查找 {days} 天前的文件...")
        
        for file_path in target_path.rglob('*'):
            if file_path.is_file():
                # 检查文件扩展名
                if target['extensions'] and file_path.suffix.lower() not in target['extensions']:
                    continue
                
                # 检查文件年龄
                file_age = self.get_file_age_days(file_path)
                if file_age >= days:
                    file_size = file_path.stat().st_size
                    expired_files.append({
                        'path': file_path,
                        'age_days': file_age,
                        'size_bytes': file_size,
                        'size_mb': file_size / (1024 * 1024)
                    })
        
        return expired_files
    
    def preview_cleanup(self, target_type: str = None, days: int = None):
        """预览清理操作"""
        print("=" * 60)
        print("🔍 项目清理预览")
        print("=" * 60)
        
        targets_to_scan = [target_type] if target_type else list(self.cleanup_targets.keys())
        total_files = 0
        total_size_mb = 0
        
        for target in targets_to_scan:
            expired_files = self.scan_files(target, days)
            
            if expired_files:
                target_info = self.cleanup_targets[target]
                print(f"\n📂 {target_info['description']} ({target_info['path']})")
                print(f"   找到 {len(expired_files)} 个过期文件:")
                
                for file_info in expired_files[:10]:  # 只显示前10个
                    rel_path = file_info['path'].relative_to(self.project_root)
                    print(f"   - {rel_path} ({file_info['age_days']}天前, {file_info['size_mb']:.2f}MB)")
                
                if len(expired_files) > 10:
                    print(f"   ... 还有 {len(expired_files) - 10} 个文件")
                
                files_size_mb = sum(f['size_mb'] for f in expired_files)
                print(f"   小计: {len(expired_files)} 个文件, {files_size_mb:.2f}MB")
                
                total_files += len(expired_files)
                total_size_mb += files_size_mb
            else:
                target_info = self.cleanup_targets[target]
                print(f"\n📂 {target_info['description']} ({target_info['path']})")
                print("   ✅ 没有找到过期文件")
        
        print("\n" + "=" * 60)
        print(f"📊 总计: {total_files} 个文件, {total_size_mb:.2f}MB")
        print("=" * 60)
        
        return total_files > 0
    
    def cleanup_files(self, target_type: str = None, days: int = None, confirm: bool = False):
        """执行清理操作"""
        if not confirm:
            print("⚠️ 这是预览模式，使用 --confirm 参数执行实际清理")
            return self.preview_cleanup(target_type, days)
        
        print("=" * 60)
        print("🧹 开始执行项目清理")
        print("=" * 60)
        
        targets_to_clean = [target_type] if target_type else list(self.cleanup_targets.keys())
        total_deleted = 0
        total_size_mb = 0
        
        for target in targets_to_clean:
            expired_files = self.scan_files(target, days)
            
            if expired_files:
                target_info = self.cleanup_targets[target]
                print(f"\n🗑️ 清理 {target_info['description']}...")
                
                deleted_count = 0
                deleted_size_mb = 0
                
                for file_info in expired_files:
                    try:
                        file_info['path'].unlink()
                        deleted_count += 1
                        deleted_size_mb += file_info['size_mb']
                        log.info(f"删除文件: {file_info['path']}")
                    except Exception as e:
                        log.error(f"删除文件失败: {file_info['path']}, 错误: {e}")
                
                print(f"   ✅ 删除了 {deleted_count} 个文件, 释放 {deleted_size_mb:.2f}MB")
                total_deleted += deleted_count
                total_size_mb += deleted_size_mb
                
                # 清理空目录
                self._cleanup_empty_dirs(self.project_root / target_info['path'])
        
        print("\n" + "=" * 60)
        print(f"🎉 清理完成: 删除了 {total_deleted} 个文件, 释放 {total_size_mb:.2f}MB")
        print("=" * 60)
    
    def _cleanup_empty_dirs(self, root_path: Path):
        """清理空目录"""
        try:
            for dir_path in root_path.rglob('*'):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    try:
                        dir_path.rmdir()
                        log.info(f"删除空目录: {dir_path}")
                    except Exception as e:
                        log.debug(f"删除空目录失败: {dir_path}, 错误: {e}")
        except Exception as e:
            log.debug(f"清理空目录失败: {e}")
    
    def show_statistics(self):
        """显示项目文件统计"""
        print("=" * 60)
        print("📊 项目文件统计")
        print("=" * 60)
        
        for target_type, target_info in self.cleanup_targets.items():
            target_path = self.project_root / target_info['path']
            
            if not target_path.exists():
                print(f"\n📂 {target_info['description']} ({target_info['path']})")
                print("   ❌ 目录不存在")
                continue
            
            files = []
            for file_path in target_path.rglob('*'):
                if file_path.is_file():
                    if target_info['extensions'] and file_path.suffix.lower() not in target_info['extensions']:
                        continue
                    
                    file_size = file_path.stat().st_size
                    file_age = self.get_file_age_days(file_path)
                    files.append({
                        'size_mb': file_size / (1024 * 1024),
                        'age_days': file_age
                    })
            
            if files:
                total_size_mb = sum(f['size_mb'] for f in files)
                avg_age = sum(f['age_days'] for f in files) / len(files)
                old_files = len([f for f in files if f['age_days'] >= target_info['default_days']])
                
                print(f"\n📂 {target_info['description']} ({target_info['path']})")
                print(f"   文件数量: {len(files)}")
                print(f"   总大小: {total_size_mb:.2f}MB")
                print(f"   平均年龄: {avg_age:.1f}天")
                print(f"   过期文件: {old_files} 个 (>{target_info['default_days']}天)")
            else:
                print(f"\n📂 {target_info['description']} ({target_info['path']})")
                print("   📁 目录为空")
        
        print("\n" + "=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='项目清理管理工具')
    parser.add_argument('--type', choices=['screenshots', 'logs', 'allure_results', 'debug_reports', 'test_reports', 'temp_files'],
                       help='指定清理类型')
    parser.add_argument('--days', type=int, help='清理多少天前的文件')
    parser.add_argument('--confirm', action='store_true', help='确认执行清理（否则只预览）')
    parser.add_argument('--stats', action='store_true', help='显示文件统计信息')
    parser.add_argument('--all', action='store_true', help='显示所有信息')
    
    args = parser.parse_args()
    
    cleanup_manager = CleanupManager()
    
    if args.stats or args.all:
        cleanup_manager.show_statistics()
    
    if args.all or (not args.stats and (args.type or args.days or args.confirm)):
        cleanup_manager.cleanup_files(args.type, args.days, args.confirm)
    
    if not any([args.stats, args.all, args.type, args.days, args.confirm]):
        # 默认显示统计信息
        cleanup_manager.show_statistics()


if __name__ == "__main__":
    main()
