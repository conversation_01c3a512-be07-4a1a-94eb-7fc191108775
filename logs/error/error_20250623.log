2025-06-23 21:36:46 | ERROR | __main__:test_basic_logging:22 | 这是一条错误日志
2025-06-23 21:36:46 | ERROR | __main__:test_categorized_logging:47 | 这是一条错误日志，应该被分类到error目录
2025-06-23 21:36:46 | CRITICAL | __main__:test_categorized_logging:48 | 这是一条严重错误日志
2025-06-23 21:36:46 | ERROR | core.logger:log_error_with_screenshot:211 | ❌ 错误: 元素未找到 | 截图: screenshots/error_20250623.png
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 1 - 这是一条错误信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 2 - 这是一条错误信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 3 - 这是一条错误信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 4 - 这是一条错误信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 5 - 这是一条错误信息
2025-06-23 21:36:46 | ERROR | __main__:test_different_scenarios:137 | 错误场景: 错误处理测试
2025-06-23 21:36:46 | ERROR | core.logger:log_error_with_screenshot:211 | ❌ 错误: 错误处理测试失败 | 截图: error_错误处理测试.png
2025-06-23 22:20:38 | ERROR | __main__:text_to_speech:146 | 没有可用的TTS服务
2025-06-23 22:20:38 | ERROR | __main__:speak_text:370 | 语音生成失败
2025-06-23 22:36:04 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:04 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\hello_test.wav
2025-06-23 22:36:04 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: hello_test.wav
2025-06-23 22:36:06 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:06 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\nihao_test.wav
2025-06-23 22:36:06 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: nihao_test.wav
2025-06-23 22:36:08 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:08 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\open_bluetooth.wav
2025-06-23 22:36:08 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: open_bluetooth.wav
2025-06-23 22:36:10 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:10 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\close_bluetooth.wav
2025-06-23 22:36:10 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: close_bluetooth.wav
2025-06-23 22:36:12 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:12 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\what_time.wav
2025-06-23 22:36:12 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: what_time.wav
2025-06-23 22:36:14 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:14 | ERROR | __main__:test_tts_service_comparison:141 |   ❌ edge_tts: 生成失败
2025-06-23 22:36:16 | ERROR | utils.tts_utils:_gtts_generate:238 | Google TTS生成的文件验证失败
2025-06-23 22:36:16 | ERROR | __main__:test_tts_service_comparison:141 |   ❌ gtts: 生成失败
2025-06-23 22:36:23 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:23 | ERROR | __main__:test_tts_file_verification:194 | 无法生成测试音频文件
2025-06-23 22:36:23 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:23 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:25 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:25 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:27 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:27 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:28 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:28 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:30 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:30 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:30 | ERROR | __main__:test_tts_performance:294 | ❌ 所有性能测试都失败
2025-06-23 22:48:31 | ERROR | utils.tts_utils:_edge_tts_generate:322 | Edge TTS生成异常: No audio was received. Please verify that your parameters are correct.
2025-06-23 22:48:31 | ERROR | utils.tts_utils:generate_audio_file:720 | ❌ 音频文件生成失败: data\en\こんにちは.wav
2025-06-23 22:48:31 | ERROR | __main__:test_language_classification:63 |   ❌ 生成失败: こんにちは
2025-06-23 22:48:33 | ERROR | utils.tts_utils:_edge_tts_generate:322 | Edge TTS生成异常: No audio was received. Please verify that your parameters are correct.
2025-06-23 22:48:33 | ERROR | utils.tts_utils:generate_audio_file:720 | ❌ 音频文件生成失败: data\en\안녕하세요.wav
2025-06-23 22:48:33 | ERROR | __main__:test_language_classification:63 |   ❌ 生成失败: 안녕하세요
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/open_bluetooth.wav, 实际: data\en\open_bluetooth.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/close_bluetooth.wav, 实际: data\en\close_bluetooth.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/打开蓝牙.wav, 实际: data\en\打开蓝牙.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/关闭蓝牙.wav, 实际: data\zh\关闭蓝牙.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/what_time_is_it.wav, 实际: data\en\what_time_is_it.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/现在几点了.wav, 实际: data\en\现在几点了.wav
2025-06-23 23:23:27 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-06-23 23:23:27 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-06-23 23:23:27 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-06-23 23:23:34 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /openatx/atx-agent/releases/download/0.10.0/atx-agent_0.10.0_linux_arm64.tar.gz (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:992)')))
