2025-07-22 20:44:12 | ERROR | testcases.test_ella.base_ella_test:ella_app:30 | ❌ Ella应用启动异常: Ella页面加载失败
assert False
 +  where False = wait_for_page_load(timeout=15)
 +    where wait_for_page_load = <pages.apps.ella.main_page_refactored.EllaMainPageRefactored object at 0x000002B883170A50>.wait_for_page_load
2025-07-22 21:23:50 | ERROR | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:305 | 从asr_txt获取响应失败，已达到最大重试次数: (-32001, 'androidx.test.uiautomator.UiObjectNotFoundException', ({'mask': 2097152, 'childOrSibling': [], 'childOrSiblingSelector': [], 'resourceId': 'com.transsion.aivoiceassistant:id/asr_text'},))
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第1次返回Ella应用失败
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第2次返回Ella应用失败
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第3次返回Ella应用失败
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:115 | 经过3次尝试仍无法返回Ella页面，强制获取响应
2025-07-22 21:50:35 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 21:58:31 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 22:02:32 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 22:04:20 | ERROR | utils.excel_utils:read_test_data:28 | Excel文件不存在: testcases/test_ella/Ella_Test_Cases.xlsx
