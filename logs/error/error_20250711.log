2025-07-11 09:27:31 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:27:31 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:27:32 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_page_load_failed.png
2025-07-11 09:28:11 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:28:11 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:28:11 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_page_load_failed.png
2025-07-11 09:54:29 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:54:29 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:54:30 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_page_load_failed.png
2025-07-11 10:11:35 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 10:11:35 | ERROR | testcases.test_ella.test_take_photo_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 10:11:37 | ERROR | testcases.test_ella.test_take_photo_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaTakePhotoCommand\ella_photo_page_load_failed.png
2025-07-11 10:11:59 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 10:11:59 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 10:12:00 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_page_load_failed.png
2025-07-11 12:09:13 | ERROR | pages.apps.ella.main_page:wait_for_page_load:182 | ❌ Ella应用包未加载
2025-07-11 12:09:13 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 12:09:14 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_page_load_failed.png
2025-07-11 12:12:00 | ERROR | __main__:_check_page_elements:172 | 获取页面源码失败: ('Unknown RPC error: -32001 java.lang.NullPointerException', (False, 50), "java.lang.NullPointerException: Attempt to read from field 'int android.accessibilityservice.AccessibilityServiceInfo.flags' on a null object reference in method 'void androidx.test.uiautomator.UiDevice.setCompressedLayoutHeirarchy(boolean)'\n\tat androidx.test.uiautomator.UiDevice.setCompressedLayoutHeirarchy(UiDevice.java:234)\n\tat com.github.uiautomator.stub.AutomatorServiceImpl.dumpWindowHierarchy(AutomatorServiceImpl.java:295)\n\tat com.github.uiautomator.stub.AutomatorServiceImpl.dumpWindowHierarchy(AutomatorServiceImpl.java:284)\n\tat java.lang.reflect.Method.invoke(Native Method)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.invoke(JsonRpcBasicServer.java:467)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleObject(JsonRpcBasicServer.java:352)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleJsonNodeRequest(JsonRpcBasicServer.java:283)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleRequest(JsonRpcBasicServer.java:251)\n\tat com.github.uiautomator.stub.AutomatorHttpServer.serve(AutomatorHttpServer.java:100)\n\tat fi.iki.elonen.NanoHTTPD.serve(NanoHTTPD.java:2244)\n\tat fi.iki.elonen.NanoHTTPD$HTTPSession.execute(NanoHTTPD.java:945)\n\tat fi.iki.elonen.NanoHTTPD$ClientHandler.run(NanoHTTPD.java:192)\n\tat java.lang.Thread.run(Thread.java:1012)\n")
2025-07-11 14:09:04 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:09:04 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:09:04 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:09:08 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: USB device 13764254B4001229 is offline
2025-07-11 14:11:07 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1437 | ❌ 无法确保输入框就绪
2025-07-11 14:13:49 | ERROR | core.base_driver:screenshot:305 | 截图失败: unknown file extension: 
2025-07-11 14:13:49 | ERROR | __main__:test_ella_input_fix:85 | 测试过程中发生异常: unknown file extension: 
2025-07-11 14:13:49 | ERROR | __main__:main:177 | 💥 Ella输入框修复测试失败
2025-07-11 14:15:16 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1402 | ❌ 无法确保输入框就绪
2025-07-11 14:15:16 | ERROR | __main__:test_ella_input_fix:72 | ❌ 输入框检测失败
2025-07-11 14:15:18 | ERROR | __main__:main:177 | 💥 Ella输入框修复测试失败
2025-07-11 14:17:03 | ERROR | __main__:get_page_source:85 | 获取页面源码失败: ('Unknown RPC error: -32001 java.lang.NullPointerException', (False, 50), "java.lang.NullPointerException: Attempt to read from field 'int android.accessibilityservice.AccessibilityServiceInfo.flags' on a null object reference in method 'void androidx.test.uiautomator.UiDevice.setCompressedLayoutHeirarchy(boolean)'\n\tat androidx.test.uiautomator.UiDevice.setCompressedLayoutHeirarchy(UiDevice.java:234)\n\tat com.github.uiautomator.stub.AutomatorServiceImpl.dumpWindowHierarchy(AutomatorServiceImpl.java:295)\n\tat com.github.uiautomator.stub.AutomatorServiceImpl.dumpWindowHierarchy(AutomatorServiceImpl.java:284)\n\tat java.lang.reflect.Method.invoke(Native Method)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.invoke(JsonRpcBasicServer.java:467)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleObject(JsonRpcBasicServer.java:352)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleJsonNodeRequest(JsonRpcBasicServer.java:283)\n\tat com.googlecode.jsonrpc4j.JsonRpcBasicServer.handleRequest(JsonRpcBasicServer.java:251)\n\tat com.github.uiautomator.stub.AutomatorHttpServer.serve(AutomatorHttpServer.java:100)\n\tat fi.iki.elonen.NanoHTTPD.serve(NanoHTTPD.java:2244)\n\tat fi.iki.elonen.NanoHTTPD$HTTPSession.execute(NanoHTTPD.java:945)\n\tat fi.iki.elonen.NanoHTTPD$ClientHandler.run(NanoHTTPD.java:192)\n\tat java.lang.Thread.run(Thread.java:1012)\n")
2025-07-11 14:24:52 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:24:52 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:24:52 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:24:56 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [输入框]: Invalid version: ''
2025-07-11 14:24:57 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:24:58 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:24:59 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:24:59 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [发送按钮]: Invalid version: ''
2025-07-11 14:25:00 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:25:01 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:25:02 | ERROR | pages.apps.ella.main_page:_wait_for_ui_elements:342 | 等待UI元素失败: Invalid version: ''
2025-07-11 14:25:02 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 14:25:02 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:43 | 📸 页面加载失败截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_page_load_failed.png
2025-07-11 14:33:08 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:33:08 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:33:08 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:33:16 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:33:19 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:33:23 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:33:26 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:33:30 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:33:33 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:33:36 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:33:44 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:33:47 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:33:48 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1539 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 14:42:36 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:42:36 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:42:36 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:42:44 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:42:47 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:42:51 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:42:54 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:43:57 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:44:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:44:26 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:44:26 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:44:26 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:44:33 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:44:36 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:44:40 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:44:43 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:44:46 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:44:49 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:44:53 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:45:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:45:03 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:45:07 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:45:10 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:45:13 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:45:16 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1616 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 14:45:19 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:45:23 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:45:27 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1650 | ❌ 无法确保输入框就绪
2025-07-11 15:47:46 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 15:47:46 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 15:47:46 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 15:47:54 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:47:57 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:48:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 15:48:04 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 15:48:07 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 15:48:10 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 15:48:14 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 15:48:22 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:48:25 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:48:28 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 15:48:32 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 15:48:35 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 15:48:38 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1616 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 15:48:41 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:48:45 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:48:49 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1650 | ❌ 无法确保输入框就绪
2025-07-11 16:49:07 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:49:07 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 16:49:07 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 16:49:27 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:49:44 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:50:03 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
