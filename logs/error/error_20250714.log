2025-07-14 19:26:27 | ERROR | core.base_element:click:237 | 点击元素失败 [输入框]: {'code': -32002, 'data': "Selector [resourceId='com.transsion.aivoiceassistant:id/et_input']", 'method': 'wait'}
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_bluetooth_simple_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_weather_query_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_set_alarm_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_take_photo_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_clock_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_bluetooth_voice.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_refactored.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_bluetooth_simple_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_weather_query_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_set_alarm_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_take_photo_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_clock_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_bluetooth_voice.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_refactored.py: module 'importlib' has no attribute 'util'
2025-07-14 21:29:53 | ERROR | testcases.test_ella.test_bluetooth_simple_command_concise:_test_command:165 | ❌ open bluetooth 测试失败: 蓝牙未开启
assert False
