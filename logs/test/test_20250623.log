2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:34 | 开始执行测试用例 TestCalculator
2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:35 | pytest 测试框架启动
2025-06-23 21:36:46 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: TestEllaBluetoothCommand
2025-06-23 21:36:46 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: TestEllaBluetoothCommand - ✅ 成功, 耗时: 2.50秒
2025-06-23 21:58:43 | INFO | __main__:test_voice_input_fallback:230 | 🎯 测试回退机制，命令: test fallback mechanism
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: test fallback mechanism (持续时间: 2.0秒)
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 21:59:20 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 21:59:20 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 21:59:21 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_initial_state.png
2025-06-23 21:59:44 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_command_sent.png
2025-06-23 21:59:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_response_received.png
2025-06-23 21:59:56 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_test_completed.png
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 22:07:23 | INFO | utils.tts_utils:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687642.wav
2025-06-23 22:07:26 | INFO | utils.tts_utils:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687646.wav
2025-06-23 22:07:29 | INFO | utils.tts_utils:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687648.wav
2025-06-23 22:12:30 | INFO | utils.excel_utils:read_test_data:31 | 读取Excel文件: testcases/test_ella/Ella_Test_Cases.xlsx
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 22:12:36 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 22:12:36 | INFO | test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-06-23 22:12:36 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_initial_state.png
2025-06-23 22:13:01 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_command_sent.png
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 22:15:07 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_initial.png
2025-06-23 22:15:29 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_sent.png
2025-06-23 22:15:33 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_response.png
2025-06-23 22:15:40 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_completed.png
2025-06-23 22:15:43 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_initial.png
2025-06-23 22:16:04 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_sent.png
2025-06-23 22:16:09 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_response.png
2025-06-23 22:16:15 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_completed.png
2025-06-23 22:25:44 | INFO | utils.tts_utils:speak_text:365 | 🎤 开始朗读文本: 'Hello, this is a test'
2025-06-23 22:25:45 | INFO | utils.tts_utils:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688744.wav
2025-06-23 22:25:47 | INFO | utils.tts_utils:speak_text:385 | ✅ 文本朗读完成: 'Hello, this is a test'
2025-06-23 22:26:03 | INFO | utils.tts_utils:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688762.wav
2025-06-23 22:30:05 | INFO | __main__:_edge_tts_generate:183 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688999.wav
2025-06-23 22:36:03 | INFO | __main__:test_tts_file_generation:40 | 🎯 测试 1/5: 'Hello, this is a test' (en-US)
2025-06-23 22:36:03 | INFO | utils.tts_utils:generate_audio_file:572 | 📁 生成音频文件: 'Hello, this is a test' -> test_audio_files\hello_test.wav
2025-06-23 22:36:03 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'Hello, this is a test' (语音: en-US-AriaNeural)
2025-06-23 22:36:04 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\hello_test.wav
2025-06-23 22:36:04 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: hello_test.wav
2025-06-23 22:36:05 | INFO | utils.tts_utils:generate_audio_file:572 | 📁 生成音频文件: '你好，这是一个测试' -> test_audio_files\nihao_test.wav
2025-06-23 22:36:06 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\nihao_test.wav
2025-06-23 22:36:06 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: nihao_test.wav
2025-06-23 22:36:07 | INFO | utils.tts_utils:generate_audio_file:572 | 📁 生成音频文件: 'open bluetooth' -> test_audio_files\open_bluetooth.wav
2025-06-23 22:36:08 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\open_bluetooth.wav
2025-06-23 22:36:09 | INFO | utils.tts_utils:generate_audio_file:572 | 📁 生成音频文件: 'close bluetooth' -> test_audio_files\close_bluetooth.wav
2025-06-23 22:36:10 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\close_bluetooth.wav
2025-06-23 22:36:11 | INFO | utils.tts_utils:generate_audio_file:572 | 📁 生成音频文件: 'what time is it' -> test_audio_files\what_time.wav
2025-06-23 22:36:12 | ERROR | utils.tts_utils:generate_audio_file:587 | ❌ 音频文件生成失败: test_audio_files\what_time.wav
2025-06-23 22:36:18 | INFO | utils.tts_utils:_pyttsx3_generate:299 | ✅ pyttsx3生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689377.wav
2025-06-23 22:36:22 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'This is a verification test' (语音: en-US-AriaNeural)
2025-06-23 22:36:25 | INFO | __main__:test_tts_performance:253 | 🧪 测试 长文本: 'This is a longer text to test the performance of TTS generation and playback functionality.'
2025-06-23 22:36:25 | INFO | utils.tts_utils:speak_text:513 | 🎤 开始朗读文本: 'This is a longer text to test the performance of TTS generation and playback functionality.' (语言: en-US, 音量: 0.5)
2025-06-23 22:36:25 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'This is a longer text to test the performance of TTS generation and playback functionality.' (语音: en-US-AriaNeural)
2025-06-23 22:38:28 | INFO | utils.tts_utils:_edge_tts_generate:198 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689507.wav
2025-06-23 22:39:23 | INFO | utils.tts_utils:_edge_tts_generate:198 | ✅ Edge TTS生成成功: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689562.wav
2025-06-23 22:39:25 | INFO | utils.tts_utils:generate_audio_file:587 | 📁 生成音频文件: 'open bluetooth' -> open_bluetooth_test.wav
2025-06-23 22:39:26 | INFO | utils.tts_utils:_edge_tts_generate:198 | ✅ Edge TTS生成成功: open_bluetooth_test.wav
2025-06-23 22:39:27 | INFO | utils.tts_utils:generate_audio_file:599 | ✅ 音频文件生成成功: open_bluetooth_test.wav (10.3KB)
2025-06-23 22:39:27 | INFO | utils.tts_utils:play_audio:417 | 🔊 开始播放音频: open_bluetooth_test.wav (10.3KB)
2025-06-23 22:39:27 | INFO | utils.tts_utils:play_audio:426 | ✅ 音频播放完成: open_bluetooth_test.wav
2025-06-23 22:48:38 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'test cache functionality' -> data\en\test_cache_functionality.wav
2025-06-23 22:48:38 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'test cache functionality' (语音: en-US-AriaNeural)
2025-06-23 22:48:39 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\test_cache_functionality.wav
2025-06-23 22:48:40 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\test_cache_functionality.wav (15.2KB)
2025-06-23 22:48:40 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'test cache functionality' -> data\en\test_cache_functionality.wav
2025-06-23 22:48:40 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'test cache functionality' (语音: en-US-AriaNeural)
2025-06-23 22:48:41 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\test_cache_functionality.wav
2025-06-23 22:48:41 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\test_cache_functionality.wav (15.2KB)
2025-06-23 22:48:41 | INFO | __main__:test_directory_structure:127 | ✅ data目录存在: D:\PythonProject\app_test\data
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:32 | 📁 项目根目录: D:\PythonProject\app_test
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:33 | 📁 数据目录: D:\PythonProject\app_test\data
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:34 | 📁 数据目录(绝对路径): D:\PythonProject\app_test\data
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:44 | ✅ 找到的项目标识: ['core', 'pages', 'testcases', 'utils']
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\zh\关闭蓝牙.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\en\what_time_is_it.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:92 |   绝对路径: D:\PythonProject\app_test\data\en\现在几点了.wav
2025-06-23 23:00:57 | INFO | __main__:test_file_generation_with_relative_paths:129 | 🧪 测试 1/2: 'test relative path' (en-US)
2025-06-23 23:00:57 | INFO | utils.tts_utils:generate_audio_file:780 | 📁 生成音频文件: 'test relative path' -> data\en\test_relative_path.wav
2025-06-23 23:00:57 | INFO | utils.tts_utils:_edge_tts_generate:353 | 🎤 使用Edge TTS生成语音: 'test relative path' (语音: en-US-AriaNeural)
2025-06-23 23:00:58 | INFO | utils.tts_utils:_edge_tts_generate:377 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_relative_path.wav
2025-06-23 23:00:58 | INFO | utils.tts_utils:generate_audio_file:796 | ✅ 音频文件生成成功: data\en\test_relative_path.wav (13.6KB)
2025-06-23 23:00:58 | INFO | __main__:test_file_generation_with_relative_paths:138 |   ✅ 生成成功: data\en\test_relative_path.wav
2025-06-23 23:01:00 | INFO | utils.tts_utils:_edge_tts_generate:377 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\测试相对路径.wav
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:176 | ✅ 项目data目录存在: D:\PythonProject\app_test\data
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:196 |     📄 test_relative_path.wav (13.6KB)
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:219 |   📁 数据目录: D:\PythonProject\app_test\data
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:238 |       📄 test_relative_path.wav (13.6KB)
2025-06-23 23:02:47 | INFO | utils.tts_utils:_edge_tts_generate:379 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:02:49 | INFO | utils.tts_utils:_edge_tts_generate:379 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:03:43 | INFO | utils.tts_utils:_edge_tts_generate:381 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:03:45 | INFO | utils.tts_utils:_edge_tts_generate:381 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:04:41 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\zh\测试中文路径.wav
2025-06-23 23:04:42 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'test english path' -> data\en\test_english_path.wav
2025-06-23 23:04:42 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'test english path' (语音: en-US-AriaNeural)
2025-06-23 23:04:43 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_english_path.wav
2025-06-23 23:04:44 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\test_english_path.wav (12.9KB)
2025-06-23 23:08:12 | INFO | __main__:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:08:47 | INFO | __main__:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:09:09 | INFO | __main__:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:14:23 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\test_cache_logic.wav
2025-06-23 23:14:23 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\test_cache_logic.wav
2025-06-23 23:14:23 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'test cache logic' (en-US)
2025-06-23 23:14:23 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'test cache logic' -> data\en\test_cache_logic.wav
2025-06-23 23:14:23 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'test cache logic' (语音: en-US-AriaNeural)
2025-06-23 23:14:25 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:25 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\test_cache_logic.wav (13.5KB)
2025-06-23 23:14:26 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_cache_logic.wav (13.5KB)
2025-06-23 23:14:27 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_cache_logic.wav
2025-06-23 23:14:27 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'test cache logic'
2025-06-23 23:14:27 | INFO | __main__:test_voice_command_cache_logic:49 | ✅ 文件已生成: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13.5KB)
2025-06-23 23:14:27 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\test_cache_logic.wav
2025-06-23 23:14:28 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\test_cache_logic.wav (13.5KB)
2025-06-23 23:14:28 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_cache_logic.wav (13.5KB)
2025-06-23 23:14:28 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_cache_logic.wav
2025-06-23 23:14:28 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'test cache logic'
2025-06-23 23:14:33 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:14:36 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:14:40 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:14:42 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\test_file_verification.wav
2025-06-23 23:14:42 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\test_file_verification.wav
2025-06-23 23:14:42 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'test file verification' (en-US)
2025-06-23 23:14:42 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'test file verification' -> data\en\test_file_verification.wav
2025-06-23 23:14:42 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'test file verification' (语音: en-US-AriaNeural)
2025-06-23 23:14:44 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:44 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\test_file_verification.wav (15.2KB)
2025-06-23 23:14:45 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_file_verification.wav (15.2KB)
2025-06-23 23:14:45 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_file_verification.wav
2025-06-23 23:14:45 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'test file verification'
2025-06-23 23:14:45 | INFO | __main__:test_voice_command_file_verification:168 | ✅ 文件存在: D:\PythonProject\app_test\data\en\test_file_verification.wav (15.2KB)
2025-06-23 23:14:46 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_file_verification.wav (15.2KB)
2025-06-23 23:14:46 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_file_verification.wav
2025-06-23 23:14:48 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:54 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:58 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 23:26:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:26:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:26:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:26:50 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:55 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:26:51 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_initial.png
2025-06-23 23:26:52 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:77 | ✅ 页面和输入框状态就绪
2025-06-23 23:27:14 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:91 | ✅ 模拟语音命令执行成功，耗时: 21.99秒
2025-06-23 23:27:14 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_sent.png
2025-06-23 23:27:18 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_response.png
2025-06-23 23:27:21 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:120 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:27:21 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:129 | ✅ 响应内容与命令相关
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:139 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:149 | ✅ 蓝牙已成功开启
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:153 | 📊 蓝牙状态变化: 否
2025-06-23 23:27:24 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_completed.png
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:178 | 🎉 模拟语音输入open bluetooth测试完成
2025-06-23 23:27:25 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:27:25 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:27:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:27:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:190 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:27:26 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_initial.png
2025-06-23 23:27:28 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:206 | ✅ 页面和输入框状态就绪
2025-06-23 23:27:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:225 | ✅ TTS语音命令执行成功，耗时: 21.59秒
2025-06-23 23:27:49 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_sent.png
2025-06-23 23:27:55 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_response.png
2025-06-23 23:27:58 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:254 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:27:58 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:263 | ✅ TTS响应内容与命令相关
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:273 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:277 | ✅ 蓝牙已成功开启
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:281 | 📊 蓝牙状态变化: 否
2025-06-23 23:28:02 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_completed.png
2025-06-23 23:28:02 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:308 | 🎉 TTS真实语音输入open bluetooth测试完成
2025-06-23 23:28:02 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 23:34:13 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\en\test_cache_priority.wav
2025-06-23 23:34:13 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: 'test cache priority' (en-US)
2025-06-23 23:34:13 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'test cache priority' -> data\en\test_cache_priority.wav
2025-06-23 23:34:13 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'test cache priority' (语音: en-US-AriaNeural)
2025-06-23 23:34:14 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:15 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\test_cache_priority.wav (13.8KB)
2025-06-23 23:34:15 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_cache_priority.wav (13.8KB)
2025-06-23 23:34:17 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_cache_priority.wav
2025-06-23 23:34:17 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: 'test cache priority' (播放耗时: 2.01秒, 总耗时: 4.00秒)
2025-06-23 23:34:17 | INFO | __main__:test_cache_priority_logic:49 | ✅ 文件已生成: D:\PythonProject\app_test\data\en\test_cache_priority.wav (13.8KB)
2025-06-23 23:34:17 | INFO | pages.apps.ella.main_page:_play_voice_command_file:562 | 🔍 检查缓存文件: data\en\test_cache_priority.wav
2025-06-23 23:34:17 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 🎯 缓存命中! 使用已存在文件: data\en\test_cache_priority.wav (13.8KB)
2025-06-23 23:34:18 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_cache_priority.wav (13.8KB)
2025-06-23 23:34:18 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_cache_priority.wav
2025-06-23 23:34:18 | INFO | pages.apps.ella.main_page:_play_voice_command_file:576 | ✅ 缓存文件播放成功: 'test cache priority' (播放耗时: 0.53秒)
2025-06-23 23:34:18 | INFO | __main__:test_real_voice_command_with_cache:99 | 🧪 测试 1/2: 英文测试 - 'test real voice cache' (en-US)
2025-06-23 23:34:18 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\en\test_real_voice_cache.wav
2025-06-23 23:34:18 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: 'test real voice cache' (en-US)
2025-06-23 23:34:18 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'test real voice cache' -> data\en\test_real_voice_cache.wav
2025-06-23 23:34:18 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'test real voice cache' (语音: en-US-AriaNeural)
2025-06-23 23:34:19 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav
2025-06-23 23:34:20 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\test_real_voice_cache.wav (14.2KB)
2025-06-23 23:34:20 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: test_real_voice_cache.wav (14.2KB)
2025-06-23 23:34:20 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: test_real_voice_cache.wav
2025-06-23 23:34:20 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: 'test real voice cache' (播放耗时: 0.61秒, 总耗时: 2.53秒)
2025-06-23 23:34:20 | INFO | __main__:test_real_voice_command_with_cache:119 |   📁 文件路径: data\en\test_real_voice_cache.wav (14.2KB)
2025-06-23 23:34:23 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav
2025-06-23 23:34:29 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:33 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:37 | INFO | utils.tts_utils:_edge_tts_generate:385 | ✅ Edge TTS生成成功: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 23:35:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:35:53 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:35:54 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:35:54 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:55 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:35:54 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_initial.png
2025-06-23 23:35:56 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:77 | ✅ 页面和输入框状态就绪
2025-06-23 23:36:19 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:91 | ✅ 模拟语音命令执行成功，耗时: 22.50秒
2025-06-23 23:36:19 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_sent.png
2025-06-23 23:36:23 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_response.png
2025-06-23 23:36:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:120 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:36:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:129 | ✅ 响应内容与命令相关
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:139 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:149 | ✅ 蓝牙已成功开启
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:153 | 📊 蓝牙状态变化: 否
2025-06-23 23:36:30 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_voice_completed.png
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:178 | 🎉 模拟语音输入open bluetooth测试完成
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:36:32 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:36:32 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:190 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:36:33 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_initial.png
2025-06-23 23:36:34 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:206 | ✅ 页面和输入框状态就绪
2025-06-23 23:36:56 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:225 | ✅ TTS语音命令执行成功，耗时: 21.91秒
2025-06-23 23:36:56 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_sent.png
2025-06-23 23:37:03 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestOpenBluetoothVoice\open_bluetooth_tts_response.png
2025-06-23 23:37:06 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:254 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:37:06 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:263 | ✅ TTS响应内容与命令相关
2025-06-23 23:37:10 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
