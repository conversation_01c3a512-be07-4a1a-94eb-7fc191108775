2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:44:15 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:44:18 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:44:19 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_initial_state.png
2025-07-10 21:44:19 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:84 | 闹钟初始状态: 有闹钟
2025-07-10 21:44:31 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_command_sent.png
2025-07-10 21:44:31 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:113 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:44:43 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_response_received.png
2025-07-10 21:44:43 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:141 | ✅ 收到AI响应
2025-07-10 21:44:46 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:169 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:44:46 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:189 | 闹钟最终状态: 有闹钟
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:193 | 闹钟设置验证: 成功
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:209 | ✅ 闹钟设置验证通过
2025-07-10 21:44:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_test_completed.png
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | 🎉 set alarm命令测试完成
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4485, 'temperature': 345, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:46:16 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:46:19 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:46:20 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_initial_state.png
2025-07-10 21:46:20 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:84 | 闹钟初始状态: 有闹钟
2025-07-10 21:46:35 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_command_sent.png
2025-07-10 21:46:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:113 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:46:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_response_received.png
2025-07-10 21:46:42 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:141 | ✅ 收到AI响应
2025-07-10 21:46:46 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:169 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:46:46 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:189 | 闹钟最终状态: 有闹钟
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:193 | 闹钟设置验证: 成功
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:209 | ✅ 闹钟设置验证通过
2025-07-10 21:46:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_test_completed.png
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | 🎉 set alarm命令测试完成
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:46:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:54:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:54:49 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:54:52 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:54:52 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:54:52 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:54:53 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 3
2025-07-10 21:55:02 | WARNING | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:107 | ⚠️ 闹钟清空失败，但继续测试
2025-07-10 21:55:03 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\alarms_cleared.png
2025-07-10 21:55:03 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_initial_state.png
2025-07-10 21:55:04 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 3 个闹钟
2025-07-10 21:55:17 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_command_sent.png
2025-07-10 21:55:17 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:55:25 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_response_received.png
2025-07-10 21:55:25 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 21:55:29 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:55:29 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:55:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 21:55:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 21:55:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 21:55:37 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 21:55:37 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:286 | ⚠️ 闹钟数量未增加，但继续验证其他指标
2025-07-10 21:55:37 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 21:55:38 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_test_completed.png
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:57:29 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:57:29 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:57:32 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:57:32 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:57:32 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:57:33 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 3
2025-07-10 21:57:40 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:88 | ✅ 闹钟清空成功
2025-07-10 21:57:41 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:92 | 清空后闹钟数量: 0
2025-07-10 21:57:41 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:105 | ✅ 所有闹钟已成功清空
2025-07-10 21:57:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\alarms_cleared.png
2025-07-10 21:57:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_initial_state.png
2025-07-10 21:57:43 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 0 个闹钟
2025-07-10 21:58:16 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_command_sent.png
2025-07-10 21:58:16 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:58:24 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_response_received.png
2025-07-10 21:58:24 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 21:58:27 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:58:27 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:58:34 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 21:58:34 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 21:58:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:276 | ✅ 闹钟数量增加: 0 -> 3
2025-07-10 21:58:36 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:283 | ⚠️ 闹钟已增加但未找到目标时间: 10:00
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 21:58:36 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_test_completed.png
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:58:37 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 346, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:59:46 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:59:49 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:59:49 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_app_started.png
2025-07-10 21:59:49 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:59:50 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 5
2025-07-10 21:59:57 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:88 | ✅ 闹钟清空成功
2025-07-10 21:59:59 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:92 | 清空后闹钟数量: 0
2025-07-10 21:59:59 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:105 | ✅ 所有闹钟已成功清空
2025-07-10 21:59:59 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\alarms_cleared.png
2025-07-10 21:59:59 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_initial_state.png
2025-07-10 22:00:00 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 0 个闹钟
2025-07-10 22:00:14 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_command_sent.png
2025-07-10 22:00:14 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 22:00:18 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_response_received.png
2025-07-10 22:00:18 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 22:00:24 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 22:00:24 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 22:00:30 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 22:00:30 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 22:00:31 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:276 | ✅ 闹钟数量增加: 0 -> 3
2025-07-10 22:00:32 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:283 | ⚠️ 闹钟已增加但未找到目标时间: 10:00
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 22:00:32 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaSetAlarmCommand\ella_alarm_test_completed.png
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 22:10:02 | INFO | testcases.test_ella.test_weather_query_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 22:10:06 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_app_started.png
2025-07-10 22:10:06 | INFO | testcases.test_ella.test_weather_query_command:ella_app:36 | 📸 启动成功截图: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_app_started.png
2025-07-10 22:10:06 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_initial_state.png
2025-07-10 22:10:06 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:85 | 天气应用初始状态: 已打开
2025-07-10 22:10:19 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_command_sent.png
2025-07-10 22:10:19 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:114 | ✅ 成功执行命令: what is the weather like in shanghai tomorrow
2025-07-10 22:10:23 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_response_received.png
2025-07-10 22:10:23 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:142 | ✅ 收到AI响应
2025-07-10 22:10:29 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:170 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 22:10:29 | WARNING | testcases.test_ella.test_weather_query_command:test_weather_query_command:180 | ⚠️ 响应未包含命令相关内容，但继续测试: what is the weather like in shanghai tomorrow
2025-07-10 22:10:32 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:207 | 天气应用最终状态: 已打开
2025-07-10 22:10:32 | WARNING | testcases.test_ella.test_weather_query_command:test_weather_query_command:213 | ⚠️ 天气查询验证未完全通过，但测试继续
2025-07-10 22:10:32 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\TestEllaWeatherQueryCommand\ella_weather_test_completed.png
2025-07-10 22:10:32 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:238 | 🎉 天气查询命令测试完成
2025-07-10 22:10:33 | INFO | testcases.test_ella.test_weather_query_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 22:10:33 | INFO | testcases.test_ella.test_weather_query_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
