2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 10:42:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 10:42:00 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 10:42:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 10:42:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 10:42:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:135 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:148 | ✅ 状态验证通过: True -> None
2025-07-22 10:42:20 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:simple_command_test:245 | 🎉 open wifi 测试完成
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:185 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:216 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:217 | 缺失内容: ['WI-FI']
2025-07-22 10:42:21 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_104220.png
2025-07-22 10:42:21 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_104220.png
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'str'>, 内容: 蓝牙已成功开启，设备可以被发现
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已成功开启'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'str'>, 内容: 蓝牙已成功开启，设备可以被发现，连接状态良好
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已成功开启'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备可以被发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '', '设备']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '设备可发现', '']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '   ', None]
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | ⚠️ 响应文本为空或只包含空白字符
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:234 | ⚠️ 响应未包含期望内容: '蓝牙'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:242 | 缺失内容: ['蓝牙']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:243 | 搜索文本: ''
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['蓝牙已打开', '设备可发现']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:234 | ⚠️ 响应未包含期望内容: 'WiFi'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | ❌ 部分期望内容未找到 (2/3)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:242 | 缺失内容: ['WiFi']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:243 | 搜索文本: '蓝牙已打开 设备可发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 全部匹配
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:339 | ⚠️ 未找到期望内容: '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:308 | ✅ [合并模式] 找到期望内容: '设备'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:346 | ❌ 部分期望内容未找到 (1/2)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:347 | 缺失内容: ['蓝牙已打开']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:348 | 响应内容: ['蓝牙', '已打开', '设备可发现']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: individual, 匹配模式: 全部匹配
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:315 | ✅ [独立模式] 在响应项1中找到期望内容: '蓝牙' -> '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:315 | ✅ [独立模式] 在响应项2中找到期望内容: '设备' -> '设备可发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:344 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:308 | ✅ [合并模式] 找到期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:335 | 🎉 [任意匹配模式] 找到期望内容，验证通过: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '蓝牙', '已打开', '']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:32:55 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:33:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:33:00 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:33:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:33:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:33:11 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:33:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:33:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:33:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:33:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_203314.png
2025-07-22 20:33:14 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_203314.png
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:33:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:33:44 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:33:44 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:33:46 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:33:47 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:33:56 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', '', 'Wi-Fi', '', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />']'
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:33:59 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', '', 'Wi-Fi', '', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />']
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:33:59 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_203359.png
2025-07-22 20:33:59 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_203359.png
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:44:12 | ERROR | testcases.test_ella.base_ella_test:ella_app:30 | ❌ Ella应用启动异常: Ella页面加载失败
assert False
 +  where False = wait_for_page_load(timeout=15)
 +    where wait_for_page_load = <pages.apps.ella.main_page_refactored.EllaMainPageRefactored object at 0x000002B883170A50>.wait_for_page_load
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:44:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:44:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:44:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:44:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:44:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:45:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:45:07 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:45:08 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:45:08 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_204508.png
2025-07-22 20:45:08 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_204508.png
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:47:24 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:47:29 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:47:30 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:47:32 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open wifi
2025-07-22 20:47:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:47:40 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:47:43 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: True -> True
2025-07-22 20:47:44 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open wifi 测试完成
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:47:44 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:48:15 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:48:15 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app打开应用，状态: None
2025-07-22 20:48:17 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open app
2025-07-22 20:48:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:48:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open app', 'Which app should I open?', '', '']'
2025-07-22 20:48:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: None -> None
2025-07-22 20:48:27 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open app 测试完成
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open app', 'Which app should I open?', '', '']
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:48:27 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:49:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:49:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: True
2025-07-22 20:49:57 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open bluetooth
2025-07-22 20:49:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:50:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']'
2025-07-22 20:50:06 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: True -> True
2025-07-22 20:50:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open bluetooth 测试完成
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'bluetooth'
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'is turned on now'
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:50:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:52:46 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'done'
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:52:55 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:53:35 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:53:35 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella打开应用，状态: None
2025-07-22 20:53:37 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open ella
2025-07-22 20:53:37 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:53:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open ella', 'Done!', '', '']'
2025-07-22 20:53:45 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: None -> None
2025-07-22 20:53:46 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open ella 测试完成
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open ella', 'Done!', '', '']
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'Done'
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:53:46 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:08:02 | INFO | testcases.test_ella.base_ella_test:add_custom_status_check:258 | 添加自定义状态检查配置: calculator -> 计算器应用状态
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:13:30 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:13:31 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone打开应用，状态: True
2025-07-22 21:13:33 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open phone
2025-07-22 21:13:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:13:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13']'
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:13:47 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open phone 测试完成
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13']
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13'
2025-07-22 21:13:47 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_211347.png
2025-07-22 21:13:47 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_211347.png
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:23:45 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:23:45 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone打开应用，状态: True
2025-07-22 21:23:48 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open phone
2025-07-22 21:23:48 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:23:50 | ERROR | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:305 | 从asr_txt获取响应失败，已达到最大重试次数: (-32001, 'androidx.test.uiautomator.UiObjectNotFoundException', ({'mask': 2097152, 'childOrSibling': [], 'childOrSiblingSelector': [], 'resourceId': 'com.transsion.aivoiceassistant:id/asr_text'},))
2025-07-22 21:23:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23']'
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:24:02 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open phone 测试完成
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23']
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23'
2025-07-22 21:24:03 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_212402.png
2025-07-22 21:24:03 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_212402.png
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:26:13 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:416 | ✅ 响应包含期望内容: 'done'
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:424 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 21:26:22 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:27:19 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:27:24 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:27:24 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contact打开应用，状态: True
2025-07-22 21:27:27 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open contact
2025-07-22 21:27:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:27:36 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27']'
2025-07-22 21:27:41 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:27:42 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:27:42 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open contact 测试完成
2025-07-22 21:27:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27']
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27'
2025-07-22 21:27:42 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_212742.png
2025-07-22 21:27:42 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_20250722_212742.png
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: False
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contacts，状态: False
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: False
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第1次尝试返回
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第1次返回Ella应用失败
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第2次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第2次尝试返回
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第2次返回Ella应用失败
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第3次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第3次尝试返回
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第3次返回Ella应用失败
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:115 | 经过3次尝试仍无法返回Ella页面，强制获取响应
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:417 | get_response_all_text()返回空，尝试备用方法
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:422 | 所有响应获取方法都返回空
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '[]'
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:417 | get_response_all_text()返回空，尝试备用方法
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:422 | 所有响应获取方法都返回空
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contacts，状态: False
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:44:24 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:44:24 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone，状态: True
2025-07-22 21:44:27 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open phone
2025-07-22 21:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 21:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 21:44:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open phone', 'Done!', '', '']'
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 21:44:38 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open phone 测试完成
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open phone', 'Done!', '', '']
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 21:44:38 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:50:34 | INFO | __main__:run_tests:71 | 执行命令: python -m pytest testcases/test_ella -v --alluredir D:\aigc\app_test\reports/allure-results --clean-alluredir
2025-07-22 21:50:35 | INFO | __main__:generate_allure_report:110 | 执行命令: allure generate D:\aigc\app_test\reports/allure-results -o D:\aigc\app_test\reports/allure-report --clean
2025-07-22 21:58:31 | INFO | __main__:run_tests:71 | 执行命令: python -m pytest testcases/test_ella -v --alluredir D:\aigc\app_test\reports/allure-results --clean-alluredir
2025-07-22 21:58:31 | INFO | __main__:generate_allure_report:110 | 执行命令: allure generate D:\aigc\app_test\reports/allure-results -o D:\aigc\app_test\reports/allure-report --clean
2025-07-22 22:02:31 | INFO | __main__:run_tests:71 | 执行命令: python -m pytest testcases/test_ella -v --alluredir D:\aigc\app_test\reports/allure-results --clean-alluredir
2025-07-22 22:02:32 | INFO | __main__:generate_allure_report:110 | 执行命令: allure generate D:\aigc\app_test\reports/allure-results -o D:\aigc\app_test\reports/allure-report --clean
2025-07-22 22:04:20 | ERROR | utils.excel_utils:read_test_data:28 | Excel文件不存在: testcases/test_ella/Ella_Test_Cases.xlsx
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:04:21 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:04:21 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-22 22:04:25 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-22 22:04:26 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:26 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:26 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-07-22 22:04:27 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-22 22:04:34 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-07-22 22:04:34 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-22 22:04:40 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_response_received.png
2025-07-22 22:04:40 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-22 22:04:45 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_test_completed.png
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-22 22:04:48 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-22 22:04:48 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-22 22:04:49 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-22 22:04:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:50 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-22 22:04:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_initial_state.png
2025-07-22 22:04:50 | INFO | testcases.test_ella.history.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 开启
2025-07-22 22:04:57 | INFO | testcases.test_ella.history.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-22 22:04:58 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-22 22:04:59 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app，状态: None
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open app
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:05:54 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]'
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: None -> None
2025-07-22 22:06:02 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open app 测试完成
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:02 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:08 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:08 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: True
2025-07-22 22:06:11 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open bluetooth
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:06:21 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open bluetooth 测试完成
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'bluetooth'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'is turned on now'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 22:06:21 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:27 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:36 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:06:42 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella，状态: None
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open ella
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:06:50 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open ella', 'Done!', '', '']'
2025-07-22 22:06:50 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: None -> None
2025-07-22 22:06:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open ella 测试完成
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open ella', 'Done!', '', '']
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'Done'
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:51 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone，状态: True
2025-07-22 22:06:59 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open phone
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:07:05 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open phone', 'Done!', '', '']'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:07:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open phone 测试完成
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open phone', 'Done!', '', '']
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:07:10 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:07:16 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:07:17 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi，状态: True
2025-07-22 22:07:19 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open wifi
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:07:31 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open wifi 测试完成
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:07:31 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:25:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:25:46 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:25:46 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:25:46 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: None
2025-07-22 22:25:48 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:25:52 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: None -> None
2025-07-22 22:26:00 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:537 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:544 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:545 | 缺失内容: ['WI-FI']
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | 搜索文本: 'open flashlight Flashlight is turned on now. Flashlight'
2025-07-22 22:26:00 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_222600.png
2025-07-22 22:26:00 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_wifi_20250722_222600.png
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:26:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:26:54 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:26:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: None
2025-07-22 22:26:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:27:00 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: None -> None
2025-07-22 22:27:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:27:08 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250722_222707.png
2025-07-22 22:27:08 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250722_222707.png
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:37:51 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:37:51 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-22 22:37:54 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> False
2025-07-22 22:38:05 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:38:06 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250722_223805.png
2025-07-22 22:38:06 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250722_223805.png
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
