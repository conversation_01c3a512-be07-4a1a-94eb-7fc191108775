2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:24:56 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:24:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:24:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:25:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:25:01 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:25:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:25:01 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-07-14 19:25:02 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 关闭
2025-07-14 19:25:10 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-07-14 19:25:10 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-14 19:25:21 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_response_received.png
2025-07-14 19:25:21 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-14 19:25:25 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: '已执行!'
2025-07-14 19:25:25 | WARNING | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 关闭
2025-07-14 19:25:28 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250714_192528.png
2025-07-14 19:25:28 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250714_192528.png
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:25:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:25:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:25:32 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:25:33 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:25:33 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:25:33 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_initial_state.png
2025-07-14 19:25:33 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 关闭
2025-07-14 19:25:41 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-14 19:25:49 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:292 | ✅ 收到AI响应
2025-07-14 19:25:54 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:308 | AI响应内容: '已执行!'
2025-07-14 19:25:54 | WARNING | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:318 | ⚠️ 响应未包含命令相关内容，但继续测试: close bluetooth
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:328 | 蓝牙最终状态: 关闭
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:333 | ✅ 蓝牙已成功关闭
2025-07-14 19:25:57 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_test_completed.png
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:341 | 🎉 close bluetooth命令测试完成
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:26:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:26:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:26:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:26:01 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:26:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:27:06 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'is bluetooth on' 响应: 已执行!
2025-07-14 19:27:06 | WARNING | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:387 | ⚠️ 命令 'is bluetooth on' 响应未包含蓝牙相关内容
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'check bluetooth' 响应: 月光照明导航原理？
2025-07-14 19:28:37 | WARNING | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:387 | ⚠️ 命令 'check bluetooth' 响应未包含蓝牙相关内容
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:28:40 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:28:40 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:28:41 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:28:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:28:42 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:28:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_initial_state.png
2025-07-14 19:28:42 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:407 | 蓝牙初始状态: 关闭
2025-07-14 19:29:04 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_command_sent.png
2025-07-14 19:29:04 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:436 | ✅ 成功执行语音命令: open bluetooth
2025-07-14 19:29:11 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_response_received.png
2025-07-14 19:29:11 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:464 | ✅ 收到AI响应
2025-07-14 19:29:16 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:487 | AI响应内容: '已执行!'
2025-07-14 19:29:16 | WARNING | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:497 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:29:18 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:507 | 蓝牙最终状态: 关闭
2025-07-14 19:29:19 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_voice_open_bluetooth_command_20250714_192918.png
2025-07-14 19:29:19 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_voice_open_bluetooth_command_20250714_192918.png
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:29:21 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:29:21 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:29:23 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:29:23 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:29:23 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:29:23 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_close_initial_state.png
2025-07-14 19:29:24 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:557 | 蓝牙初始状态: 关闭
2025-07-14 19:29:45 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:573 | ✅ 成功执行语音命令: close bluetooth
2025-07-14 19:29:51 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:591 | ✅ 收到AI响应
2025-07-14 19:29:56 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:597 | AI响应内容: '已执行!'
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:607 | 蓝牙最终状态: 关闭
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:612 | ✅ 蓝牙已成功关闭
2025-07-14 19:29:59 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_close_test_completed.png
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:620 | 🎉 语音输入close bluetooth命令测试完成
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:30:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:30:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:30:03 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:30:03 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:30:03 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:30:04 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_initial_state.png
2025-07-14 19:30:04 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:638 | 蓝牙初始状态: 关闭
2025-07-14 19:30:26 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_command_sent.png
2025-07-14 19:30:26 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:672 | ✅ 成功执行TTS语音命令: open bluetooth
2025-07-14 19:30:34 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_response_received.png
2025-07-14 19:30:34 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:700 | ✅ 收到AI响应
2025-07-14 19:30:39 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:723 | AI响应内容: '已执行!'
2025-07-14 19:30:39 | WARNING | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:733 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:743 | 蓝牙最终状态: 关闭
2025-07-14 19:30:42 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_real_tts_voice_open_bluetooth_command_20250714_193042.png
2025-07-14 19:30:42 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\failure_test_real_tts_voice_open_bluetooth_command_20250714_193042.png
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 19:36:03 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:36:03 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:05 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:05 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:07 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:07 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:36:07 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:36:07 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_initial_state.png
2025-07-14 19:36:08 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-14 19:36:16 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_command_sent.png
2025-07-14 19:36:16 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-14 19:36:20 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_response_received.png
2025-07-14 19:36:20 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-14 19:36:24 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:36:24 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-14 19:36:26 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_test_completed.png
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:36:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:36:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:31 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:36:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:36:31 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_initial_state.png
2025-07-14 19:36:31 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 开启
2025-07-14 19:36:39 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-14 19:36:45 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:292 | ✅ 收到AI响应
2025-07-14 19:36:48 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:308 | AI响应内容: 'Bluetooth is turned off now.'
2025-07-14 19:36:48 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:316 | ✅ 响应包含命令相关内容: close bluetooth
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:328 | 蓝牙最终状态: 关闭
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:333 | ✅ 蓝牙已成功关闭
2025-07-14 19:36:51 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_close_test_completed.png
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:341 | 🎉 close bluetooth命令测试完成
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:55 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:36:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:37:44 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'is bluetooth on' 响应: is bluetooth on
2025-07-14 19:37:44 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:385 | ✅ 命令 'is bluetooth on' 响应包含蓝牙相关内容
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'check bluetooth' 响应: is bluetooth on
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:385 | ✅ 命令 'check bluetooth' 响应包含蓝牙相关内容
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:38:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:38:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:38:15 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:38:15 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:38:15 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:38:15 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_initial_state.png
2025-07-14 19:38:16 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:407 | 蓝牙初始状态: 关闭
2025-07-14 19:38:38 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_command_sent.png
2025-07-14 19:38:38 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:436 | ✅ 成功执行语音命令: open bluetooth
2025-07-14 19:38:44 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_response_received.png
2025-07-14 19:38:44 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:464 | ✅ 收到AI响应
2025-07-14 19:38:47 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:487 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:38:47 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:495 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:507 | 蓝牙最终状态: 开启
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:518 | ✅ 蓝牙已成功开启
2025-07-14 19:38:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_test_completed.png
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:539 | 🎉 语音输入open bluetooth命令测试完成
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:38:52 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:38:52 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:38:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:38:54 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:38:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:38:55 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_close_initial_state.png
2025-07-14 19:38:55 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:557 | 蓝牙初始状态: 开启
2025-07-14 19:39:17 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:573 | ✅ 成功执行语音命令: close bluetooth
2025-07-14 19:39:24 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:591 | ✅ 收到AI响应
2025-07-14 19:39:28 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:597 | AI响应内容: 'Bluetooth is turned off now.'
2025-07-14 19:39:30 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:607 | 蓝牙最终状态: 关闭
2025-07-14 19:39:30 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:612 | ✅ 蓝牙已成功关闭
2025-07-14 19:39:31 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_voice_close_test_completed.png
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:620 | 🎉 语音输入close bluetooth命令测试完成
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:39:33 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:39:33 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:39:35 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:39:35 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:39:35 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:36 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_app_started.png
2025-07-14 19:39:35 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_initial_state.png
2025-07-14 19:39:35 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:638 | 蓝牙初始状态: 关闭
2025-07-14 19:39:57 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_command_sent.png
2025-07-14 19:39:57 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:672 | ✅ 成功执行TTS语音命令: open bluetooth
2025-07-14 19:40:06 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_response_received.png
2025-07-14 19:40:06 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:700 | ✅ 收到AI响应
2025-07-14 19:40:10 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:723 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:40:10 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:731 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:743 | 蓝牙最终状态: 开启
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:754 | ✅ 蓝牙已成功开启
2025-07-14 19:40:12 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommand\ella_tts_voice_test_completed.png
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:777 | 🎉 TTS真实语音输入open bluetooth命令测试完成
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:40:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (1/8): test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_bluetooth_simple_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_bluetooth_simple_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (2/8): test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_weather_query_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_weather_query_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_weather_query_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_weather_query_command.py: ['check_bluetooth_status']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_weather_query_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (3/8): test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_set_alarm_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_set_alarm_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_set_alarm_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_set_alarm_command.py: ['check_bluetooth_status']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_set_alarm_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (4/8): test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_take_photo_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_take_photo_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_take_photo_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_take_photo_command.py: ['check_bluetooth_status']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_take_photo_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (5/8): test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_clock_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_clock_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_clock_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_clock_command.py: ['execute_text_command', 'wait_for_response', 'get_response_text', 'check_bluetooth_status']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_clock_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (6/8): test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_bluetooth_voice.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_bluetooth_voice.py: ['execute_text_command']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_bluetooth_voice.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (7/8): test_open_contacts_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_contacts_command.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_contacts_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_class_structure:108 | ⚠️ 未使用重构后的页面类: test_open_contacts_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_contacts_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_page_class_usage:148 | ⚠️ 导入语句不正确: test_open_contacts_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_contacts_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_contacts_command.py: ['check_bluetooth_status']
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_contacts_command.py
2025-07-14 21:00:16 | WARNING | __main__:validate_single_file:257 | ⚠️ 验证部分失败: test_open_contacts_command.py
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:275 | 验证文件 (8/8): test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_contacts_refactored.py
2025-07-14 21:00:16 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_refactored.py: module 'importlib' has no attribute 'util'
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_contacts_refactored.py
2025-07-14 21:00:16 | INFO | __main__:generate_validation_report:357 | 📄 验证报告已生成: D:\aigc\app_test\reports\ella_validation_report.md
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (1/8): test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_bluetooth_simple_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_bluetooth_simple_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (2/8): test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_weather_query_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_weather_query_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_weather_query_command.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_weather_query_command.py: ['check_bluetooth_status']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_weather_query_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (3/8): test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_set_alarm_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_set_alarm_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_set_alarm_command.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_set_alarm_command.py: ['check_bluetooth_status']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_set_alarm_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (4/8): test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_take_photo_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_take_photo_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_take_photo_command.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_take_photo_command.py: ['check_bluetooth_status']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_take_photo_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (5/8): test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_clock_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_clock_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_clock_command.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_clock_command.py: ['execute_text_command', 'wait_for_response', 'get_response_text', 'check_bluetooth_status']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_clock_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (6/8): test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_bluetooth_voice.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_bluetooth_voice.py: ['execute_text_command']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_bluetooth_voice.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (7/8): test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_contacts_command.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_command.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_contacts_command.py
2025-07-14 21:01:22 | WARNING | __main__:validate_method_compatibility:202 | ⚠️ 缺少方法调用 test_open_contacts_command.py: ['check_bluetooth_status']
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_contacts_command.py
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:275 | 验证文件 (8/8): test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_import:60 | 🔍 验证导入: test_open_contacts_refactored.py
2025-07-14 21:01:22 | ERROR | __main__:validate_import:85 | ❌ 验证导入失败 test_open_contacts_refactored.py: module 'importlib' has no attribute 'util'
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:100 | 🔍 验证类结构: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_class_structure:121 | ✅ 类结构正确: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:139 | 🔍 验证页面类使用: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_page_class_usage:162 | ✅ 页面类使用正确: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:180 | 🔍 验证方法兼容性: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_method_compatibility:205 | ✅ 方法兼容性检查完成: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:validate_single_file:255 | 🎉 验证成功: test_open_contacts_refactored.py
2025-07-14 21:01:22 | INFO | __main__:generate_validation_report:357 | 📄 验证报告已生成: D:\aigc\app_test\reports\ella_validation_report.md
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:05:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:05:32 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:24 | 🚀 开始启动Ella应用（优化版本）...
2025-07-14 21:05:35 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:28 | ✅ Ella应用启动成功
2025-07-14 21:05:35 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:31 | ⏳ 等待Ella页面加载...
2025-07-14 21:05:38 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:33 | ✅ Ella页面加载完成
2025-07-14 21:05:38 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_app_started_optimized.png
2025-07-14 21:05:38 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:37 | 📸 启动成功截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_app_started_optimized.png
2025-07-14 21:05:38 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_initial_state_optimized.png
2025-07-14 21:05:39 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:85 | 蓝牙初始状态: 开启
2025-07-14 21:05:41 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_command_sent.png
2025-07-14 21:05:41 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:114 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:05:45 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_response_received.png
2025-07-14 21:05:45 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:142 | ✅ 收到AI响应
2025-07-14 21:05:47 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:170 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 21:05:47 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:178 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 21:05:49 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:190 | 蓝牙最终状态: 开启
2025-07-14 21:05:49 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:201 | ✅ 蓝牙已成功开启
2025-07-14 21:05:50 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandOptimized\ella_test_completed_optimized.png
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:223 | 🎉 open bluetooth命令测试完成（优化版本）
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:63 | 🧹 清理Ella应用...
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:65 | ✅ Ella应用已停止
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:17:08 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:17:10 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:17:13 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:17:17 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> True
2025-07-14 21:17:17 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-14 21:17:17 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:19:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:19:08 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:19:11 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: close bluetooth
2025-07-14 21:19:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:19:18 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\failure_test_close_bluetooth_concise_20250714_211917.png
2025-07-14 21:19:18 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\failure_test_close_bluetooth_concise_20250714_211917.png
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:29:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:29:53 | ERROR | testcases.test_ella.test_bluetooth_simple_command_concise:_test_command:165 | ❌ open bluetooth 测试失败: 蓝牙未开启
assert False
2025-07-14 21:29:53 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandFunctional\failure_test_bluetooth_functional_20250714_212953.png
2025-07-14 21:29:53 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandFunctional\failure_test_bluetooth_functional_20250714_212953.png
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:30:50 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:30:53 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:30:56 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:30:59 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: False -> True
2025-07-14 21:31:00 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-14 21:31:00 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:31:16 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:31:22 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:31:24 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: close bluetooth
2025-07-14 21:31:39 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:31:42 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> False
2025-07-14 21:31:43 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-14 21:31:43 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 close bluetooth 测试完成
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:32:33 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:32:36 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: what is bluetooth status
2025-07-14 21:32:39 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:32:43 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-14 21:32:43 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 what is bluetooth status 测试完成
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
