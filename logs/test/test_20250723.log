2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:17:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:17:01 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:17:03 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> False
2025-07-23 10:17:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:17:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250723_101714.png
2025-07-23 10:17:14 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250723_101714.png
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: True
2025-07-23 10:25:49 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: True -> True
2025-07-23 10:26:01 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:26:01 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:32:25 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:32:25 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:32:30 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:32:41 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:32:41 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> True
2025-07-23 10:32:42 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:32:42 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:38:09 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:38:09 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:38:09 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:38:09 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:38:14 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:38:15 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:38:18 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:38:22 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:38:22 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:38:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> True
2025-07-23 10:38:30 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:38:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:38:31 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:38:31 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:38:31 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:38:31 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:41:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:41:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:41:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:41:51 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:41:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:41:58 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:42:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:42:06 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:42:06 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:42:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> True
2025-07-23 10:42:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:42:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:42:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:42:15 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:42:15 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:42:15 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 12:05:31 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 12:05:31 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 12:05:31 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 12:05:31 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 12:05:37 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 12:05:37 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open clock，状态: True
2025-07-23 12:05:38 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open clock
2025-07-23 12:05:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 12:05:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 12:05:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 12:05:49 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open clock', 'Done!', '', '']'
2025-07-23 12:05:49 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: True -> True
2025-07-23 12:05:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 12:05:50 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open clock 测试完成
2025-07-23 12:05:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open clock', 'Done!', '', '']
2025-07-23 12:05:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: 'done'
2025-07-23 12:05:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:548 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 12:05:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 12:05:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 12:05:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 12:05:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:03:04 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:03:04 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:03:04 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:03:04 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:03:10 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:03:10 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open countdown，状态: None
2025-07-23 13:03:12 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open countdown
2025-07-23 13:03:12 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:03:12 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:03:13 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open countdown', '', '', '', "13:03 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh Liverpool Eye Guehi as Konate's Successor Switch voices Tesla Bets on AI Future open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter"]'
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: None -> None
2025-07-23 13:03:20 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open countdown 测试完成
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open countdown', '', '', '', "13:03 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh Liverpool Eye Guehi as Konate's Successor Switch voices Tesla Bets on AI Future open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter"]
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: 'For how long'
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '5 minutes'
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '10 minutes'
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '20 minutes'
2025-07-23 13:03:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:548 | 🎉 所有期望内容都已找到 (4/4)
2025-07-23 13:03:21 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:03:21 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:03:21 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:03:21 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:06:49 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:06:49 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:06:49 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:06:50 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:06:55 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:06:55 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open countdown，状态: None
2025-07-23 13:06:58 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open countdown
2025-07-23 13:06:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:06:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:06:59 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:07:06 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open countdown', '', '', '', '13:07 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? OpenAI, Oracle Expand Stargate Data Centers Fàbregas Courts Messi for Como open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter']'
2025-07-23 13:07:06 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: None -> None
2025-07-23 13:07:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open countdown 测试完成
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open countdown', '', '', '', '13:07 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? OpenAI, Oracle Expand Stargate Data Centers Fàbregas Courts Messi for Como open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter']
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: 'For how long'
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '5 minutes'
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '10 minutes'
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '20 minutes'
2025-07-23 13:07:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:548 | 🎉 所有期望内容都已找到 (4/4)
2025-07-23 13:07:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:07:07 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:07:07 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:07:07 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:07:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:07:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:07:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:07:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:07:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:07:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open countdown，状态: None
2025-07-23 13:07:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open countdown
2025-07-23 13:07:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:07:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:07:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:08:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open countdown', '', '', '', "Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Wolves Reject Lakers' DiVincenzo Bid Google's $2.4B AI Talent Grab open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter 13:08"]'
2025-07-23 13:08:04 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: None -> None
2025-07-23 13:08:05 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open countdown 测试完成
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open countdown', '', '', '', "Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Wolves Reject Lakers' DiVincenzo Bid Google's $2.4B AI Talent Grab open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter 13:08"]
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: 'For how long'
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '5 minutes'
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '10 minutes'
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: '20 minutes'
2025-07-23 13:08:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:548 | 🎉 所有期望内容都已找到 (4/4)
2025-07-23 13:08:05 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:08:05 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:08:05 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:08:05 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:12:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:12:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:12:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:12:51 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:12:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:12:57 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open camera，状态: True
2025-07-23 13:12:59 | ERROR | core.base_element:click:237 | 点击元素失败 [发送按钮]: (-32001, 'androidx.test.uiautomator.UiObjectNotFoundException', ({'mask': 2097152, 'childOrSibling': [], 'childOrSiblingSelector': [], 'resourceId': 'com.transsion.aivoiceassistant:id/fl_btn_three_btn'},))
2025-07-23 13:12:59 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open camera
2025-07-23 13:13:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:13:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:13:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:13:10 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['', '', '', '', "Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13"]'
2025-07-23 13:13:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: True -> True
2025-07-23 13:13:11 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:13:11 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open camera 测试完成
2025-07-23 13:13:11 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', "Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13"]
2025-07-23 13:13:11 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:543 | ⚠️ 响应未包含期望内容: 'Done'
2025-07-23 13:13:11 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:550 | ❌ 部分期望内容未找到 (0/1)
2025-07-23 13:13:11 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:551 | 缺失内容: ['Done']
2025-07-23 13:13:11 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:552 | 搜索文本: 'Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13'
2025-07-23 13:13:11 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_camera_20250723_131311.png
2025-07-23 13:13:11 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_camera_20250723_131311.png
2025-07-23 13:13:11 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:13:11 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:13:11 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:13:31 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:13:31 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:13:31 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:13:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:13:37 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:13:37 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open camera，状态: True
2025-07-23 13:13:39 | INFO | testcases.test_ella.base_ella_test:_execute_command:359 | ✅ 成功执行命令: open camera
2025-07-23 13:13:43 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:13:43 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:13:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open camera', 'Done!', '', '']'
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:459 | ✅ 状态验证通过: True -> True
2025-07-23 13:13:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:simple_command_test:684 | 🎉 open camera 测试完成
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:496 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open camera', 'Done!', '', '']
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:540 | ✅ 响应包含期望内容: 'Done'
2025-07-23 13:13:50 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:548 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 13:13:50 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:13:51 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:13:51 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:13:51 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:22:47 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:22:47 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:22:47 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:22:48 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:22:53 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:22:53 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open facebook，状态: True
2025-07-23 13:22:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: open facebook
2025-07-23 13:22:59 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:22:59 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:23:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:23:06 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open facebook', 'Done!', '', '']'
2025-07-23 13:23:06 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: True -> True
2025-07-23 13:23:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:23:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 open facebook 测试完成
2025-07-23 13:23:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open facebook', 'Done!', '', '']
2025-07-23 13:23:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | ✅ 响应包含期望内容: 'Done'
2025-07-23 13:23:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:554 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 13:23:07 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:23:07 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:23:07 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:23:07 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:23:32 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:23:32 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:23:32 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:23:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:23:38 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:23:38 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open facebook，状态: False
2025-07-23 13:23:41 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: open facebook
2025-07-23 13:23:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:23:44 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:23:46 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:23:51 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open facebook', 'Done!', '', '']'
2025-07-23 13:23:51 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: False -> True
2025-07-23 13:23:52 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:23:52 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 open facebook 测试完成
2025-07-23 13:23:52 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open facebook', 'Done!', '', '']
2025-07-23 13:23:52 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | ✅ 响应包含期望内容: 'Done'
2025-07-23 13:23:52 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:554 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 13:23:52 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:23:52 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:23:52 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:23:52 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:25:06 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:25:06 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:25:06 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:25:07 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:25:12 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:25:13 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open dialer，状态: True
2025-07-23 13:25:15 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: open dialer
2025-07-23 13:25:21 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:25:21 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:25:21 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:25:26 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open dialer', 'Done!', '', '']'
2025-07-23 13:25:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: True -> True
2025-07-23 13:25:27 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:25:27 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 open dialer 测试完成
2025-07-23 13:25:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open dialer', 'Done!', '', '']
2025-07-23 13:25:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | ✅ 响应包含期望内容: 'Done'
2025-07-23 13:25:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:554 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 13:25:27 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:25:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:25:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:25:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 13:26:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 13:26:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 13:26:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 13:26:54 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 13:27:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 13:27:00 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open folax，状态: None
2025-07-23 13:27:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: open folax
2025-07-23 13:27:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 13:27:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 13:27:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open folax', 'Done!', '', '']'
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: None -> None
2025-07-23 13:27:08 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 open folax 测试完成
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open folax', 'Done!', '', '']
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | ✅ 响应包含期望内容: 'Done'
2025-07-23 13:27:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:554 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 13:27:09 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 13:27:09 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 13:27:09 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 13:27:09 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 16:28:36 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 16:28:36 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 16:28:36 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 16:28:37 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 16:28:42 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 16:28:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令order a burger，状态: None
2025-07-23 16:28:45 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: order a burger
2025-07-23 16:28:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 16:28:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 16:28:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 16:28:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['order a burger', "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:\n\n- Image Q&A: Who is this? Where is this?\n- Numbers and Contacts: Call this number; save this new contact to my contacts\n- Address Navigation: Navigate to this address\n- Note creation: Create a new note with the text on the screen\n- Schedule Extraction: Create this schedule for me\n- Copywriting Assistance: Write a caption for this picture\n\nThese are just a few of the features. Activate Ella to explore new functionalities right away.", '', '']'
2025-07-23 16:28:52 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: None -> None
2025-07-23 16:28:52 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 16:28:52 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 order a burger 测试完成
2025-07-23 16:28:52 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['order a burger', "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:\n\n- Image Q&A: Who is this? Where is this?\n- Numbers and Contacts: Call this number; save this new contact to my contacts\n- Address Navigation: Navigate to this address\n- Note creation: Create a new note with the text on the screen\n- Schedule Extraction: Create this schedule for me\n- Copywriting Assistance: Write a caption for this picture\n\nThese are just a few of the features. Activate Ella to explore new functionalities right away.", '', '']
2025-07-23 16:28:52 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:549 | ⚠️ 响应未包含期望内容: 'Sorry'
2025-07-23 16:28:52 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:556 | ❌ 部分期望内容未找到 (0/1)
2025-07-23 16:28:52 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:557 | 缺失内容: ['Sorry']
2025-07-23 16:28:52 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:558 | 搜索文本: 'order a burger What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away.'
2025-07-23 16:28:52 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_order_a_burger_20250723_162852.png
2025-07-23 16:28:52 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_order_a_burger_20250723_162852.png
2025-07-23 16:28:53 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 16:28:53 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 16:28:53 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 16:30:19 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 16:30:19 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 16:30:19 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 16:30:20 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 16:30:25 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 16:30:25 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令order a burger，状态: None
2025-07-23 16:30:27 | INFO | testcases.test_ella.base_ella_test:_execute_command:365 | ✅ 成功执行命令: order a burger
2025-07-23 16:30:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 16:30:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 16:30:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['order a burger', "Sorry, I'm still learning how to help you order food delivery.", '', '']'
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:465 | ✅ 状态验证通过: None -> None
2025-07-23 16:30:35 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:simple_command_test:690 | 🎉 order a burger 测试完成
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:502 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['order a burger', "Sorry, I'm still learning how to help you order food delivery.", '', '']
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | ✅ 响应包含期望内容: 'Sorry'
2025-07-23 16:30:35 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:554 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 16:30:35 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 16:30:36 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 16:30:36 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 16:30:36 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
