2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-15 20:18:43 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-15 20:18:47 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-15 20:18:50 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-15 20:18:54 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: False -> True
2025-07-15 20:18:55 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-15 20:18:55 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
