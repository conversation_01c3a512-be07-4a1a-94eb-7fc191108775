2025-07-11 12:12:02 | INFO | core.base_driver:screenshot:301 | 截图保存: D:\PythonProject\app_test\reports/screenshots\unknown\ella_debug_20250711_121201.png
2025-07-11 12:12:02 | INFO | __main__:_take_debug_screenshot:229 | ✅ 调试截图已保存: D:\PythonProject\app_test\reports/screenshots\unknown\ella_debug_20250711_121201.png
2025-07-11 12:12:02 | INFO | __main__:get_detailed_app_info:249 | 应用权限信息: ShellResponse(output='        c9c0ff8 com.transsion.aivoiceassistant/com.transsion.ella.service.WakeupService filter d208cd1 permission com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE\n        c9c0ff8 com.transsion.aivoiceassistant/com.transsion.ella.service.WakeupService filter d208cd1 permission com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE\n        c9c0ff8 com.transsion.aivoiceassistant/com.transsion.ella.service.WakeupService filter c69d336 permission com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE\n        c9c0ff8 com.transsion.aivoiceassistant/com.transsion.ella.service.WakeupService filter 92d9237 permission com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE\n        bc9a3f com.transsion.aivoiceassistant/com.transsion.ella.aiservice.voiceinteraction.AiVoiceInteractionService filter 3070f0c permission android.permission.BIND_VOICE_INTERACTION\n        87e33a4 com.transsion.aivoiceassistant/com.transsion.ella.service.ReadAccessibilityService filter 650250d permission android.permission.BIND_ACCESSIBILITY_SERVICE\n        bc9a3f com.transsion.aivoiceassistant/com.transsion.ella.aiservice.voiceinteraction.AiVoiceInteractionService filter fdf4055 permission android.permission.BIND_VOICE_INTERACTION\n  Permission [com.transsion.aivoiceassistant.permission.BIND_VOICE_SERVICE] (5f51804):\n    perm=PermissionInfo{eedd698 com.transsion.aivoiceassistant.permission.BIND_VOICE_SERVICE}\n  Permission [com.transsion.aivoiceassistant.permission.BIND_AIVA2SPORTHEATH_SERVICE] (5d5822):\n    perm=PermissionInfo{935c444 com.transsion.aivoiceassistant.permission.BIND_AIVA2SPORTHEATH_SERVICE}\n  Permission [com.transsion.ella.permission.START_ACTIVITY] (4e512b3):\n    perm=PermissionInfo{e027e62 com.transsion.ella.permission.START_ACTIVITY}\n  Permission [com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE] (aadb070):\n    perm=PermissionInfo{ece0b0 com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE}\n  Permission [com.transsion.aivoiceassistant.permission.VOICE_STATE] (e566fe9):\n    perm=PermissionInfo{38e1eae com.transsion.aivoiceassistant.permission.VOICE_STATE}\n  Permission [com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE] (81cd46e):\n    perm=PermissionInfo{db757dc com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE}\n    declared permissions:\n      com.transsion.ella.permission.START_ACTIVITY: prot=signature\n      com.transsion.aivoiceassistant.permission.VOICE_STATE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_VOICE_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_AIVA2SPORTHEATH_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE: prot=signature\n    requested permissions:\n      transsion.permission.notebook\n      android.permission.REAL_GET_TASKS\n      spacesaver.permission.ACCESS_API\n      com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE\n      android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS\n      hyperion.permission.USE_SECURE_STORAGE\n      android.permission.POST_NOTIFICATIONS\n      android.permission.READ_CALL_LOG\n      com.google.android.c2dm.permission.RECEIVE\n      android.permission.ACCESS_FINE_LOCATION\n      com.transsion.easypic.permission.BIND_EASY_PIC_SERVICE\n      android.permission.ACCESS_NOTIFICATION_POLICY\n      android.permission.ACCESS_HIDDEN_PROFILES\n      com.google.android.providers.gsf.permission.READ_GSERVICES\n      com.transsion.aicore.permission.RUN_STATES\n      com.transsion.aicore.permission.LLM_SUPPORT\n      android.permission.INTERNAL_SYSTEM_WINDOW\n      hitranslate.hls.service.permission\n      android.permission.NETWORK_SETTINGS\n      hiservice.permission.connection\n      android.permission.FOREGROUND_SERVICE\n      android.permission.READ_MEDIA_VISUAL_USER_SELECTED\n      android.permission.RECEIVE_BOOT_COMPLETED\n      com.mediatek.permission.USE_VOICE_COMMAND_SERVICE\n      android.permission.DEVICE_POWER\n      android.permission.BLUETOOTH_CONNECT\n      com.sh.smart.caller.permission.READ_AUTOANSWER_SETTING\n      android.permission.BLUETOOTH\n      com.android.alarm.permission.SET_ALARM\n      android.permission.INTERNET\n      android.permission.REORDER_TASKS\n      android.permission.BLUETOOTH_ADMIN\n      android.permission.READ_EXTERNAL_STORAGE\n      com.transsion.aicore.main.permission.MM_PROVIDER\n      com.transsion.aicoreservice.permission.ACCESS_AI_SERVICE\n      android.permission.INTERACT_ACROSS_USERS_FULL\n      com.transsion.aicore.main.permission.MM_RECEIVER\n      android.permission.WRITE_SECURE_SETTINGS\n      android.permission.ACCESS_COARSE_LOCATION\n      android.permission.READ_SEARCH_INDEXABLES\n      android.permission.READ_PHONE_STATE\n      android.permission.READ_PRIVILEGED_PHONE_STATE\n      android.permission.READ_AIWALLPAPER_USAGE\n      android.permission.CALL_PHONE\n      android.permission.READ_MEDIA_IMAGES\n      android.permission.CONTROL_DISPLAY_BRIGHTNESS\n      android.permission.CHANGE_WIFI_STATE\n      android.permission.SYSTEM_APPLICATION_OVERLAY\n      android.permission.MODIFY_DAY_NIGHT_MODE\n      android.permission.SET_PREFERRED_APPLICATIONS\n      android.permission.ACCESS_NETWORK_STATE\n      android.permission.CAMERA\n      android.permission.INTERACT_ACROSS_USERS\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE\n      com.android.permissioncontroller.permission.MANAGE_ROLES_FROM_CONTROLLER\n      android.permission.INSTALL_SELF_UPDATES\n      android.permission.READ_MEDIA_VIDEO\n      transsion.permission.health\n      com.afmobi.boomplayer.permission.BPSDKPLAYKIT\n      android.permission.REBOOT\n      com.transsion.dataservice.permission.WRITE\n      android.permission.FOREGROUND_SERVICE_MICROPHONE\n      android.permission.PROCESS_OUTGOING_CALLS\n      android.permission.SCHEDULE_EXACT_ALARM\n      hyperion.permission.USE_SECURE_CRYPTO\n      com.transsion.aivoiceassistant.permission.BIND_DATA_SERVICE\n      com.transsion.tpush.permission.READ_PROVIDER\n      com.transsion.kolun.assistant.permission.WRITE_FUNCTION_CONFIGURATION\n      com.transsion.kolun.assistant.permission.READ_FUNCTION_CONFIGURATION\n      android.permission.VIBRATE\n      com.transsion.dataservice.permission.READ\n      com.transsion.tpush.permission.WRITE_PROVIDER\n      android.permission.READ_CLIPBOARD_IN_BACKGROUND\n      com.transsion.permission.OCR_SUGGESTION_CHANGE\n      hoffnung.permission.READ_DEVICE_INFO\n      android.permission.ACCESS_WIFI_STATE\n      android.permission.MODIFY_PHONE_STATE\n      android.permission.QUERY_ALL_PACKAGES\n      android.permission.RECORD_AUDIO\n      com.google.android.gms.permission.AD_ID\n      android.permission.NETWORK_AIRPLANE_MODE\n      android.permission.WAKE_LOCK\n      android.permission.READ_CONTACTS\n      android.permission.UPDATE_APP_OPS_STATS\n    install permissions:\n      transsion.permission.notebook: granted=true\n      android.permission.REAL_GET_TASKS: granted=true\n      android.permission.ACCESS_CACHE_FILESYSTEM: granted=true\n      android.permission.FOREGROUND_SERVICE_CAMERA: granted=true\n      android.permission.NFC_PREFERRED_PAYMENT_INFO: granted=true\n      spacesaver.permission.ACCESS_API: granted=true\n      android.permission.ACCESS_GPU_SERVICE: granted=true\n      android.permission.MANAGE_APPOPS: granted=true\n      android.permission.BIND_INCALL_SERVICE: granted=true\n      android.permission.WRITE_SETTINGS: granted=true\n      com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE: granted=true\n      android.permission.READ_WALLPAPER_INTERNAL: granted=true\n      android.permission.CONFIGURE_WIFI_DISPLAY: granted=true\n      android.permission.CONFIGURE_DISPLAY_COLOR_MODE: granted=true\n      android.permission.RECOVERY: granted=true\n      android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS: granted=true\n      hyperion.permission.USE_SECURE_STORAGE: granted=true\n      com.transsion.hilauncher.permission.READ_ICON: granted=true\n      android.permission.LIST_ENABLED_CREDENTIAL_PROVIDERS: granted=true\n      android.permission.LOCATION_BYPASS: granted=true\n      com.transsion.phonemanager.permission.C2D_MESSAGE: granted=true\n      android.permission.BIND_ATTENTION_SERVICE: granted=true\n      com.google.android.c2dm.permission.RECEIVE: granted=true\n      android.permission.STORAGE_INTERNAL: granted=true\n      android.permission.USE_CREDENTIALS: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES_FULL: granted=true\n      android.permission.READ_SYSTEM_UPDATE_INFO: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS: granted=true\n      android.permission.MANAGE_EXTERNAL_STORAGE: granted=true\n      android.permission.ACCESS_CHECKIN_PROPERTIES: granted=true\n      com.transsion.easypic.permission.BIND_EASY_PIC_SERVICE: granted=true\n      android.permission.ACCESS_NOTIFICATION_POLICY: granted=true\n      android.permission.REMAP_MODIFIER_KEYS: granted=true\n      android.permission.MODIFY_AUDIO_ROUTING: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES: granted=true\n      com.mediatek.permission.omacp.install: granted=true\n      android.permission.READ_SAFETY_CENTER_STATUS: granted=true\n      com.xui.xhide.permission.READ_DATA: granted=true\n      com.google.android.providers.gsf.permission.READ_GSERVICES: granted=true\n      android.permission.CONTROL_KEYGUARD_SECURE_NOTIFICATIONS: granted=true\n      android.permission.READ_WIFI_CREDENTIAL: granted=true\n      android.permission.QUERY_AUDIO_STATE: granted=true\n      com.transsion.aicore.permission.RUN_STATES: granted=true\n      android.permission.INSTALL_LOCATION_PROVIDER: granted=true\n      android.permission.USE_RESERVED_DISK: granted=true\n      com.transsion.tranfacmode.permission.SHUTDOWN: granted=true\n      android.permission.START_ACTIVITY_AS_CALLER: granted=true\n      android.permission.SYSTEM_ALERT_WINDOW: granted=true\n      com.transsion.cutepet.provider.permission: granted=true\n      android.permission.BROADCAST_PHONE_ACCOUNT_REGISTRATION: granted=true\n      android.permission.START_TASKS_FROM_RECENTS: granted=true\n      android.permission.NFC_TRANSACTION_EVENT: granted=true\n      com.transsion.aicore.permission.LLM_SUPPORT: granted=true\n      android.permission.CLEAR_APP_USER_DATA: granted=true\n      android.permission.BROADCAST_CALLLOG_INFO: granted=true\n      android.permission.MANAGE_SOUND_TRIGGER: granted=true\n      android.permission.CONTROL_UI_TRACING: granted=true\n      android.permission.SHUTDOWN: granted=true\n      android.permission.NFC: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS_PRIVILEGED: granted=true\n      android.permission.INTERNAL_SYSTEM_WINDOW: granted=true\n      android.permission.BIND_ROTATION_RESOLVER_SERVICE: granted=true\n      hitranslate.hls.service.permission: granted=true\n      android.permission.NETWORK_SETTINGS: granted=true\n      android.permission.CHANGE_OVERLAY_PACKAGES: granted=true\n      android.permission.START_ANY_ACTIVITY: granted=true\n      android.permission.CALL_PRIVILEGED: granted=true\n      hiservice.permission.connection: granted=true\n      android.permission.CHANGE_NETWORK_STATE: granted=true\n      android.permission.MASTER_CLEAR: granted=true\n      android.permission.FOREGROUND_SERVICE: granted=true\n      android.permission.WRITE_SYNC_SETTINGS: granted=true\n      android.permission.READ_INSTALLED_SESSION_PATHS: granted=true\n      android.permission.ALLOW_PLACE_IN_MULTI_PANE_SETTINGS: granted=true\n      android.permission.MANAGE_DYNAMIC_SYSTEM: granted=true\n      android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK: granted=true\n      android.permission.MANAGE_ACTIVITY_TASKS: granted=true\n      android.permission.RECEIVE_BOOT_COMPLETED: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROCESSING: granted=true\n      com.transsion.gamemode.permission.READ_APP_LIST: granted=true\n      com.google.android.googleapps.permission.GOOGLE_AUTH: granted=true\n      android.permission.FOREGROUND_SERVICE_SPECIAL_USE: granted=true\n      android.permission.MANAGE_ROLE_HOLDERS: granted=true\n      android.permission.PEERS_MAC_ADDRESS: granted=true\n      android.permission.DEVICE_POWER: granted=true\n      android.permission.ENFORCE_UPDATE_OWNERSHIP: granted=true\n      android.permission.HIGH_SAMPLING_RATE_SENSORS: granted=true\n      android.permission.FOREGROUND_SERVICE_LOCATION: granted=true\n      android.permission.READ_PRINT_SERVICES: granted=true\n      android.permission.EXPAND_STATUS_BAR: granted=true\n      com.transsion.applock.permission.READ_APP_LOCK: granted=true\n      android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS: granted=true\n      android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER: granted=true\n      android.permission.RESTART_WIFI_SUBSYSTEM: granted=true\n      android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS: granted=true\n      android.permission.READ_PROFILE: granted=true\n      com.sh.smart.caller.permission.READ_AUTOANSWER_SETTING: granted=true\n      android.permission.BLUETOOTH: granted=true\n      com.android.alarm.permission.SET_ALARM: granted=true\n      android.permission.CAPTURE_AUDIO_HOTWORD: granted=true\n      android.permission.WRITE_MEDIA_STORAGE: granted=true\n      android.permission.WRITE_BLOCKED_NUMBERS: granted=true\n      os_noticenter.permission.CONFIG_PROVIDER: granted=true\n      android.permission.WATCH_APPOPS: granted=true\n      android.permission.GET_TASKS: granted=true\n      android.permission.BIND_REMOTE_LOCKSCREEN_VALIDATION_SERVICE: granted=true\n      android.permission.INTERNET: granted=true\n      com.dts.permission.DTS_EFFECT: granted=true\n      android.permission.REORDER_TASKS: granted=true\n      android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION: granted=true\n      android.permission.BLUETOOTH_ADMIN: granted=true\n      android.permission.CONTROL_VPN: granted=true\n      android.permission.UPDATE_DEVICE_STATS: granted=true\n      android.permission.READ_PRECISE_PHONE_STATE: granted=true\n      android.permission.MANAGE_FINGERPRINT: granted=true\n      com.transsion.permission.Exception: granted=true\n      android.permission.READ_PROJECTION_STATE: granted=true\n      com.transsion.permission.unsee.peekproof: granted=true\n      com.xui.xhide.permission.READ_DATA_FOR_THIRD_PARTY: granted=true\n      android.permission.SUBSCRIBE_TO_KEYGUARD_LOCKED_STATE: granted=true\n      android.permission.SEND_SAFETY_CENTER_UPDATE: granted=true\n      android.permission.ACCESS_INSTANT_APPS: granted=true\n      android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS: granted=true\n      com.transsion.aicore.main.permission.MM_PROVIDER: granted=true\n      com.android.taglog.permission.TagLog: granted=true\n      android.permission.GET_PACKAGE_SIZE: granted=true\n      android.permission.ACCESS_CONTEXT_HUB: granted=true\n      android.permission.MANAGE_USB: granted=true\n      android.permission.INTERACT_ACROSS_USERS_FULL: granted=true\n      android.permission.STOP_APP_SWITCHES: granted=true\n      android.permission.RECEIVE_MEDIA_RESOURCE_USAGE: granted=true\n      android.permission.ACCESS_LAST_KNOWN_CELL_ID: granted=true\n      android.permission.BATTERY_STATS: granted=true\n      android.permission.CAPTURE_VOICE_COMMUNICATION_OUTPUT: granted=true\n      android.permission.PACKAGE_USAGE_STATS: granted=true\n      com.transsion.aicore.main.permission.MM_RECEIVER: granted=true\n      android.permission.MOUNT_UNMOUNT_FILESYSTEMS: granted=true\n      android.permission.TETHER_PRIVILEGED: granted=true\n      android.permission.WRITE_SECURE_SETTINGS: granted=true\n      android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED: granted=true\n      android.permission.BIND_SCREENING_SERVICE: granted=true\n      android.permission.CAPTURE_AUDIO_OUTPUT: granted=true\n      android.permission.MANAGE_DEBUGGING: granted=true\n      android.permission.MOVE_PACKAGE: granted=true\n      android.permission.SET_ACTIVITY_WATCHER: granted=true\n      hoffnung.permission.READ_CONFIG_PROVIDER: granted=true\n      android.permission.READ_BLOCKED_NUMBERS: granted=true\n      com.xui.xhide.permission.C2D_MESSAGE: granted=true\n      android.permission.SATELLITE_COMMUNICATION: granted=true\n      android.permission.STATUS_BAR_SERVICE: granted=true\n      android.permission.READ_SEARCH_INDEXABLES: granted=true\n      android.permission.USE_FULL_SCREEN_INTENT: granted=true\n      android.permission.RADIO_SCAN_WITHOUT_LOCATION: granted=true\n      android.permission.ACCESS_LOCATION_EXTRA_COMMANDS: granted=true\n      android.permission.READ_PRIVILEGED_PHONE_STATE: granted=true\n      com.transsion.kolun.assistant.permission.ABILITY: granted=true\n      android.permission.READ_SIGNAL_PREDICT: granted=true\n      android.permission.ACCESS_DOWNLOAD_MANAGER: granted=true\n      android.permission.BROADCAST_STICKY: granted=true\n      android.permission.INSTALL_PACKAGE_UPDATES: granted=true\n      android.permission.BLUETOOTH_PRIVILEGED: granted=true\n      android.permission.READ_AIWALLPAPER_USAGE: granted=true\n      android.permission.HARDWARE_TEST: granted=true\n      android.permission.USE_BIOMETRIC_INTERNAL: granted=true\n      com.transsion.magicfont.provider.permission.WRITE_PROVIDER: granted=true\n      android.permission.WRITE_OBB: granted=true\n      android.permission.INSTALL_DYNAMIC_SYSTEM: granted=true\n      android.permission.CONTROL_DISPLAY_BRIGHTNESS: granted=true\n      android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME: granted=true\n      android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE: granted=true\n      android.permission.ACCESS_KEYGUARD_SECURE_STORAGE: granted=true\n      android.permission.BIND_JOB_SERVICE: granted=true\n      android.permission.OBSERVE_ROLE_HOLDERS: granted=true\n      android.permission.CONFIRM_FULL_BACKUP: granted=true\n      android.permission.CAPTURE_SECURE_VIDEO_OUTPUT: granted=true\n      android.permission.WRITE_APN_SETTINGS: granted=true\n      android.permission.CHANGE_WIFI_STATE: granted=true\n      android.permission.SYSTEM_APPLICATION_OVERLAY: granted=true\n      android.permission.MODIFY_DAY_NIGHT_MODE: granted=true\n      android.permission.MANAGE_BIOMETRIC: granted=true\n      android.permission.FOREGROUND_SERVICE_DATA_SYNC: granted=true\n      com.transsion.hilauncher.permission.READ_SETTINGS: granted=true\n      android.permission.MANAGE_USERS: granted=true\n      android.permission.SET_PREFERRED_APPLICATIONS: granted=true\n      android.permission.FLASHLIGHT: granted=true\n      android.permission.ACCESS_NETWORK_STATE: granted=true\n      android.permission.DISABLE_KEYGUARD: granted=true\n      android.permission.BACKUP: granted=true\n      android.permission.CHANGE_CONFIGURATION: granted=true\n      android.permission.USER_ACTIVITY: granted=true\n      android.permission.LOCAL_MAC_ADDRESS: granted=true\n      android.permission.READ_LOGS: granted=true\n      android.permission.COPY_PROTECTED_DATA: granted=true\n      android.permission.GET_BACKGROUND_INSTALLED_PACKAGES: granted=true\n      android.permission.INTERACT_ACROSS_USERS: granted=true\n      android.permission.SET_WALLPAPER: granted=true\n      android.permission.SET_KEYBOARD_LAYOUT: granted=true\n      android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS: granted=true\n      android.permission.READ_DREAM_STATE: granted=true\n      android.permission.START_VIEW_APP_FEATURES: granted=true\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE: granted=true\n      com.android.permissioncontroller.permission.MANAGE_ROLES_FROM_CONTROLLER: granted=true\n      android.permission.MANAGE_ENHANCED_CONFIRMATION_STATES: granted=true\n      android.permission.HANDLE_QUERY_PACKAGE_RESTART: granted=true\n      android.permission.INSTALL_SELF_UPDATES: granted=true\n      android.permission.MANAGE_APP_OPS_RESTRICTIONS: granted=true\n      android.permission.KILL_BACKGROUND_PROCESSES: granted=true\n      android.permission.MANAGE_USER_OEM_UNLOCK_STATE: granted=true\n      android.permission.USE_FINGERPRINT: granted=true\n      android.permission.REQUEST_NETWORK_SCORES: granted=true\n      android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS: granted=true\n      android.permission.READ_BASIC_PHONE_STATE: granted=true\n      android.permission.WRITE_USER_DICTIONARY: granted=true\n      transsion.permission.health: granted=true\n      android.permission.READ_DREAM_SUPPRESSION: granted=true\n      android.permission.READ_SYNC_STATS: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION: granted=true\n      android.permission.ACCESS_SHORTCUTS: granted=true\n      android.permission.REBOOT: granted=true\n      com.transsion.dataservice.permission.WRITE: granted=true\n      android.permission.MOUNT_FORMAT_FILESYSTEMS: granted=true\n      android.permission.FOREGROUND_SERVICE_MICROPHONE: granted=true\n      android.permission.SCHEDULE_EXACT_ALARM: granted=true\n      android.permission.REQUEST_DELETE_PACKAGES: granted=true\n      android.permission.OEM_UNLOCK_STATE: granted=true\n      android.permission.MANAGE_DEVICE_ADMINS: granted=true\n      hyperion.permission.USE_SECURE_CRYPTO: granted=true\n      android.permission.CHANGE_APP_IDLE_STATE: granted=true\n      android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_DATA_SERVICE: granted=true\n      com.transsion.tpush.permission.READ_PROVIDER: granted=true\n      android.permission.TEST_BLACKLISTED_PASSWORD: granted=true\n      android.permission.ACCESS_PDB_STATE: granted=true\n      android.permission.MANAGE_NETWORK_POLICY: granted=true\n      android.permission.MANAGE_NOTIFICATION_LISTENERS: granted=true\n      android.permission.SET_POINTER_SPEED: granted=true\n      android.permission.MANAGE_NOTIFICATIONS: granted=true\n      transsion.permission.APP_HIDE_LISTENER: granted=true\n      android.permission.USE_COLORIZED_NOTIFICATIONS: granted=true\n      android.permission.MANAGE_GAME_MODE: granted=true\n      android.permission.SEND_SHOW_SUSPENDED_APP_DETAILS: granted=true\n      android.permission.CONNECTIVITY_INTERNAL: granted=true\n      android.permission.MANAGE_MEDIA_PROJECTION: granted=true\n      android.permission.READ_SYNC_SETTINGS: granted=true\n      android.permission.QUERY_USERS: granted=true\n      android.permission.START_ACTIVITIES_FROM_BACKGROUND: granted=true\n      com.transsion.kolun.assistant.permission.WRITE_FUNCTION_CONFIGURATION: granted=true\n      android.permission.BIND_CELL_BROADCAST_SERVICE: granted=true\n      transsion.permission.welife: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK: granted=true\n      com.transsion.magicfont.provider.permission.READ_PROVIDER: granted=true\n      android.permission.LOADER_USAGE_STATS: granted=true\n      android.permission.OVERRIDE_WIFI_CONFIG: granted=true\n      android.permission.FORCE_STOP_PACKAGES: granted=true\n      android.permission.SUGGEST_MANUAL_TIME_AND_ZONE: granted=true\n      android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS: granted=true\n      android.permission.CAPTURE_VIDEO_OUTPUT: granted=true\n      android.permission.ACCESS_NOTIFICATIONS: granted=true\n      android.permission.HANDLE_CALL_INTENT: granted=true\n      android.permission.MEDIA_RESOURCE_OVERRIDE_PID: granted=true\n      com.transsion.kolun.assistant.permission.READ_FUNCTION_CONFIGURATION: granted=true\n      android.permission.CUSTOMIZE_SYSTEM_UI: granted=true\n      android.permission.VIBRATE: granted=true\n      android.permission.MANAGE_APP_HIBERNATION: granted=true\n      com.transsion.dataservice.permission.READ: granted=true\n      android.permission.MANAGE_ACTIVITY_STACKS: granted=true\n      android.permission.HANDLE_CAR_MODE_CHANGES: granted=true\n      android.permission.INTERACT_ACROSS_PROFILES: granted=true\n      android.permission.READ_USER_DICTIONARY: granted=true\n      android.permission.WHITELIST_RESTRICTED_PERMISSIONS: granted=true\n      com.transsion.tpush.permission.WRITE_PROVIDER: granted=true\n      android.permission.CREATE_USERS: granted=true\n      android.permission.READ_CLIPBOARD_IN_BACKGROUND: granted=true\n      com.transsion.permission.OCR_SUGGESTION_CHANGE: granted=true\n      android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS: granted=true\n      android.permission.CRYPT_KEEPER: granted=true\n      hoffnung.permission.READ_DEVICE_INFO: granted=true\n      android.permission.READ_SYSTEM_GRAMMATICAL_GENDER: granted=true\n      android.permission.CALL_AUDIO_INTERCEPTION: granted=true\n      android.permission.DISPATCH_PROVISIONING_MESSAGE: granted=true\n      transsion.aegis.permission.READ_CONTENT: granted=true\n      android.permission.ACCESS_WIFI_STATE: granted=true\n      android.permission.READ_APP_SPECIFIC_LOCALES: granted=true\n      android.permission.TURN_SCREEN_ON: granted=true\n      android.permission.USE_BIOMETRIC: granted=true\n      gamespace.transsion.permission.gameaccelerator: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE: granted=true\n      android.permission.MANAGE_APP_OPS_MODES: granted=true\n      android.permission.REQUEST_INSTALL_PACKAGES: granted=true\n      android.permission.CONTROL_INCALL_EXPERIENCE: granted=true\n      android.permission.MODIFY_PHONE_STATE: granted=true\n      com.transsion.smartpanel.permission.hideHandle: granted=true\n      android.permission.STATUS_BAR: granted=true\n      com.xui.xhide.permission.WRITE_DATA: granted=true\n      android.permission.QUERY_ALL_PACKAGES: granted=true\n      transsion.permission.READ_SPLITSCREEN_SETTING: granted=true\n      com.google.android.gms.permission.AD_ID: granted=true\n      android.permission.READ_DEVICE_CONFIG: granted=true\n      android.permission.LOCATION_HARDWARE: granted=true\n      android.permission.SUPPRESS_CLIPBOARD_ACCESS_NOTIFICATION: granted=true\n      android.permission.UNLIMITED_TOASTS: granted=true\n      android.permission.NETWORK_AIRPLANE_MODE: granted=true\n      android.permission.QUERY_ADMIN_POLICY: granted=true\n      android.permission.WAKE_LOCK: granted=true\n      android.permission.INJECT_EVENTS: granted=true\n      android.permission.BIND_NETWORK_RECOMMENDATION_SERVICE: granted=true\n      com.transsion.permission.UX_DETECTOR_SERVICE: granted=true\n      android.permission.UPDATE_APP_OPS_STATS: granted=true\n      android.permission.READ_OEM_UNLOCK_STATE: granted=true\n      android.permission.MEDIA_CONTENT_CONTROL: granted=true\n      android.permission.DELETE_PACKAGES: granted=true\n    declared permissions:\n      com.transsion.ella.permission.START_ACTIVITY: prot=signature\n      com.transsion.aivoiceassistant.permission.VOICE_STATE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_VOICE_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_AIVA2SPORTHEATH_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE: prot=normal\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE: prot=signature\n    requested permissions:\n      transsion.permission.notebook\n      android.permission.REAL_GET_TASKS\n      spacesaver.permission.ACCESS_API\n      com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE\n      android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS\n      hyperion.permission.USE_SECURE_STORAGE\n      android.permission.READ_CALL_LOG\n      com.google.android.c2dm.permission.RECEIVE\n      android.permission.ACCESS_FINE_LOCATION\n      com.transsion.easypic.permission.BIND_EASY_PIC_SERVICE\n      android.permission.ACCESS_NOTIFICATION_POLICY\n      android.permission.ACCESS_HIDDEN_PROFILES\n      com.google.android.providers.gsf.permission.READ_GSERVICES\n      com.transsion.aicore.permission.RUN_STATES\n      com.transsion.aicore.permission.LLM_SUPPORT\n      android.permission.INTERNAL_SYSTEM_WINDOW\n      hitranslate.hls.service.permission\n      android.permission.NETWORK_SETTINGS\n      hiservice.permission.connection\n      android.permission.FOREGROUND_SERVICE\n      android.permission.READ_MEDIA_VISUAL_USER_SELECTED\n      android.permission.RECEIVE_BOOT_COMPLETED\n      com.mediatek.permission.USE_VOICE_COMMAND_SERVICE\n      android.permission.DEVICE_POWER\n      android.permission.BLUETOOTH_CONNECT\n      com.sh.smart.caller.permission.READ_AUTOANSWER_SETTING\n      android.permission.BLUETOOTH\n      com.android.alarm.permission.SET_ALARM\n      android.permission.INTERNET\n      android.permission.REORDER_TASKS\n      android.permission.BLUETOOTH_ADMIN\n      android.permission.READ_EXTERNAL_STORAGE\n      com.transsion.aicore.main.permission.MM_PROVIDER\n      com.transsion.aicoreservice.permission.ACCESS_AI_SERVICE\n      android.permission.INTERACT_ACROSS_USERS_FULL\n      com.transsion.aicore.main.permission.MM_RECEIVER\n      android.permission.WRITE_SECURE_SETTINGS\n      android.permission.ACCESS_COARSE_LOCATION\n      android.permission.READ_SEARCH_INDEXABLES\n      android.permission.READ_PHONE_STATE\n      android.permission.READ_PRIVILEGED_PHONE_STATE\n      android.permission.READ_AIWALLPAPER_USAGE\n      android.permission.CALL_PHONE\n      android.permission.READ_MEDIA_IMAGES\n      android.permission.CONTROL_DISPLAY_BRIGHTNESS\n      android.permission.CHANGE_WIFI_STATE\n      android.permission.SYSTEM_APPLICATION_OVERLAY\n      android.permission.MODIFY_DAY_NIGHT_MODE\n      android.permission.SET_PREFERRED_APPLICATIONS\n      android.permission.ACCESS_NETWORK_STATE\n      android.permission.CAMERA\n      android.permission.INTERACT_ACROSS_USERS\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE\n      com.android.permissioncontroller.permission.MANAGE_ROLES_FROM_CONTROLLER\n      android.permission.INSTALL_SELF_UPDATES\n      android.permission.READ_MEDIA_VIDEO\n      transsion.permission.health\n      com.afmobi.boomplayer.permission.BPSDKPLAYKIT\n      android.permission.REBOOT\n      com.transsion.dataservice.permission.WRITE\n      android.permission.FOREGROUND_SERVICE_MICROPHONE\n      android.permission.PROCESS_OUTGOING_CALLS\n      android.permission.SCHEDULE_EXACT_ALARM\n      hyperion.permission.USE_SECURE_CRYPTO\n      com.transsion.aivoiceassistant.permission.BIND_DATA_SERVICE\n      com.transsion.tpush.permission.READ_PROVIDER\n      com.transsion.kolun.assistant.permission.WRITE_FUNCTION_CONFIGURATION\n      com.transsion.kolun.assistant.permission.READ_FUNCTION_CONFIGURATION\n      android.permission.VIBRATE\n      com.transsion.dataservice.permission.READ\n      com.transsion.tpush.permission.WRITE_PROVIDER\n      android.permission.READ_CLIPBOARD_IN_BACKGROUND\n      com.transsion.permission.OCR_SUGGESTION_CHANGE\n      hoffnung.permission.READ_DEVICE_INFO\n      android.permission.ACCESS_WIFI_STATE\n      android.permission.MODIFY_PHONE_STATE\n      android.permission.QUERY_ALL_PACKAGES\n      android.permission.RECORD_AUDIO\n      com.google.android.gms.permission.AD_ID\n      android.permission.NETWORK_AIRPLANE_MODE\n      android.permission.WAKE_LOCK\n      android.permission.READ_CONTACTS\n      android.permission.UPDATE_APP_OPS_STATS\n    install permissions:\n      transsion.permission.notebook: granted=true\n      android.permission.REAL_GET_TASKS: granted=true\n      android.permission.ACCESS_CACHE_FILESYSTEM: granted=true\n      android.permission.FOREGROUND_SERVICE_CAMERA: granted=true\n      android.permission.NFC_PREFERRED_PAYMENT_INFO: granted=true\n      spacesaver.permission.ACCESS_API: granted=true\n      android.permission.ACCESS_GPU_SERVICE: granted=true\n      android.permission.MANAGE_APPOPS: granted=true\n      android.permission.BIND_INCALL_SERVICE: granted=true\n      android.permission.WRITE_SETTINGS: granted=true\n      com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE: granted=true\n      android.permission.READ_WALLPAPER_INTERNAL: granted=true\n      android.permission.CONFIGURE_WIFI_DISPLAY: granted=true\n      android.permission.CONFIGURE_DISPLAY_COLOR_MODE: granted=true\n      android.permission.RECOVERY: granted=true\n      android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS: granted=true\n      hyperion.permission.USE_SECURE_STORAGE: granted=true\n      com.transsion.hilauncher.permission.READ_ICON: granted=true\n      android.permission.LIST_ENABLED_CREDENTIAL_PROVIDERS: granted=true\n      android.permission.LOCATION_BYPASS: granted=true\n      com.transsion.phonemanager.permission.C2D_MESSAGE: granted=true\n      android.permission.BIND_ATTENTION_SERVICE: granted=true\n      com.google.android.c2dm.permission.RECEIVE: granted=true\n      android.permission.STORAGE_INTERNAL: granted=true\n      android.permission.USE_CREDENTIALS: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES_FULL: granted=true\n      android.permission.READ_SYSTEM_UPDATE_INFO: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS: granted=true\n      android.permission.MANAGE_EXTERNAL_STORAGE: granted=true\n      android.permission.ACCESS_CHECKIN_PROPERTIES: granted=true\n      com.transsion.easypic.permission.BIND_EASY_PIC_SERVICE: granted=true\n      android.permission.ACCESS_NOTIFICATION_POLICY: granted=true\n      android.permission.REMAP_MODIFIER_KEYS: granted=true\n      android.permission.MODIFY_AUDIO_ROUTING: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES: granted=true\n      com.mediatek.permission.omacp.install: granted=true\n      android.permission.READ_SAFETY_CENTER_STATUS: granted=true\n      com.xui.xhide.permission.READ_DATA: granted=true\n      com.google.android.providers.gsf.permission.READ_GSERVICES: granted=true\n      android.permission.CONTROL_KEYGUARD_SECURE_NOTIFICATIONS: granted=true\n      android.permission.READ_WIFI_CREDENTIAL: granted=true\n      android.permission.QUERY_AUDIO_STATE: granted=true\n      com.transsion.aicore.permission.RUN_STATES: granted=true\n      android.permission.INSTALL_LOCATION_PROVIDER: granted=true\n      android.permission.USE_RESERVED_DISK: granted=true\n      com.transsion.tranfacmode.permission.SHUTDOWN: granted=true\n      android.permission.START_ACTIVITY_AS_CALLER: granted=true\n      android.permission.SYSTEM_ALERT_WINDOW: granted=true\n      com.transsion.cutepet.provider.permission: granted=true\n      android.permission.BROADCAST_PHONE_ACCOUNT_REGISTRATION: granted=true\n      android.permission.START_TASKS_FROM_RECENTS: granted=true\n      android.permission.NFC_TRANSACTION_EVENT: granted=true\n      com.transsion.aicore.permission.LLM_SUPPORT: granted=true\n      android.permission.CLEAR_APP_USER_DATA: granted=true\n      android.permission.BROADCAST_CALLLOG_INFO: granted=true\n      android.permission.MANAGE_SOUND_TRIGGER: granted=true\n      android.permission.CONTROL_UI_TRACING: granted=true\n      android.permission.SHUTDOWN: granted=true\n      android.permission.NFC: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS_PRIVILEGED: granted=true\n      android.permission.INTERNAL_SYSTEM_WINDOW: granted=true\n      android.permission.BIND_ROTATION_RESOLVER_SERVICE: granted=true\n      hitranslate.hls.service.permission: granted=true\n      android.permission.NETWORK_SETTINGS: granted=true\n      android.permission.CHANGE_OVERLAY_PACKAGES: granted=true\n      android.permission.START_ANY_ACTIVITY: granted=true\n      android.permission.CALL_PRIVILEGED: granted=true\n      hiservice.permission.connection: granted=true\n      android.permission.CHANGE_NETWORK_STATE: granted=true\n      android.permission.MASTER_CLEAR: granted=true\n      android.permission.FOREGROUND_SERVICE: granted=true\n      android.permission.WRITE_SYNC_SETTINGS: granted=true\n      android.permission.READ_INSTALLED_SESSION_PATHS: granted=true\n      android.permission.ALLOW_PLACE_IN_MULTI_PANE_SETTINGS: granted=true\n      android.permission.MANAGE_DYNAMIC_SYSTEM: granted=true\n      android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK: granted=true\n      android.permission.MANAGE_ACTIVITY_TASKS: granted=true\n      android.permission.RECEIVE_BOOT_COMPLETED: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROCESSING: granted=true\n      com.transsion.gamemode.permission.READ_APP_LIST: granted=true\n      com.google.android.googleapps.permission.GOOGLE_AUTH: granted=true\n      android.permission.FOREGROUND_SERVICE_SPECIAL_USE: granted=true\n      android.permission.MANAGE_ROLE_HOLDERS: granted=true\n      android.permission.PEERS_MAC_ADDRESS: granted=true\n      android.permission.DEVICE_POWER: granted=true\n      android.permission.ENFORCE_UPDATE_OWNERSHIP: granted=true\n      android.permission.HIGH_SAMPLING_RATE_SENSORS: granted=true\n      android.permission.FOREGROUND_SERVICE_LOCATION: granted=true\n      android.permission.READ_PRINT_SERVICES: granted=true\n      android.permission.EXPAND_STATUS_BAR: granted=true\n      com.transsion.applock.permission.READ_APP_LOCK: granted=true\n      android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS: granted=true\n      android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER: granted=true\n      android.permission.RESTART_WIFI_SUBSYSTEM: granted=true\n      android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS: granted=true\n      android.permission.READ_PROFILE: granted=true\n      com.sh.smart.caller.permission.READ_AUTOANSWER_SETTING: granted=true\n      android.permission.BLUETOOTH: granted=true\n      com.android.alarm.permission.SET_ALARM: granted=true\n      android.permission.CAPTURE_AUDIO_HOTWORD: granted=true\n      android.permission.WRITE_MEDIA_STORAGE: granted=true\n      android.permission.WRITE_BLOCKED_NUMBERS: granted=true\n      os_noticenter.permission.CONFIG_PROVIDER: granted=true\n      android.permission.WATCH_APPOPS: granted=true\n      android.permission.GET_TASKS: granted=true\n      android.permission.BIND_REMOTE_LOCKSCREEN_VALIDATION_SERVICE: granted=true\n      android.permission.INTERNET: granted=true\n      com.dts.permission.DTS_EFFECT: granted=true\n      android.permission.REORDER_TASKS: granted=true\n      android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION: granted=true\n      android.permission.BLUETOOTH_ADMIN: granted=true\n      android.permission.CONTROL_VPN: granted=true\n      android.permission.UPDATE_DEVICE_STATS: granted=true\n      android.permission.READ_PRECISE_PHONE_STATE: granted=true\n      android.permission.MANAGE_FINGERPRINT: granted=true\n      com.transsion.permission.Exception: granted=true\n      android.permission.READ_PROJECTION_STATE: granted=true\n      com.transsion.permission.unsee.peekproof: granted=true\n      com.xui.xhide.permission.READ_DATA_FOR_THIRD_PARTY: granted=true\n      android.permission.SUBSCRIBE_TO_KEYGUARD_LOCKED_STATE: granted=true\n      android.permission.SEND_SAFETY_CENTER_UPDATE: granted=true\n      android.permission.ACCESS_INSTANT_APPS: granted=true\n      android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS: granted=true\n      com.transsion.aicore.main.permission.MM_PROVIDER: granted=true\n      com.android.taglog.permission.TagLog: granted=true\n      android.permission.GET_PACKAGE_SIZE: granted=true\n      android.permission.ACCESS_CONTEXT_HUB: granted=true\n      android.permission.MANAGE_USB: granted=true\n      android.permission.INTERACT_ACROSS_USERS_FULL: granted=true\n      android.permission.STOP_APP_SWITCHES: granted=true\n      android.permission.RECEIVE_MEDIA_RESOURCE_USAGE: granted=true\n      android.permission.ACCESS_LAST_KNOWN_CELL_ID: granted=true\n      android.permission.BATTERY_STATS: granted=true\n      android.permission.CAPTURE_VOICE_COMMUNICATION_OUTPUT: granted=true\n      android.permission.PACKAGE_USAGE_STATS: granted=true\n      com.transsion.aicore.main.permission.MM_RECEIVER: granted=true\n      android.permission.MOUNT_UNMOUNT_FILESYSTEMS: granted=true\n      android.permission.TETHER_PRIVILEGED: granted=true\n      android.permission.WRITE_SECURE_SETTINGS: granted=true\n      android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED: granted=true\n      android.permission.BIND_SCREENING_SERVICE: granted=true\n      android.permission.CAPTURE_AUDIO_OUTPUT: granted=true\n      android.permission.MANAGE_DEBUGGING: granted=true\n      android.permission.MOVE_PACKAGE: granted=true\n      android.permission.SET_ACTIVITY_WATCHER: granted=true\n      hoffnung.permission.READ_CONFIG_PROVIDER: granted=true\n      android.permission.READ_BLOCKED_NUMBERS: granted=true\n      com.xui.xhide.permission.C2D_MESSAGE: granted=true\n      android.permission.SATELLITE_COMMUNICATION: granted=true\n      android.permission.STATUS_BAR_SERVICE: granted=true\n      android.permission.READ_SEARCH_INDEXABLES: granted=true\n      android.permission.USE_FULL_SCREEN_INTENT: granted=true\n      android.permission.RADIO_SCAN_WITHOUT_LOCATION: granted=true\n      android.permission.ACCESS_LOCATION_EXTRA_COMMANDS: granted=true\n      android.permission.READ_PRIVILEGED_PHONE_STATE: granted=true\n      com.transsion.kolun.assistant.permission.ABILITY: granted=true\n      android.permission.READ_SIGNAL_PREDICT: granted=true\n      android.permission.ACCESS_DOWNLOAD_MANAGER: granted=true\n      android.permission.BROADCAST_STICKY: granted=true\n      android.permission.INSTALL_PACKAGE_UPDATES: granted=true\n      android.permission.BLUETOOTH_PRIVILEGED: granted=true\n      android.permission.READ_AIWALLPAPER_USAGE: granted=true\n      android.permission.HARDWARE_TEST: granted=true\n      android.permission.USE_BIOMETRIC_INTERNAL: granted=true\n      com.transsion.magicfont.provider.permission.WRITE_PROVIDER: granted=true\n      android.permission.WRITE_OBB: granted=true\n      android.permission.INSTALL_DYNAMIC_SYSTEM: granted=true\n      android.permission.CONTROL_DISPLAY_BRIGHTNESS: granted=true\n      android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME: granted=true\n      android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE: granted=true\n      android.permission.ACCESS_KEYGUARD_SECURE_STORAGE: granted=true\n      android.permission.BIND_JOB_SERVICE: granted=true\n      android.permission.OBSERVE_ROLE_HOLDERS: granted=true\n      android.permission.CONFIRM_FULL_BACKUP: granted=true\n      android.permission.CAPTURE_SECURE_VIDEO_OUTPUT: granted=true\n      android.permission.WRITE_APN_SETTINGS: granted=true\n      android.permission.CHANGE_WIFI_STATE: granted=true\n      android.permission.SYSTEM_APPLICATION_OVERLAY: granted=true\n      android.permission.MODIFY_DAY_NIGHT_MODE: granted=true\n      android.permission.MANAGE_BIOMETRIC: granted=true\n      android.permission.FOREGROUND_SERVICE_DATA_SYNC: granted=true\n      com.transsion.hilauncher.permission.READ_SETTINGS: granted=true\n      android.permission.MANAGE_USERS: granted=true\n      android.permission.SET_PREFERRED_APPLICATIONS: granted=true\n      android.permission.FLASHLIGHT: granted=true\n      android.permission.ACCESS_NETWORK_STATE: granted=true\n      android.permission.DISABLE_KEYGUARD: granted=true\n      android.permission.BACKUP: granted=true\n      android.permission.CHANGE_CONFIGURATION: granted=true\n      android.permission.USER_ACTIVITY: granted=true\n      android.permission.LOCAL_MAC_ADDRESS: granted=true\n      android.permission.READ_LOGS: granted=true\n      android.permission.COPY_PROTECTED_DATA: granted=true\n      android.permission.GET_BACKGROUND_INSTALLED_PACKAGES: granted=true\n      android.permission.INTERACT_ACROSS_USERS: granted=true\n      android.permission.SET_WALLPAPER: granted=true\n      android.permission.SET_KEYBOARD_LAYOUT: granted=true\n      android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS: granted=true\n      android.permission.READ_DREAM_STATE: granted=true\n      android.permission.START_VIEW_APP_FEATURES: granted=true\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE: granted=true\n      com.android.permissioncontroller.permission.MANAGE_ROLES_FROM_CONTROLLER: granted=true\n      android.permission.MANAGE_ENHANCED_CONFIRMATION_STATES: granted=true\n      android.permission.HANDLE_QUERY_PACKAGE_RESTART: granted=true\n      android.permission.INSTALL_SELF_UPDATES: granted=true\n      android.permission.MANAGE_APP_OPS_RESTRICTIONS: granted=true\n      android.permission.KILL_BACKGROUND_PROCESSES: granted=true\n      android.permission.MANAGE_USER_OEM_UNLOCK_STATE: granted=true\n      android.permission.USE_FINGERPRINT: granted=true\n      android.permission.REQUEST_NETWORK_SCORES: granted=true\n      android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS: granted=true\n      android.permission.READ_BASIC_PHONE_STATE: granted=true\n      android.permission.WRITE_USER_DICTIONARY: granted=true\n      transsion.permission.health: granted=true\n      android.permission.READ_DREAM_SUPPRESSION: granted=true\n      android.permission.READ_SYNC_STATS: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION: granted=true\n      android.permission.ACCESS_SHORTCUTS: granted=true\n      android.permission.REBOOT: granted=true\n      com.transsion.dataservice.permission.WRITE: granted=true\n      android.permission.MOUNT_FORMAT_FILESYSTEMS: granted=true\n      android.permission.FOREGROUND_SERVICE_MICROPHONE: granted=true\n      android.permission.SCHEDULE_EXACT_ALARM: granted=true\n      android.permission.REQUEST_DELETE_PACKAGES: granted=true\n      android.permission.OEM_UNLOCK_STATE: granted=true\n      android.permission.MANAGE_DEVICE_ADMINS: granted=true\n      hyperion.permission.USE_SECURE_CRYPTO: granted=true\n      android.permission.CHANGE_APP_IDLE_STATE: granted=true\n      android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_DATA_SERVICE: granted=true\n      com.transsion.tpush.permission.READ_PROVIDER: granted=true\n      android.permission.TEST_BLACKLISTED_PASSWORD: granted=true\n      android.permission.ACCESS_PDB_STATE: granted=true\n      android.permission.MANAGE_NETWORK_POLICY: granted=true\n      android.permission.MANAGE_NOTIFICATION_LISTENERS: granted=true\n      android.permission.SET_POINTER_SPEED: granted=true\n      android.permission.MANAGE_NOTIFICATIONS: granted=true\n      transsion.permission.APP_HIDE_LISTENER: granted=true\n      android.permission.USE_COLORIZED_NOTIFICATIONS: granted=true\n      android.permission.MANAGE_GAME_MODE: granted=true\n      android.permission.SEND_SHOW_SUSPENDED_APP_DETAILS: granted=true\n      android.permission.CONNECTIVITY_INTERNAL: granted=true\n      android.permission.MANAGE_MEDIA_PROJECTION: granted=true\n      android.permission.READ_SYNC_SETTINGS: granted=true\n      android.permission.QUERY_USERS: granted=true\n      android.permission.START_ACTIVITIES_FROM_BACKGROUND: granted=true\n      com.transsion.kolun.assistant.permission.WRITE_FUNCTION_CONFIGURATION: granted=true\n      android.permission.BIND_CELL_BROADCAST_SERVICE: granted=true\n      transsion.permission.welife: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK: granted=true\n      com.transsion.magicfont.provider.permission.READ_PROVIDER: granted=true\n      android.permission.LOADER_USAGE_STATS: granted=true\n      android.permission.OVERRIDE_WIFI_CONFIG: granted=true\n      android.permission.FORCE_STOP_PACKAGES: granted=true\n      android.permission.SUGGEST_MANUAL_TIME_AND_ZONE: granted=true\n      android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS: granted=true\n      android.permission.CAPTURE_VIDEO_OUTPUT: granted=true\n      android.permission.ACCESS_NOTIFICATIONS: granted=true\n      android.permission.HANDLE_CALL_INTENT: granted=true\n      android.permission.MEDIA_RESOURCE_OVERRIDE_PID: granted=true\n      com.transsion.kolun.assistant.permission.READ_FUNCTION_CONFIGURATION: granted=true\n      android.permission.CUSTOMIZE_SYSTEM_UI: granted=true\n      android.permission.VIBRATE: granted=true\n      android.permission.MANAGE_APP_HIBERNATION: granted=true\n      com.transsion.dataservice.permission.READ: granted=true\n      android.permission.MANAGE_ACTIVITY_STACKS: granted=true\n      android.permission.HANDLE_CAR_MODE_CHANGES: granted=true\n      android.permission.INTERACT_ACROSS_PROFILES: granted=true\n      android.permission.READ_USER_DICTIONARY: granted=true\n      android.permission.WHITELIST_RESTRICTED_PERMISSIONS: granted=true\n      com.transsion.tpush.permission.WRITE_PROVIDER: granted=true\n      android.permission.CREATE_USERS: granted=true\n      android.permission.READ_CLIPBOARD_IN_BACKGROUND: granted=true\n      com.transsion.permission.OCR_SUGGESTION_CHANGE: granted=true\n      android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS: granted=true\n      android.permission.CRYPT_KEEPER: granted=true\n      hoffnung.permission.READ_DEVICE_INFO: granted=true\n      android.permission.READ_SYSTEM_GRAMMATICAL_GENDER: granted=true\n      android.permission.CALL_AUDIO_INTERCEPTION: granted=true\n      android.permission.DISPATCH_PROVISIONING_MESSAGE: granted=true\n      transsion.aegis.permission.READ_CONTENT: granted=true\n      android.permission.ACCESS_WIFI_STATE: granted=true\n      android.permission.READ_APP_SPECIFIC_LOCALES: granted=true\n      android.permission.TURN_SCREEN_ON: granted=true\n      android.permission.USE_BIOMETRIC: granted=true\n      gamespace.transsion.permission.gameaccelerator: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE: granted=true\n      android.permission.MANAGE_APP_OPS_MODES: granted=true\n      android.permission.REQUEST_INSTALL_PACKAGES: granted=true\n      android.permission.CONTROL_INCALL_EXPERIENCE: granted=true\n      android.permission.MODIFY_PHONE_STATE: granted=true\n      com.transsion.smartpanel.permission.hideHandle: granted=true\n      android.permission.STATUS_BAR: granted=true\n      com.xui.xhide.permission.WRITE_DATA: granted=true\n      android.permission.QUERY_ALL_PACKAGES: granted=true\n      transsion.permission.READ_SPLITSCREEN_SETTING: granted=true\n      com.google.android.gms.permission.AD_ID: granted=true\n      android.permission.READ_DEVICE_CONFIG: granted=true\n      android.permission.LOCATION_HARDWARE: granted=true\n      android.permission.SUPPRESS_CLIPBOARD_ACCESS_NOTIFICATION: granted=true\n      android.permission.UNLIMITED_TOASTS: granted=true\n      android.permission.NETWORK_AIRPLANE_MODE: granted=true\n      android.permission.QUERY_ADMIN_POLICY: granted=true\n      android.permission.WAKE_LOCK: granted=true\n      android.permission.INJECT_EVENTS: granted=true\n      android.permission.BIND_NETWORK_RECOMMENDATION_SERVICE: granted=true\n      com.transsion.permission.UX_DETECTOR_SERVICE: granted=true\n      android.permission.UPDATE_APP_OPS_STATS: granted=true\n      android.permission.READ_OEM_UNLOCK_STATE: granted=true\n      android.permission.MEDIA_CONTENT_CONTROL: granted=true\n      android.permission.DELETE_PACKAGES: granted=true\n        com.google.android.permissioncontroller\n      com.google.android.permissioncontroller:\n    install permissions:\n      transsion.permission.notebook: granted=true\n      android.permission.REAL_GET_TASKS: granted=true\n      android.permission.ACCESS_CACHE_FILESYSTEM: granted=true\n      android.permission.FOREGROUND_SERVICE_CAMERA: granted=true\n      android.permission.NFC_PREFERRED_PAYMENT_INFO: granted=true\n      spacesaver.permission.ACCESS_API: granted=true\n      android.permission.ACCESS_GPU_SERVICE: granted=true\n      android.permission.MANAGE_APPOPS: granted=true\n      android.permission.BIND_INCALL_SERVICE: granted=true\n      android.permission.WRITE_SETTINGS: granted=true\n      com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE: granted=true\n      android.permission.READ_WALLPAPER_INTERNAL: granted=true\n      android.permission.CONFIGURE_WIFI_DISPLAY: granted=true\n      android.permission.CONFIGURE_DISPLAY_COLOR_MODE: granted=true\n      android.permission.RECOVERY: granted=true\n      android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS: granted=true\n      hyperion.permission.USE_SECURE_STORAGE: granted=true\n      com.transsion.hilauncher.permission.READ_ICON: granted=true\n      android.permission.LIST_ENABLED_CREDENTIAL_PROVIDERS: granted=true\n      android.permission.LOCATION_BYPASS: granted=true\n      com.transsion.phonemanager.permission.C2D_MESSAGE: granted=true\n      android.permission.BIND_ATTENTION_SERVICE: granted=true\n      com.google.android.c2dm.permission.RECEIVE: granted=true\n      android.permission.STORAGE_INTERNAL: granted=true\n      android.permission.USE_CREDENTIALS: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES_FULL: granted=true\n      android.permission.READ_SYSTEM_UPDATE_INFO: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS: granted=true\n      android.permission.MANAGE_EXTERNAL_STORAGE: granted=true\n      android.permission.ACCESS_CHECKIN_PROPERTIES: granted=true\n      com.transsion.easypic.permission.BIND_EASY_PIC_SERVICE: granted=true\n      android.permission.ACCESS_NOTIFICATION_POLICY: granted=true\n      android.permission.REMAP_MODIFIER_KEYS: granted=true\n      android.permission.MODIFY_AUDIO_ROUTING: granted=true\n      android.permission.ACCESS_HIDDEN_PROFILES: granted=true\n      com.mediatek.permission.omacp.install: granted=true\n      android.permission.READ_SAFETY_CENTER_STATUS: granted=true\n      com.xui.xhide.permission.READ_DATA: granted=true\n      com.google.android.providers.gsf.permission.READ_GSERVICES: granted=true\n      android.permission.CONTROL_KEYGUARD_SECURE_NOTIFICATIONS: granted=true\n      android.permission.READ_WIFI_CREDENTIAL: granted=true\n      android.permission.QUERY_AUDIO_STATE: granted=true\n      com.transsion.aicore.permission.RUN_STATES: granted=true\n      android.permission.INSTALL_LOCATION_PROVIDER: granted=true\n      android.permission.USE_RESERVED_DISK: granted=true\n      com.transsion.tranfacmode.permission.SHUTDOWN: granted=true\n      android.permission.START_ACTIVITY_AS_CALLER: granted=true\n      android.permission.SYSTEM_ALERT_WINDOW: granted=true\n      com.transsion.cutepet.provider.permission: granted=true\n      android.permission.BROADCAST_PHONE_ACCOUNT_REGISTRATION: granted=true\n      android.permission.START_TASKS_FROM_RECENTS: granted=true\n      android.permission.NFC_TRANSACTION_EVENT: granted=true\n      com.transsion.aicore.permission.LLM_SUPPORT: granted=true\n      android.permission.CLEAR_APP_USER_DATA: granted=true\n      android.permission.BROADCAST_CALLLOG_INFO: granted=true\n      android.permission.MANAGE_SOUND_TRIGGER: granted=true\n      android.permission.CONTROL_UI_TRACING: granted=true\n      android.permission.SHUTDOWN: granted=true\n      android.permission.NFC: granted=true\n      android.permission.MODIFY_AUDIO_SETTINGS_PRIVILEGED: granted=true\n      android.permission.INTERNAL_SYSTEM_WINDOW: granted=true\n      android.permission.BIND_ROTATION_RESOLVER_SERVICE: granted=true\n      hitranslate.hls.service.permission: granted=true\n      android.permission.NETWORK_SETTINGS: granted=true\n      android.permission.CHANGE_OVERLAY_PACKAGES: granted=true\n      android.permission.START_ANY_ACTIVITY: granted=true\n      android.permission.CALL_PRIVILEGED: granted=true\n      hiservice.permission.connection: granted=true\n      android.permission.CHANGE_NETWORK_STATE: granted=true\n      android.permission.MASTER_CLEAR: granted=true\n      android.permission.FOREGROUND_SERVICE: granted=true\n      android.permission.WRITE_SYNC_SETTINGS: granted=true\n      android.permission.READ_INSTALLED_SESSION_PATHS: granted=true\n      android.permission.ALLOW_PLACE_IN_MULTI_PANE_SETTINGS: granted=true\n      android.permission.MANAGE_DYNAMIC_SYSTEM: granted=true\n      android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK: granted=true\n      android.permission.MANAGE_ACTIVITY_TASKS: granted=true\n      android.permission.RECEIVE_BOOT_COMPLETED: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROCESSING: granted=true\n      com.transsion.gamemode.permission.READ_APP_LIST: granted=true\n      com.google.android.googleapps.permission.GOOGLE_AUTH: granted=true\n      android.permission.FOREGROUND_SERVICE_SPECIAL_USE: granted=true\n      android.permission.MANAGE_ROLE_HOLDERS: granted=true\n      android.permission.PEERS_MAC_ADDRESS: granted=true\n      android.permission.DEVICE_POWER: granted=true\n      android.permission.ENFORCE_UPDATE_OWNERSHIP: granted=true\n      android.permission.HIGH_SAMPLING_RATE_SENSORS: granted=true\n      android.permission.FOREGROUND_SERVICE_LOCATION: granted=true\n      android.permission.READ_PRINT_SERVICES: granted=true\n      android.permission.EXPAND_STATUS_BAR: granted=true\n      com.transsion.applock.permission.READ_APP_LOCK: granted=true\n      android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS: granted=true\n      android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER: granted=true\n      android.permission.RESTART_WIFI_SUBSYSTEM: granted=true\n      android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS: granted=true\n      android.permission.READ_PROFILE: granted=true\n      com.sh.smart.caller.permission.READ_AUTOANSWER_SETTING: granted=true\n      android.permission.BLUETOOTH: granted=true\n      com.android.alarm.permission.SET_ALARM: granted=true\n      android.permission.CAPTURE_AUDIO_HOTWORD: granted=true\n      android.permission.WRITE_MEDIA_STORAGE: granted=true\n      android.permission.WRITE_BLOCKED_NUMBERS: granted=true\n      os_noticenter.permission.CONFIG_PROVIDER: granted=true\n      android.permission.WATCH_APPOPS: granted=true\n      android.permission.GET_TASKS: granted=true\n      android.permission.BIND_REMOTE_LOCKSCREEN_VALIDATION_SERVICE: granted=true\n      android.permission.INTERNET: granted=true\n      com.dts.permission.DTS_EFFECT: granted=true\n      android.permission.REORDER_TASKS: granted=true\n      android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION: granted=true\n      android.permission.BLUETOOTH_ADMIN: granted=true\n      android.permission.CONTROL_VPN: granted=true\n      android.permission.UPDATE_DEVICE_STATS: granted=true\n      android.permission.READ_PRECISE_PHONE_STATE: granted=true\n      android.permission.MANAGE_FINGERPRINT: granted=true\n      com.transsion.permission.Exception: granted=true\n      android.permission.READ_PROJECTION_STATE: granted=true\n      com.transsion.permission.unsee.peekproof: granted=true\n      com.xui.xhide.permission.READ_DATA_FOR_THIRD_PARTY: granted=true\n      android.permission.SUBSCRIBE_TO_KEYGUARD_LOCKED_STATE: granted=true\n      android.permission.SEND_SAFETY_CENTER_UPDATE: granted=true\n      android.permission.ACCESS_INSTANT_APPS: granted=true\n      android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS: granted=true\n      com.transsion.aicore.main.permission.MM_PROVIDER: granted=true\n      com.android.taglog.permission.TagLog: granted=true\n      android.permission.GET_PACKAGE_SIZE: granted=true\n      android.permission.ACCESS_CONTEXT_HUB: granted=true\n      android.permission.MANAGE_USB: granted=true\n      android.permission.INTERACT_ACROSS_USERS_FULL: granted=true\n      android.permission.STOP_APP_SWITCHES: granted=true\n      android.permission.RECEIVE_MEDIA_RESOURCE_USAGE: granted=true\n      android.permission.ACCESS_LAST_KNOWN_CELL_ID: granted=true\n      android.permission.BATTERY_STATS: granted=true\n      android.permission.CAPTURE_VOICE_COMMUNICATION_OUTPUT: granted=true\n      android.permission.PACKAGE_USAGE_STATS: granted=true\n      com.transsion.aicore.main.permission.MM_RECEIVER: granted=true\n      android.permission.MOUNT_UNMOUNT_FILESYSTEMS: granted=true\n      android.permission.TETHER_PRIVILEGED: granted=true\n      android.permission.WRITE_SECURE_SETTINGS: granted=true\n      android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED: granted=true\n      android.permission.BIND_SCREENING_SERVICE: granted=true\n      android.permission.CAPTURE_AUDIO_OUTPUT: granted=true\n      android.permission.MANAGE_DEBUGGING: granted=true\n      android.permission.MOVE_PACKAGE: granted=true\n      android.permission.SET_ACTIVITY_WATCHER: granted=true\n      hoffnung.permission.READ_CONFIG_PROVIDER: granted=true\n      android.permission.READ_BLOCKED_NUMBERS: granted=true\n      com.xui.xhide.permission.C2D_MESSAGE: granted=true\n      android.permission.SATELLITE_COMMUNICATION: granted=true\n      android.permission.STATUS_BAR_SERVICE: granted=true\n      android.permission.READ_SEARCH_INDEXABLES: granted=true\n      android.permission.USE_FULL_SCREEN_INTENT: granted=true\n      android.permission.RADIO_SCAN_WITHOUT_LOCATION: granted=true\n      android.permission.ACCESS_LOCATION_EXTRA_COMMANDS: granted=true\n      android.permission.READ_PRIVILEGED_PHONE_STATE: granted=true\n      com.transsion.kolun.assistant.permission.ABILITY: granted=true\n      android.permission.READ_SIGNAL_PREDICT: granted=true\n      android.permission.ACCESS_DOWNLOAD_MANAGER: granted=true\n      android.permission.BROADCAST_STICKY: granted=true\n      android.permission.INSTALL_PACKAGE_UPDATES: granted=true\n      android.permission.BLUETOOTH_PRIVILEGED: granted=true\n      android.permission.READ_AIWALLPAPER_USAGE: granted=true\n      android.permission.HARDWARE_TEST: granted=true\n      android.permission.USE_BIOMETRIC_INTERNAL: granted=true\n      com.transsion.magicfont.provider.permission.WRITE_PROVIDER: granted=true\n      android.permission.WRITE_OBB: granted=true\n      android.permission.INSTALL_DYNAMIC_SYSTEM: granted=true\n      android.permission.CONTROL_DISPLAY_BRIGHTNESS: granted=true\n      android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME: granted=true\n      android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE: granted=true\n      android.permission.ACCESS_KEYGUARD_SECURE_STORAGE: granted=true\n      android.permission.BIND_JOB_SERVICE: granted=true\n      android.permission.OBSERVE_ROLE_HOLDERS: granted=true\n      android.permission.CONFIRM_FULL_BACKUP: granted=true\n      android.permission.CAPTURE_SECURE_VIDEO_OUTPUT: granted=true\n      android.permission.WRITE_APN_SETTINGS: granted=true\n      android.permission.CHANGE_WIFI_STATE: granted=true\n      android.permission.SYSTEM_APPLICATION_OVERLAY: granted=true\n      android.permission.MODIFY_DAY_NIGHT_MODE: granted=true\n      android.permission.MANAGE_BIOMETRIC: granted=true\n      android.permission.FOREGROUND_SERVICE_DATA_SYNC: granted=true\n      com.transsion.hilauncher.permission.READ_SETTINGS: granted=true\n      android.permission.MANAGE_USERS: granted=true\n      android.permission.SET_PREFERRED_APPLICATIONS: granted=true\n      android.permission.FLASHLIGHT: granted=true\n      android.permission.ACCESS_NETWORK_STATE: granted=true\n      android.permission.DISABLE_KEYGUARD: granted=true\n      android.permission.BACKUP: granted=true\n      android.permission.CHANGE_CONFIGURATION: granted=true\n      android.permission.USER_ACTIVITY: granted=true\n      android.permission.LOCAL_MAC_ADDRESS: granted=true\n      android.permission.READ_LOGS: granted=true\n      android.permission.COPY_PROTECTED_DATA: granted=true\n      android.permission.GET_BACKGROUND_INSTALLED_PACKAGES: granted=true\n      android.permission.INTERACT_ACROSS_USERS: granted=true\n      android.permission.SET_WALLPAPER: granted=true\n      android.permission.SET_KEYBOARD_LAYOUT: granted=true\n      android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS: granted=true\n      android.permission.READ_DREAM_STATE: granted=true\n      android.permission.START_VIEW_APP_FEATURES: granted=true\n      com.transsion.aivoiceassistant.aisystem.permission.BIND_SYSTEM_SERVER_SERVICE: granted=true\n      com.android.permissioncontroller.permission.MANAGE_ROLES_FROM_CONTROLLER: granted=true\n      android.permission.MANAGE_ENHANCED_CONFIRMATION_STATES: granted=true\n      android.permission.HANDLE_QUERY_PACKAGE_RESTART: granted=true\n      android.permission.INSTALL_SELF_UPDATES: granted=true\n      android.permission.MANAGE_APP_OPS_RESTRICTIONS: granted=true\n      android.permission.KILL_BACKGROUND_PROCESSES: granted=true\n      android.permission.MANAGE_USER_OEM_UNLOCK_STATE: granted=true\n      android.permission.USE_FINGERPRINT: granted=true\n      android.permission.REQUEST_NETWORK_SCORES: granted=true\n      android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS: granted=true\n      android.permission.READ_BASIC_PHONE_STATE: granted=true\n      android.permission.WRITE_USER_DICTIONARY: granted=true\n      transsion.permission.health: granted=true\n      android.permission.READ_DREAM_SUPPRESSION: granted=true\n      android.permission.READ_SYNC_STATS: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION: granted=true\n      android.permission.ACCESS_SHORTCUTS: granted=true\n      android.permission.REBOOT: granted=true\n      com.transsion.dataservice.permission.WRITE: granted=true\n      android.permission.MOUNT_FORMAT_FILESYSTEMS: granted=true\n      android.permission.FOREGROUND_SERVICE_MICROPHONE: granted=true\n      android.permission.SCHEDULE_EXACT_ALARM: granted=true\n      android.permission.REQUEST_DELETE_PACKAGES: granted=true\n      android.permission.OEM_UNLOCK_STATE: granted=true\n      android.permission.MANAGE_DEVICE_ADMINS: granted=true\n      hyperion.permission.USE_SECURE_CRYPTO: granted=true\n      android.permission.CHANGE_APP_IDLE_STATE: granted=true\n      android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_DATA_SERVICE: granted=true\n      com.transsion.tpush.permission.READ_PROVIDER: granted=true\n      android.permission.TEST_BLACKLISTED_PASSWORD: granted=true\n      android.permission.ACCESS_PDB_STATE: granted=true\n      android.permission.MANAGE_NETWORK_POLICY: granted=true\n      android.permission.MANAGE_NOTIFICATION_LISTENERS: granted=true\n      android.permission.SET_POINTER_SPEED: granted=true\n      android.permission.MANAGE_NOTIFICATIONS: granted=true\n      transsion.permission.APP_HIDE_LISTENER: granted=true\n      android.permission.USE_COLORIZED_NOTIFICATIONS: granted=true\n      android.permission.MANAGE_GAME_MODE: granted=true\n      android.permission.SEND_SHOW_SUSPENDED_APP_DETAILS: granted=true\n      android.permission.CONNECTIVITY_INTERNAL: granted=true\n      android.permission.MANAGE_MEDIA_PROJECTION: granted=true\n      android.permission.READ_SYNC_SETTINGS: granted=true\n      android.permission.QUERY_USERS: granted=true\n      android.permission.START_ACTIVITIES_FROM_BACKGROUND: granted=true\n      com.transsion.kolun.assistant.permission.WRITE_FUNCTION_CONFIGURATION: granted=true\n      android.permission.BIND_CELL_BROADCAST_SERVICE: granted=true\n      transsion.permission.welife: granted=true\n      android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK: granted=true\n      com.transsion.magicfont.provider.permission.READ_PROVIDER: granted=true\n      android.permission.LOADER_USAGE_STATS: granted=true\n      android.permission.OVERRIDE_WIFI_CONFIG: granted=true\n      android.permission.FORCE_STOP_PACKAGES: granted=true\n      android.permission.SUGGEST_MANUAL_TIME_AND_ZONE: granted=true\n      android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS: granted=true\n      android.permission.CAPTURE_VIDEO_OUTPUT: granted=true\n      android.permission.ACCESS_NOTIFICATIONS: granted=true\n      android.permission.HANDLE_CALL_INTENT: granted=true\n      android.permission.MEDIA_RESOURCE_OVERRIDE_PID: granted=true\n      com.transsion.kolun.assistant.permission.READ_FUNCTION_CONFIGURATION: granted=true\n      android.permission.CUSTOMIZE_SYSTEM_UI: granted=true\n      android.permission.VIBRATE: granted=true\n      android.permission.MANAGE_APP_HIBERNATION: granted=true\n      com.transsion.dataservice.permission.READ: granted=true\n      android.permission.MANAGE_ACTIVITY_STACKS: granted=true\n      android.permission.HANDLE_CAR_MODE_CHANGES: granted=true\n      android.permission.INTERACT_ACROSS_PROFILES: granted=true\n      android.permission.READ_USER_DICTIONARY: granted=true\n      android.permission.WHITELIST_RESTRICTED_PERMISSIONS: granted=true\n      com.transsion.tpush.permission.WRITE_PROVIDER: granted=true\n      android.permission.CREATE_USERS: granted=true\n      android.permission.READ_CLIPBOARD_IN_BACKGROUND: granted=true\n      com.transsion.permission.OCR_SUGGESTION_CHANGE: granted=true\n      android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS: granted=true\n      android.permission.CRYPT_KEEPER: granted=true\n      hoffnung.permission.READ_DEVICE_INFO: granted=true\n      android.permission.READ_SYSTEM_GRAMMATICAL_GENDER: granted=true\n      android.permission.CALL_AUDIO_INTERCEPTION: granted=true\n      android.permission.DISPATCH_PROVISIONING_MESSAGE: granted=true\n      transsion.aegis.permission.READ_CONTENT: granted=true\n      android.permission.ACCESS_WIFI_STATE: granted=true\n      android.permission.READ_APP_SPECIFIC_LOCALES: granted=true\n      android.permission.TURN_SCREEN_ON: granted=true\n      android.permission.USE_BIOMETRIC: granted=true\n      gamespace.transsion.permission.gameaccelerator: granted=true\n      com.transsion.aivoiceassistant.permission.BIND_FLOATWINDOW_SERVICE: granted=true\n      android.permission.MANAGE_APP_OPS_MODES: granted=true\n      android.permission.REQUEST_INSTALL_PACKAGES: granted=true\n      android.permission.CONTROL_INCALL_EXPERIENCE: granted=true\n      android.permission.MODIFY_PHONE_STATE: granted=true\n      com.transsion.smartpanel.permission.hideHandle: granted=true\n      android.permission.STATUS_BAR: granted=true\n      com.xui.xhide.permission.WRITE_DATA: granted=true\n      android.permission.QUERY_ALL_PACKAGES: granted=true\n      transsion.permission.READ_SPLITSCREEN_SETTING: granted=true\n      com.google.android.gms.permission.AD_ID: granted=true\n      android.permission.READ_DEVICE_CONFIG: granted=true\n      android.permission.LOCATION_HARDWARE: granted=true\n      android.permission.SUPPRESS_CLIPBOARD_ACCESS_NOTIFICATION: granted=true\n      android.permission.UNLIMITED_TOASTS: granted=true\n      android.permission.NETWORK_AIRPLANE_MODE: granted=true\n      android.permission.QUERY_ADMIN_POLICY: granted=true\n      android.permission.WAKE_LOCK: granted=true\n      android.permission.INJECT_EVENTS: granted=true\n      android.permission.BIND_NETWORK_RECOMMENDATION_SERVICE: granted=true\n      com.transsion.permission.UX_DETECTOR_SERVICE: granted=true\n      android.permission.UPDATE_APP_OPS_STATS: granted=true\n      android.permission.READ_OEM_UNLOCK_STATE: granted=true\n      android.permission.MEDIA_CONTENT_CONTROL: granted=true\n      android.permission.DELETE_PACKAGES: granted=true\n      runtime permissions:\n        android.permission.READ_SMS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_CALENDAR: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.POST_NOTIFICATIONS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_CALL_LOG: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_INSTALLER_EXEMPT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.ACCESS_FINE_LOCATION: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.ANSWER_PHONE_CALLS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.RECEIVE_WAP_PUSH: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_PHONE_NUMBERS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_MEDIA_VISUAL_USER_SELECTED: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.NEARBY_WIFI_DEVICES: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.BLUETOOTH_CONNECT: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_EXTERNAL_STORAGE: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_INSTALLER_EXEMPT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.ACCESS_COARSE_LOCATION: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|REVOKE_WHEN_REQUESTED|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_PHONE_STATE: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.SEND_SMS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.CALL_PHONE: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_MEDIA_IMAGES: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.WRITE_CONTACTS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.CAMERA: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.WRITE_CALENDAR: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.WRITE_CALL_LOG: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_MEDIA_AUDIO: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_MEDIA_VIDEO: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.PROCESS_OUTGOING_CALLS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|USER_SENSITIVE_WHEN_GRANTED|USER_SENSITIVE_WHEN_DENIED|RESTRICTION_INSTALLER_EXEMPT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.BLUETOOTH_ADVERTISE: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.GET_ACCOUNTS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.WRITE_EXTERNAL_STORAGE: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.RECORD_AUDIO: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.READ_CONTACTS: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.ACCESS_BACKGROUND_LOCATION: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n        android.permission.BLUETOOTH_SCAN: granted=true, flags=[ SYSTEM_FIXED|GRANTED_BY_DEFAULT|RESTRICTION_SYSTEM_EXEMPT|RESTRICTION_UPGRADE_EXEMPT]\n', exit_code=0)
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:09:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:09:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:09:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:09:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:09:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:09:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:09:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:10:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:10:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:10:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:10:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:10:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:10:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:10:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:13:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:13:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:13:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:13:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:13:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:13:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:13:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:14:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:14:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:14:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:14:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:14:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:14:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:14:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:15:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:15:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:15:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:15:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:15:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:15:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:15:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:15:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:15:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:15:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [聊天消息列表]: False
2025-07-11 14:15:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-07-11 14:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 14:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [发送按钮]: False
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.TextView: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.EditText: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.Button: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.ImageButton: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.ImageView: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.LinearLayout: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.RelativeLayout: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.FrameLayout: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.widget.ScrollView: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ androidx.recyclerview.widget.RecyclerView: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.view.View: 0 个
2025-07-11 14:17:03 | DEBUG | __main__:analyze_page_structure:140 |   ❌ android.view.ViewGroup: 0 个
2025-07-11 14:17:30 | INFO | __main__:save_analysis_results:270 | 设备信息已保存到: debug/ella_device_info.json
2025-07-11 14:17:30 | INFO | __main__:save_analysis_results:276 | 分析报告已保存到: debug/ella_analysis_report.txt
2025-07-11 14:21:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:21:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:21:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:21:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:21:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:21:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:21:56 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: 你好
2025-07-11 14:22:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:22:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:06 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: 测试输入
2025-07-11 14:22:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:22:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:16 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:17 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-07-11 14:22:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:22:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:27 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: what time is it
2025-07-11 14:22:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:22:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:22:40 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: 测试完整流程
2025-07-11 14:23:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:23:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-11 14:23:37 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-07-11 14:23:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-11 14:23:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-07-11 14:30:26 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:27 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [发送按钮]: True
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:30:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:30 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:30 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:30:30 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:33 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:33 | DEBUG | core.base_element:get_text:288 | 获取元素文本 [输入框]: 测试错误处理
2025-07-11 14:30:38 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:39 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:40 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:41 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:42 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:47 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:30:48 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [发送按钮]: True
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:30:49 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:50 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:51 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:30:52 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:55 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:30:55 | DEBUG | core.base_element:get_text:288 | 获取元素文本 [输入框]: 测试错误处理
2025-07-11 14:31:03 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:31:03 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [发送按钮]: True
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:31:05 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:06 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:07 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:31:07 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:09 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:09 | DEBUG | core.base_element:get_text:288 | 获取元素文本 [输入框]: 测试错误处理
2025-07-11 14:31:17 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:31:17 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:18 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:18 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:31:19 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:31:19 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [发送按钮]: True
2025-07-11 14:31:19 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 14:31:19 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:20 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:20 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:31:21 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:23 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:31:23 | DEBUG | core.base_element:get_text:288 | 获取元素文本 [输入框]: 测试错误处理
2025-07-11 14:33:37 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:426 | 检查TextView元素失败: Invalid version: ''
2025-07-11 14:33:38 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:435 | 检查EditText元素失败: Invalid version: ''
2025-07-11 14:39:14 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:39:14 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:16 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:16 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:16 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:16 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:17 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:17 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:18 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:19 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:20 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:20 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:22 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:22 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:23 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:23 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:25 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:25 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:53 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:53 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:57 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:57 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:39:59 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:39:59 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:40:06 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:40:07 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [发送按钮]: True
2025-07-11 14:40:07 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 14:40:07 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-11 14:41:14 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-11 14:41:15 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:18 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:18 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:41:18 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:20 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:20 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-11 14:41:20 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:22 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:23 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-11 14:41:24 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:28 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [输入框]: True
2025-07-11 14:41:29 | DEBUG | core.base_element:get_text:288 | 获取元素文本 [输入框]: open bluetooth
2025-07-11 14:41:31 | DEBUG | core.base_element:is_exists:113 | 元素存在性检查 [TTS播放按钮]: True
2025-07-11 14:44:53 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:426 | 检查TextView元素失败: Invalid version: ''
2025-07-11 14:44:54 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:435 | 检查EditText元素失败: Invalid version: ''
2025-07-11 14:45:14 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1392 | 通用UI元素检测失败: Invalid version: ''
2025-07-11 14:45:15 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1404 | 页面文本检测失败: Invalid version: ''
2025-07-11 14:45:24 | DEBUG | pages.apps.ella.main_page:_check_generic_input_elements:1715 | 检查通用输入框元素失败: Invalid version: ''
2025-07-11 14:45:25 | DEBUG | pages.apps.ella.main_page:_activate_input_by_coordinates:1746 | 通过坐标激活输入失败: Invalid version: ''
2025-07-11 14:45:26 | DEBUG | pages.apps.ella.main_page:_activate_input_by_scrolling:1788 | 通过滑动激活输入失败: Invalid version: ''
2025-07-11 14:45:27 | DEBUG | pages.apps.ella.main_page:_check_any_interactive_elements:1823 | 检查可交互元素失败: Invalid version: ''
2025-07-11 15:48:15 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:426 | 检查TextView元素失败: Invalid version: ''
2025-07-11 15:48:16 | DEBUG | pages.apps.ella.main_page:_safe_check_generic_elements:435 | 检查EditText元素失败: Invalid version: ''
2025-07-11 15:48:36 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1392 | 通用UI元素检测失败: Invalid version: ''
2025-07-11 15:48:37 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1404 | 页面文本检测失败: Invalid version: ''
2025-07-11 15:48:46 | DEBUG | pages.apps.ella.main_page:_check_generic_input_elements:1715 | 检查通用输入框元素失败: Invalid version: ''
2025-07-11 15:48:47 | DEBUG | pages.apps.ella.main_page:_activate_input_by_coordinates:1746 | 通过坐标激活输入失败: Invalid version: ''
2025-07-11 15:48:48 | DEBUG | pages.apps.ella.main_page:_activate_input_by_scrolling:1788 | 通过滑动激活输入失败: Invalid version: ''
2025-07-11 15:48:49 | DEBUG | pages.apps.ella.main_page:_check_any_interactive_elements:1823 | 检查可交互元素失败: Invalid version: ''
2025-07-11 16:51:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 16:51:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 16:51:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-11 16:51:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-11 16:51:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: True
2025-07-11 16:51:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-11 16:51:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 16:51:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 16:51:26 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1401 | 检测到对话页面文本: Ella
2025-07-11 16:51:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-11 16:51:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-11 16:51:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-11 16:51:48 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1401 | 检测到对话页面文本: Ella
2025-07-11 16:51:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:51:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:51:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:52:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:52:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:52:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:52:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:52:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:52:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:52:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-11 16:52:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-11 16:52:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-11 16:52:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-11 16:52:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-11 16:52:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
