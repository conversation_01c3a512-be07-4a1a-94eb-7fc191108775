2025-07-22 10:41:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:00 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-22 10:42:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 10:42:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 10:42:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_check_area:129 | check_area节点不存在
2025-07-22 10:42:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:224 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-22 10:42:17 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:252 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,413][984,526]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Dow Up Ahead of Earnings" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,443][936,496]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,669]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Switch voices" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,586][936,639]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,699][984,812]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="Man Utd's $350M Squad Dilemma" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,729][936,782]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[636,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open wifi" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[684,938][876,991]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,913][1002,1015]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][1032,1323]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][632,1323]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1069][632,1323]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Wi-Fi is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1099][584,1152]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[512,1206][584,1278]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Wi-Fi" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1425][834,1486]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1425][984,1491]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1563][1032,1683]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1596][150,1650]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1600][286,1646]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][339,1830]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1743][156,1809]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1758][195,1794]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1743][297,1809]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="5" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="6" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Lower the brightness" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][588,1964]" drawing-order="1" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="10:42" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="10:42" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="智慧5G通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[720,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[720,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[728,39][929,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[728,39][914,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[728,41][760,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[775,41][798,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[813,41][843,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,41][914,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,41][914,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,49][914,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,49][914,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,49][914,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[858,49][869,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[872,49][914,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[870,49][914,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[749,39][914,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="正在充电，已完成百分之 96。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[929,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[929,55][996,89]" drawing-order="1" hint="" display-id="0" />

                  <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1001,51][1020,92]" drawing-order="2" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

</hierarchy>
2025-07-22 16:51:08 | INFO | __main__:_get_response_from_page_dump:357 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="androidx.recyclerview.widget.RecyclerView" package="com.android.systemui" content-desc="点按相应功能即可开始使用：" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="Ella" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/icon_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[964,1733][1063,1832]" drawing-order="1" hint="" display-id="0" />

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][396,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][396,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="16:51" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="16:51" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="短信通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="智慧5G通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                      <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[350,53][387,90]" drawing-order="5" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,294][1032,973]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,294][1032,973]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,294][984,376]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,294][276,356]" drawing-order="1" hint="" display-id="0" />

                                <node index="2" text="I can answer your questions, summarize content, and provide creative inspiration." resource-id="com.transsion.aivoiceassistant:id/tv_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,294][984,376]" drawing-order="3" hint="" display-id="0" />

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_replace" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,424][984,496]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="Refresh" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,434][894,487]" drawing-order="2" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_replace" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,424][984,496]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,526][984,639]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Galatasaray Targets Donnarumma" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,556][936,609]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,669][984,782]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Meta's AI Push: Growth Potential" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,699][936,752]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,812][984,925]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="How to use Ask About Screen" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,842][936,895]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1021][1032,1134]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[630,1021][1032,1134]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open app" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[678,1051][876,1104]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,1026][1002,1128]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1182][1032,1436]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1182][685,1436]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1182][685,1436]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Which app should I open?" resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1212][637,1265]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[565,1319][637,1391]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1454][1032,1562]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1454][1032,1562]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1454][339,1562]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1475][156,1541]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1490][195,1526]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1475][297,1541]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1592][1032,1964]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1592][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Whatsapp" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1592][353,1696]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="Instagram" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1726][351,1830]" drawing-order="2" hint="" display-id="0" />

                              <node index="2" text="Calendar" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][326,1964]" drawing-order="3" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Please enter" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

</hierarchy>
2025-07-22 17:16:45 | DEBUG | __main__:get_response_from_asr_txt:183 | 从asr_txt获取响应失败: ('Unknown RPC error: -32601 method not found', ({'mask': 2097152, 'childOrSibling': [], 'childOrSiblingSelector': [], 'resourceId': 'com.transsion.aivoiceassistant:id/asr_text'},), None)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:249 | asr_txt节点不存在 (第1次尝试)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:29:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:249 | asr_txt节点不存在 (第2次尝试)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:249 | asr_txt节点不存在 (第3次尝试)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: ""
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:263 | robot_text节点文本为空 (第1次尝试)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第2次)
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: ""
2025-07-22 17:29:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:263 | robot_text节点文本为空 (第2次尝试)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第3次)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: ""
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:263 | robot_text节点文本为空 (第3次尝试)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "   "
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:274 | function_name节点文本清理后为空 (第1次尝试)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "   "
2025-07-22 17:29:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:274 | function_name节点文本清理后为空 (第2次尝试)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "   "
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:274 | function_name节点文本清理后为空 (第3次尝试)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "蓝牙已关闭"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "蓝牙已关闭"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:42 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:43 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:44 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:29:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第3次尝试)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第1次尝试)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第2次尝试)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "已打开"
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_control文本不符合AI响应格式: 已打开 (第3次尝试)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "打开蓝牙"
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "蓝牙已打开"
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第1次尝试)
2025-07-22 17:30:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:30:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "蓝牙"
2025-07-22 17:30:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: 蓝牙 (第2次尝试)
2025-07-22 17:33:44 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:33:44 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:33:44 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第1次尝试)
2025-07-22 17:33:45 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:33:45 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:33:45 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第2次尝试)
2025-07-22 17:33:45 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:33:46 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:33:46 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第3次尝试)
2025-07-22 17:35:43 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:35:44 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:35:44 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第1次尝试)
2025-07-22 17:35:44 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:35:44 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:35:44 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第2次尝试)
2025-07-22 17:35:45 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:35:45 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:35:45 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第3次尝试)
2025-07-22 17:37:36 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:37:37 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:37:37 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第1次尝试)
2025-07-22 17:37:37 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:37:37 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:37:37 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第2次尝试)
2025-07-22 17:37:38 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:37:38 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:37:38 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第3次尝试)
2025-07-22 17:37:38 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:37:39 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:37:39 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第1次尝试)
2025-07-22 17:37:39 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第2次)
2025-07-22 17:37:39 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:37:39 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第2次尝试)
2025-07-22 17:37:40 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第3次)
2025-07-22 17:37:40 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:37:40 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第3次尝试)
2025-07-22 17:37:40 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:37:40 | DEBUG | __main__:_get_element_text_with_retry:249 | function_name节点不存在 (第1次尝试)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:249 | function_name节点不存在 (第2次尝试)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:249 | function_name节点不存在 (第3次尝试)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:37:41 | DEBUG | __main__:_get_element_text_with_retry:249 | function_control节点不存在 (第1次尝试)
2025-07-22 17:37:42 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:37:42 | DEBUG | __main__:_get_element_text_with_retry:249 | function_control节点不存在 (第2次尝试)
2025-07-22 17:37:42 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:37:43 | DEBUG | __main__:_get_element_text_with_retry:249 | function_control节点不存在 (第3次尝试)
2025-07-22 17:38:10 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:38:11 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:38:11 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第1次尝试)
2025-07-22 17:38:11 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:38:11 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:38:11 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第2次尝试)
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open app"
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open app (第3次尝试)
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:38:12 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第1次尝试)
2025-07-22 17:38:13 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第2次)
2025-07-22 17:38:13 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:38:13 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第2次尝试)
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第3次)
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Which app should I open?"
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:288 | robot_text文本不符合AI响应格式: Which app should I open? (第3次尝试)
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:14 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第1次尝试)
2025-07-22 17:38:15 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:38:15 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:15 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第2次尝试)
2025-07-22 17:38:15 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第3次尝试)
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第1次尝试)
2025-07-22 17:38:16 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:38:17 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:17 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第2次尝试)
2025-07-22 17:38:17 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:38:18 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:18 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第3次尝试)
2025-07-22 17:38:49 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 17:38:50 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open Bluetooth"
2025-07-22 17:38:50 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open Bluetooth (第1次尝试)
2025-07-22 17:38:50 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 17:38:50 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open Bluetooth"
2025-07-22 17:38:50 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open Bluetooth (第2次尝试)
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:259 | 从asr_txt节点获取到原始文本: "open Bluetooth"
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:288 | asr_txt文本不符合AI响应格式: open Bluetooth (第3次尝试)
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:259 | 从robot_text节点获取到原始文本: "Bluetooth is turned on now."
2025-07-22 17:38:51 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 17:38:52 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:52 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第1次尝试)
2025-07-22 17:38:52 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 17:38:52 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:52 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第2次尝试)
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_name节点获取到原始文本: "Bluetooth"
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:288 | function_name文本不符合AI响应格式: Bluetooth (第3次尝试)
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:53 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第1次尝试)
2025-07-22 17:38:54 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 17:38:54 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:54 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第2次尝试)
2025-07-22 17:38:55 | DEBUG | __main__:_get_element_text_with_retry:242 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 17:38:55 | DEBUG | __main__:_get_element_text_with_retry:259 | 从function_control节点获取到原始文本: "None"
2025-07-22 17:38:55 | DEBUG | __main__:_get_element_text_with_retry:263 | function_control节点文本为空 (第3次尝试)
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:202 | 响应文本(字符串): 蓝牙已成功开启，设备可以被发现
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:202 | 响应文本(字符串): 蓝牙已成功开启，设备可以被发现，连接状态良好
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['打开蓝牙', '蓝牙已打开', '', '设备'], 过滤后=['打开蓝牙', '蓝牙已打开', '设备'], 合并后=打开蓝牙 蓝牙已打开 设备
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['打开蓝牙', '蓝牙已打开', '设备可发现', ''], 过滤后=['打开蓝牙', '蓝牙已打开', '设备可发现'], 合并后=打开蓝牙 蓝牙已打开 设备可发现
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['', '   ', None], 过滤后=[], 合并后=
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['蓝牙已打开', '设备可发现'], 过滤后=['蓝牙已打开', '设备可发现'], 合并后=蓝牙已打开 设备可发现
2025-07-22 20:31:22 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['打开蓝牙', '蓝牙已打开', '蓝牙', '已打开', ''], 过滤后=['打开蓝牙', '蓝牙已打开', '蓝牙', '已打开'], 合并后=打开蓝牙 蓝牙已打开 蓝牙 已打开
2025-07-22 20:32:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 20:33:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-22 20:33:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第1次尝试)
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第2次尝试)
2025-07-22 20:33:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 20:33:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第3次尝试)
2025-07-22 20:33:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 20:33:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | robot_text节点不存在 (第1次尝试)
2025-07-22 20:33:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第2次)
2025-07-22 20:33:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从robot_text节点获取到原始文本: "Wi-Fi is turned on now."
2025-07-22 20:33:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now. (第2次尝试)
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第3次)
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从robot_text节点获取到原始文本: "Wi-Fi is turned on now."
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now. (第3次尝试)
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_name节点获取到原始文本: "Wi-Fi"
2025-07-22 20:33:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | function_name文本不符合AI响应格式: Wi-Fi (第1次尝试)
2025-07-22 20:33:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 20:33:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_name节点获取到原始文本: "Wi-Fi"
2025-07-22 20:33:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | function_name文本不符合AI响应格式: Wi-Fi (第2次尝试)
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_name节点获取到原始文本: "Wi-Fi"
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | function_name文本不符合AI响应格式: Wi-Fi (第3次尝试)
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第1次尝试)
2025-07-22 20:33:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 20:33:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第2次尝试)
2025-07-22 20:33:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 20:33:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第3次尝试)
2025-07-22 20:33:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 20:33:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-22 20:33:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-22 20:33:47 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-22 20:33:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-22 20:33:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第1次尝试)
2025-07-22 20:33:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第2次尝试)
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从asr_txt节点获取到原始文本: "open wifi"
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | asr_txt文本不符合AI响应格式: open wifi (第3次尝试)
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第1次)
2025-07-22 20:33:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | robot_text节点不存在 (第1次尝试)
2025-07-22 20:33:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第2次)
2025-07-22 20:33:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | robot_text节点不存在 (第2次尝试)
2025-07-22 20:33:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从robot_text节点获取响应 (第3次)
2025-07-22 20:33:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | robot_text节点不存在 (第3次尝试)
2025-07-22 20:33:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第1次)
2025-07-22 20:33:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | function_name节点不存在 (第1次尝试)
2025-07-22 20:33:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第2次)
2025-07-22 20:33:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:247 | function_name节点不存在 (第2次尝试)
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_name节点获取响应 (第3次)
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_name节点获取到原始文本: "Wi-Fi"
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:286 | function_name文本不符合AI响应格式: Wi-Fi (第3次尝试)
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第1次)
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第1次尝试)
2025-07-22 20:33:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第2次)
2025-07-22 20:33:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第2次尝试)
2025-07-22 20:33:53 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:240 | 尝试从function_control节点获取响应 (第3次)
2025-07-22 20:33:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:257 | 从function_control节点获取到原始文本: "None"
2025-07-22 20:33:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点文本为空 (第3次尝试)
2025-07-22 20:33:55 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:448 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,294][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,294][1032,860]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,294][1032,860]" drawing-order="1" hint="" display-id="0">

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_replace" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,311][984,383]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="Refresh" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,321][894,374]" drawing-order="2" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_replace" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,311][984,383]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,413][984,526]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="UMG Files for US Listing" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,443][936,496]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,669]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Galatasaray Targets Donnarumma" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,586][936,639]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,699][984,812]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="How to use Ask About Screen" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,729][936,782]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[636,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open wifi" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[684,938][876,991]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,913][1002,1015]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][1032,1323]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][632,1323]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1069][632,1323]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Wi-Fi is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1099][584,1152]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[512,1206][584,1278]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Wi-Fi" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1425][834,1486]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1425][984,1491]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1563][1032,1683]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1596][150,1650]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1600][286,1646]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][339,1830]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1743][156,1809]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1758][195,1794]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1743][297,1809]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="5" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="6" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Dim the screen" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][460,1964]" drawing-order="1" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="androidx.recyclerview.widget.RecyclerView" package="com.android.systemui" content-desc="点按相应功能即可开始使用：" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="Ella" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[947,1716][1080,1849]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/icon_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[964,1733][1063,1832]" drawing-order="1" hint="" display-id="0" />

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][396,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][396,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="20:33" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="20:33" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][396,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="短信通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="智慧5G通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                      <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[350,53][387,90]" drawing-order="5" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

</hierarchy>
2025-07-22 20:33:59 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:207 | 响应文本(列表转换): 原始列表=['open wifi', '', 'Wi-Fi', '', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'], 过滤后=['open wifi', 'Wi-Fi', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'], 合并后=open wifi Wi-Fi <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
