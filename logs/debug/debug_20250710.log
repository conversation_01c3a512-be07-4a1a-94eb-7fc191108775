2025-07-10 21:44:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 21:44:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:44:30 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:44:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:44:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:44:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:44:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:44:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-07-10 21:46:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:21 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 21:46:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:46:33 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:46:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:46:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-07-10 21:55:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 21:55:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:55:16 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:55:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:55:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:55:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-07-10 21:57:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:57:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:57:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-10 21:58:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 21:58:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 21:58:15 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:58:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 21:58:21 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-07-10 21:58:36 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 1: RTC_WAKEUP #58: Alarm{4bc8635 type 0 origWhen 1752195600000 whenElapsed 43818334 com.transsion.deskclock}
2025-07-10 21:58:36 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 2: RTC_WAKEUP #60: Alarm{7277aca type 0 origWhen 1752199200000 whenElapsed 47418333 com.transsion.deskclock}
2025-07-10 21:58:36 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 3: RTC_WAKEUP #61: Alarm{575043b type 0 origWhen 1752199200000 whenElapsed 47418334 com.transsion.deskclock}
2025-07-10 22:00:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 22:00:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:00:13 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 22:00:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-07-10 22:00:32 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 1: RTC_WAKEUP #75: Alarm{43b26b7 type 0 origWhen 1752195600000 whenElapsed 43818333 com.transsion.deskclock}
2025-07-10 22:00:32 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 2: RTC_WAKEUP #77: Alarm{b856e24 type 0 origWhen 1752199200000 whenElapsed 47418333 com.transsion.deskclock}
2025-07-10 22:00:32 | DEBUG | pages.apps.ella.main_page:verify_alarm_in_list:2363 | 检查闹钟 3: RTC_WAKEUP #78: Alarm{3370d8d type 0 origWhen 1752199200000 whenElapsed 47418334 com.transsion.deskclock}
2025-07-10 22:10:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-10 22:10:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-07-10 22:10:18 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: what is the weather like in shanghai tomorrow
2025-07-10 22:10:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
