2025-07-14 19:24:59 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:24:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:02 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:25:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:04 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:25:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:25:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:09 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:25:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:25:31 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:25:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:33 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:25:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:35 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:25:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:25:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:25:40 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: close bluetooth
2025-07-14 19:25:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:25:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:26:00 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:26:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:02 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:26:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:04 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:26:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:26:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:10 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: bluetooth status
2025-07-14 19:26:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:26:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:26:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:15 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:26:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:17 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:26:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:26:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:26:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:26:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-14 19:27:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:27:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:07 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:27:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:08 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:27:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:27:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:27:15 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: check bluetooth
2025-07-14 19:27:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:28:40 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:28:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:43 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:45 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:28:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:28:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:28:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:28:49 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:28:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:28:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:28:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:28:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:28:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:28:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:28:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:28:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:28:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:28:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:28:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:28:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:28:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:28:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:28:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:28:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:28:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:28:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:28:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:28:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:58 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:28:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:28:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:29:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:03 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:29:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:29:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:29:22 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:29:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:24 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:29:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:26 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:29:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:29:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:29:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:29:30 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:29:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:29:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:29:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:29:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:29:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:29:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:29:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:29:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:29:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:29:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:29:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:29:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:29:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:29:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:29:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:29:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:29:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:29:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:29:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:29:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:29:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:29:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:29:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:29:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:29:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:39 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:29:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:29:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:29:44 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: close bluetooth
2025-07-14 19:29:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:29:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:30:02 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:30:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:04 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:30:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:06 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\aigc\app_test (标识: requirements.txt)
2025-07-14 19:30:06 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\aigc\app_test\data
2025-07-14 19:30:06 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-07-14 19:30:06 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-07-14 19:30:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:06 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:30:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:30:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:30:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:30:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:30:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:30:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:30:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:30:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:30:11 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:30:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:30:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:30:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:30:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:30:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:30:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:30:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:30:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:30:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:30:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:30:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:30:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:30:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:30:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:30:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:30:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:30:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:30:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:30:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:30:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:20 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:30:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:30:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:30:25 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:30:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:30:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:30:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:36:05 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:36:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:08 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:10 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:36:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:15 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:36:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:36:29 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:36:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:32 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:33 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:36:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:38 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: close bluetooth
2025-07-14 19:36:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:36:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:36:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:36:54 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:36:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:56 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:58 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:36:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:36:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:37:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:04 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: bluetooth status
2025-07-14 19:37:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:37:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:37:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:09 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:37:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:11 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:37:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:37:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:18 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: is bluetooth on
2025-07-14 19:37:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:37:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:44 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:37:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:47 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:37:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:37:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:37:54 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: check bluetooth
2025-07-14 19:38:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:38:13 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:38:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:16 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:18 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:38:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:38:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:38:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:38:22 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:38:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:38:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:38:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:38:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:38:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:38:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:38:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:38:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:38:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:38:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:38:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:38:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:38:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:38:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:31 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:38:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:38:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:37 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:38:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:38:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:38:53 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:38:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:55 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:38:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:57 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:38:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:38:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:38:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:39:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:02 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:39:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:39:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:11 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:39:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:39:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:16 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: close bluetooth
2025-07-14 19:39:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:39:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:39:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 19:39:33 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-14 19:39:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:36 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:39:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:37 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\aigc\app_test (标识: requirements.txt)
2025-07-14 19:39:37 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\aigc\app_test\data
2025-07-14 19:39:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-07-14 19:39:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-07-14 19:39:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:37 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:39:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-14 19:39:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: True
2025-07-14 19:39:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:42 | DEBUG | pages.apps.ella.main_page:start_voice_input:1148 | 点击后未进入语音录制状态，尝试下一个按钮
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [麦克风按钮]: False
2025-07-14 19:39:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(文本)]: False
2025-07-14 19:39:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [图像按钮]: False
2025-07-14 19:39:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-14 19:39:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音录制按钮]: False
2025-07-14 19:39:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态指示器]: False
2025-07-14 19:39:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态(英文)]: False
2025-07-14 19:39:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制状态文本]: False
2025-07-14 19:39:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [正在录制文本]: False
2025-07-14 19:39:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [录制进度条]: False
2025-07-14 19:39:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:50 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-14 19:39:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-14 19:39:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 19:39:56 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-14 19:39:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:40:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:40:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: False
2025-07-14 19:40:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:00:16 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:01:22 | DEBUG | __main__:validate_import:86 | 详细错误: Traceback (most recent call last):
  File "D:\aigc\app_test\tools\optimization_tools\validate_optimized_tests.py", line 66, in validate_import
    spec = importlib.util.spec_from_file_location(
           ^^^^^^^^^^^^^^
AttributeError: module 'importlib' has no attribute 'util'

2025-07-14 21:05:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:39 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:05:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:05:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:17:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:09 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:17:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:17:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:19:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:09 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:19:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:19:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:29:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:47 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:29:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:29:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:30:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:51 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:30:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:30:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:31:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:22 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:31:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:31:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-14 21:31:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-14 21:32:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:34 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-14 21:32:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-14 21:32:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
