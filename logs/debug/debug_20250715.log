2025-07-15 20:18:42 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-15 20:18:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-15 20:18:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-15 20:18:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: (-32001, 'androidx.test.uiautomator.UiObjectNotFoundException', ({'mask': 16777232, 'childOrSibling': [], 'childOrSiblingSelector': [], 'className': 'android.widget.TextView', 'instance': 12},))
