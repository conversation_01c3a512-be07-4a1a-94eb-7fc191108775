2025-07-16 17:08:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 17:08:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 17:08:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 17:08:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 17:08:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-16 17:08:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-16 17:08:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: False
2025-07-16 17:08:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 17:08:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 17:08:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 17:08:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 17:08:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-16 17:08:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-16 17:08:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-16 17:08:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: False
2025-07-16 17:08:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 17:08:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 17:08:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 17:08:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 17:08:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-16 17:08:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-16 17:08:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: False
2025-07-16 17:08:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 17:08:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 17:08:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 17:08:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 17:08:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:08:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:13 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:08:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:08:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:08:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:08:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:09:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:47 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:09:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:09:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:10:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:26 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:10:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:10:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:52 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:10:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:10:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:15:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:18 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:15:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:15:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:17:09 | DEBUG | pages.apps.ella.main_page:_ensure_service_health:302 | UIAutomator2服务健康检查通过
2025-07-16 17:17:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:12 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-16 17:17:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:14 | DEBUG | pages.apps.ella.main_page:_check_chat_page_indicators:1363 | 检测到输入框，在对话页面
2025-07-16 17:17:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: True
2025-07-16 17:17:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:19 | DEBUG | core.base_element:get_text:348 | 获取元素文本 [输入框]: open bluetooth
2025-07-16 17:17:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [TTS播放按钮]: True
2025-07-16 17:17:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:17:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:18:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:18:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:18:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:18:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:19:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:31 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:19:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:19:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:37:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:23 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:37:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:37:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:37:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: (-32001, 'androidx.test.uiautomator.UiObjectNotFoundException', ({'mask': 16777232, 'childOrSibling': [], 'childOrSiblingSelector': [], 'className': 'android.widget.TextView', 'instance': 10},))
2025-07-16 17:47:15 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:17 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:47:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 17:47:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:23 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 17:47:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 17:47:23 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:03:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:25 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:03:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 18:03:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:03:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-16 18:03:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-16 18:03:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-16 18:03:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-16 18:03:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-16 18:03:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:03:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:14:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:11 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:14:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:12 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-16 18:14:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:16 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:14:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-16 18:14:17 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-16 18:14:22 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
