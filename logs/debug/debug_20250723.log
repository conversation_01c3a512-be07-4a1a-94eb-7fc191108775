2025-07-23 10:16:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:00 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:17:01 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): False
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:17:03 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:17', 'Dialogue', 'Explore', '10:16', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Federal Grants to Nonprofits Slashed', 'How to use Ask About Screen', 'Grizzlies Sign Ty Jerome']...
2025-07-23 10:17:03 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:17:03 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:17:07 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): False
2025-07-23 10:17:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:17:14 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 10:25:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:45 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:25:45 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:25:49 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:25', 'Dialogue', 'Explore', 'What is Ask About Screen?', 'GOP Split on Epstein Probe', 'Haliburton Reacts to Turner Trade', 'open flashlight', 'Flashlight is turned on now.', 'Flashlight', 'Set Up']...
2025-07-23 10:25:50 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:25', 'Dialogue', 'Explore', 'What is Ask About Screen?', 'GOP Split on Epstein Probe', 'Haliburton Reacts to Turner Trade', 'open flashlight', 'Flashlight is turned on now.', 'Flashlight', 'Set Up']...
2025-07-23 10:25:50 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:25:50 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:25:53 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): True
2025-07-23 10:25:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:53 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:26:01 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 10:32:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-23 10:32:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-23 10:32:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-23 10:32:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-23 10:32:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-23 10:32:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-23 10:32:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: True
2025-07-23 10:32:25 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:32:25 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): False
2025-07-23 10:32:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-23 10:32:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-23 10:32:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-23 10:32:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-23 10:32:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-23 10:32:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-23 10:32:29 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:32:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:32:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:32:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:32:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:32:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:32:30 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '10:32', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Warriors Eye Celtics Star in Trade', 'US Seeks SpaceX Alternatives', 'Switch voices', 'open flashlight']...
2025-07-23 10:32:30 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:32:30 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:32:34 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): True
2025-07-23 10:32:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:32:34 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:32:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:32:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:32:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:32:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:32:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:32:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:32:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:32:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:32:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:32:37 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:32:38 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:32:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:32:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:32:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:32:39 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:32:40 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:32:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:32:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:32:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:32:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:32:41 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:32:42 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 10:38:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:14 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:38:15 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): False
2025-07-23 10:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:16 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:17 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:38:18 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:38', 'Dialogue', 'Explore', '10:38', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'SpaceX Falcon 9 Launch Amid Storms', 'MarJon Beauchamp Shines at NBA Summer League', 'Switch to a male voice']...
2025-07-23 10:38:19 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:38:19 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:38:22 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): True
2025-07-23 10:38:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:38:23 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:38:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:38:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:38:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:38:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:38:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:38:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:38:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:38:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:38:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:38:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:38:27 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:38:28 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:38:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:38:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:38:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:38:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:38:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:38:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:38:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:38:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:38:30 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 10:41:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [Ella欢迎消息]: False
2025-07-23 10:41:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [聊天消息列表]: True
2025-07-23 10:41:57 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:41:58 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): False
2025-07-23 10:41:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: False
2025-07-23 10:41:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-23 10:41:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-23 10:41:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-23 10:41:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-23 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:00 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:42:03 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:42', 'Dialogue', 'Explore', '10:41', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'How to use Ask About Screen', 'Filipowski Wins Summer League MVP', 'US Envoy Pursues Gaza Truce']...
2025-07-23 10:42:03 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:42:03 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:42:06 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): True
2025-07-23 10:42:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:42:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:42:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:42:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:42:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:42:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:42:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:42:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:42:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:42:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:42:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:42:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:42:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:42:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:42:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:42:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:42:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:42:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:42:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:42:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:42:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:42:14 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 12:05:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:37 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: clock (clock状态)
2025-07-23 12:05:37 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取clock状态(初始): True
2025-07-23 12:05:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:37 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 12:05:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 12:05:39 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '12:05', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Switch to a male voice', 'Filipowski Wins Summer League MVP', 'Ellison Tops Global Earnings', 'open clock']...
2025-07-23 12:05:39 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: clock (clock状态)
2025-07-23 12:05:39 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:209 | 等待状态变化: 3.0秒
2025-07-23 12:05:42 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取clock状态(最终): True
2025-07-23 12:05:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 12:05:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 12:05:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 12:05:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open clock"
2025-07-23 12:05:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open clock (第1次尝试)
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open clock"
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open clock (第2次尝试)
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open clock"
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open clock (第3次尝试)
2025-07-23 12:05:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 12:05:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 12:05:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 12:05:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 12:05:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 12:05:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 12:05:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 12:05:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 12:05:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 12:05:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 12:05:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 12:05:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 12:05:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 12:05:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 12:05:50 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['open clock', 'Done!', '', ''], 过滤后=['open clock', 'Done!'], 合并后=open clock Done!
2025-07-23 13:03:09 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:10 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:03:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:10 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:03:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:10 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:03:12 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['13:03', 'Dialogue', 'Explore', '13:03', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', "Liverpool Eye Guehi as Konate's Successor", 'Switch voices', 'Tesla Bets on AI Future']...
2025-07-23 13:03:12 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:03:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:03:13 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:03:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:03:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:03:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第1次尝试)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第2次尝试)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第3次尝试)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:03:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第1次尝试)
2025-07-23 13:03:15 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 13:03:15 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第2次尝试)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第3次尝试)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:03:16 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:03:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:03:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:03:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:03:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:03:17 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:03:18 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:03:18 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:03:18 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:03:20 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 15 个文本: ['13:03', 'Dialogue', 'Explore', 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', "Liverpool Eye Guehi as Konate's Successor", 'Switch voices', 'Tesla Bets on AI Future', 'open countdown', 'For how long?']...
2025-07-23 13:03:20 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['open countdown', '', '', '', "13:03 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh Liverpool Eye Guehi as Konate's Successor Switch voices Tesla Bets on AI Future open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter"], 过滤后=['open countdown', "13:03 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh Liverpool Eye Guehi as Konate's Successor Switch voices Tesla Bets on AI Future open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter"], 合并后=open countdown 13:03 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh Liverpool Eye Guehi as Konate's Successor Switch voices Tesla Bets on AI Future open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter
2025-07-23 13:06:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:55 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:06:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:06:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:06:58 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['13:06', 'Dialogue', 'Explore', '13:06', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', 'OpenAI, Oracle Expand Stargate Data Centers', 'Fàbregas Courts Messi for Como']...
2025-07-23 13:06:58 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['13:06', 'Dialogue', 'Explore', '13:06', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', 'OpenAI, Oracle Expand Stargate Data Centers', 'Fàbregas Courts Messi for Como']...
2025-07-23 13:06:58 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:06:59 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:06:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:06:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:06:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第1次尝试)
2025-07-23 13:07:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:07:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:07:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第2次尝试)
2025-07-23 13:07:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第3次尝试)
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第1次尝试)
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 13:07:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第2次尝试)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第3次尝试)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:07:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:07:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:07:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:07:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:07:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:07:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:07:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:07:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:07:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:07:06 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 15 个文本: ['13:07', 'Dialogue', 'Explore', 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', 'OpenAI, Oracle Expand Stargate Data Centers', 'Fàbregas Courts Messi for Como', 'open countdown', 'For how long?']...
2025-07-23 13:07:07 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['open countdown', '', '', '', '13:07 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? OpenAI, Oracle Expand Stargate Data Centers Fàbregas Courts Messi for Como open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter'], 过滤后=['open countdown', '13:07 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? OpenAI, Oracle Expand Stargate Data Centers Fàbregas Courts Messi for Como open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter'], 合并后=open countdown 13:07 Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? OpenAI, Oracle Expand Stargate Data Centers Fàbregas Courts Messi for Como open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter
2025-07-23 13:07:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:54 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:07:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:55 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:07:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:07:57 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '13:07', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', "Wolves Reject Lakers' DiVincenzo Bid", "Google's $2.4B AI Talent Grab", 'open countdown']...
2025-07-23 13:07:57 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:185 | 未识别的命令类型: open countdown
2025-07-23 13:07:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:07:57 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:07:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:07:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:07:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第1次尝试)
2025-07-23 13:07:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:07:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:07:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第2次尝试)
2025-07-23 13:07:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:07:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open countdown"
2025-07-23 13:07:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open countdown (第3次尝试)
2025-07-23 13:07:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:07:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第1次尝试)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第2次尝试)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第3次尝试)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:08:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:08:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:08:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:08:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:08:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:08:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:08:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:08:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:08:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:08:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:08:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:08:04 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 15 个文本: ['Dialogue', 'Explore', 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', "Wolves Reject Lakers' DiVincenzo Bid", "Google's $2.4B AI Talent Grab", 'open countdown', 'For how long?', '5 minutes']...
2025-07-23 13:08:05 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['open countdown', '', '', '', "Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Wolves Reject Lakers' DiVincenzo Bid Google's $2.4B AI Talent Grab open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter 13:08"], 过滤后=['open countdown', "Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Wolves Reject Lakers' DiVincenzo Bid Google's $2.4B AI Talent Grab open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter 13:08"], 合并后=open countdown Dialogue Explore I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Wolves Reject Lakers' DiVincenzo Bid Google's $2.4B AI Talent Grab open countdown For how long? 5 minutes 10 minutes 20 minutes DeepSeek-R1 Please enter 13:08
2025-07-23 13:12:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:57 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: camera (相机应用状态)
2025-07-23 13:12:57 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取相机应用状态(初始): True
2025-07-23 13:12:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:57 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:12:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:12:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:12:59 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 10 个文本: ['Dialogue', 'Explore', '13:12', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', "Haliburton Joins NBA's Saddest Club", "Colbert's Trump Joke Post-Cancellation", '13:12']...
2025-07-23 13:13:00 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 11 个文本: ['Dialogue', 'Explore', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', "Haliburton Joins NBA's Saddest Club", "Colbert's Trump Joke Post-Cancellation", 'DeepSeek-R1', 'open camera']...
2025-07-23 13:13:00 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: camera (相机应用状态)
2025-07-23 13:13:00 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:209 | 等待状态变化: 3.0秒
2025-07-23 13:13:03 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取相机应用状态(最终): True
2025-07-23 13:13:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:13:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:13:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | asr_txt节点不存在 (第1次尝试)
2025-07-23 13:13:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:13:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | asr_txt节点不存在 (第2次尝试)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | asr_txt节点不存在 (第3次尝试)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第1次尝试)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 13:13:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第2次尝试)
2025-07-23 13:13:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 13:13:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第3次尝试)
2025-07-23 13:13:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:13:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:13:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:13:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:13:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:13:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:13:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:13:10 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 11 个文本: ['Dialogue', 'Explore', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'What is Ask About Screen?', "Haliburton Joins NBA's Saddest Club", "Colbert's Trump Joke Post-Cancellation", 'DeepSeek-R1', 'open camera']...
2025-07-23 13:13:11 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['', '', '', '', "Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13"], 过滤后=["Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13"], 合并后=Dialogue Explore Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh What is Ask About Screen? Haliburton Joins NBA's Saddest Club Colbert's Trump Joke Post-Cancellation DeepSeek-R1 open camera 13:13
2025-07-23 13:13:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:37 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: camera (相机应用状态)
2025-07-23 13:13:37 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取相机应用状态(初始): True
2025-07-23 13:13:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:37 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:13:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:13:39 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '13:13', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', "O'Leary: Fed Risk to US Investment", 'How to use Ask About Screen', 'Barcelona Miss Out on Bardghji', 'open camera']...
2025-07-23 13:13:40 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:182 | 检测到命令类型: camera (相机应用状态)
2025-07-23 13:13:40 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:209 | 等待状态变化: 3.0秒
2025-07-23 13:13:43 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:220 | 获取相机应用状态(最终): True
2025-07-23 13:13:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:13:45 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:13:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:13:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open camera"
2025-07-23 13:13:45 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open camera (第1次尝试)
2025-07-23 13:13:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:13:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open camera"
2025-07-23 13:13:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open camera (第2次尝试)
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open camera"
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open camera (第3次尝试)
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:13:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:13:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:13:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:13:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:13:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:13:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:13:50 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:516 | 响应文本(列表转换): 原始列表=['open camera', 'Done!', '', ''], 过滤后=['open camera', 'Done!'], 合并后=open camera Done!
2025-07-23 13:22:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:53 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: facebook (clock状态)
2025-07-23 13:22:53 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取clock状态(初始): True
2025-07-23 13:22:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:54 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:22:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:22:55 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:22:56 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '13:22', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'BetMGM Bonus: $150 for WNBA, MLB', 'Asia-Pacific Markets Rise on US-Japan Deal', 'Switch voices', 'open facebook']...
2025-07-23 13:22:56 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: facebook (clock状态)
2025-07-23 13:22:56 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:215 | 等待状态变化: 3.0秒
2025-07-23 13:22:59 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取clock状态(最终): True
2025-07-23 13:23:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:23:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:23:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第1次尝试)
2025-07-23 13:23:02 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第2次尝试)
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第3次尝试)
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 13:23:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:23:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:23:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:23:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:23:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:23:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:23:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:23:07 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['open facebook', 'Done!', '', ''], 过滤后=['open facebook', 'Done!'], 合并后=open facebook Done!
2025-07-23 13:23:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:38 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: facebook (clock状态)
2025-07-23 13:23:38 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取clock状态(初始): False
2025-07-23 13:23:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:39 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:23:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:40 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:23:41 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 12 个文本: ['Dialogue', 'Explore', '13:23', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Ellison Tops Global Earnings', 'Lakers Pursue KCP Trade', 'What is Ask About Screen?', 'open facebook']...
2025-07-23 13:23:41 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: facebook (clock状态)
2025-07-23 13:23:41 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:215 | 等待状态变化: 3.0秒
2025-07-23 13:23:44 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取clock状态(最终): True
2025-07-23 13:23:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:23:46 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:23:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:23:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第1次尝试)
2025-07-23 13:23:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第2次尝试)
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open facebook"
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open facebook (第3次尝试)
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 13:23:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:23:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:23:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:23:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:23:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:23:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:23:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:23:52 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['open facebook', 'Done!', '', ''], 过滤后=['open facebook', 'Done!'], 合并后=open facebook Done!
2025-07-23 13:25:11 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:12 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: contacts (联系人应用状态)
2025-07-23 13:25:13 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取联系人应用状态(初始): True
2025-07-23 13:25:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:13 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:25:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:13 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:25:16 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['13:25', 'Dialogue', 'Explore', '13:25', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Screen off', 'Open Facebook', 'Turn on Flashlight']...
2025-07-23 13:25:16 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:188 | 检测到命令类型: contacts (联系人应用状态)
2025-07-23 13:25:16 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:215 | 等待状态变化: 3.0秒
2025-07-23 13:25:21 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:226 | 获取联系人应用状态(最终): True
2025-07-23 13:25:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:25:21 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:25:22 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:25:22 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open dialer"
2025-07-23 13:25:22 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open dialer (第1次尝试)
2025-07-23 13:25:22 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open dialer"
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open dialer (第2次尝试)
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open dialer"
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open dialer (第3次尝试)
2025-07-23 13:25:23 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:25:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 13:25:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:25:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:25:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:25:24 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:25:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:25:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:25:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:25:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:25:25 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:25:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:25:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:25:26 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:25:27 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['open dialer', 'Done!', '', ''], 过滤后=['open dialer', 'Done!'], 合并后=open dialer Done!
2025-07-23 13:26:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:00 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: open folax
2025-07-23 13:27:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:00 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:27:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 13:27:02 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['Dialogue', 'Explore', '13:26', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', "Sixers' Maxey Upbeat Post-Injury", 'US Seeks SpaceX Alternative for Defense', 'What is Ask About Screen?', 'open folax']...
2025-07-23 13:27:02 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: open folax
2025-07-23 13:27:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 13:27:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 13:27:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 13:27:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open folax"
2025-07-23 13:27:03 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open folax (第1次尝试)
2025-07-23 13:27:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 13:27:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open folax"
2025-07-23 13:27:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open folax (第2次尝试)
2025-07-23 13:27:04 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open folax"
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open folax (第3次尝试)
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | robot_text节点不存在 (第1次尝试)
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Done!"
2025-07-23 13:27:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 13:27:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 13:27:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 13:27:06 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 13:27:07 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 13:27:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 13:27:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 13:27:08 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['open folax', 'Done!', '', ''], 过滤后=['open folax', 'Done!'], 合并后=open folax Done!
2025-07-23 16:28:41 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:42 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: order a burger
2025-07-23 16:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:43 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 16:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 16:28:45 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 8 个文本: ['Dialogue', 'Explore', "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:&#10;&#10;- Image Q&A: Who is this? Where is this?&#10;- Numbers and Contacts: Call this number; save this new contact to my contacts&#10;- Address Navigation: Navigate to this address&#10;- Note creation: Create a new note with the text on the screen&#10;- Schedule Extraction: Create this schedule for me&#10;- Copywriting Assistance: Write a caption for this picture&#10;&#10;These are just a few of the features. Activate Ella to explore new functionalities right away.", 'order a burger', 'Ella is thinking…', 'DeepSeek-R1', 'Feel free to ask me any questions…', '16:28']...
2025-07-23 16:28:45 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: order a burger
2025-07-23 16:28:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:28:45 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 16:28:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 16:28:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:28:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第1次尝试)
2025-07-23 16:28:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第2次尝试)
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第3次尝试)
2025-07-23 16:28:47 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 16:28:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away."
2025-07-23 16:28:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away. (第1次尝试)
2025-07-23 16:28:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 16:28:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away."
2025-07-23 16:28:48 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away. (第2次尝试)
2025-07-23 16:28:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 16:28:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away."
2025-07-23 16:28:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away. (第3次尝试)
2025-07-23 16:28:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 16:28:49 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 16:28:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 16:28:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 16:28:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 16:28:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 16:28:50 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 16:28:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 16:28:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 16:28:51 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 16:28:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 16:28:52 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 16:28:52 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['order a burger', "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:\n\n- Image Q&A: Who is this? Where is this?\n- Numbers and Contacts: Call this number; save this new contact to my contacts\n- Address Navigation: Navigate to this address\n- Note creation: Create a new note with the text on the screen\n- Schedule Extraction: Create this schedule for me\n- Copywriting Assistance: Write a caption for this picture\n\nThese are just a few of the features. Activate Ella to explore new functionalities right away.", '', ''], 过滤后=['order a burger', "What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:\n\n- Image Q&A: Who is this? Where is this?\n- Numbers and Contacts: Call this number; save this new contact to my contacts\n- Address Navigation: Navigate to this address\n- Note creation: Create a new note with the text on the screen\n- Schedule Extraction: Create this schedule for me\n- Copywriting Assistance: Write a caption for this picture\n\nThese are just a few of the features. Activate Ella to explore new functionalities right away."], 合并后=order a burger What should you do when you spot something interesting on your phone and want to learn more? You can activate Ella anytime, anywhere, to inquire about what's on your screen. Ella will search or process the information for you. Let's give it a try:

- Image Q&A: Who is this? Where is this?
- Numbers and Contacts: Call this number; save this new contact to my contacts
- Address Navigation: Navigate to this address
- Note creation: Create a new note with the text on the screen
- Schedule Extraction: Create this schedule for me
- Copywriting Assistance: Write a caption for this picture

These are just a few of the features. Activate Ella to explore new functionalities right away.
2025-07-23 16:30:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:25 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: order a burger
2025-07-23 16:30:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:26 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 16:30:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 16:30:28 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 15 个文本: ['Dialogue', 'Explore', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Switch to a male voice', 'Federal Grants to Nonprofits Slashed', 'Knicks Eye Mikal Bridges Extension', 'order a burger', "Sorry, I'm still learning how to help you order food delivery."]...
2025-07-23 16:30:28 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 15 个文本: ['Dialogue', 'Explore', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Switch to a male voice', 'Federal Grants to Nonprofits Slashed', 'Knicks Eye Mikal Bridges Extension', 'order a burger', "Sorry, I'm still learning how to help you order food delivery."]...
2025-07-23 16:30:28 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:191 | 未识别的命令类型: order a burger
2025-07-23 16:30:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 16:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 16:30:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 16:30:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:30:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第1次尝试)
2025-07-23 16:30:29 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第2次尝试)
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "order a burger"
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: order a burger (第3次尝试)
2025-07-23 16:30:30 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 16:30:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Sorry, I'm still learning how to help you order food delivery."
2025-07-23 16:30:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Sorry, I'm still learning how to help you order food delivery. (第1次尝试)
2025-07-23 16:30:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 16:30:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Sorry, I'm still learning how to help you order food delivery."
2025-07-23 16:30:31 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Sorry, I'm still learning how to help you order food delivery. (第2次尝试)
2025-07-23 16:30:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 16:30:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Sorry, I'm still learning how to help you order food delivery."
2025-07-23 16:30:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Sorry, I'm still learning how to help you order food delivery. (第3次尝试)
2025-07-23 16:30:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 16:30:32 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第1次尝试)
2025-07-23 16:30:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 16:30:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第2次尝试)
2025-07-23 16:30:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 16:30:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_name节点不存在 (第3次尝试)
2025-07-23 16:30:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 16:30:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第1次尝试)
2025-07-23 16:30:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 16:30:34 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第2次尝试)
2025-07-23 16:30:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 16:30:35 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:246 | function_control节点不存在 (第3次尝试)
2025-07-23 16:30:35 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:522 | 响应文本(列表转换): 原始列表=['order a burger', "Sorry, I'm still learning how to help you order food delivery.", '', ''], 过滤后=['order a burger', "Sorry, I'm still learning how to help you order food delivery."], 合并后=order a burger Sorry, I'm still learning how to help you order food delivery.
