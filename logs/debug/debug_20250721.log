2025-07-21 15:15:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:15:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:35 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:16:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 15:19:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 19:44:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 19:44:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-21 20:18:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:06 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:14 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:15 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:16 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:59 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:19:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:53 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:54 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:56 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:23:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-21 20:23:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:43 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:30:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:30 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:55:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:55:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:26 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:148 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="20:55" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="20:55" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,413][984,526]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Switch to a male voice" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,443][936,496]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,669]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Ouaddou Praises Mbuthuma's Impact" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,586][936,639]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,699][984,812]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="Musk's Tesla Safety Crisis" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,729][936,782]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[502,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open bluetooth" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[550,938][876,991]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,913][1002,1015]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][1032,1323]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Bluetooth is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1099][686,1152]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[614,1206][686,1278]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Bluetooth" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1425][834,1486]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1425][984,1491]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1563][1032,1683]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1596][150,1650]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1600][286,1646]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][339,1830]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1743][156,1809]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1758][195,1794]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1743][297,1809]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="5" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="6" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Lower the brightness" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][588,1964]" drawing-order="1" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

</hierarchy>
2025-07-21 21:06:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:06:59 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 21:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:07:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 21:07:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:07:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 21:12:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 21:12:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:29 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 21:12:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 21:12:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:04:23 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:24 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:04:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:04:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:04:27 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:06:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:58 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:06:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:06:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:07:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:07:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:09:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:34 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:09:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:09:36 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:09:36 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:09:36 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_check_area:129 | check_area节点不存在
2025-07-21 22:09:38 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:252 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,432][1032,478]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="22:09 " resource-id="com.transsion.aivoiceassistant:id/tv_time" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,432][1032,478]" drawing-order="1" hint="" display-id="0" />

                          </node>

                          <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,526][1032,1423]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,526][1032,1423]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,776]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,576][276,756]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Hi, I'm Ella" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,556][553,617]" drawing-order="2" hint="" display-id="0" />

                                <node index="2" text="I can answer your questions, summarize content, and provide creative inspiration." resource-id="com.transsion.aivoiceassistant:id/tv_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,623][984,776]" drawing-order="3" hint="" display-id="0" />

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_replace" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,824][984,896]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="Refresh" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,834][894,887]" drawing-order="2" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_replace" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,824][984,896]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,926][984,1039]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Tesla's 2025 Model S Lease Offer" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,956][936,1009]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1069][984,1232]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="EA FC 26 Unveils Gameplay Revolution" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,1099][936,1202]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1262][984,1375]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="What is Ask About Screen?" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,1292][936,1345]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1471][1032,1584]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[635,1471][1032,1584]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open ella" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[683,1501][876,1554]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,1476][1002,1578]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_loading" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1632][582,1779]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_loading_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1662][168,1734]" drawing-order="1" hint="" display-id="0" />

                            <node index="1" text="Ella is thinking…" resource-id="com.transsion.aivoiceassistant:id/tv_loading" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[198,1671][534,1724]" drawing-order="2" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="22:09" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="22:09" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

</hierarchy>
2025-07-21 22:20:45 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:05 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:24:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:24:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:24:08 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:24:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_check_area:129 | check_area节点不存在
2025-07-21 22:24:21 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:224 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-21 22:24:22 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:252 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,384][984,476]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Taylor Swift at Selena's 33rd Bash" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,393][936,446]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,506][984,669]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Guardians Reject Trump Name Critique" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,536][936,639]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,699][984,812]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="Switch to a female voice" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,729][936,782]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[502,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open bluetooth" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[550,938][876,991]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,913][1002,1015]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][1032,1323]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Bluetooth is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1099][686,1152]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[614,1206][686,1278]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Bluetooth" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1425][834,1486]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1425][984,1491]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1563][1032,1683]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1596][150,1650]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1600][286,1646]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][339,1830]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1743][156,1809]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1758][195,1794]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1743][297,1809]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="5" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="6" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Lower the brightness" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][588,1964]" drawing-order="1" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="22:24" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="22:24" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

</hierarchy>
2025-07-21 22:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:26:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:26:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:26:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:26:33 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_check_area:129 | check_area节点不存在
2025-07-21 22:26:35 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:252 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,432][1032,478]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="22:26 " resource-id="com.transsion.aivoiceassistant:id/tv_time" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,432][1032,478]" drawing-order="1" hint="" display-id="0" />

                          </node>

                          <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,526][1032,1373]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,526][1032,1373]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,776]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,576][276,756]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Hi, I'm Ella" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,556][553,617]" drawing-order="2" hint="" display-id="0" />

                                <node index="2" text="I can answer your questions, summarize content, and provide creative inspiration." resource-id="com.transsion.aivoiceassistant:id/tv_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,623][984,776]" drawing-order="3" hint="" display-id="0" />

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_replace" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,824][984,896]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="Refresh" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,834][894,887]" drawing-order="2" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_replace" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,824][984,896]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,926][984,1039]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Cavaliers' 2025 Offseason Strategy" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,956][936,1009]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1069][984,1182]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Nature: Kimi K2 a 'DeepSeek Moment'" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,1099][936,1152]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1212][984,1325]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="Switch to a female voice" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,1242][936,1295]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1421][1032,1534]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[636,1421][1032,1534]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open wifi" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[684,1451][876,1504]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,1426][1002,1528]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_loading" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1582][582,1729]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_loading_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1612][168,1684]" drawing-order="1" hint="" display-id="0" />

                            <node index="1" text="Ella is thinking…" resource-id="com.transsion.aivoiceassistant:id/tv_loading" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[198,1621][534,1674]" drawing-order="2" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="22:26" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="22:26" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

</hierarchy>
2025-07-21 22:28:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:02 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:28:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 22:28:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 22:28:05 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 22:28:05 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_check_area:129 | check_area节点不存在
2025-07-21 22:28:07 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:252 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,994]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,994]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,384][984,397]" drawing-order="1" hint="" display-id="0">

                                <node index="2" text="I can answer your questions, summarize content, and provide creative inspiration." resource-id="com.transsion.aivoiceassistant:id/tv_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[306,384][984,397]" drawing-order="3" hint="" display-id="0" />

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_replace" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,445][984,517]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="Refresh" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[739,455][894,508]" drawing-order="2" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_replace" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,445][984,517]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,547][984,660]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Konaté's Future: PSG Out?" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,577][936,630]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,690][984,803]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Bill Gates Buys ND Farmland" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,720][936,773]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,833][984,946]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="How to use Ask About Screen" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,863][936,916]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1042][1032,1155]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[636,1042][1032,1155]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open wifi" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[684,1072][876,1125]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,1047][1002,1149]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1203][1032,1457]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1203][632,1457]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1203][632,1457]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Wi-Fi is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1233][584,1286]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[512,1340][584,1412]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1487][1032,1838]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1487][1032,1838]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1517][1032,1667]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1517][1032,1667]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Wi-Fi" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1559][834,1620]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1559][984,1625]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1697][1032,1817]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1730][150,1784]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1734][286,1780]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1856][1032,1964]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1856][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1856][339,1964]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1877][156,1943]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1892][195,1928]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1877][297,1943]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="22:28" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="22:28" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

</hierarchy>
