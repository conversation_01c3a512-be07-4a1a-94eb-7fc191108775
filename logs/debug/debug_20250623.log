2025-06-23 21:36:46 | DEBUG | __main__:test_basic_logging:23 | 这是一条调试日志
2025-06-23 21:36:46 | DEBUG | __main__:test_categorized_logging:38 | 这是一条调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_categorized_logging:39 | 调试变量值: x=10, y=20
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 1 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 2 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 3 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 4 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 5 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 6 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 7 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 8 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 9 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 10 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 11 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 12 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 13 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 14 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 15 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 16 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 17 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 18 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 19 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_log_volume:94 | 调试日志 20 - 调试信息
2025-06-23 21:36:46 | DEBUG | __main__:test_different_scenarios:132 | 调试场景: 调试元素定位
2025-06-23 21:36:46 | DEBUG | __main__:test_different_scenarios:133 | 元素定位: 找到按钮元素
2025-06-23 21:36:46 | DEBUG | __main__:test_different_scenarios:134 | 元素属性: text='确定', enabled=true
2025-06-23 21:56:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:56:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:56:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:56:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:56:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:56:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:56:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:56:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:56:59 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:56:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:57:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:57:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:57:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:57:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:57:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 21:57:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:13 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 21:57:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 21:57:16 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 21:57:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:57:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:57:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:28 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:57:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:57:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:57:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 21:57:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:41 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: what time is it
2025-06-23 21:57:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 21:57:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:57:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:57:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:57:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:54 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:57:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:57:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:57:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:57:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:57:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:57:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:57:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:57:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 21:58:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:09 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: close bluetooth
2025-06-23 21:58:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 21:58:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:58:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:58:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:23 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:58:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:58:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:58:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:58:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音停止按钮]: False
2025-06-23 21:58:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [停止录制按钮]: False
2025-06-23 21:58:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [停止按钮(英文)]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [完成按钮]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [停止按钮(文本)]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [完成按钮(文本)]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [确定按钮]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:58:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:38 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: None
2025-06-23 21:58:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 21:58:39 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [文本输入框(备选)]: None
2025-06-23 21:58:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:58:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:58:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:58:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:49 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:58:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:58:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:58:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:58:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:59:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 21:59:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 21:59:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:59:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:59:29 | DEBUG | pages.apps.ella.main_page:start_voice_input:526 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 21:59:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 21:59:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 21:59:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 21:59:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 21:59:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 21:59:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:59:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:59:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:59:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:59:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:59:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:59:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:59:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:59:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:59:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:59:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:59:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:59:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 21:59:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 21:59:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 21:59:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 21:59:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 21:59:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 21:59:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 21:59:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 21:59:43 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 21:59:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 22:07:24 | DEBUG | utils.tts_utils:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:07:24 | DEBUG | utils.tts_utils:_play_with_playsound:309 | playsound不可用
2025-06-23 22:07:25 | DEBUG | utils.tts_utils:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687642.wav
2025-06-23 22:07:26 | DEBUG | utils.tts_utils:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:07:26 | DEBUG | utils.tts_utils:_play_with_playsound:309 | playsound不可用
2025-06-23 22:07:27 | DEBUG | utils.tts_utils:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687646.wav
2025-06-23 22:07:29 | DEBUG | utils.tts_utils:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:07:29 | DEBUG | utils.tts_utils:_play_with_playsound:309 | playsound不可用
2025-06-23 22:07:29 | DEBUG | utils.tts_utils:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750687648.wav
2025-06-23 22:07:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:31 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:07:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:07:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:07:37 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:07:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:07:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:07:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:07:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:07:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:07:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:07:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:07:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:07:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:07:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:07:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:07:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:07:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:07:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:07:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:07:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:07:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:07:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:07:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:07:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:07:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:07:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:07:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:07:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:07:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:07:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:07:51 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:07:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 22:08:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:08:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:08:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:05 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:08:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:08:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:08:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:08:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:08:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:08:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:08:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:08:16 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:18 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: what time is it
2025-06-23 22:08:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 22:08:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:29 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:08:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:33 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:08:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:08:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:08:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:08:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:08:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:08:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:08:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:08:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:08:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:46 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: close bluetooth
2025-06-23 22:08:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 22:08:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:08:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:09:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:09:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:09:04 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:09:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:09:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:09:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:09:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:09:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:09:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:09:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:09:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:09:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:09:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:09:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:09:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:09:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:09:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:09:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:09:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:09:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:09:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:09:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:09:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:09:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:09:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:09:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:16 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:17 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:09:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 22:09:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:09:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:09:30 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:09:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 22:12:31 | DEBUG | utils.excel_utils:read_test_data:69 | 读取测试数据: 测试开启蓝牙 - open bluetooth
2025-06-23 22:12:31 | DEBUG | utils.excel_utils:read_test_data:69 | 读取测试数据: 测试关闭蓝牙 - close bluetooth
2025-06-23 22:12:31 | DEBUG | utils.excel_utils:read_test_data:69 | 读取测试数据: 测试打开时钟 - open clock
2025-06-23 22:12:31 | DEBUG | utils.excel_utils:read_test_data:69 | 读取测试数据: 测试查询天气 - what is the weather today
2025-06-23 22:12:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:12:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:12:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:12:44 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:12:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:12:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:12:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:12:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:12:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:12:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:12:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:12:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:12:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:12:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:12:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:12:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:12:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:12:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:12:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:12:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:12:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:12:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:12:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:12:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:12:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:12:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:12:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:12:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:12:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:54 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:12:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:12:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:13:00 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:13:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 22:15:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:15:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:14 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:15:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:15:15 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:17 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:19 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:21 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:22 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:23 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:24 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:15:25 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:27 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:28 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:15:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 22:15:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 22:15:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 22:15:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:50 | DEBUG | pages.apps.ella.main_page:start_voice_input:653 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 22:15:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 22:15:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 22:15:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 22:15:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:56 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 22:15:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 22:15:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 22:15:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 22:15:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 22:15:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 22:15:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:15:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:16:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:16:00 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 22:16:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:16:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 22:16:03 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 22:16:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 22:25:46 | DEBUG | utils.tts_utils:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:25:46 | DEBUG | utils.tts_utils:_play_with_playsound:309 | playsound不可用
2025-06-23 22:25:47 | DEBUG | utils.tts_utils:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688744.wav
2025-06-23 22:26:03 | DEBUG | utils.tts_utils:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:26:03 | DEBUG | utils.tts_utils:_play_with_playsound:309 | playsound不可用
2025-06-23 22:26:03 | DEBUG | utils.tts_utils:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688762.wav
2025-06-23 22:30:05 | DEBUG | __main__:_play_with_pygame:296 | pygame播放失败: Unknown WAVE format
2025-06-23 22:30:05 | DEBUG | __main__:_play_with_playsound:309 | playsound不可用
2025-06-23 22:30:06 | DEBUG | __main__:speak_text:380 | 清理临时文件: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750688999.wav
2025-06-23 22:36:03 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:04 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: test_audio_files\hello_test.wav
2025-06-23 22:36:05 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:06 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: test_audio_files\nihao_test.wav
2025-06-23 22:36:07 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:08 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: test_audio_files\open_bluetooth.wav
2025-06-23 22:36:09 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:10 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: test_audio_files\close_bluetooth.wav
2025-06-23 22:36:11 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:12 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: test_audio_files\what_time.wav
2025-06-23 22:36:13 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:14 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689373.wav
2025-06-23 22:36:15 | DEBUG | utils.tts_utils:_gtts_generate:229 | 开始Google TTS生成...
2025-06-23 22:36:16 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689375.wav
2025-06-23 22:36:17 | DEBUG | utils.tts_utils:_pyttsx3_generate:265 | 初始化pyttsx3引擎...
2025-06-23 22:36:17 | DEBUG | utils.tts_utils:_pyttsx3_generate:277 | 选择中文语音: Microsoft Huihui Desktop - Chinese (Simplified)
2025-06-23 22:36:17 | DEBUG | utils.tts_utils:_pyttsx3_generate:290 | 开始pyttsx3生成...
2025-06-23 22:36:18 | DEBUG | utils.tts_utils:_verify_audio_file:378 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689377.wav (86660 bytes)
2025-06-23 22:36:18 | DEBUG | utils.tts_utils:_verify_audio_file:378 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689377.wav (86660 bytes)
2025-06-23 22:36:22 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:23 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689382.wav
2025-06-23 22:36:23 | DEBUG | utils.tts_utils:speak_text:517 | 步骤1: 生成语音文件...
2025-06-23 22:36:23 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:23 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689383.wav
2025-06-23 22:36:24 | DEBUG | utils.tts_utils:speak_text:517 | 步骤1: 生成语音文件...
2025-06-23 22:36:24 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:25 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689384.wav
2025-06-23 22:36:25 | DEBUG | utils.tts_utils:speak_text:517 | 步骤1: 生成语音文件...
2025-06-23 22:36:25 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:27 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689385.wav
2025-06-23 22:36:27 | DEBUG | utils.tts_utils:speak_text:517 | 步骤1: 生成语音文件...
2025-06-23 22:36:27 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:28 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689387.wav
2025-06-23 22:36:28 | DEBUG | utils.tts_utils:speak_text:517 | 步骤1: 生成语音文件...
2025-06-23 22:36:28 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:36:30 | DEBUG | utils.tts_utils:_verify_audio_file:350 | WAV文件头部无效: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689388.wav
2025-06-23 22:38:27 | DEBUG | utils.tts_utils:speak_text:532 | 步骤1: 生成语音文件...
2025-06-23 22:38:27 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:38:28 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689507.wav
2025-06-23 22:38:28 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689507.wav (10512 bytes)
2025-06-23 22:38:28 | DEBUG | utils.tts_utils:speak_text:539 | 语音生成耗时: 1.33秒
2025-06-23 22:38:28 | DEBUG | utils.tts_utils:speak_text:542 | 步骤2: 播放语音文件...
2025-06-23 22:38:29 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689507.wav
2025-06-23 22:38:29 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689507.wav (10512 bytes)
2025-06-23 22:38:29 | DEBUG | utils.tts_utils:_play_with_pygame:458 | pygame播放失败: Unknown WAVE format
2025-06-23 22:38:29 | DEBUG | utils.tts_utils:_play_with_playsound:471 | playsound不可用
2025-06-23 22:38:30 | DEBUG | utils.tts_utils:speak_text:548 | 音频播放耗时: 1.51秒
2025-06-23 22:38:30 | DEBUG | utils.tts_utils:speak_text:553 | 步骤3: 清理临时文件...
2025-06-23 22:38:30 | DEBUG | utils.tts_utils:speak_text:555 | ✅ 清理临时文件: tts_1750689507.wav
2025-06-23 22:39:22 | DEBUG | utils.tts_utils:speak_text:532 | 步骤1: 生成语音文件...
2025-06-23 22:39:22 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689562.wav
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689562.wav (10512 bytes)
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:speak_text:539 | 语音生成耗时: 1.27秒
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:speak_text:542 | 步骤2: 播放语音文件...
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689562.wav
2025-06-23 22:39:23 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: C:\Users\<USER>\AppData\Local\Temp\app_test_tts\tts_1750689562.wav (10512 bytes)
2025-06-23 22:39:24 | DEBUG | utils.tts_utils:_play_with_pygame:458 | pygame播放失败: Unknown WAVE format
2025-06-23 22:39:24 | DEBUG | utils.tts_utils:_play_with_playsound:471 | playsound不可用
2025-06-23 22:39:25 | DEBUG | utils.tts_utils:speak_text:548 | 音频播放耗时: 1.73秒
2025-06-23 22:39:25 | DEBUG | utils.tts_utils:speak_text:553 | 步骤3: 清理临时文件...
2025-06-23 22:39:25 | DEBUG | utils.tts_utils:speak_text:555 | ✅ 清理临时文件: tts_1750689562.wav
2025-06-23 22:39:25 | DEBUG | utils.tts_utils:_edge_tts_generate:193 | 开始Edge TTS异步生成...
2025-06-23 22:39:26 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: open_bluetooth_test.wav
2025-06-23 22:39:26 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: open_bluetooth_test.wav (10512 bytes)
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: open_bluetooth_test.wav
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: open_bluetooth_test.wav (10512 bytes)
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_verify_audio_file:377 | WAV文件头部可能异常，但继续: open_bluetooth_test.wav
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_verify_audio_file:393 | 音频文件验证通过: open_bluetooth_test.wav (10512 bytes)
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_play_with_pygame:458 | pygame播放失败: Unknown WAVE format
2025-06-23 22:39:27 | DEBUG | utils.tts_utils:_play_with_playsound:471 | playsound不可用
2025-06-23 22:48:14 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:15 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:48:15 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:48:16 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:48:16 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:48:16 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:17 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\close_bluetooth.wav
2025-06-23 22:48:17 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\close_bluetooth.wav (10368 bytes)
2025-06-23 22:48:18 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\close_bluetooth.wav
2025-06-23 22:48:18 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\close_bluetooth.wav (10368 bytes)
2025-06-23 22:48:18 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:20 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:48:20 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:48:20 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:48:20 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:48:21 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:24 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\關閉藍牙.wav
2025-06-23 22:48:24 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\關閉藍牙.wav (9648 bytes)
2025-06-23 22:48:24 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\關閉藍牙.wav
2025-06-23 22:48:24 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\關閉藍牙.wav (9648 bytes)
2025-06-23 22:48:25 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:26 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\what_time_is_it.wav
2025-06-23 22:48:26 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\what_time_is_it.wav (10944 bytes)
2025-06-23 22:48:27 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\what_time_is_it.wav
2025-06-23 22:48:27 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\what_time_is_it.wav (10944 bytes)
2025-06-23 22:48:27 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:29 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:48:29 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:48:30 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:48:30 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:48:30 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:32 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:33 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:35 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Bonjour.wav
2025-06-23 22:48:35 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Bonjour.wav (7632 bytes)
2025-06-23 22:48:35 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Bonjour.wav
2025-06-23 22:48:35 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Bonjour.wav (7632 bytes)
2025-06-23 22:48:35 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:37 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hallo.wav
2025-06-23 22:48:37 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Hallo.wav (7056 bytes)
2025-06-23 22:48:37 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hallo.wav
2025-06-23 22:48:37 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Hallo.wav (7056 bytes)
2025-06-23 22:48:38 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:39 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:39 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:40 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:40 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:40 | DEBUG | utils.tts_utils:_edge_tts_generate:301 | 删除已存在的文件: data\en\test_cache_functionality.wav
2025-06-23 22:48:40 | DEBUG | utils.tts_utils:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:48:41 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:41 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:41 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:41 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:42 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Bonjour.wav
2025-06-23 22:48:42 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Bonjour.wav (7632 bytes)
2025-06-23 22:48:42 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\close_bluetooth.wav
2025-06-23 22:48:42 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\close_bluetooth.wav (10368 bytes)
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hallo.wav
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Hallo.wav (7056 bytes)
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:43 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:44 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\what_time_is_it.wav
2025-06-23 22:48:44 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\what_time_is_it.wav (10944 bytes)
2025-06-23 22:48:47 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\こんにちは.wav (0 bytes)
2025-06-23 22:48:47 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:48:47 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:48:48 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\關閉藍牙.wav
2025-06-23 22:48:48 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\關閉藍牙.wav (9648 bytes)
2025-06-23 22:48:51 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\안녕하세요.wav (0 bytes)
2025-06-23 22:48:51 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:48:51 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Bonjour.wav
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Bonjour.wav (7632 bytes)
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\close_bluetooth.wav
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\close_bluetooth.wav (10368 bytes)
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hallo.wav
2025-06-23 22:48:52 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Hallo.wav (7056 bytes)
2025-06-23 22:48:53 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:48:53 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:48:53 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:48:53 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:48:54 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\what_time_is_it.wav
2025-06-23 22:48:54 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\what_time_is_it.wav (10944 bytes)
2025-06-23 22:48:57 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\こんにちは.wav (0 bytes)
2025-06-23 22:48:57 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:48:57 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:48:57 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\關閉藍牙.wav
2025-06-23 22:48:57 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\關閉藍牙.wav (9648 bytes)
2025-06-23 22:49:00 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\안녕하세요.wav (0 bytes)
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:speak_text:650 | 步骤1: 生成语音文件...
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:speak_text:657 | 语音生成耗时: 0.40秒
2025-06-23 22:49:01 | DEBUG | utils.tts_utils:speak_text:660 | 步骤2: 播放语音文件...
2025-06-23 22:49:02 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:49:02 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 22:49:02 | DEBUG | utils.tts_utils:_play_with_pygame:575 | pygame播放失败: Unknown WAVE format
2025-06-23 22:49:02 | DEBUG | utils.tts_utils:_play_with_playsound:588 | playsound不可用
2025-06-23 22:49:03 | DEBUG | utils.tts_utils:speak_text:666 | 音频播放耗时: 1.80秒
2025-06-23 22:49:03 | DEBUG | utils.tts_utils:speak_text:671 | 步骤3: 清理临时文件...
2025-06-23 22:49:03 | DEBUG | utils.tts_utils:speak_text:673 | ✅ 清理临时文件: open_bluetooth.wav
2025-06-23 22:49:04 | DEBUG | utils.tts_utils:speak_text:650 | 步骤1: 生成语音文件...
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:speak_text:657 | 语音生成耗时: 0.52秒
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:speak_text:660 | 步骤2: 播放语音文件...
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\打开蓝牙.wav
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_play_with_pygame:575 | pygame播放失败: Unknown WAVE format
2025-06-23 22:49:05 | DEBUG | utils.tts_utils:_play_with_playsound:588 | playsound不可用
2025-06-23 22:49:06 | DEBUG | utils.tts_utils:speak_text:666 | 音频播放耗时: 0.97秒
2025-06-23 22:49:06 | DEBUG | utils.tts_utils:speak_text:671 | 步骤3: 清理临时文件...
2025-06-23 22:49:06 | DEBUG | utils.tts_utils:speak_text:673 | ✅ 清理临时文件: 打开蓝牙.wav
2025-06-23 22:49:07 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Bonjour.wav
2025-06-23 22:49:07 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Bonjour.wav (7632 bytes)
2025-06-23 22:49:07 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\close_bluetooth.wav
2025-06-23 22:49:07 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\close_bluetooth.wav (10368 bytes)
2025-06-23 22:49:08 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hallo.wav
2025-06-23 22:49:08 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\Hallo.wav (7056 bytes)
2025-06-23 22:49:08 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\test_cache_functionality.wav
2025-06-23 22:49:08 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\test_cache_functionality.wav (15552 bytes)
2025-06-23 22:49:09 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\what_time_is_it.wav
2025-06-23 22:49:09 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\what_time_is_it.wav (10944 bytes)
2025-06-23 22:49:12 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\こんにちは.wav (0 bytes)
2025-06-23 22:49:12 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\關閉藍牙.wav
2025-06-23 22:49:12 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\en\關閉藍牙.wav (9648 bytes)
2025-06-23 22:49:15 | DEBUG | utils.tts_utils:_verify_audio_file:471 | 音频文件太小: data\en\안녕하세요.wav (0 bytes)
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\Bonjour.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\close_bluetooth.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\Hallo.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\test_cache_functionality.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\what_time_is_it.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\こんにちは.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\關閉藍牙.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:clear_cache:818 | 删除缓存文件: data\en\안녕하세요.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\zh\现在几点了.wav
2025-06-23 22:49:16 | DEBUG | utils.tts_utils:_verify_audio_file:510 | 音频文件验证通过: data\zh\现在几点了.wav (10512 bytes)
2025-06-23 22:51:54 | DEBUG | __main__:speak_text:650 | 步骤1: 生成语音文件...
2025-06-23 22:51:54 | DEBUG | __main__:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:52:00 | DEBUG | __main__:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hello_world.wav
2025-06-23 22:52:00 | DEBUG | __main__:_verify_audio_file:510 | 音频文件验证通过: data\en\Hello_world.wav (12816 bytes)
2025-06-23 22:52:00 | DEBUG | __main__:speak_text:657 | 语音生成耗时: 5.75秒
2025-06-23 22:52:00 | DEBUG | __main__:speak_text:660 | 步骤2: 播放语音文件...
2025-06-23 22:52:00 | DEBUG | __main__:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\Hello_world.wav
2025-06-23 22:52:00 | DEBUG | __main__:_verify_audio_file:510 | 音频文件验证通过: data\en\Hello_world.wav (12816 bytes)
2025-06-23 22:52:01 | DEBUG | __main__:_play_with_pygame:575 | pygame播放失败: Unknown WAVE format
2025-06-23 22:52:01 | DEBUG | __main__:_play_with_playsound:588 | playsound不可用
2025-06-23 22:52:02 | DEBUG | __main__:speak_text:666 | 音频播放耗时: 1.69秒
2025-06-23 22:52:02 | DEBUG | __main__:speak_text:671 | 步骤3: 清理临时文件...
2025-06-23 22:52:02 | DEBUG | __main__:speak_text:673 | ✅ 清理临时文件: Hello_world.wav
2025-06-23 22:54:36 | DEBUG | __main__:_edge_tts_generate:310 | 开始Edge TTS异步生成...
2025-06-23 22:54:42 | DEBUG | __main__:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:54:42 | DEBUG | __main__:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (10512 bytes)
2025-06-23 22:54:42 | DEBUG | __main__:_verify_audio_file:494 | WAV文件头部可能异常，但继续: data\en\open_bluetooth.wav
2025-06-23 22:54:42 | DEBUG | __main__:_verify_audio_file:510 | 音频文件验证通过: data\en\open_bluetooth.wav (10512 bytes)
2025-06-23 23:00:56 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:00:56 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:00:57 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:00:57 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:00:57 | DEBUG | utils.tts_utils:_edge_tts_generate:372 | 开始Edge TTS异步生成...
2025-06-23 23:00:58 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_relative_path.wav
2025-06-23 23:00:58 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_relative_path.wav (13968 bytes)
2025-06-23 23:00:58 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_relative_path.wav
2025-06-23 23:00:58 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_relative_path.wav (13968 bytes)
2025-06-23 23:00:59 | DEBUG | utils.tts_utils:_edge_tts_generate:372 | 开始Edge TTS异步生成...
2025-06-23 23:01:00 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\测试相对路径.wav
2025-06-23 23:01:00 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\测试相对路径.wav (12528 bytes)
2025-06-23 23:01:01 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\测试相对路径.wav
2025-06-23 23:01:01 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\测试相对路径.wav (12528 bytes)
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_relative_path.wav
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_relative_path.wav (13968 bytes)
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\测试相对路径.wav
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\en\测试相对路径.wav (12528 bytes)
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:556 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\现在几点了.wav
2025-06-23 23:01:02 | DEBUG | utils.tts_utils:_verify_audio_file:572 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\现在几点了.wav (10512 bytes)
2025-06-23 23:02:45 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:02:45 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:02:46 | DEBUG | utils.tts_utils:_edge_tts_generate:374 | 开始Edge TTS异步生成...
2025-06-23 23:02:47 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:02:47 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:02:48 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:02:48 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:02:48 | DEBUG | utils.tts_utils:_edge_tts_generate:374 | 开始Edge TTS异步生成...
2025-06-23 23:02:49 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:02:49 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 23:02:49 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:02:49 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 23:02:50 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:02:50 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:02:50 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_relative_path.wav
2025-06-23 23:02:50 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_relative_path.wav (13968 bytes)
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\测试相对路径.wav
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\en\测试相对路径.wav (12528 bytes)
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:558 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\现在几点了.wav
2025-06-23 23:02:51 | DEBUG | utils.tts_utils:_verify_audio_file:574 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\现在几点了.wav (10512 bytes)
2025-06-23 23:03:41 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:03:41 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:03:41 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: zh-CN -> en
2025-06-23 23:03:41 | DEBUG | utils.tts_utils:_edge_tts_generate:367 | 删除已存在的文件: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:03:41 | DEBUG | utils.tts_utils:_edge_tts_generate:376 | 开始Edge TTS异步生成...
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_verify_audio_file:560 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_verify_audio_file:576 | 音频文件验证通过: D:\PythonProject\app_test\data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_verify_audio_file:560 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\打开蓝牙.wav
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_verify_audio_file:576 | 音频文件验证通过: D:\PythonProject\app_test\data\en\打开蓝牙.wav (9792 bytes)
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: zh-CN -> en
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: en-US -> en
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_edge_tts_generate:367 | 删除已存在的文件: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:03:43 | DEBUG | utils.tts_utils:_edge_tts_generate:376 | 开始Edge TTS异步生成...
2025-06-23 23:03:45 | DEBUG | utils.tts_utils:_verify_audio_file:560 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:03:45 | DEBUG | utils.tts_utils:_verify_audio_file:576 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:03:45 | DEBUG | utils.tts_utils:_verify_audio_file:560 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:03:45 | DEBUG | utils.tts_utils:_verify_audio_file:576 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:03:45 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: en-US -> en
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: zh-CN -> en
2025-06-23 23:03:56 | DEBUG | utils.tts_utils:_get_language_code:144 | 语言代码映射: en-US -> en
2025-06-23 23:04:05 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:04:05 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:04:06 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:04:06 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:04:30 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:04:30 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh -> zh
2025-06-23 23:04:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en -> en
2025-06-23 23:04:39 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:04:39 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:04:40 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:04:40 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:04:41 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试中文路径.wav
2025-06-23 23:04:41 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试中文路径.wav (12240 bytes)
2025-06-23 23:04:42 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试中文路径.wav
2025-06-23 23:04:42 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试中文路径.wav (12240 bytes)
2025-06-23 23:04:42 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:04:42 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:04:42 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:04:43 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_english_path.wav
2025-06-23 23:04:43 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_english_path.wav (13248 bytes)
2025-06-23 23:04:44 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_english_path.wav
2025-06-23 23:04:44 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_english_path.wav (13248 bytes)
2025-06-23 23:04:44 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:08:06 | DEBUG | __main__:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:08:06 | DEBUG | __main__:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:08:06 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:08:06 | DEBUG | __main__:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:08:12 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:08:12 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\open_bluetooth.wav (10512 bytes)
2025-06-23 23:08:12 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:08:12 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\open_bluetooth.wav (10512 bytes)
2025-06-23 23:08:12 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:08:40 | DEBUG | __main__:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:08:40 | DEBUG | __main__:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:08:41 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:08:41 | DEBUG | __main__:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:08:47 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:08:47 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:08:47 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:08:47 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:08:47 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:09:03 | DEBUG | __main__:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:09:03 | DEBUG | __main__:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:09:03 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:09:03 | DEBUG | __main__:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:09:09 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:09:09 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_bluetooth.wav (12096 bytes)
2025-06-23 23:09:09 | DEBUG | __main__:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:09:09 | DEBUG | __main__:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_bluetooth.wav (12096 bytes)
2025-06-23 23:09:09 | DEBUG | __main__:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:23 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:25 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:26 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:26 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:26 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:26 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:27 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:27 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:27 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:27 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:14:28 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_bluetooth.wav (12096 bytes)
2025-06-23 23:14:29 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:29 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:29 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:14:29 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\open_bluetooth.wav (10512 bytes)
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:30 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:31 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:32 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\打开蓝牙.wav (9792 bytes)
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\打开蓝牙.wav (9792 bytes)
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:33 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\打开蓝牙.wav (9792 bytes)
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:34 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:35 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:35 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:35 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:35 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:36 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:14:36 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_wifi.wav (12096 bytes)
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_wifi.wav (12096 bytes)
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_wifi.wav (12096 bytes)
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:38 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:38 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:38 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:38 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\关闭WiFi.wav (10080 bytes)
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\关闭WiFi.wav (10080 bytes)
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:40 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\关闭WiFi.wav (10080 bytes)
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:41 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:14:42 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:42 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:42 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:42 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:44 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:45 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:46 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:48 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:49 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:50 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:51 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:52 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:14:52 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:14:52 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:52 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:53 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:53 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:53 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:53 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:54 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:55 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:56 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:58 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:59 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:15:00 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:15:00 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:15:00 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:15:00 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:15:01 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:15:01 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_bluetooth.wav (12096 bytes)
2025-06-23 23:15:01 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:15:01 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_wifi.wav (12096 bytes)
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:15:02 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:15:03 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:15:03 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:15:03 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:15:03 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\open_bluetooth.wav (10512 bytes)
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:15:04 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\关闭WiFi.wav (10080 bytes)
2025-06-23 23:15:05 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:15:05 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\打开蓝牙.wav (9792 bytes)
2025-06-23 23:26:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:26:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:26:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:26:53 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:26:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 23:26:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 23:26:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:26:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:26:58 | DEBUG | pages.apps.ella.main_page:start_voice_input:758 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 23:26:58 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 23:26:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 23:26:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 23:26:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 23:26:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:04 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:09 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 23:27:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:13 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 23:27:16 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
2025-06-23 23:27:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:26 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:28 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 23:27:30 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 23:27:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:32 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:33 | DEBUG | pages.apps.ella.main_page:start_voice_input:758 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 23:27:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 23:27:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 23:27:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 23:27:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 23:27:34 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:36 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:38 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:27:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:27:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:27:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:27:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:27:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:27:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:42 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 23:27:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:27:48 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 23:27:50 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:13 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:13 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:14 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:14 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:15 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:15 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:15 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:15 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:15 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:16 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:16 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:17 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:17 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:18 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:18 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:19 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav
2025-06-23 23:34:19 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav (14544 bytes)
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav (14544 bytes)
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav (14544 bytes)
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:20 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:21 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:21 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:23 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav
2025-06-23 23:34:23 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav (14400 bytes)
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav (14400 bytes)
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav (14400 bytes)
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:24 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:25 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:34:26 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:26 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:26 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:26 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:28 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:28 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:29 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:29 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:30 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:30 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:31 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:32 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:32 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:33 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:33 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:34 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:34 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:35 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:36 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:36 | DEBUG | utils.tts_utils:_edge_tts_generate:380 | 开始Edge TTS异步生成...
2025-06-23 23:34:37 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:37 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:37 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:37 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:37 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:38 | DEBUG | pages.apps.ella.main_page:_play_voice_command_file:551 | 使用TTS服务: edge_tts
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: en-US -> en
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:38 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:39 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:39 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:39 | DEBUG | utils.tts_utils:_play_with_pygame:645 | pygame播放失败: Unknown WAVE format
2025-06-23 23:34:39 | DEBUG | utils.tts_utils:_play_with_playsound:658 | playsound不可用
2025-06-23 23:34:40 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_bluetooth.wav
2025-06-23 23:34:40 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_bluetooth.wav (12096 bytes)
2025-06-23 23:34:40 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\close_wifi.wav
2025-06-23 23:34:40 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\close_wifi.wav (12096 bytes)
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\hi.wav
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\hi.wav (9216 bytes)
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\open_bluetooth.wav
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\open_bluetooth.wav (12096 bytes)
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:34:41 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\please_turn_on_the_bluetooth_connection.wav (17712 bytes)
2025-06-23 23:34:42 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_logic.wav
2025-06-23 23:34:42 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_logic.wav (13824 bytes)
2025-06-23 23:34:42 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_cache_priority.wav
2025-06-23 23:34:42 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_cache_priority.wav (14112 bytes)
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_file_verification.wav
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_file_verification.wav (15552 bytes)
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\test_real_voice_cache.wav (14544 bytes)
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav
2025-06-23 23:34:43 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\en\what_time_is_it_now.wav (13392 bytes)
2025-06-23 23:34:44 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\open_bluetooth.wav
2025-06-23 23:34:44 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\open_bluetooth.wav (10512 bytes)
2025-06-23 23:34:44 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\关闭WiFi.wav
2025-06-23 23:34:44 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\关闭WiFi.wav (10080 bytes)
2025-06-23 23:34:45 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\打开蓝牙.wav
2025-06-23 23:34:45 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\打开蓝牙.wav (9792 bytes)
2025-06-23 23:34:45 | DEBUG | utils.tts_utils:_verify_audio_file:564 | WAV文件头部可能异常，但继续: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav
2025-06-23 23:34:45 | DEBUG | utils.tts_utils:_verify_audio_file:580 | 音频文件验证通过: D:\PythonProject\app_test\data\zh\测试真实语音缓存.wav (14400 bytes)
2025-06-23 23:35:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:35:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:35:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:35:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:35:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 23:35:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 23:36:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:01 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:02 | DEBUG | pages.apps.ella.main_page:start_voice_input:820 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 23:36:02 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 23:36:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 23:36:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 23:36:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 23:36:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 23:36:03 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 23:36:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:05 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:06 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:07 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:08 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:10 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:11 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:12 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:13 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 23:36:14 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:18 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:18 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 23:36:20 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 23:36:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:33 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:34 | DEBUG | utils.tts_utils:_get_project_root:119 | 找到项目根目录: D:\PythonProject\app_test (标识: requirements.txt)
2025-06-23 23:36:34 | DEBUG | utils.tts_utils:__init__:31 | TTS数据目录: D:\PythonProject\app_test\data
2025-06-23 23:36:35 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:36:35 | DEBUG | utils.tts_utils:_get_language_code:148 | 语言代码映射: zh-CN -> zh
2025-06-23 23:36:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:35 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮]: False
2025-06-23 23:36:37 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(备选)]: True
2025-06-23 23:36:39 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:40 | DEBUG | pages.apps.ella.main_page:start_voice_input:820 | 点击后未进入语音录制状态，尝试下一个按钮
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音输入按钮(描述)]: False
2025-06-23 23:36:40 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(英文描述)]: False
2025-06-23 23:36:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [麦克风按钮]: False
2025-06-23 23:36:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音按钮(文本)]: False
2025-06-23 23:36:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [图像按钮]: False
2025-06-23 23:36:41 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: False
2025-06-23 23:36:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:43 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:44 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:45 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:46 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:47 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [语音录制按钮]: False
2025-06-23 23:36:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态指示器]: False
2025-06-23 23:36:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态(英文)]: False
2025-06-23 23:36:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制状态文本]: False
2025-06-23 23:36:48 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [正在录制文本]: False
2025-06-23 23:36:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [录制进度条]: False
2025-06-23 23:36:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:49 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:51 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [文本输入框(备选)]: True
2025-06-23 23:36:52 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:55 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [输入框]: True
2025-06-23 23:36:55 | DEBUG | core.base_element:get_text:246 | 获取元素文本 [输入框]: open bluetooth
2025-06-23 23:36:57 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: False
2025-06-23 23:36:59 | DEBUG | core.base_element:is_exists:109 | 元素存在性检查 [TTS播放按钮]: True
