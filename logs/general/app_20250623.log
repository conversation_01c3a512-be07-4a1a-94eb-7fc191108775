2025-06-23 21:36:46 | INFO | __main__:test_basic_logging:20 | 这是一条普通信息日志
2025-06-23 21:36:46 | WARNING | __main__:test_basic_logging:21 | 这是一条警告日志
2025-06-23 21:36:46 | ERROR | __main__:test_basic_logging:22 | 这是一条错误日志
2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:33 | 这是一条测试相关的日志
2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:42 | 性能测试: 页面加载耗时 2.5秒
2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:43 | 响应时间: 1.2秒
2025-06-23 21:36:46 | INFO | __main__:test_categorized_logging:44 | timing: 操作完成，耗时 0.8秒
2025-06-23 21:36:46 | CRITICAL | __main__:test_categorized_logging:48 | 这是一条严重错误日志
2025-06-23 21:36:46 | INFO | core.logger:log_step:205 | 📋 执行步骤: 启动Ella应用
2025-06-23 21:36:46 | INFO | core.logger:log_step:205 | 📋 执行步骤: 输入语音命令
2025-06-23 21:36:46 | INFO | core.logger:log_step:205 | 📋 执行步骤: 验证响应结果
2025-06-23 21:36:46 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 页面加载 耗时 1.800秒 包含5个元素
2025-06-23 21:36:46 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 元素查找 耗时 0.300秒 使用UIAutomator2
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 1/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 1 - 这是一条警告信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 1 - 这是一条错误信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 1 - 操作耗时 0.100秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 2/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 3/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 4/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 5/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 6/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 7/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 8/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 9/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 10/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 11/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 2 - 这是一条警告信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 12/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 13/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 14/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 15/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 16/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 2 - 操作耗时 0.250秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 17/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 18/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 19/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 20/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 21/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 3 - 这是一条警告信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 2 - 这是一条错误信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 22/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 23/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 24/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 25/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 26/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 27/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 28/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 29/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 30/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 31/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 4 - 这是一条警告信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 3 - 操作耗时 0.400秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 32/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 33/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 34/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 35/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 36/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 37/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 38/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 39/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 40/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 41/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 5 - 这是一条警告信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 3 - 这是一条错误信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 42/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 43/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 44/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 45/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 46/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 4 - 操作耗时 0.550秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 47/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 48/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 49/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 50/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 51/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 6 - 这是一条警告信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 52/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 53/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 54/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 55/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 56/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 57/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 58/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 59/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 60/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 61/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 7 - 这是一条警告信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 4 - 这是一条错误信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 5 - 操作耗时 0.700秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 62/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 63/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 64/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 65/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 66/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 67/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 68/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 69/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 70/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 71/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 8 - 这是一条警告信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 72/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 73/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 74/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 75/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 76/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 6 - 操作耗时 0.850秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 77/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 78/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 79/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 80/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 81/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 9 - 这是一条警告信息
2025-06-23 21:36:46 | ERROR | __main__:test_log_volume:91 | 错误日志 5 - 这是一条错误信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 82/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 83/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 84/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 85/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 86/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 87/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 88/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 89/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 90/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 91/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | WARNING | __main__:test_log_volume:88 | 警告日志 10 - 这是一条警告信息
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:97 | 性能日志 7 - 操作耗时 1.000秒
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 92/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 93/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 94/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 95/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 96/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 97/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 98/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 99/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | __main__:test_log_volume:85 | 批量日志测试 100/100 - 这是一条测试日志，用于验证日志轮转功能
2025-06-23 21:36:46 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 批量日志写入 耗时 0.030秒 写入100条日志
2025-06-23 21:36:46 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: 计算器测试
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:123 | 执行测试步骤: 启动应用
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:124 | 执行测试步骤: 验证功能
2025-06-23 21:36:46 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: 计算器测试 - ✅ 成功, 耗时: 1.50秒
2025-06-23 21:36:46 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: Ella语音助手测试
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:123 | 执行测试步骤: 启动应用
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:124 | 执行测试步骤: 验证功能
2025-06-23 21:36:46 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: Ella语音助手测试 - ✅ 成功, 耗时: 1.50秒
2025-06-23 21:36:46 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: 设置页面测试
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:123 | 执行测试步骤: 启动应用
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:124 | 执行测试步骤: 验证功能
2025-06-23 21:36:46 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: 设置页面测试 - ✅ 成功, 耗时: 1.50秒
2025-06-23 21:36:46 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 性能基准测试 耗时 2.300秒 基准测试
2025-06-23 21:36:46 | INFO | __main__:test_different_scenarios:129 | 性能指标: CPU使用率 15%, 内存使用 120MB
2025-06-23 21:36:46 | ERROR | __main__:test_different_scenarios:137 | 错误场景: 错误处理测试
2025-06-23 21:56:49 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 21:56:49 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 21:56:49 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 21:56:49 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 21:56:49 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 21:56:49 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 21:56:49 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 21:56:50 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 21:56:50 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 21:56:51 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 21:56:51 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 21:56:51 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 21:56:51 | INFO | __main__:test_voice_input_functionality:18 | 🎤 开始测试语音输入功能...
2025-06-23 21:56:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 21:56:51 | INFO | __main__:test_voice_input_functionality:25 | 📱 启动Ella应用...
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 21:56:51 | INFO | __main__:test_voice_input_functionality:35 | ✅ Ella应用启动成功
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:56:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:56:52 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:56:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:56:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:56:53 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:56:53 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:56:53 | INFO | __main__:test_voice_input_functionality:57 | 🎯 测试第1次语音输入: open bluetooth
2025-06-23 21:56:53 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: open bluetooth (持续时间: 3.0秒)
2025-06-23 21:56:53 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:56:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:56:54 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:56:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:56:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:56:55 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:56:55 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:56:55 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:56:56 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:56:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:56:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:56:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:56:57 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:56:57 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:57:00 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:57:00 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:57:01 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:57:03 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:57:05 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:57:07 | WARNING | pages.apps.ella.main_page:start_voice_input:558 | ❌ 无法启动语音输入
2025-06-23 21:57:07 | WARNING | pages.apps.ella.main_page:execute_voice_command:453 | 无法启动语音输入，回退到文本输入
2025-06-23 21:57:07 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 21:57:07 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:57:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:57:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:57:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:09 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:09 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:57:09 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 21:57:09 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 21:57:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:10 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 21:57:10 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 21:57:10 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 21:57:10 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 21:57:10 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:11 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:12 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 21:57:12 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 21:57:12 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:57:13 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 21:57:13 | INFO | __main__:test_voice_input_functionality:64 | 语音命令 'open bluetooth' 结果:
2025-06-23 21:57:13 | INFO | __main__:test_voice_input_functionality:65 |   - 执行时间: 20.09秒
2025-06-23 21:57:13 | INFO | __main__:test_voice_input_functionality:66 |   - 执行状态: ✅ 成功
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:wait_for_response:1036 | 快速等待AI响应，超时时间: 8秒
2025-06-23 21:57:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:wait_for_response:1051 | 初始元素数量: 13
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:_check_response_elements:1192 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1125 | 检测到响应相关元素
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:wait_for_response:1069 | ✅ 快速检测到AI响应
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:get_response_text_smart:1458 | 智能获取响应文本...
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:17 | INFO | pages.apps.ella.main_page:get_response_text:1482 | 获取AI响应文本
2025-06-23 21:57:20 | INFO | pages.apps.ella.main_page:get_response_text:1498 | 页面上所有文本元素数量: 13
2025-06-23 21:57:20 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:57:20 | INFO | pages.apps.ella.main_page:get_response_text:1506 | 找到AI响应: 蓝牙 已打开
2025-06-23 21:57:20 | INFO | pages.apps.ella.main_page:get_response_text:1519 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 21:57:20 | INFO | __main__:test_voice_input_functionality:73 |   - 响应内容: '蓝牙 已打开'
2025-06-23 21:57:23 | INFO | __main__:test_voice_input_functionality:57 | 🎯 测试第2次语音输入: what time is it
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: what time is it (持续时间: 2.5秒)
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:57:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:57:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:24 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:57:25 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:57:25 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:57:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:57:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:57:25 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:57:25 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:57:29 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:57:29 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:57:29 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:57:31 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:57:34 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:57:36 | WARNING | pages.apps.ella.main_page:start_voice_input:558 | ❌ 无法启动语音输入
2025-06-23 21:57:36 | WARNING | pages.apps.ella.main_page:execute_voice_command:453 | 无法启动语音输入，回退到文本输入
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: what time is it
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:57:36 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:57:37 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:57:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:37 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:37 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:57:38 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: what time is it
2025-06-23 21:57:38 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:38 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 21:57:38 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 21:57:38 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 21:57:38 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 21:57:39 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 21:57:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:39 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:40 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: what time is it
2025-06-23 21:57:40 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: what time is it
2025-06-23 21:57:41 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: what time is it
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:41 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: what time is it
2025-06-23 21:57:41 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:57:42 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 21:57:42 | INFO | __main__:test_voice_input_functionality:64 | 语音命令 'what time is it' 结果:
2025-06-23 21:57:42 | INFO | __main__:test_voice_input_functionality:65 |   - 执行时间: 18.91秒
2025-06-23 21:57:42 | INFO | __main__:test_voice_input_functionality:66 |   - 执行状态: ✅ 成功
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:wait_for_response:1036 | 快速等待AI响应，超时时间: 8秒
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:wait_for_response:1051 | 初始元素数量: 13
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1353 | 检测到TTS播放按钮
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1120 | 检测到TTS按钮，表示有AI响应
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:wait_for_response:1069 | ✅ 快速检测到AI响应
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:get_response_text_smart:1458 | 智能获取响应文本...
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:43 | INFO | pages.apps.ella.main_page:get_response_text:1482 | 获取AI响应文本
2025-06-23 21:57:46 | INFO | pages.apps.ella.main_page:get_response_text:1498 | 页面上所有文本元素数量: 12
2025-06-23 21:57:46 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:57:46 | INFO | pages.apps.ella.main_page:get_response_text:1506 | 找到AI响应: 蓝牙 已打开
2025-06-23 21:57:46 | INFO | pages.apps.ella.main_page:get_response_text:1519 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 21:57:46 | INFO | __main__:test_voice_input_functionality:73 |   - 响应内容: '蓝牙 已打开'
2025-06-23 21:57:49 | INFO | __main__:test_voice_input_functionality:57 | 🎯 测试第3次语音输入: close bluetooth
2025-06-23 21:57:49 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: close bluetooth (持续时间: 3.0秒)
2025-06-23 21:57:49 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:57:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:57:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:57:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:57:50 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:57:50 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:57:51 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:57:51 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:57:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:57:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:57:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:57:52 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:57:52 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:57:55 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:57:55 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:57:55 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:57:57 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:58:00 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:58:03 | WARNING | pages.apps.ella.main_page:start_voice_input:558 | ❌ 无法启动语音输入
2025-06-23 21:58:03 | WARNING | pages.apps.ella.main_page:execute_voice_command:453 | 无法启动语音输入，回退到文本输入
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: close bluetooth
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:58:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:58:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:58:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:04 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:58:05 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: close bluetooth
2025-06-23 21:58:05 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 21:58:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:05 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 21:58:05 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 21:58:06 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 21:58:06 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 21:58:06 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:07 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:08 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: close bluetooth
2025-06-23 21:58:08 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: close bluetooth
2025-06-23 21:58:09 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: close bluetooth
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:09 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: close bluetooth
2025-06-23 21:58:09 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:58:10 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 21:58:10 | INFO | __main__:test_voice_input_functionality:64 | 语音命令 'close bluetooth' 结果:
2025-06-23 21:58:10 | INFO | __main__:test_voice_input_functionality:65 |   - 执行时间: 20.66秒
2025-06-23 21:58:10 | INFO | __main__:test_voice_input_functionality:66 |   - 执行状态: ✅ 成功
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:wait_for_response:1036 | 快速等待AI响应，超时时间: 8秒
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:wait_for_response:1051 | 初始元素数量: 12
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1353 | 检测到TTS播放按钮
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1120 | 检测到TTS按钮，表示有AI响应
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:wait_for_response:1069 | ✅ 快速检测到AI响应
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:get_response_text_smart:1458 | 智能获取响应文本...
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:11 | INFO | pages.apps.ella.main_page:get_response_text:1482 | 获取AI响应文本
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:get_response_text:1498 | 页面上所有文本元素数量: 12
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:get_response_text:1506 | 找到AI响应: 蓝牙 已打开
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*关闭 -> 蓝牙 已关闭
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:get_response_text:1506 | 找到AI响应: 蓝牙 已关闭
2025-06-23 21:58:14 | INFO | pages.apps.ella.main_page:get_response_text:1519 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 21:58:14 | INFO | __main__:test_voice_input_functionality:73 |   - 响应内容: '蓝牙 已打开'
2025-06-23 21:58:17 | INFO | __main__:test_voice_input_functionality:84 | 📊 语音输入测试结果:
2025-06-23 21:58:17 | INFO | __main__:test_voice_input_functionality:85 |   - 成功率: 100.0% (3/3)
2025-06-23 21:58:17 | INFO | __main__:test_voice_input_functionality:86 |   - 总测试命令: 3
2025-06-23 21:58:17 | INFO | __main__:test_voice_input_functionality:92 | ✅ 语音输入功能测试通过！
2025-06-23 21:58:17 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 21:58:17 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 21:58:17 | INFO | __main__:test_voice_input_components:114 | 🔧 测试语音输入组件功能...
2025-06-23 21:58:17 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 21:58:17 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 21:58:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:58:18 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:58:19 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:58:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:19 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:19 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:58:20 | INFO | __main__:test_voice_input_components:149 | 🧪 测试组件: 启动语音输入
2025-06-23 21:58:20 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:58:20 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:58:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:58:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:58:21 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:58:21 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:58:24 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:58:24 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:58:24 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:58:26 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:58:29 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:58:31 | WARNING | pages.apps.ella.main_page:start_voice_input:558 | ❌ 无法启动语音输入
2025-06-23 21:58:31 | INFO | __main__:test_voice_input_components:162 |   - 结果: False
2025-06-23 21:58:31 | INFO | __main__:test_voice_input_components:163 |   - 耗时: 11.409秒
2025-06-23 21:58:32 | INFO | __main__:test_voice_input_components:149 | 🧪 测试组件: 检查录制状态
2025-06-23 21:58:33 | INFO | __main__:test_voice_input_components:162 |   - 结果: False
2025-06-23 21:58:33 | INFO | __main__:test_voice_input_components:163 |   - 耗时: 1.017秒
2025-06-23 21:58:34 | INFO | __main__:test_voice_input_components:149 | 🧪 测试组件: 停止语音输入
2025-06-23 21:58:34 | INFO | pages.apps.ella.main_page:stop_voice_input:573 | 停止语音输入...
2025-06-23 21:58:35 | INFO | pages.apps.ella.main_page:stop_voice_input:605 | 尝试点击输入框区域停止录制
2025-06-23 21:58:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:36 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:36 | INFO | __main__:test_voice_input_components:162 |   - 结果: True
2025-06-23 21:58:36 | INFO | __main__:test_voice_input_components:163 |   - 耗时: 2.448秒
2025-06-23 21:58:37 | INFO | __main__:test_voice_input_components:149 | 🧪 测试组件: 检查语音识别结果
2025-06-23 21:58:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 21:58:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 21:58:39 | INFO | pages.apps.ella.main_page:_check_voice_recognition_result:674 | 未检测到语音识别结果
2025-06-23 21:58:39 | INFO | __main__:test_voice_input_components:162 |   - 结果: False
2025-06-23 21:58:39 | INFO | __main__:test_voice_input_components:163 |   - 耗时: 1.387秒
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:177 | 📋 组件测试报告:
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:181 |   - 启动语音输入: ✅ 成功
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:181 |   - 检查录制状态: ✅ 成功
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:181 |   - 停止语音输入: ✅ 成功
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:181 |   - 检查语音识别结果: ✅ 成功
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_components:186 |   - 组件成功率: 100.0%
2025-06-23 21:58:40 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 21:58:40 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 21:58:40 | INFO | __main__:test_voice_input_fallback:203 | 🔄 测试语音输入回退机制...
2025-06-23 21:58:40 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 21:58:40 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 21:58:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:58:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:58:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:42 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:58:43 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:58:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:58:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:58:44 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:58:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:58:45 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:58:46 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:58:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:58:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:58:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:58:46 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:58:46 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:58:50 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:58:50 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:58:50 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:58:52 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:58:55 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:58:55 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 21:58:55 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 21:59:14 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 21:59:14 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 21:59:14 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 21:59:14 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 21:59:14 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 21:59:14 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 21:59:14 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 21:59:16 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 21:59:16 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 21:59:17 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 21:59:17 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 21:59:17 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 21:59:17 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 21:59:17 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 21:59:17 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-23 21:59:17 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-23 21:59:17 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-23 21:59:20 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-23 21:59:20 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-23 21:59:20 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 21:59:20 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:20 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 21:59:20 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1599 | 检查蓝牙状态
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1613 | 蓝牙状态: 关闭 (值: 0)
2025-06-23 21:59:21 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:407 | 蓝牙初始状态: 关闭
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:22 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:59:22 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:59:22 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:59:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:23 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: open bluetooth (持续时间: 3.0秒)
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:24 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:59:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:59:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:59:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:24 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:59:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:59:25 | INFO | pages.apps.ella.main_page:start_voice_input:498 | 启动语音输入...
2025-06-23 21:59:26 | INFO | pages.apps.ella.main_page:start_voice_input:516 | 找到语音按钮: 语音按钮(备选)
2025-06-23 21:59:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 21:59:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 21:59:27 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 21:59:27 | INFO | pages.apps.ella.main_page:start_voice_input:518 | ✅ 语音按钮点击成功
2025-06-23 21:59:30 | INFO | pages.apps.ella.main_page:start_voice_input:531 | 尝试长按输入框启动语音输入...
2025-06-23 21:59:30 | INFO | pages.apps.ella.main_page:start_voice_input:540 | 尝试通过坐标点击语音按钮区域...
2025-06-23 21:59:30 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (972, 2160)
2025-06-23 21:59:33 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (918, 2160)
2025-06-23 21:59:35 | INFO | pages.apps.ella.main_page:start_voice_input:551 | 尝试点击坐标 (540, 2280)
2025-06-23 21:59:38 | WARNING | pages.apps.ella.main_page:start_voice_input:558 | ❌ 无法启动语音输入
2025-06-23 21:59:38 | WARNING | pages.apps.ella.main_page:execute_voice_command:453 | 无法启动语音输入，回退到文本输入
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-06-23 21:59:38 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:928 | 确保输入框就绪...
2025-06-23 21:59:39 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:932 | 找到主输入框
2025-06-23 21:59:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:39 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:59:39 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:936 | ✅ 主输入框已激活
2025-06-23 21:59:40 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 21:59:40 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:40 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 21:59:40 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 21:59:41 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 21:59:41 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 21:59:41 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 21:59:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:41 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 21:59:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:42 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 21:59:42 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 21:59:43 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 21:59:43 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 21:59:43 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:59:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 21:59:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 21:59:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 21:59:44 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 21:59:44 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 21:59:44 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 21:59:44 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 21:59:44 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:436 | ✅ 成功执行语音命令: open bluetooth
2025-06-23 21:59:44 | INFO | pages.apps.ella.main_page:wait_for_response:1036 | 快速等待AI响应，超时时间: 10秒
2025-06-23 21:59:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:wait_for_response:1051 | 初始元素数量: 13
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:47 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:59:47 | INFO | pages.apps.ella.main_page:_check_response_elements:1192 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 21:59:47 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1125 | 检测到响应相关元素
2025-06-23 21:59:47 | INFO | pages.apps.ella.main_page:wait_for_response:1069 | ✅ 快速检测到AI响应
2025-06-23 21:59:50 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:464 | ✅ 收到AI响应
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:get_response_text_smart:1458 | 智能获取响应文本...
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:50 | INFO | pages.apps.ella.main_page:get_response_text:1482 | 获取AI响应文本
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:get_response_text:1498 | 页面上所有文本元素数量: 13
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:_is_ai_response:1321 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:get_response_text:1506 | 找到AI响应: 蓝牙 已打开
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:get_response_text:1519 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 21:59:53 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:487 | AI响应内容: '蓝牙 已打开'
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:verify_command_in_response:1635 | 验证响应是否包含命令: open bluetooth
2025-06-23 21:59:53 | INFO | pages.apps.ella.main_page:verify_command_in_response:1665 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 21:59:53 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:495 | ✅ 响应包含命令相关内容: open bluetooth
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1434 | 智能检查蓝牙状态...
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:721 | 检查当前进程是否是Ella...
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:728 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:729 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:738 | ✅ 当前在Ella应用进程
2025-06-23 21:59:55 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1599 | 检查蓝牙状态
2025-06-23 21:59:56 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1613 | 蓝牙状态: 开启 (值: 1)
2025-06-23 21:59:56 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:507 | 蓝牙最终状态: 开启
2025-06-23 21:59:56 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:518 | ✅ 蓝牙已成功开启
2025-06-23 21:59:56 | INFO | test_bluetooth_command:test_voice_open_bluetooth_command:539 | 🎉 语音输入open bluetooth命令测试完成
2025-06-23 21:59:56 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-23 21:59:56 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 21:59:56 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 21:59:56 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 21:59:56 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 22:06:50 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 22:06:50 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 22:06:50 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 22:06:50 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 22:06:50 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 22:06:50 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 22:06:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 22:06:51 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 22:06:51 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 22:06:52 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 22:06:52 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 22:06:52 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 22:06:52 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: None
2025-06-23 22:06:52 | INFO | __main__:test_tts_service:19 | 🔧 测试TTS服务可用性...
2025-06-23 22:06:52 | INFO | __main__:test_tts_service:25 | TTS服务状态:
2025-06-23 22:06:52 | INFO | __main__:test_tts_service:26 |   - 选择的服务: None
2025-06-23 22:06:52 | INFO | __main__:test_tts_service:27 |   - 可用服务: {'edge_tts': False, 'gtts': False, 'pyttsx3': False}
2025-06-23 22:06:52 | WARNING | __main__:test_tts_service:30 | 没有可用的TTS服务，尝试安装依赖...
2025-06-23 22:06:52 | INFO | utils.tts_utils:install_dependencies:101 | 安装TTS依赖包...
2025-06-23 22:07:02 | INFO | utils.tts_utils:install_dependencies:109 | ✅ 成功安装: edge-tts
2025-06-23 22:07:04 | INFO | utils.tts_utils:install_dependencies:109 | ✅ 成功安装: gtts
2025-06-23 22:07:11 | INFO | utils.tts_utils:install_dependencies:109 | ✅ 成功安装: pyttsx3
2025-06-23 22:07:18 | INFO | utils.tts_utils:install_dependencies:109 | ✅ 成功安装: pygame
2025-06-23 22:07:22 | WARNING | utils.tts_utils:install_dependencies:111 | ⚠️ 安装失败: playsound
2025-06-23 22:07:22 | INFO | __main__:test_tts_service:33 | ✅ TTS依赖安装成功
2025-06-23 22:07:22 | INFO | __main__:test_tts_service:36 |   - 新选择的服务: edge_tts
2025-06-23 22:07:22 | INFO | __main__:test_tts_service:49 | 🎤 测试TTS: 'Hello' (en-US)
2025-06-23 22:07:22 | INFO | utils.tts_utils:speak_text:365 | 🎤 开始朗读文本: 'Hello'
2025-06-23 22:07:25 | INFO | utils.tts_utils:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:07:25 | INFO | utils.tts_utils:speak_text:385 | ✅ 文本朗读完成: 'Hello'
2025-06-23 22:07:25 | INFO | __main__:test_tts_service:53 | ✅ TTS测试成功: 'Hello'
2025-06-23 22:07:26 | INFO | __main__:test_tts_service:49 | 🎤 测试TTS: '你好' (zh-CN)
2025-06-23 22:07:26 | INFO | utils.tts_utils:speak_text:365 | 🎤 开始朗读文本: '你好'
2025-06-23 22:07:27 | INFO | utils.tts_utils:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:07:27 | INFO | utils.tts_utils:speak_text:385 | ✅ 文本朗读完成: '你好'
2025-06-23 22:07:27 | INFO | __main__:test_tts_service:53 | ✅ TTS测试成功: '你好'
2025-06-23 22:07:28 | INFO | __main__:test_tts_service:49 | 🎤 测试TTS: 'open bluetooth' (zh-CN)
2025-06-23 22:07:28 | INFO | utils.tts_utils:speak_text:365 | 🎤 开始朗读文本: 'open bluetooth'
2025-06-23 22:07:29 | INFO | utils.tts_utils:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:07:29 | INFO | utils.tts_utils:speak_text:385 | ✅ 文本朗读完成: 'open bluetooth'
2025-06-23 22:07:29 | INFO | __main__:test_tts_service:53 | ✅ TTS测试成功: 'open bluetooth'
2025-06-23 22:07:30 | INFO | __main__:test_tts_voice_input_functionality:68 | 🎤 开始测试TTS语音输入功能...
2025-06-23 22:07:30 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 22:07:30 | INFO | __main__:test_tts_voice_input_functionality:75 | 📱 启动Ella应用...
2025-06-23 22:07:30 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:07:30 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 22:07:30 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:07:30 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 22:07:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 22:07:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 22:07:31 | INFO | __main__:test_tts_voice_input_functionality:85 | ✅ Ella应用启动成功
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:07:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:31 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:07:31 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:07:32 | INFO | __main__:test_tts_voice_input_functionality:107 | 🎯 测试第1次TTS语音输入: open bluetooth (zh-CN)
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN)
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:07:32 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:07:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:33 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:07:33 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:07:34 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:07:35 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:07:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:07:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:07:35 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:07:35 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:07:38 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:07:38 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:07:38 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:07:41 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:07:43 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:07:45 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:07:45 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:07:45 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:07:45 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:07:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:07:46 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:07:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:47 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:07:47 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:07:47 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:07:47 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:07:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:48 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:07:48 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:07:48 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:07:48 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:07:48 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:49 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:07:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:50 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:07:50 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:07:50 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:07:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:07:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:07:51 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:07:51 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:07:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:07:52 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:07:52 | INFO | __main__:test_tts_voice_input_functionality:119 | TTS语音命令 'open bluetooth' 结果:
2025-06-23 22:07:52 | INFO | __main__:test_tts_voice_input_functionality:120 |   - 执行时间: 19.66秒
2025-06-23 22:07:52 | INFO | __main__:test_tts_voice_input_functionality:121 |   - 执行状态: ✅ 成功
2025-06-23 22:07:52 | INFO | __main__:test_tts_voice_input_functionality:122 |   - TTS语言: zh-CN
2025-06-23 22:07:52 | INFO | __main__:test_tts_voice_input_functionality:123 |   - TTS音量: 0.8
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 10秒
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1480 | 检测到TTS播放按钮
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1247 | 检测到TTS按钮，表示有AI响应
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:07:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:07:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:07:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:07:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:07:53 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:07:56 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 13
2025-06-23 22:07:56 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:07:56 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:07:56 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:07:56 | INFO | __main__:test_tts_voice_input_functionality:130 |   - 响应内容: '蓝牙 已打开'
2025-06-23 22:08:01 | INFO | __main__:test_tts_voice_input_functionality:107 | 🎯 测试第2次TTS语音输入: what time is it (en-US)
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'what time is it' (语言: en-US)
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:01 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:02 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:02 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:08:02 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:08:03 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:08:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:08:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:08:03 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:08:03 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:08:06 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:08:06 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:08:06 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:08:09 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:08:11 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:08:13 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:08:13 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:08:13 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: what time is it
2025-06-23 22:08:13 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:14 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:14 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:08:15 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: what time is it
2025-06-23 22:08:15 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:15 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:08:15 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:08:16 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:08:16 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:08:16 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:08:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:16 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:17 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: what time is it
2025-06-23 22:08:17 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: what time is it
2025-06-23 22:08:18 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: what time is it
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:18 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: what time is it
2025-06-23 22:08:18 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:08:19 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:08:19 | INFO | __main__:test_tts_voice_input_functionality:119 | TTS语音命令 'what time is it' 结果:
2025-06-23 22:08:19 | INFO | __main__:test_tts_voice_input_functionality:120 |   - 执行时间: 17.94秒
2025-06-23 22:08:19 | INFO | __main__:test_tts_voice_input_functionality:121 |   - 执行状态: ✅ 成功
2025-06-23 22:08:19 | INFO | __main__:test_tts_voice_input_functionality:122 |   - TTS语言: en-US
2025-06-23 22:08:19 | INFO | __main__:test_tts_voice_input_functionality:123 |   - TTS音量: 0.7
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 10秒
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1480 | 检测到TTS播放按钮
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1247 | 检测到TTS按钮，表示有AI响应
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:08:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:20 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:08:23 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 12
2025-06-23 22:08:23 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:08:23 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:08:23 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:08:23 | INFO | __main__:test_tts_voice_input_functionality:130 |   - 响应内容: '蓝牙 已打开'
2025-06-23 22:08:28 | INFO | __main__:test_tts_voice_input_functionality:107 | 🎯 测试第3次TTS语音输入: close bluetooth (zh-CN)
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'close bluetooth' (语言: zh-CN)
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:28 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:29 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:29 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:08:29 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:08:30 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:08:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:08:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:08:30 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:08:30 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:08:33 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:08:34 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:08:34 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:08:36 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:08:38 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:08:40 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:08:40 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:08:40 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: close bluetooth
2025-06-23 22:08:40 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:41 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:42 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:08:42 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: close bluetooth
2025-06-23 22:08:42 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:08:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:42 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:08:42 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:08:43 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:08:43 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:08:43 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:44 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:45 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: close bluetooth
2025-06-23 22:08:45 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: close bluetooth
2025-06-23 22:08:45 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: close bluetooth
2025-06-23 22:08:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: close bluetooth
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:08:46 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:08:46 | INFO | __main__:test_tts_voice_input_functionality:119 | TTS语音命令 'close bluetooth' 结果:
2025-06-23 22:08:46 | INFO | __main__:test_tts_voice_input_functionality:120 |   - 执行时间: 18.55秒
2025-06-23 22:08:46 | INFO | __main__:test_tts_voice_input_functionality:121 |   - 执行状态: ✅ 成功
2025-06-23 22:08:46 | INFO | __main__:test_tts_voice_input_functionality:122 |   - TTS语言: zh-CN
2025-06-23 22:08:46 | INFO | __main__:test_tts_voice_input_functionality:123 |   - TTS音量: 0.8
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 10秒
2025-06-23 22:08:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 12
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1480 | 检测到TTS播放按钮
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1247 | 检测到TTS按钮，表示有AI响应
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:08:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:48 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:48 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:48 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:48 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 12
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*关闭 -> 蓝牙 已关闭
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已关闭
2025-06-23 22:08:50 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:08:50 | INFO | __main__:test_tts_voice_input_functionality:130 |   - 响应内容: '蓝牙 已打开'
2025-06-23 22:08:55 | INFO | __main__:test_tts_voice_input_functionality:141 | 📊 TTS语音输入测试结果:
2025-06-23 22:08:55 | INFO | __main__:test_tts_voice_input_functionality:142 |   - 成功率: 100.0% (3/3)
2025-06-23 22:08:55 | INFO | __main__:test_tts_voice_input_functionality:143 |   - 总测试命令: 3
2025-06-23 22:08:55 | INFO | __main__:test_tts_voice_input_functionality:149 | ✅ TTS语音输入功能测试通过！
2025-06-23 22:08:55 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:08:55 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 22:08:55 | INFO | __main__:test_tts_voice_vs_text_comparison:171 | 🔄 对比TTS语音输入和文本输入效果...
2025-06-23 22:08:55 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 22:08:56 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:08:56 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 22:08:56 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:08:56 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 22:08:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 22:08:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:57 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:58 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:08:58 | INFO | __main__:test_tts_voice_vs_text_comparison:198 | 🎤 测试TTS语音输入: open bluetooth
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN)
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:08:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:08:59 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:08:59 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:08:59 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:08:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:08:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:08:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:08:59 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:08:59 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:09:00 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:09:01 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:09:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:09:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:09:01 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:09:01 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:09:04 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:09:05 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:09:05 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:09:07 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:09:09 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:09:11 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:09:11 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:09:11 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:09:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:09:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:09:12 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:09:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:13 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:09:13 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:09:13 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:09:13 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:09:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:13 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:09:13 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:09:14 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:09:14 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:09:14 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:15 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:09:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:16 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:09:16 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:09:16 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:09:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:09:17 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 8秒
2025-06-23 22:09:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:_check_response_elements:1319 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1252 | 检测到响应相关元素
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:09:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:20 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:09:22 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 13
2025-06-23 22:09:22 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:09:22 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:09:22 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:09:22 | INFO | __main__:test_tts_voice_vs_text_comparison:209 | TTS语音输入结果:
2025-06-23 22:09:22 | INFO | __main__:test_tts_voice_vs_text_comparison:210 |   - 执行时间: 19.24秒
2025-06-23 22:09:22 | INFO | __main__:test_tts_voice_vs_text_comparison:211 |   - 执行状态: ✅ 成功
2025-06-23 22:09:22 | INFO | __main__:test_tts_voice_vs_text_comparison:212 |   - 响应内容: '蓝牙 已打开'
2025-06-23 22:09:25 | INFO | __main__:test_tts_voice_vs_text_comparison:218 | 📝 测试文本输入: open bluetooth
2025-06-23 22:09:25 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:09:25 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:09:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:09:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:09:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:27 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:09:27 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:09:27 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:09:27 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:09:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:27 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:09:27 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:09:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:09:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:09:28 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:09:28 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:09:28 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:09:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:28 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:09:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:29 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:09:29 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:09:30 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:09:30 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:09:30 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:09:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:09:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:09:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:09:31 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 8秒
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1480 | 检测到TTS播放按钮
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1247 | 检测到TTS按钮，表示有AI响应
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:09:32 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 12
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:09:35 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:229 | 文本输入结果:
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:230 |   - 执行时间: 5.79秒
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:231 |   - 执行状态: ✅ 成功
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:232 |   - 响应内容: '蓝牙 已打开'
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:235 | 📊 对比分析:
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:236 |   - TTS语音输入时间: 19.24秒
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:237 |   - 文本输入时间: 5.79秒
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:238 |   - 时间差异: 13.45秒
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:242 |   - 响应一致性: ✅ 一致
2025-06-23 22:09:35 | INFO | __main__:test_tts_voice_vs_text_comparison:248 | ✅ TTS语音输入功能可用，可以替代文本输入
2025-06-23 22:09:35 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:09:35 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 22:12:26 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 22:12:26 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 22:12:26 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 22:12:26 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 22:12:26 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 22:12:26 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 22:12:26 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 22:12:27 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 22:12:27 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 22:12:28 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 22:12:28 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 22:12:28 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 22:12:31 | INFO | utils.excel_utils:read_test_data:71 | 成功读取 4 条测试数据
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 22:12:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 22:12:32 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 22:12:32 | INFO | test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-06-23 22:12:32 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:12:32 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-06-23 22:12:35 | INFO | test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-06-23 22:12:35 | INFO | test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-06-23 22:12:35 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:12:35 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:35 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 22:12:35 | INFO | test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-06-23 22:12:36 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1726 | 检查蓝牙状态
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1740 | 蓝牙状态: 开启 (值: 1)
2025-06-23 22:12:37 | INFO | test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:638 | 蓝牙初始状态: 开启
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:12:37 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:12:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:38 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:12:38 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN)
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:12:39 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:12:40 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:12:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:40 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:12:40 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:12:41 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:12:41 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:12:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:12:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:12:42 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:12:42 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:12:45 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:12:45 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:12:46 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:12:48 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:12:50 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:12:52 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:12:52 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:12:52 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:12:52 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:12:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:12:53 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:12:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:54 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:12:54 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:12:54 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:12:54 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:12:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:55 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:12:55 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:12:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:12:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:12:56 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:12:56 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:12:57 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:12:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:57 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:12:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:12:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:12:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:12:58 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:12:58 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:12:59 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:13:00 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:13:00 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:13:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:13:00 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:13:00 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:13:00 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:13:00 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:13:01 | INFO | test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:672 | ✅ 成功执行TTS语音命令: open bluetooth
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 12秒
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:13:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:13:02 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1480 | 检测到TTS播放按钮
2025-06-23 22:13:02 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1247 | 检测到TTS按钮，表示有AI响应
2025-06-23 22:13:02 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:13:05 | INFO | test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-06-23 22:13:05 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:13:05 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 22:13:05 | INFO | test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 22:13:05 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 22:15:04 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 22:15:04 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 22:15:04 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 22:15:04 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 22:15:04 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 22:15:04 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 22:15:04 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 22:15:06 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 22:15:06 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 22:15:06 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 22:15:06 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 22:15:06 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 22:15:06 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 22:15:06 | INFO | __main__:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 22:15:06 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 22:15:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 22:15:07 | INFO | __main__:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1726 | 检查蓝牙状态
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1740 | 蓝牙状态: 开启 (值: 1)
2025-06-23 22:15:07 | INFO | __main__:test_simulated_voice_open_bluetooth:55 | 🔵 蓝牙初始状态: 开启
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:08 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:15:09 | INFO | __main__:test_simulated_voice_open_bluetooth:77 | ✅ 页面和输入框状态就绪
2025-06-23 22:15:09 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: 模拟语音输入_open bluetooth
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:execute_voice_command:439 | 执行语音命令: open bluetooth (持续时间: 3.0秒)
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:09 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:10 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:10 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:10 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:15:11 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:15:12 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:15:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:15:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:15:12 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:15:12 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:15:15 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:15:15 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:15:16 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:15:18 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:15:20 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:15:22 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:15:22 | WARNING | pages.apps.ella.main_page:execute_voice_command:453 | 无法启动语音输入，回退到文本输入
2025-06-23 22:15:22 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:15:22 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:22 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:24 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:15:24 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:15:24 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:15:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:24 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:15:24 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:15:25 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:15:25 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:15:25 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:26 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:27 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:15:27 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:15:27 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:15:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:28 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:15:28 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:15:28 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:15:28 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:15:28 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:15:28 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:15:28 | INFO | __main__:test_simulated_voice_open_bluetooth:91 | ✅ 模拟语音命令执行成功，耗时: 19.53秒
2025-06-23 22:15:28 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 模拟语音命令执行 耗时 19.530秒 
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 10秒
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:31 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:15:31 | INFO | pages.apps.ella.main_page:_check_response_elements:1319 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 22:15:31 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1252 | 检测到响应相关元素
2025-06-23 22:15:31 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:15:33 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:15:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:34 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 13
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:15:36 | INFO | __main__:test_simulated_voice_open_bluetooth:120 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:verify_command_in_response:1762 | 验证响应是否包含命令: open bluetooth
2025-06-23 22:15:36 | INFO | pages.apps.ella.main_page:verify_command_in_response:1792 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 22:15:36 | INFO | __main__:test_simulated_voice_open_bluetooth:129 | ✅ 响应内容与命令相关
2025-06-23 22:15:39 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1561 | 智能检查蓝牙状态...
2025-06-23 22:15:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1726 | 检查蓝牙状态
2025-06-23 22:15:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1740 | 蓝牙状态: 开启 (值: 1)
2025-06-23 22:15:40 | INFO | __main__:test_simulated_voice_open_bluetooth:139 | 🔵 蓝牙最终状态: 开启
2025-06-23 22:15:40 | INFO | __main__:test_simulated_voice_open_bluetooth:149 | ✅ 蓝牙已成功开启
2025-06-23 22:15:40 | INFO | __main__:test_simulated_voice_open_bluetooth:153 | 📊 蓝牙状态变化: 否
2025-06-23 22:15:40 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: 模拟语音输入_open bluetooth - ✅ 成功, 耗时: 19.53秒
2025-06-23 22:15:40 | INFO | __main__:test_simulated_voice_open_bluetooth:178 | 🎉 模拟语音输入open bluetooth测试完成
2025-06-23 22:15:40 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:15:40 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 22:15:40 | INFO | __main__:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 22:15:42 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 22:15:42 | INFO | __main__:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 22:15:42 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 22:15:43 | INFO | __main__:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1726 | 检查蓝牙状态
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1740 | 蓝牙状态: 开启 (值: 1)
2025-06-23 22:15:43 | INFO | __main__:test_tts_voice_open_bluetooth:190 | 🔵 蓝牙初始状态: 开启
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:44 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:44 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:15:45 | INFO | __main__:test_tts_voice_open_bluetooth:206 | ✅ 页面和输入框状态就绪
2025-06-23 22:15:45 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: TTS语音输入_open bluetooth
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:execute_real_voice_command:505 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN)
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:45 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:46 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:46 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:15:46 | INFO | pages.apps.ella.main_page:start_voice_input:625 | 启动语音输入...
2025-06-23 22:15:47 | INFO | pages.apps.ella.main_page:start_voice_input:643 | 找到语音按钮: 语音按钮(备选)
2025-06-23 22:15:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 22:15:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 22:15:48 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 22:15:48 | INFO | pages.apps.ella.main_page:start_voice_input:645 | ✅ 语音按钮点击成功
2025-06-23 22:15:51 | INFO | pages.apps.ella.main_page:start_voice_input:658 | 尝试长按输入框启动语音输入...
2025-06-23 22:15:51 | INFO | pages.apps.ella.main_page:start_voice_input:667 | 尝试通过坐标点击语音按钮区域...
2025-06-23 22:15:51 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (972, 2160)
2025-06-23 22:15:53 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (918, 2160)
2025-06-23 22:15:56 | INFO | pages.apps.ella.main_page:start_voice_input:678 | 尝试点击坐标 (540, 2280)
2025-06-23 22:15:58 | WARNING | pages.apps.ella.main_page:start_voice_input:685 | ❌ 无法启动语音输入
2025-06-23 22:15:58 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:519 | 无法启动语音输入，回退到文本输入
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:816 | 确保在对话页面...
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:827 | ✅ 已在对话页面
2025-06-23 22:15:58 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1055 | 确保输入框就绪...
2025-06-23 22:15:59 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1059 | 找到主输入框
2025-06-23 22:15:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:15:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:15:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:15:59 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:15:59 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1063 | ✅ 主输入框已激活
2025-06-23 22:16:00 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 22:16:00 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:16:00 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 22:16:00 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 22:16:01 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 22:16:01 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 22:16:01 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 22:16:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:16:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:16:01 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 22:16:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:16:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:16:02 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 22:16:02 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 22:16:03 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 22:16:03 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 22:16:03 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 22:16:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 22:16:04 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 22:16:04 | INFO | __main__:test_tts_voice_open_bluetooth:225 | ✅ TTS语音命令执行成功，耗时: 19.04秒
2025-06-23 22:16:04 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: TTS语音命令执行 耗时 19.038秒 
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:wait_for_response:1163 | 快速等待AI响应，超时时间: 12秒
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:wait_for_response:1178 | 初始元素数量: 13
2025-06-23 22:16:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:16:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:16:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:16:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:16:06 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:16:06 | INFO | pages.apps.ella.main_page:_check_response_elements:1319 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 22:16:06 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1252 | 检测到响应相关元素
2025-06-23 22:16:06 | INFO | pages.apps.ella.main_page:wait_for_response:1196 | ✅ 快速检测到AI响应
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:get_response_text_smart:1585 | 智能获取响应文本...
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:16:09 | INFO | pages.apps.ella.main_page:get_response_text:1609 | 获取AI响应文本
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:get_response_text:1625 | 页面上所有文本元素数量: 13
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:_is_ai_response:1461 | 匹配到AI响应特征: 帮我扩展风景照
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 帮我扩展风景照
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:_is_ai_response:1448 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:get_response_text:1633 | 找到AI响应: 蓝牙 已打开
2025-06-23 22:16:11 | INFO | pages.apps.ella.main_page:get_response_text:1646 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 22:16:12 | INFO | __main__:test_tts_voice_open_bluetooth:254 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 22:16:12 | INFO | pages.apps.ella.main_page:verify_command_in_response:1762 | 验证响应是否包含命令: open bluetooth
2025-06-23 22:16:12 | INFO | pages.apps.ella.main_page:verify_command_in_response:1792 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 22:16:12 | INFO | __main__:test_tts_voice_open_bluetooth:263 | ✅ TTS响应内容与命令相关
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1561 | 智能检查蓝牙状态...
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:848 | 检查当前进程是否是Ella...
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:855 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:856 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:865 | ✅ 当前在Ella应用进程
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1726 | 检查蓝牙状态
2025-06-23 22:16:15 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1740 | 蓝牙状态: 开启 (值: 1)
2025-06-23 22:16:15 | INFO | __main__:test_tts_voice_open_bluetooth:273 | 🔵 蓝牙最终状态: 开启
2025-06-23 22:16:15 | INFO | __main__:test_tts_voice_open_bluetooth:277 | ✅ 蓝牙已成功开启
2025-06-23 22:16:15 | INFO | __main__:test_tts_voice_open_bluetooth:281 | 📊 蓝牙状态变化: 否
2025-06-23 22:16:15 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: TTS语音输入_open bluetooth - ✅ 成功, 耗时: 19.04秒
2025-06-23 22:16:15 | INFO | __main__:test_tts_voice_open_bluetooth:308 | 🎉 TTS真实语音输入open bluetooth测试完成
2025-06-23 22:16:15 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 22:16:15 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 22:16:16 | INFO | __main__:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 22:19:48 | INFO | __main__:__init__:53 | 选择的TTS服务: None
2025-06-23 22:19:48 | INFO | __main__:__init__:53 | 选择的TTS服务: None
2025-06-23 22:19:48 | INFO | __main__:install_dependencies:101 | 安装TTS依赖包...
2025-06-23 22:19:58 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: edge-tts
2025-06-23 22:20:08 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: gtts
2025-06-23 22:20:19 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: pyttsx3
2025-06-23 22:20:29 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: pygame
2025-06-23 22:20:38 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: playsound
2025-06-23 22:20:38 | INFO | __main__:speak_text:365 | 🎤 开始朗读文本: 'Hello, world!'
2025-06-23 22:20:38 | ERROR | __main__:text_to_speech:146 | 没有可用的TTS服务
2025-06-23 22:20:38 | ERROR | __main__:speak_text:370 | 语音生成失败
2025-06-23 22:25:44 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:25:47 | INFO | utils.tts_utils:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:26:02 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:26:02 | INFO | utils.tts_utils:speak_text:365 | 🎤 开始朗读文本: '打开蓝牙'
2025-06-23 22:26:03 | INFO | utils.tts_utils:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:26:03 | INFO | utils.tts_utils:speak_text:385 | ✅ 文本朗读完成: '打开蓝牙'
2025-06-23 22:26:48 | INFO | __main__:__init__:53 | 选择的TTS服务: None
2025-06-23 22:26:48 | INFO | __main__:__init__:53 | 选择的TTS服务: None
2025-06-23 22:26:48 | INFO | __main__:install_dependencies:101 | 安装TTS依赖包...
2025-06-23 22:26:57 | WARNING | __main__:install_dependencies:111 | ⚠️ 安装失败: edge-tts
2025-06-23 22:29:59 | INFO | __main__:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:29:59 | INFO | __main__:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:29:59 | INFO | __main__:speak_text:365 | 🎤 开始朗读文本: 'Hello, world!'
2025-06-23 22:30:06 | INFO | __main__:_play_with_system:344 | ✅ 系统播放器播放完成
2025-06-23 22:30:06 | INFO | __main__:speak_text:385 | ✅ 文本朗读完成: 'Hello, world!'
2025-06-23 22:36:03 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:36:03 | INFO | __main__:test_tts_file_generation:19 | 🧪 测试TTS文件生成功能...
2025-06-23 22:36:03 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:36:04 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:05 | INFO | __main__:test_tts_file_generation:40 | 🎯 测试 2/5: '你好，这是一个测试' (zh-CN)
2025-06-23 22:36:05 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: '你好，这是一个测试' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:06 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:07 | INFO | __main__:test_tts_file_generation:40 | 🎯 测试 3/5: 'open bluetooth' (zh-CN)
2025-06-23 22:36:07 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:08 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:08 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: open_bluetooth.wav
2025-06-23 22:36:09 | INFO | __main__:test_tts_file_generation:40 | 🎯 测试 4/5: 'close bluetooth' (zh-CN)
2025-06-23 22:36:09 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'close bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:10 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:10 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: close_bluetooth.wav
2025-06-23 22:36:11 | INFO | __main__:test_tts_file_generation:40 | 🎯 测试 5/5: 'what time is it' (en-US)
2025-06-23 22:36:11 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'what time is it' (语音: en-US-AriaNeural)
2025-06-23 22:36:12 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:12 | ERROR | __main__:test_tts_file_generation:66 |   ❌ 生成失败: what_time.wav
2025-06-23 22:36:13 | INFO | __main__:test_tts_file_generation:72 | 📊 文件生成测试结果: 0/5 成功 (0.0%)
2025-06-23 22:36:13 | INFO | __main__:test_tts_service_comparison:83 | 🔄 对比不同TTS服务...
2025-06-23 22:36:13 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:36:13 | INFO | __main__:test_tts_service_comparison:92 | 可用的TTS服务: ['edge_tts', 'gtts', 'pyttsx3']
2025-06-23 22:36:13 | INFO | __main__:test_tts_service_comparison:101 | 🧪 测试服务: edge_tts
2025-06-23 22:36:13 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:14 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:14 | ERROR | __main__:test_tts_service_comparison:141 |   ❌ edge_tts: 生成失败
2025-06-23 22:36:15 | INFO | __main__:test_tts_service_comparison:101 | 🧪 测试服务: gtts
2025-06-23 22:36:15 | INFO | utils.tts_utils:_gtts_generate:214 | 🎤 使用Google TTS生成语音: 'open bluetooth' (语言: zh)
2025-06-23 22:36:16 | ERROR | utils.tts_utils:_gtts_generate:238 | Google TTS生成的文件验证失败
2025-06-23 22:36:16 | ERROR | __main__:test_tts_service_comparison:141 |   ❌ gtts: 生成失败
2025-06-23 22:36:17 | INFO | __main__:test_tts_service_comparison:101 | 🧪 测试服务: pyttsx3
2025-06-23 22:36:17 | INFO | utils.tts_utils:_pyttsx3_generate:250 | 🎤 使用pyttsx3生成语音: 'open bluetooth' (离线TTS)
2025-06-23 22:36:18 | INFO | utils.tts_utils:play_audio:402 | 🔊 开始播放音频: tts_1750689377.wav (84.6KB)
2025-06-23 22:36:21 | INFO | utils.tts_utils:_play_with_pygame:436 | ✅ pygame播放完成
2025-06-23 22:36:21 | INFO | utils.tts_utils:play_audio:411 | ✅ 音频播放完成: tts_1750689377.wav
2025-06-23 22:36:21 | INFO | __main__:test_tts_service_comparison:128 |   ✅ pyttsx3: 生成 1.22秒, 文件 84.6KB, 播放 成功
2025-06-23 22:36:22 | INFO | __main__:test_tts_service_comparison:157 | 📊 TTS服务对比报告:
2025-06-23 22:36:22 | INFO | __main__:test_tts_service_comparison:165 |   edge_tts    : ❌ 文件生成失败
2025-06-23 22:36:22 | INFO | __main__:test_tts_service_comparison:165 |   gtts        : ❌ 文件生成失败
2025-06-23 22:36:22 | INFO | __main__:test_tts_service_comparison:160 |   pyttsx3     : ✅ 生成 1.22秒, 文件 84.6KB, 播放 ✅
2025-06-23 22:36:22 | INFO | __main__:test_tts_service_comparison:173 | 🏆 推荐服务: pyttsx3 (生成最快)
2025-06-23 22:36:22 | INFO | __main__:test_tts_file_verification:184 | 🔍 测试音频文件验证功能...
2025-06-23 22:36:22 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:36:23 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:23 | ERROR | __main__:test_tts_file_verification:194 | 无法生成测试音频文件
2025-06-23 22:36:23 | INFO | __main__:test_tts_performance:238 | ⚡ 测试TTS性能...
2025-06-23 22:36:23 | INFO | __main__:test_tts_performance:253 | 🧪 测试 短文本: 'Hi'
2025-06-23 22:36:23 | INFO | utils.tts_utils:speak_text:513 | 🎤 开始朗读文本: 'Hi' (语言: en-US, 音量: 0.5)
2025-06-23 22:36:23 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'Hi' (语音: en-US-AriaNeural)
2025-06-23 22:36:23 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:23 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:23 | INFO | __main__:test_tts_performance:267 |   结果: ❌ 失败, 耗时: 0.86秒
2025-06-23 22:36:24 | INFO | __main__:test_tts_performance:253 | 🧪 测试 中等文本: 'Hello, how are you today?'
2025-06-23 22:36:24 | INFO | utils.tts_utils:speak_text:513 | 🎤 开始朗读文本: 'Hello, how are you today?' (语言: en-US, 音量: 0.5)
2025-06-23 22:36:24 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'Hello, how are you today?' (语音: en-US-AriaNeural)
2025-06-23 22:36:25 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:25 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:25 | INFO | __main__:test_tts_performance:267 |   结果: ❌ 失败, 耗时: 0.97秒
2025-06-23 22:36:27 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:27 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:27 | INFO | __main__:test_tts_performance:267 |   结果: ❌ 失败, 耗时: 1.18秒
2025-06-23 22:36:27 | INFO | __main__:test_tts_performance:253 | 🧪 测试 中文短文本: '你好'
2025-06-23 22:36:27 | INFO | utils.tts_utils:speak_text:513 | 🎤 开始朗读文本: '你好' (语言: zh-CN, 音量: 0.5)
2025-06-23 22:36:27 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: '你好' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:28 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:28 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:28 | INFO | __main__:test_tts_performance:267 |   结果: ❌ 失败, 耗时: 0.86秒
2025-06-23 22:36:28 | INFO | __main__:test_tts_performance:253 | 🧪 测试 中文长文本: '你好，今天天气怎么样？希望你过得愉快。'
2025-06-23 22:36:28 | INFO | utils.tts_utils:speak_text:513 | 🎤 开始朗读文本: '你好，今天天气怎么样？希望你过得愉快。' (语言: zh-CN, 音量: 0.5)
2025-06-23 22:36:28 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: '你好，今天天气怎么样？希望你过得愉快。' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:36:30 | ERROR | utils.tts_utils:_edge_tts_generate:201 | Edge TTS生成的文件验证失败
2025-06-23 22:36:30 | ERROR | utils.tts_utils:speak_text:520 | ❌ 语音生成失败
2025-06-23 22:36:30 | INFO | __main__:test_tts_performance:267 |   结果: ❌ 失败, 耗时: 1.36秒
2025-06-23 22:36:30 | INFO | __main__:test_tts_performance:272 | 📊 TTS性能分析:
2025-06-23 22:36:30 | ERROR | __main__:test_tts_performance:294 | ❌ 所有性能测试都失败
2025-06-23 22:38:27 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:38:27 | INFO | utils.tts_utils:speak_text:528 | 🎤 开始朗读文本: 'open bluetooth' (语言: zh-CN, 音量: 0.7)
2025-06-23 22:38:27 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:38:29 | INFO | utils.tts_utils:play_audio:417 | 🔊 开始播放音频: tts_1750689507.wav (10.3KB)
2025-06-23 22:38:30 | INFO | utils.tts_utils:_play_with_system:506 | ✅ 系统播放器播放完成
2025-06-23 22:38:30 | INFO | utils.tts_utils:play_audio:426 | ✅ 音频播放完成: tts_1750689507.wav
2025-06-23 22:38:30 | INFO | utils.tts_utils:speak_text:564 | ✅ 文本朗读完成: 'open bluetooth' (总耗时: 2.84秒)
2025-06-23 22:39:22 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:39:22 | INFO | utils.tts_utils:speak_text:528 | 🎤 开始朗读文本: 'open bluetooth' (语言: zh-CN, 音量: 0.7)
2025-06-23 22:39:22 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:39:23 | INFO | utils.tts_utils:play_audio:417 | 🔊 开始播放音频: tts_1750689562.wav (10.3KB)
2025-06-23 22:39:25 | INFO | utils.tts_utils:_play_with_system:506 | ✅ 系统播放器播放完成
2025-06-23 22:39:25 | INFO | utils.tts_utils:play_audio:426 | ✅ 音频播放完成: tts_1750689562.wav
2025-06-23 22:39:25 | INFO | utils.tts_utils:speak_text:564 | ✅ 文本朗读完成: 'open bluetooth' (总耗时: 3.00秒)
2025-06-23 22:39:25 | INFO | utils.tts_utils:__init__:53 | 选择的TTS服务: edge_tts
2025-06-23 22:39:25 | INFO | utils.tts_utils:_edge_tts_generate:174 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:39:27 | INFO | utils.tts_utils:_play_with_system:506 | ✅ 系统播放器播放完成
2025-06-23 22:48:14 | INFO | utils.tts_utils:__init__:85 | 选择的TTS服务: edge_tts
2025-06-23 22:48:14 | INFO | __main__:test_language_classification:22 | 🗂️ 测试语言分类存储功能...
2025-06-23 22:48:14 | INFO | __main__:test_language_classification:41 | 🧪 测试 1/10: 英文 - 'open bluetooth' (en-US)
2025-06-23 22:48:14 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 22:48:14 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: en-US-AriaNeural)
2025-06-23 22:48:15 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\open_bluetooth.wav
2025-06-23 22:48:16 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 22:48:16 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\open_bluetooth.wav
2025-06-23 22:48:16 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 11.8KB
2025-06-23 22:48:16 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 1.94秒
2025-06-23 22:48:16 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/open_bluetooth.wav
2025-06-23 22:48:16 | INFO | __main__:test_language_classification:41 | 🧪 测试 2/10: 英文(英国) - 'close bluetooth' (en-GB)
2025-06-23 22:48:16 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'close bluetooth' -> data\en\close_bluetooth.wav
2025-06-23 22:48:16 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'close bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:17 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\close_bluetooth.wav
2025-06-23 22:48:18 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\close_bluetooth.wav (10.1KB)
2025-06-23 22:48:18 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\close_bluetooth.wav
2025-06-23 22:48:18 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 10.1KB
2025-06-23 22:48:18 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 1.71秒
2025-06-23 22:48:18 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/close_bluetooth.wav
2025-06-23 22:48:18 | INFO | __main__:test_language_classification:41 | 🧪 测试 3/10: 中文(简体) - '打开蓝牙' (zh-CN)
2025-06-23 22:48:18 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: '打开蓝牙' -> data\en\打开蓝牙.wav
2025-06-23 22:48:18 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: '打开蓝牙' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:20 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\打开蓝牙.wav
2025-06-23 22:48:20 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\打开蓝牙.wav (9.6KB)
2025-06-23 22:48:20 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\打开蓝牙.wav
2025-06-23 22:48:20 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 9.6KB
2025-06-23 22:48:20 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 1.94秒
2025-06-23 22:48:20 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/打开蓝牙.wav
2025-06-23 22:48:21 | INFO | __main__:test_language_classification:41 | 🧪 测试 4/10: 中文(繁体) - '關閉藍牙' (zh-TW)
2025-06-23 22:48:21 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: '關閉藍牙' -> data\en\關閉藍牙.wav
2025-06-23 22:48:21 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: '關閉藍牙' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:24 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\關閉藍牙.wav
2025-06-23 22:48:24 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\關閉藍牙.wav (9.4KB)
2025-06-23 22:48:24 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\關閉藍牙.wav
2025-06-23 22:48:24 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 9.4KB
2025-06-23 22:48:24 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 3.57秒
2025-06-23 22:48:24 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/關閉藍牙.wav
2025-06-23 22:48:25 | INFO | __main__:test_language_classification:41 | 🧪 测试 5/10: 英文(通用) - 'what time is it' (en)
2025-06-23 22:48:25 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'what time is it' -> data\en\what_time_is_it.wav
2025-06-23 22:48:25 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'what time is it' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:26 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\what_time_is_it.wav
2025-06-23 22:48:27 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\what_time_is_it.wav (10.7KB)
2025-06-23 22:48:27 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\what_time_is_it.wav
2025-06-23 22:48:27 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 10.7KB
2025-06-23 22:48:27 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 1.74秒
2025-06-23 22:48:27 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/what_time_is_it.wav
2025-06-23 22:48:27 | INFO | __main__:test_language_classification:41 | 🧪 测试 6/10: 中文(通用) - '现在几点了' (zh)
2025-06-23 22:48:27 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: '现在几点了' -> data\zh\现在几点了.wav
2025-06-23 22:48:27 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: '现在几点了' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:29 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\zh\现在几点了.wav
2025-06-23 22:48:30 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\zh\现在几点了.wav (10.3KB)
2025-06-23 22:48:30 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\zh\现在几点了.wav
2025-06-23 22:48:30 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 10.3KB
2025-06-23 22:48:30 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 2.59秒
2025-06-23 22:48:30 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: zh/现在几点了.wav
2025-06-23 22:48:30 | INFO | __main__:test_language_classification:41 | 🧪 测试 7/10: 日文 - 'こんにちは' (ja-JP)
2025-06-23 22:48:30 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'こんにちは' -> data\en\こんにちは.wav
2025-06-23 22:48:30 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'こんにちは' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:31 | ERROR | utils.tts_utils:_edge_tts_generate:322 | Edge TTS生成异常: No audio was received. Please verify that your parameters are correct.
2025-06-23 22:48:31 | ERROR | utils.tts_utils:generate_audio_file:720 | ❌ 音频文件生成失败: data\en\こんにちは.wav
2025-06-23 22:48:31 | ERROR | __main__:test_language_classification:63 |   ❌ 生成失败: こんにちは
2025-06-23 22:48:32 | INFO | __main__:test_language_classification:41 | 🧪 测试 8/10: 韩文 - '안녕하세요' (ko-KR)
2025-06-23 22:48:32 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: '안녕하세요' -> data\en\안녕하세요.wav
2025-06-23 22:48:32 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: '안녕하세요' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:33 | ERROR | utils.tts_utils:_edge_tts_generate:322 | Edge TTS生成异常: No audio was received. Please verify that your parameters are correct.
2025-06-23 22:48:33 | ERROR | utils.tts_utils:generate_audio_file:720 | ❌ 音频文件生成失败: data\en\안녕하세요.wav
2025-06-23 22:48:33 | ERROR | __main__:test_language_classification:63 |   ❌ 生成失败: 안녕하세요
2025-06-23 22:48:33 | INFO | __main__:test_language_classification:41 | 🧪 测试 9/10: 法文 - 'Bonjour' (fr-FR)
2025-06-23 22:48:33 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'Bonjour' -> data\en\Bonjour.wav
2025-06-23 22:48:33 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'Bonjour' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:35 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\Bonjour.wav
2025-06-23 22:48:35 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\Bonjour.wav (7.5KB)
2025-06-23 22:48:35 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\Bonjour.wav
2025-06-23 22:48:35 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 7.5KB
2025-06-23 22:48:35 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 1.89秒
2025-06-23 22:48:35 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/Bonjour.wav
2025-06-23 22:48:35 | INFO | __main__:test_language_classification:41 | 🧪 测试 10/10: 德文 - 'Hallo' (de-DE)
2025-06-23 22:48:35 | INFO | utils.tts_utils:generate_audio_file:705 | 📁 生成音频文件: 'Hallo' -> data\en\Hallo.wav
2025-06-23 22:48:35 | INFO | utils.tts_utils:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'Hallo' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:48:37 | INFO | utils.tts_utils:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\Hallo.wav
2025-06-23 22:48:37 | INFO | utils.tts_utils:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\Hallo.wav (6.9KB)
2025-06-23 22:48:37 | INFO | __main__:test_language_classification:51 |   ✅ 生成成功: data\en\Hallo.wav
2025-06-23 22:48:37 | INFO | __main__:test_language_classification:52 |   📁 文件大小: 6.9KB
2025-06-23 22:48:37 | INFO | __main__:test_language_classification:53 |   ⏱️ 生成耗时: 2.03秒
2025-06-23 22:48:37 | INFO | __main__:test_language_classification:58 |   ✅ 路径正确: en/Hallo.wav
2025-06-23 22:48:38 | INFO | __main__:test_language_classification:72 | 📊 语言分类测试结果: 8/10 成功 (80.0%)
2025-06-23 22:48:38 | INFO | __main__:test_cache_functionality:79 | 💾 测试缓存功能...
2025-06-23 22:48:38 | INFO | __main__:test_cache_functionality:86 | 🧪 第一次生成音频文件...
2025-06-23 22:48:40 | INFO | __main__:test_cache_functionality:95 |   ✅ 第一次生成成功: 1.85秒
2025-06-23 22:48:40 | INFO | __main__:test_cache_functionality:98 | 🧪 第二次生成音频文件（测试缓存）...
2025-06-23 22:48:41 | INFO | __main__:test_cache_functionality:104 |   ✅ 使用缓存成功: 1.69秒
2025-06-23 22:48:41 | INFO | __main__:test_cache_functionality:105 |   📈 缓存效果: 节省 0.16秒
2025-06-23 22:48:41 | INFO | __main__:test_directory_structure:118 | 📁 测试目录结构...
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:132 | 📋 当前缓存文件结构:
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:134 |   📂 en/ (8 个文件)
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:136 |     📄 Bonjour.wav (7.5KB)
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:136 |     📄 close_bluetooth.wav (10.1KB)
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:136 |     📄 Hallo.wav (6.9KB)
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:138 |     ... 还有 5 个文件
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:134 |   📂 zh/ (1 个文件)
2025-06-23 22:48:51 | INFO | __main__:test_directory_structure:136 |     📄 现在几点了.wav (10.3KB)
2025-06-23 22:48:51 | INFO | __main__:test_cache_statistics:149 | 📊 测试缓存统计功能...
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:154 | 📈 缓存统计信息:
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:155 |   📁 数据目录: data
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:156 |   📄 总文件数: 9
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:157 |   💾 总大小: 91.4KB
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:159 |   📂 各语言统计:
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:161 |     en: 8 个文件, 81.1KB
2025-06-23 22:49:01 | INFO | __main__:test_cache_statistics:161 |     zh: 1 个文件, 10.3KB
2025-06-23 22:49:01 | INFO | __main__:test_voice_playback:172 | 🔊 测试语音播放功能...
2025-06-23 22:49:01 | INFO | __main__:test_voice_playback:182 | 🎤 测试播放: 'open bluetooth' (en-US)
2025-06-23 22:49:01 | INFO | utils.tts_utils:speak_text:646 | 🎤 开始朗读文本: 'open bluetooth' (语言: en-US, 音量: 0.6)
2025-06-23 22:49:01 | INFO | utils.tts_utils:text_to_speech:262 | 🎯 使用缓存文件: data\en\open_bluetooth.wav
2025-06-23 22:49:02 | INFO | utils.tts_utils:play_audio:534 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 22:49:03 | INFO | utils.tts_utils:_play_with_system:623 | ✅ 系统播放器播放完成
2025-06-23 22:49:03 | INFO | utils.tts_utils:play_audio:543 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 22:49:03 | INFO | utils.tts_utils:speak_text:682 | ✅ 文本朗读完成: 'open bluetooth' (总耗时: 2.20秒)
2025-06-23 22:49:03 | INFO | __main__:test_voice_playback:190 |   ✅ 播放成功: 2.20秒
2025-06-23 22:49:04 | INFO | __main__:test_voice_playback:182 | 🎤 测试播放: '打开蓝牙' (zh-CN)
2025-06-23 22:49:04 | INFO | utils.tts_utils:speak_text:646 | 🎤 开始朗读文本: '打开蓝牙' (语言: zh-CN, 音量: 0.6)
2025-06-23 22:49:05 | INFO | utils.tts_utils:text_to_speech:262 | 🎯 使用缓存文件: data\en\打开蓝牙.wav
2025-06-23 22:49:05 | INFO | utils.tts_utils:play_audio:534 | 🔊 开始播放音频: 打开蓝牙.wav (9.6KB)
2025-06-23 22:49:06 | INFO | utils.tts_utils:_play_with_system:623 | ✅ 系统播放器播放完成
2025-06-23 22:49:06 | INFO | utils.tts_utils:play_audio:543 | ✅ 音频播放完成: 打开蓝牙.wav
2025-06-23 22:49:06 | INFO | utils.tts_utils:speak_text:682 | ✅ 文本朗读完成: '打开蓝牙' (总耗时: 1.49秒)
2025-06-23 22:49:06 | INFO | __main__:test_voice_playback:190 |   ✅ 播放成功: 1.49秒
2025-06-23 22:49:07 | INFO | __main__:test_voice_playback:201 | 📊 播放测试结果: 2/2 成功 (100.0%)
2025-06-23 22:49:07 | INFO | __main__:test_cache_cleanup:208 | 🧹 测试缓存清理功能...
2025-06-23 22:49:16 | INFO | __main__:test_cache_cleanup:215 | 清理前: 7 个文件
2025-06-23 22:49:16 | INFO | __main__:test_cache_cleanup:222 | 🧪 清理英文缓存...
2025-06-23 22:49:16 | INFO | utils.tts_utils:clear_cache:833 | ✅ 清理完成，删除了 8 个缓存文件
2025-06-23 22:49:16 | INFO | __main__:test_cache_cleanup:230 | 清理后: 1 个文件
2025-06-23 22:49:16 | INFO | __main__:test_cache_cleanup:231 | ✅ 清理了 6 个英文文件
2025-06-23 22:51:54 | INFO | __main__:__init__:85 | 选择的TTS服务: edge_tts
2025-06-23 22:51:54 | INFO | __main__:__init__:85 | 选择的TTS服务: edge_tts
2025-06-23 22:51:54 | INFO | __main__:speak_text:646 | 🎤 开始朗读文本: 'Hello, world!' (语言: en-US, 音量: 1.0)
2025-06-23 22:51:54 | INFO | __main__:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'Hello, world!' (语音: en-US-AriaNeural)
2025-06-23 22:52:00 | INFO | __main__:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\Hello_world.wav
2025-06-23 22:52:00 | INFO | __main__:play_audio:534 | 🔊 开始播放音频: Hello_world.wav (12.5KB)
2025-06-23 22:52:02 | INFO | __main__:_play_with_system:623 | ✅ 系统播放器播放完成
2025-06-23 22:52:02 | INFO | __main__:play_audio:543 | ✅ 音频播放完成: Hello_world.wav
2025-06-23 22:52:02 | INFO | __main__:speak_text:682 | ✅ 文本朗读完成: 'Hello, world!' (总耗时: 7.44秒)
2025-06-23 22:54:36 | INFO | __main__:__init__:85 | 选择的TTS服务: edge_tts
2025-06-23 22:54:36 | INFO | __main__:generate_audio_file:705 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 22:54:36 | INFO | __main__:_edge_tts_generate:291 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 22:54:42 | INFO | __main__:_edge_tts_generate:315 | ✅ Edge TTS生成成功: data\en\open_bluetooth.wav
2025-06-23 22:54:42 | INFO | __main__:generate_audio_file:717 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (10.3KB)
2025-06-23 23:00:57 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:24 | 🔍 测试项目根目录检测...
2025-06-23 23:00:57 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:49 | ✅ 数据目录相对路径: data
2025-06-23 23:00:57 | INFO | __main__:test_project_root_detection:52 | ✅ 数据目录路径正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:69 | 📂 测试相对路径生成...
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 1/6: 'open bluetooth' (en-US)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\en\open_bluetooth.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/open_bluetooth.wav, 实际: data\en\open_bluetooth.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 2/6: 'close bluetooth' (en)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\en\close_bluetooth.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/close_bluetooth.wav, 实际: data\en\close_bluetooth.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 3/6: '打开蓝牙' (zh-CN)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\en\打开蓝牙.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/打开蓝牙.wav, 实际: data\en\打开蓝牙.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 4/6: '关闭蓝牙' (zh)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\zh\关闭蓝牙.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/关闭蓝牙.wav, 实际: data\zh\关闭蓝牙.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 5/6: 'what time is it' (en-US)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\en\what_time_is_it.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/en/what_time_is_it.wav, 实际: data\en\what_time_is_it.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:83 | 🧪 测试 6/6: '现在几点了' (zh-CN)
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:88 |   相对路径: data\en\现在几点了.wav
2025-06-23 23:00:57 | ERROR | __main__:test_relative_path_generation:99 |   ❌ 相对路径错误，期望: data/zh/现在几点了.wav, 实际: data\en\现在几点了.wav
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:104 |   ✅ 绝对路径格式正确
2025-06-23 23:00:57 | INFO | __main__:test_relative_path_generation:112 | 📊 相对路径生成测试结果: 0/6 成功 (0.0%)
2025-06-23 23:00:57 | INFO | __main__:test_file_generation_with_relative_paths:119 | 🎵 测试使用相对路径生成文件...
2025-06-23 23:00:58 | INFO | __main__:test_file_generation_with_relative_paths:139 |   ⏱️ 生成耗时: 1.80秒
2025-06-23 23:00:58 | INFO | __main__:test_file_generation_with_relative_paths:149 |   ✅ 文件存在: 13.6KB
2025-06-23 23:00:59 | INFO | __main__:test_file_generation_with_relative_paths:129 | 🧪 测试 2/2: '测试相对路径' (zh-CN)
2025-06-23 23:00:59 | INFO | utils.tts_utils:generate_audio_file:780 | 📁 生成音频文件: '测试相对路径' -> data\en\测试相对路径.wav
2025-06-23 23:00:59 | INFO | utils.tts_utils:_edge_tts_generate:353 | 🎤 使用Edge TTS生成语音: '测试相对路径' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:01:01 | INFO | utils.tts_utils:generate_audio_file:796 | ✅ 音频文件生成成功: data\en\测试相对路径.wav (12.2KB)
2025-06-23 23:01:01 | INFO | __main__:test_file_generation_with_relative_paths:138 |   ✅ 生成成功: data\en\测试相对路径.wav
2025-06-23 23:01:01 | INFO | __main__:test_file_generation_with_relative_paths:139 |   ⏱️ 生成耗时: 1.78秒
2025-06-23 23:01:01 | INFO | __main__:test_file_generation_with_relative_paths:149 |   ✅ 文件存在: 12.2KB
2025-06-23 23:01:01 | INFO | __main__:test_file_generation_with_relative_paths:162 | 📊 文件生成测试结果: 2/2 成功 (100.0%)
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:169 | 📁 测试目录结构...
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:184 | 📂 语言目录: ['en', 'zh']
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:193 |   en/: 2 个文件
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:196 |     📄 测试相对路径.wav (12.2KB)
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:193 |   zh/: 1 个文件
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:196 |     📄 现在几点了.wav (10.3KB)
2025-06-23 23:01:01 | INFO | __main__:test_directory_structure:200 | 📊 总文件数: 3
2025-06-23 23:01:01 | INFO | __main__:test_cache_statistics_with_relative_paths:213 | 📊 测试缓存统计（相对路径）...
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:218 | 📈 缓存统计信息:
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:220 |   📄 总文件数: 3
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:221 |   💾 总大小: 36.1KB
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:226 | ✅ 数据目录名称正确
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:231 |   📂 各语言统计:
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:233 |     en: 2 个文件, 25.9KB
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:238 |       📄 测试相对路径.wav (12.2KB)
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:233 |     zh: 1 个文件, 10.3KB
2025-06-23 23:01:02 | INFO | __main__:test_cache_statistics_with_relative_paths:238 |       📄 现在几点了.wav (10.3KB)
2025-06-23 23:02:46 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:02:46 | INFO | utils.tts_utils:generate_audio_file:782 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 23:02:46 | INFO | utils.tts_utils:_edge_tts_generate:355 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: en-US-AriaNeural)
2025-06-23 23:02:48 | INFO | utils.tts_utils:generate_audio_file:798 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:02:48 | INFO | utils.tts_utils:generate_audio_file:782 | 📁 生成音频文件: '打开蓝牙' -> data\en\打开蓝牙.wav
2025-06-23 23:02:48 | INFO | utils.tts_utils:_edge_tts_generate:355 | 🎤 使用Edge TTS生成语音: '打开蓝牙' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:02:49 | INFO | utils.tts_utils:generate_audio_file:798 | ✅ 音频文件生成成功: data\en\打开蓝牙.wav (9.6KB)
2025-06-23 23:03:41 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:03:41 | INFO | utils.tts_utils:generate_audio_file:784 | 📁 生成音频文件: '打开蓝牙' -> data\en\打开蓝牙.wav
2025-06-23 23:03:41 | INFO | utils.tts_utils:_edge_tts_generate:357 | 🎤 使用Edge TTS生成语音: '打开蓝牙' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:03:43 | INFO | utils.tts_utils:generate_audio_file:800 | ✅ 音频文件生成成功: data\en\打开蓝牙.wav (9.6KB)
2025-06-23 23:03:43 | INFO | utils.tts_utils:generate_audio_file:784 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 23:03:43 | INFO | utils.tts_utils:_edge_tts_generate:357 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: en-US-AriaNeural)
2025-06-23 23:03:45 | INFO | utils.tts_utils:generate_audio_file:800 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:03:56 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:03:56 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:06 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:06 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:31 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:31 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:40 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:04:40 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: '测试中文路径' -> data\zh\测试中文路径.wav
2025-06-23 23:04:40 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: '测试中文路径' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:04:42 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\zh\测试中文路径.wav (12.0KB)
2025-06-23 23:08:06 | INFO | __main__:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:08:06 | INFO | __main__:generate_audio_file:788 | 📁 生成音频文件: 'open bluetooth' -> data\zh\open_bluetooth.wav
2025-06-23 23:08:06 | INFO | __main__:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:08:12 | INFO | __main__:generate_audio_file:804 | ✅ 音频文件生成成功: data\zh\open_bluetooth.wav (10.3KB)
2025-06-23 23:08:41 | INFO | __main__:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:08:41 | INFO | __main__:generate_audio_file:788 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 23:08:41 | INFO | __main__:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: en-US-AriaNeural)
2025-06-23 23:08:47 | INFO | __main__:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:09:03 | INFO | __main__:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:09:03 | INFO | __main__:generate_audio_file:788 | 📁 生成音频文件: 'close bluetooth' -> data\en\close_bluetooth.wav
2025-06-23 23:09:03 | INFO | __main__:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'close bluetooth' (语音: en-US-AriaNeural)
2025-06-23 23:09:09 | INFO | __main__:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\close_bluetooth.wav (11.8KB)
2025-06-23 23:14:20 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 23:14:20 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 23:14:20 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 23:14:20 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 23:14:20 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 23:14:20 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 23:14:20 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:14:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 23:14:23 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 23:14:23 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:14:23 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 23:14:23 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 23:14:23 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:23 | INFO | __main__:test_voice_command_cache_logic:20 | 🧪 测试语音命令缓存逻辑...
2025-06-23 23:14:23 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:14:23 | INFO | __main__:test_voice_command_cache_logic:31 | 🧹 清理测试命令的缓存文件...
2025-06-23 23:14:23 | INFO | __main__:test_voice_command_cache_logic:38 | 🎯 第一次执行语音命令（应该生成新文件）...
2025-06-23 23:14:23 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:25 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:27 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:27 | INFO | __main__:test_voice_command_cache_logic:44 | ✅ 第一次执行成功，耗时: 3.63秒
2025-06-23 23:14:27 | INFO | __main__:test_voice_command_cache_logic:58 | 🎯 第二次执行语音命令（应该使用缓存文件）...
2025-06-23 23:14:27 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:27 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:28 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:28 | INFO | __main__:test_voice_command_cache_logic:64 | ✅ 第二次执行成功，耗时: 0.93秒
2025-06-23 23:14:28 | INFO | __main__:test_voice_command_cache_logic:69 | 🚀 缓存效果: 节省了 2.70秒
2025-06-23 23:14:28 | INFO | __main__:test_voice_command_cache_logic:77 | 📊 验证缓存统计...
2025-06-23 23:14:30 | INFO | __main__:test_voice_command_cache_logic:79 | 缓存文件总数: 4
2025-06-23 23:14:30 | INFO | __main__:test_voice_command_cache_logic:80 | 缓存总大小: 47.4KB
2025-06-23 23:14:30 | INFO | __main__:test_voice_command_with_different_languages:91 | 🌍 测试不同语言的语音命令...
2025-06-23 23:14:30 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:14:30 | INFO | __main__:test_voice_command_with_different_languages:106 | 🧪 测试 1/4: 英文 - 'open bluetooth' (en-US)
2025-06-23 23:14:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\open_bluetooth.wav
2025-06-23 23:14:30 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:14:31 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 23:14:31 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:31 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 23:14:31 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'open bluetooth'
2025-06-23 23:14:31 | INFO | __main__:test_voice_command_with_different_languages:114 |   ✅ 执行成功，耗时: 0.93秒
2025-06-23 23:14:31 | INFO | __main__:test_voice_command_with_different_languages:121 |   📁 文件路径: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:14:32 | INFO | __main__:test_voice_command_with_different_languages:106 | 🧪 测试 2/4: 中文 - '打开蓝牙' (zh-CN)
2025-06-23 23:14:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\zh\打开蓝牙.wav
2025-06-23 23:14:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\zh\打开蓝牙.wav
2025-06-23 23:14:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: '打开蓝牙' (zh-CN)
2025-06-23 23:14:32 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: '打开蓝牙' -> data\zh\打开蓝牙.wav
2025-06-23 23:14:32 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: '打开蓝牙' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:14:33 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\zh\打开蓝牙.wav (9.6KB)
2025-06-23 23:14:33 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:34 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: 打开蓝牙.wav (9.6KB)
2025-06-23 23:14:34 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:34 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: 打开蓝牙.wav
2025-06-23 23:14:34 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: '打开蓝牙'
2025-06-23 23:14:34 | INFO | __main__:test_voice_command_with_different_languages:114 |   ✅ 执行成功，耗时: 2.25秒
2025-06-23 23:14:34 | INFO | __main__:test_voice_command_with_different_languages:121 |   📁 文件路径: data\zh\打开蓝牙.wav (9.6KB)
2025-06-23 23:14:35 | INFO | __main__:test_voice_command_with_different_languages:106 | 🧪 测试 3/4: 英文 - 'close wifi' (en-US)
2025-06-23 23:14:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\close_wifi.wav
2025-06-23 23:14:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\close_wifi.wav
2025-06-23 23:14:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'close wifi' (en-US)
2025-06-23 23:14:35 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'close wifi' -> data\en\close_wifi.wav
2025-06-23 23:14:35 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'close wifi' (语音: en-US-AriaNeural)
2025-06-23 23:14:37 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\close_wifi.wav (11.8KB)
2025-06-23 23:14:37 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:37 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: close_wifi.wav (11.8KB)
2025-06-23 23:14:37 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:37 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: close_wifi.wav
2025-06-23 23:14:37 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'close wifi'
2025-06-23 23:14:37 | INFO | __main__:test_voice_command_with_different_languages:114 |   ✅ 执行成功，耗时: 2.43秒
2025-06-23 23:14:37 | INFO | __main__:test_voice_command_with_different_languages:121 |   📁 文件路径: data\en\close_wifi.wav (11.8KB)
2025-06-23 23:14:38 | INFO | __main__:test_voice_command_with_different_languages:106 | 🧪 测试 4/4: 中文 - '关闭WiFi' (zh-CN)
2025-06-23 23:14:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\zh\关闭WiFi.wav
2025-06-23 23:14:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\zh\关闭WiFi.wav
2025-06-23 23:14:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: '关闭WiFi' (zh-CN)
2025-06-23 23:14:38 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: '关闭WiFi' -> data\zh\关闭WiFi.wav
2025-06-23 23:14:38 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: '关闭WiFi' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:14:40 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\zh\关闭WiFi.wav (9.8KB)
2025-06-23 23:14:40 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:41 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: 关闭WiFi.wav (9.8KB)
2025-06-23 23:14:41 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:41 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: 关闭WiFi.wav
2025-06-23 23:14:41 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: '关闭WiFi'
2025-06-23 23:14:41 | INFO | __main__:test_voice_command_with_different_languages:114 |   ✅ 执行成功，耗时: 2.34秒
2025-06-23 23:14:41 | INFO | __main__:test_voice_command_with_different_languages:121 |   📁 文件路径: data\zh\关闭WiFi.wav (9.8KB)
2025-06-23 23:14:42 | INFO | __main__:test_voice_command_with_different_languages:134 | 📊 多语言测试结果: 4/4 成功 (100.0%)
2025-06-23 23:14:42 | INFO | __main__:test_voice_command_file_verification:145 | 🔍 测试语音命令文件验证...
2025-06-23 23:14:42 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:14:42 | INFO | __main__:test_voice_command_file_verification:155 | 🎵 生成测试语音文件...
2025-06-23 23:14:42 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:44 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:45 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:45 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:45 | INFO | __main__:test_voice_command_file_verification:178 | ✅ 文件验证通过
2025-06-23 23:14:45 | INFO | __main__:test_voice_command_file_verification:184 | 🔊 测试文件播放...
2025-06-23 23:14:46 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:46 | INFO | __main__:test_voice_command_file_verification:188 | ✅ 文件播放成功
2025-06-23 23:14:46 | INFO | __main__:test_voice_command_performance:202 | ⚡ 测试语音命令性能...
2025-06-23 23:14:46 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:14:46 | INFO | __main__:test_voice_command_performance:218 | 🧪 测试 短命令: 'hi'
2025-06-23 23:14:46 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:46 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\hi.wav
2025-06-23 23:14:46 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\hi.wav
2025-06-23 23:14:46 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'hi' (en-US)
2025-06-23 23:14:46 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'hi' -> data\en\hi.wav
2025-06-23 23:14:46 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'hi' (语音: en-US-AriaNeural)
2025-06-23 23:14:48 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\hi.wav (9.0KB)
2025-06-23 23:14:48 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:48 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: hi.wav (9.0KB)
2025-06-23 23:14:49 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:49 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: hi.wav
2025-06-23 23:14:49 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'hi'
2025-06-23 23:14:49 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:49 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\hi.wav
2025-06-23 23:14:49 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:49 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\hi.wav (9.0KB)
2025-06-23 23:14:49 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: hi.wav (9.0KB)
2025-06-23 23:14:50 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:50 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: hi.wav
2025-06-23 23:14:50 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'hi'
2025-06-23 23:14:50 | INFO | __main__:test_voice_command_performance:242 |   第一次: 2.67秒, 第二次: 1.01秒, 节省: 1.66秒
2025-06-23 23:14:50 | INFO | __main__:test_voice_command_performance:218 | 🧪 测试 中等命令: 'open bluetooth'
2025-06-23 23:14:50 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:50 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\open_bluetooth.wav
2025-06-23 23:14:50 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:50 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:14:51 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 23:14:51 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:51 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 23:14:51 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'open bluetooth'
2025-06-23 23:14:51 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:51 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\open_bluetooth.wav
2025-06-23 23:14:51 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:51 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:14:52 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 23:14:52 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:52 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 23:14:52 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'open bluetooth'
2025-06-23 23:14:52 | INFO | __main__:test_voice_command_performance:242 |   第一次: 0.99秒, 第二次: 0.95秒, 节省: 0.04秒
2025-06-23 23:14:53 | INFO | __main__:test_voice_command_performance:218 | 🧪 测试 长命令: 'what time is it now'
2025-06-23 23:14:53 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:53 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\what_time_is_it_now.wav
2025-06-23 23:14:53 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\what_time_is_it_now.wav
2025-06-23 23:14:53 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'what time is it now' (en-US)
2025-06-23 23:14:53 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'what time is it now' -> data\en\what_time_is_it_now.wav
2025-06-23 23:14:53 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'what time is it now' (语音: en-US-AriaNeural)
2025-06-23 23:14:54 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\what_time_is_it_now.wav (13.1KB)
2025-06-23 23:14:54 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:55 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: what_time_is_it_now.wav (13.1KB)
2025-06-23 23:14:55 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:55 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: what_time_is_it_now.wav
2025-06-23 23:14:55 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'what time is it now'
2025-06-23 23:14:55 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:55 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\what_time_is_it_now.wav
2025-06-23 23:14:55 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:55 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\what_time_is_it_now.wav (13.1KB)
2025-06-23 23:14:56 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: what_time_is_it_now.wav (13.1KB)
2025-06-23 23:14:56 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:56 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: what_time_is_it_now.wav
2025-06-23 23:14:56 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'what time is it now'
2025-06-23 23:14:56 | INFO | __main__:test_voice_command_performance:242 |   第一次: 2.38秒, 第二次: 0.94秒, 节省: 1.43秒
2025-06-23 23:14:56 | INFO | __main__:test_voice_command_performance:218 | 🧪 测试 超长命令: 'please turn on the bluetooth connection'
2025-06-23 23:14:56 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:56 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:56 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 📁 语音文件不存在，需要生成: data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:56 | INFO | pages.apps.ella.main_page:_play_voice_command_file:570 | 🎵 生成语音文件: 'please turn on the bluetooth connection' (en-US)
2025-06-23 23:14:56 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'please turn on the bluetooth connection' -> data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:56 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'please turn on the bluetooth connection' (语音: en-US-AriaNeural)
2025-06-23 23:14:58 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\please_turn_on_the_bluetooth_connection.wav (17.3KB)
2025-06-23 23:14:58 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:59 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: please_turn_on_the_bluetooth_connection.wav (17.3KB)
2025-06-23 23:14:59 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:14:59 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:59 | INFO | pages.apps.ella.main_page:_play_voice_command_file:582 | ✅ 新生成语音文件播放成功: 'please turn on the bluetooth connection'
2025-06-23 23:14:59 | INFO | pages.apps.ella.main_page:_play_voice_command_file:539 | 使用TTS服务: edge_tts
2025-06-23 23:14:59 | INFO | pages.apps.ella.main_page:_play_voice_command_file:545 | 语音文件路径: data\en\please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:14:59 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:14:59 | INFO | pages.apps.ella.main_page:_play_voice_command_file:554 | 🎯 使用已存在的语音文件: data\en\please_turn_on_the_bluetooth_connection.wav (17.3KB)
2025-06-23 23:15:00 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: please_turn_on_the_bluetooth_connection.wav (17.3KB)
2025-06-23 23:15:00 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:15:00 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: please_turn_on_the_bluetooth_connection.wav
2025-06-23 23:15:00 | INFO | pages.apps.ella.main_page:_play_voice_command_file:560 | ✅ 语音文件播放成功: 'please turn on the bluetooth connection'
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:242 |   第一次: 2.50秒, 第二次: 0.95秒, 节省: 1.55秒
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:256 | 📊 性能分析:
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:257 |   平均首次执行时间: 2.13秒
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:258 |   平均缓存执行时间: 0.96秒
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:259 |   平均节省时间: 1.17秒
2025-06-23 23:15:00 | INFO | __main__:test_voice_command_performance:260 |   缓存效率提升: 54.8%
2025-06-23 23:23:02 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 23:23:02 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 23:23:02 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 23:23:02 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 23:23:02 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 23:23:02 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 23:23:02 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:23:09 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-06-23 23:23:09 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-06-23 23:23:09 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-06-23 23:23:09 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-06-23 23:23:10 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-06-23 23:23:12 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-06-23 23:23:15 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-06-23 23:23:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:23:27 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-06-23 23:23:27 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-06-23 23:23:27 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-06-23 23:23:27 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-06-23 23:26:45 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 23:26:45 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 23:26:45 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 23:26:45 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 23:26:45 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 23:26:45 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 23:26:45 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:26:48 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 23:26:48 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 23:26:48 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:26:48 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 23:26:48 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 23:26:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 23:26:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:26:49 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:26:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 23:26:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:26:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1831 | 检查蓝牙状态
2025-06-23 23:26:50 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1845 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:26:50 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:55 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:26:51 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:26:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:26:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:26:52 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:26:52 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:77 | ✅ 页面和输入框状态就绪
2025-06-23 23:26:52 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: 模拟语音输入_open bluetooth
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:execute_voice_command:440 | 🎤 执行语音命令: 'open bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:26:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:26:53 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:26:53 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:26:53 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:26:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:26:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:26:54 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:26:54 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:26:54 | INFO | pages.apps.ella.main_page:start_voice_input:730 | 启动语音输入...
2025-06-23 23:26:55 | INFO | pages.apps.ella.main_page:start_voice_input:748 | 找到语音按钮: 语音按钮(备选)
2025-06-23 23:26:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 23:26:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:26:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 23:26:56 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 23:26:56 | INFO | pages.apps.ella.main_page:start_voice_input:750 | ✅ 语音按钮点击成功
2025-06-23 23:26:59 | INFO | pages.apps.ella.main_page:start_voice_input:763 | 尝试长按输入框启动语音输入...
2025-06-23 23:26:59 | INFO | pages.apps.ella.main_page:start_voice_input:772 | 尝试通过坐标点击语音按钮区域...
2025-06-23 23:26:59 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (972, 2160)
2025-06-23 23:27:01 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (918, 2160)
2025-06-23 23:27:04 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (540, 2280)
2025-06-23 23:27:06 | WARNING | pages.apps.ella.main_page:start_voice_input:790 | ❌ 无法启动语音输入
2025-06-23 23:27:06 | WARNING | pages.apps.ella.main_page:execute_voice_command:454 | 无法启动语音输入，回退到文本输入
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:07 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:27:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:27:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:27:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:08 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:27:08 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 23:27:08 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 23:27:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:09 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 23:27:09 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 23:27:10 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 23:27:10 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 23:27:10 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:11 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:12 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 23:27:12 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 23:27:13 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 23:27:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:13 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 23:27:13 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 23:27:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:27:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:27:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:27:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:27:14 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 23:27:14 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 23:27:14 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 23:27:14 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 23:27:14 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:91 | ✅ 模拟语音命令执行成功，耗时: 21.99秒
2025-06-23 23:27:14 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 模拟语音命令执行 耗时 21.986秒 
2025-06-23 23:27:14 | INFO | pages.apps.ella.main_page:wait_for_response:1268 | 快速等待AI响应，超时时间: 10秒
2025-06-23 23:27:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:wait_for_response:1283 | 初始元素数量: 13
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:16 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1585 | 检测到TTS播放按钮
2025-06-23 23:27:16 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1352 | 检测到TTS按钮，表示有AI响应
2025-06-23 23:27:16 | INFO | pages.apps.ella.main_page:wait_for_response:1301 | ✅ 快速检测到AI响应
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:get_response_text_smart:1690 | 智能获取响应文本...
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:18 | INFO | pages.apps.ella.main_page:get_response_text:1714 | 获取AI响应文本
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:get_response_text:1730 | 页面上所有文本元素数量: 12
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:_is_ai_response:1553 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:get_response_text:1738 | 找到AI响应: 蓝牙 已打开
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:_is_ai_response:1553 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:get_response_text:1738 | 找到AI响应: 蓝牙 已打开
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:get_response_text:1751 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 23:27:21 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:120 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:verify_command_in_response:1867 | 验证响应是否包含命令: open bluetooth
2025-06-23 23:27:21 | INFO | pages.apps.ella.main_page:verify_command_in_response:1897 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 23:27:21 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:129 | ✅ 响应内容与命令相关
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1666 | 智能检查蓝牙状态...
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1831 | 检查蓝牙状态
2025-06-23 23:27:24 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1845 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:139 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:149 | ✅ 蓝牙已成功开启
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:153 | 📊 蓝牙状态变化: 否
2025-06-23 23:27:24 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: 模拟语音输入_open bluetooth - ✅ 成功, 耗时: 21.99秒
2025-06-23 23:27:24 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:178 | 🎉 模拟语音输入open bluetooth测试完成
2025-06-23 23:27:24 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 23:27:24 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 23:27:25 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:27:25 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:27:25 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:27:25 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:27:25 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 23:27:25 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:27:25 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 23:27:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 23:27:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 23:27:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1831 | 检查蓝牙状态
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1845 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:27:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:190 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:27:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:27:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:27 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:27 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:27:28 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:206 | ✅ 页面和输入框状态就绪
2025-06-23 23:27:28 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: TTS语音输入_open bluetooth
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:execute_real_voice_command:610 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN)
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:27:28 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:27:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:29 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:29 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:27:30 | INFO | pages.apps.ella.main_page:start_voice_input:730 | 启动语音输入...
2025-06-23 23:27:30 | INFO | pages.apps.ella.main_page:start_voice_input:748 | 找到语音按钮: 语音按钮(备选)
2025-06-23 23:27:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 23:27:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 23:27:31 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 23:27:31 | INFO | pages.apps.ella.main_page:start_voice_input:750 | ✅ 语音按钮点击成功
2025-06-23 23:27:34 | INFO | pages.apps.ella.main_page:start_voice_input:763 | 尝试长按输入框启动语音输入...
2025-06-23 23:27:34 | INFO | pages.apps.ella.main_page:start_voice_input:772 | 尝试通过坐标点击语音按钮区域...
2025-06-23 23:27:34 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (972, 2160)
2025-06-23 23:27:37 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (918, 2160)
2025-06-23 23:27:39 | INFO | pages.apps.ella.main_page:start_voice_input:783 | 尝试点击坐标 (540, 2280)
2025-06-23 23:27:41 | WARNING | pages.apps.ella.main_page:start_voice_input:790 | ❌ 无法启动语音输入
2025-06-23 23:27:41 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:624 | 无法启动语音输入，回退到文本输入
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:921 | 确保在对话页面...
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:42 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:932 | ✅ 已在对话页面
2025-06-23 23:27:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1160 | 确保输入框就绪...
2025-06-23 23:27:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1164 | 找到主输入框
2025-06-23 23:27:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:42 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:42 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1168 | ✅ 主输入框已激活
2025-06-23 23:27:43 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 23:27:43 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 23:27:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:43 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 23:27:43 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 23:27:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 23:27:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 23:27:44 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 23:27:44 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 23:27:45 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 23:27:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:46 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:27:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:47 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 23:27:47 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 23:27:48 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 23:27:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:27:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:27:48 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 23:27:48 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 23:27:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:27:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:27:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:27:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:27:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:27:49 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 23:27:49 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 23:27:49 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 23:27:49 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 23:27:49 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:225 | ✅ TTS语音命令执行成功，耗时: 21.59秒
2025-06-23 23:27:49 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: TTS语音命令执行 耗时 21.585秒 
2025-06-23 23:27:49 | INFO | pages.apps.ella.main_page:wait_for_response:1268 | 快速等待AI响应，超时时间: 12秒
2025-06-23 23:27:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:wait_for_response:1283 | 初始元素数量: 13
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:51 | INFO | pages.apps.ella.main_page:_is_ai_response:1553 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:27:51 | INFO | pages.apps.ella.main_page:_check_response_elements:1424 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 23:27:51 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1357 | 检测到响应相关元素
2025-06-23 23:27:51 | INFO | pages.apps.ella.main_page:wait_for_response:1301 | ✅ 快速检测到AI响应
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:get_response_text_smart:1690 | 智能获取响应文本...
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:27:55 | INFO | pages.apps.ella.main_page:get_response_text:1714 | 获取AI响应文本
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:get_response_text:1730 | 页面上所有文本元素数量: 13
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:_is_ai_response:1553 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:get_response_text:1738 | 找到AI响应: 蓝牙 已打开
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:get_response_text:1751 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 23:27:58 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:254 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:verify_command_in_response:1867 | 验证响应是否包含命令: open bluetooth
2025-06-23 23:27:58 | INFO | pages.apps.ella.main_page:verify_command_in_response:1897 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 23:27:58 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:263 | ✅ TTS响应内容与命令相关
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1666 | 智能检查蓝牙状态...
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:953 | 检查当前进程是否是Ella...
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:960 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:970 | ✅ 当前在Ella应用进程
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1831 | 检查蓝牙状态
2025-06-23 23:28:01 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1845 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:273 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:277 | ✅ 蓝牙已成功开启
2025-06-23 23:28:01 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:281 | 📊 蓝牙状态变化: 否
2025-06-23 23:28:01 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: TTS语音输入_open bluetooth - ✅ 成功, 耗时: 21.59秒
2025-06-23 23:28:02 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:308 | 🎉 TTS真实语音输入open bluetooth测试完成
2025-06-23 23:28:02 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 23:28:02 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 23:28:02 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 23:28:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-06-23 23:34:10 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 23:34:10 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 23:34:10 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 23:34:10 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 23:34:10 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 23:34:10 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 23:34:10 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:34:12 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 23:34:12 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 23:34:12 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:34:12 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 23:34:13 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 23:34:13 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:13 | INFO | __main__:test_cache_priority_logic:20 | 🧪 测试缓存优先逻辑...
2025-06-23 23:34:13 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:34:13 | INFO | __main__:test_cache_priority_logic:31 | 🧹 清理测试命令的缓存文件...
2025-06-23 23:34:13 | INFO | __main__:test_cache_priority_logic:38 | 🎯 第一次执行（应该生成新文件）...
2025-06-23 23:34:13 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:15 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 1.99秒)
2025-06-23 23:34:17 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:17 | INFO | __main__:test_cache_priority_logic:44 | ✅ 第一次执行成功，耗时: 4.00秒
2025-06-23 23:34:17 | INFO | __main__:test_cache_priority_logic:58 | 🎯 第二次执行（应该使用缓存文件）...
2025-06-23 23:34:17 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:18 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:18 | INFO | __main__:test_cache_priority_logic:64 | ✅ 第二次执行成功，耗时: 0.94秒
2025-06-23 23:34:18 | INFO | __main__:test_cache_priority_logic:70 | 🚀 缓存效果: 节省了 3.06秒 (效率提升: 76.6%)
2025-06-23 23:34:18 | INFO | __main__:test_real_voice_command_with_cache:86 | 🎤 测试完整的execute_real_voice_command缓存功能...
2025-06-23 23:34:18 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:34:18 | INFO | __main__:test_real_voice_command_with_cache:105 | 缓存状态: 不存在
2025-06-23 23:34:18 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:20 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 1.92秒)
2025-06-23 23:34:20 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:20 | INFO | __main__:test_real_voice_command_with_cache:113 |   ✅ 执行成功，耗时: 2.54秒
2025-06-23 23:34:21 | INFO | __main__:test_real_voice_command_with_cache:99 | 🧪 测试 2/2: 中文测试 - '测试真实语音缓存' (zh-CN)
2025-06-23 23:34:21 | INFO | __main__:test_real_voice_command_with_cache:105 | 缓存状态: 不存在
2025-06-23 23:34:21 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:21 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\zh\测试真实语音缓存.wav
2025-06-23 23:34:21 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: '测试真实语音缓存' (zh-CN)
2025-06-23 23:34:21 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: '测试真实语音缓存' -> data\zh\测试真实语音缓存.wav
2025-06-23 23:34:21 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: '测试真实语音缓存' (语音: zh-CN-XiaoxiaoNeural)
2025-06-23 23:34:24 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\zh\测试真实语音缓存.wav (14.1KB)
2025-06-23 23:34:24 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 2.49秒)
2025-06-23 23:34:24 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: 测试真实语音缓存.wav (14.1KB)
2025-06-23 23:34:25 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:25 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: 测试真实语音缓存.wav
2025-06-23 23:34:25 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: '测试真实语音缓存' (播放耗时: 0.67秒, 总耗时: 3.17秒)
2025-06-23 23:34:25 | INFO | __main__:test_real_voice_command_with_cache:113 |   ✅ 执行成功，耗时: 3.18秒
2025-06-23 23:34:25 | INFO | __main__:test_real_voice_command_with_cache:119 |   📁 文件路径: data\zh\测试真实语音缓存.wav (14.1KB)
2025-06-23 23:34:26 | INFO | __main__:test_real_voice_command_with_cache:132 | 📊 完整功能测试结果: 2/2 成功 (100.0%)
2025-06-23 23:34:26 | INFO | __main__:test_cache_status_logging:143 | 📋 测试缓存状态日志功能...
2025-06-23 23:34:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:34:26 | INFO | __main__:test_cache_status_logging:154 | 🧪 测试缓存状态日志: 'existing command' (en-US)
2025-06-23 23:34:26 | INFO | pages.apps.ella.main_page:_log_voice_file_cache_status:729 | 📁 语音文件缓存状态: 不存在 - data\en\existing_command.wav (将自动生成)
2025-06-23 23:34:26 | INFO | __main__:test_cache_status_logging:154 | 🧪 测试缓存状态日志: 'non existing command xyz' (en-US)
2025-06-23 23:34:26 | INFO | pages.apps.ella.main_page:_log_voice_file_cache_status:729 | 📁 语音文件缓存状态: 不存在 - data\en\non_existing_command_xyz.wav (将自动生成)
2025-06-23 23:34:27 | INFO | __main__:test_performance_comparison:170 | ⚡ 测试性能对比...
2025-06-23 23:34:27 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:34:28 | INFO | __main__:test_performance_comparison:185 | 🧪 测试 短命令: 'hi'
2025-06-23 23:34:28 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:28 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\en\hi.wav
2025-06-23 23:34:28 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: 'hi' (en-US)
2025-06-23 23:34:28 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'hi' -> data\en\hi.wav
2025-06-23 23:34:28 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'hi' (语音: en-US-AriaNeural)
2025-06-23 23:34:30 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\hi.wav (9.0KB)
2025-06-23 23:34:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 2.08秒)
2025-06-23 23:34:30 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: hi.wav (9.0KB)
2025-06-23 23:34:30 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:30 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: hi.wav
2025-06-23 23:34:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: 'hi' (播放耗时: 0.63秒, 总耗时: 2.71秒)
2025-06-23 23:34:30 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:30 | INFO | pages.apps.ella.main_page:_play_voice_command_file:562 | 🔍 检查缓存文件: data\en\hi.wav
2025-06-23 23:34:31 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 🎯 缓存命中! 使用已存在文件: data\en\hi.wav (9.0KB)
2025-06-23 23:34:31 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: hi.wav (9.0KB)
2025-06-23 23:34:31 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:31 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: hi.wav
2025-06-23 23:34:31 | INFO | pages.apps.ella.main_page:_play_voice_command_file:576 | ✅ 缓存文件播放成功: 'hi' (播放耗时: 0.54秒)
2025-06-23 23:34:31 | INFO | __main__:test_performance_comparison:216 |   第一次: 2.72秒, 第二次: 0.94秒
2025-06-23 23:34:31 | INFO | __main__:test_performance_comparison:217 |   节省: 1.78秒, 效率提升: 65.3%
2025-06-23 23:34:32 | INFO | __main__:test_performance_comparison:185 | 🧪 测试 中等命令: 'open bluetooth'
2025-06-23 23:34:32 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\en\open_bluetooth.wav
2025-06-23 23:34:32 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: 'open bluetooth' (en-US)
2025-06-23 23:34:32 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'open bluetooth' -> data\en\open_bluetooth.wav
2025-06-23 23:34:32 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'open bluetooth' (语音: en-US-AriaNeural)
2025-06-23 23:34:34 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:34:34 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 1.82秒)
2025-06-23 23:34:34 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 23:34:34 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:34 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 23:34:34 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: 'open bluetooth' (播放耗时: 0.54秒, 总耗时: 2.37秒)
2025-06-23 23:34:34 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:34 | INFO | pages.apps.ella.main_page:_play_voice_command_file:562 | 🔍 检查缓存文件: data\en\open_bluetooth.wav
2025-06-23 23:34:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 🎯 缓存命中! 使用已存在文件: data\en\open_bluetooth.wav (11.8KB)
2025-06-23 23:34:35 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: open_bluetooth.wav (11.8KB)
2025-06-23 23:34:35 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:35 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: open_bluetooth.wav
2025-06-23 23:34:35 | INFO | pages.apps.ella.main_page:_play_voice_command_file:576 | ✅ 缓存文件播放成功: 'open bluetooth' (播放耗时: 0.53秒)
2025-06-23 23:34:35 | INFO | __main__:test_performance_comparison:216 |   第一次: 2.38秒, 第二次: 0.93秒
2025-06-23 23:34:35 | INFO | __main__:test_performance_comparison:217 |   节省: 1.44秒, 效率提升: 60.8%
2025-06-23 23:34:36 | INFO | __main__:test_performance_comparison:185 | 🧪 测试 长命令: 'what time is it now'
2025-06-23 23:34:36 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:36 | INFO | pages.apps.ella.main_page:_play_voice_command_file:585 | 📁 缓存未命中，文件不存在: data\en\what_time_is_it_now.wav
2025-06-23 23:34:36 | INFO | pages.apps.ella.main_page:_play_voice_command_file:590 | 🎵 生成新语音文件: 'what time is it now' (en-US)
2025-06-23 23:34:36 | INFO | utils.tts_utils:generate_audio_file:788 | 📁 生成音频文件: 'what time is it now' -> data\en\what_time_is_it_now.wav
2025-06-23 23:34:36 | INFO | utils.tts_utils:_edge_tts_generate:361 | 🎤 使用Edge TTS生成语音: 'what time is it now' (语音: en-US-AriaNeural)
2025-06-23 23:34:37 | INFO | utils.tts_utils:generate_audio_file:804 | ✅ 音频文件生成成功: data\en\what_time_is_it_now.wav (13.1KB)
2025-06-23 23:34:37 | INFO | pages.apps.ella.main_page:_play_voice_command_file:600 | ✅ 语音文件生成完成 (生成耗时: 1.77秒)
2025-06-23 23:34:38 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: what_time_is_it_now.wav (13.1KB)
2025-06-23 23:34:38 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:38 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: what_time_is_it_now.wav
2025-06-23 23:34:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:609 | ✅ 新文件播放成功: 'what time is it now' (播放耗时: 0.54秒, 总耗时: 2.31秒)
2025-06-23 23:34:38 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:34:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:562 | 🔍 检查缓存文件: data\en\what_time_is_it_now.wav
2025-06-23 23:34:38 | INFO | pages.apps.ella.main_page:_play_voice_command_file:567 | 🎯 缓存命中! 使用已存在文件: data\en\what_time_is_it_now.wav (13.1KB)
2025-06-23 23:34:39 | INFO | utils.tts_utils:play_audio:604 | 🔊 开始播放音频: what_time_is_it_now.wav (13.1KB)
2025-06-23 23:34:39 | INFO | utils.tts_utils:_play_with_system:693 | ✅ 系统播放器播放完成
2025-06-23 23:34:39 | INFO | utils.tts_utils:play_audio:613 | ✅ 音频播放完成: what_time_is_it_now.wav
2025-06-23 23:34:39 | INFO | pages.apps.ella.main_page:_play_voice_command_file:576 | ✅ 缓存文件播放成功: 'what time is it now' (播放耗时: 0.57秒)
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:216 |   第一次: 2.32秒, 第二次: 0.98秒
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:217 |   节省: 1.34秒, 效率提升: 57.9%
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:231 | 📊 性能分析总结:
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:232 |   平均首次执行时间: 2.47秒
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:233 |   平均缓存执行时间: 0.95秒
2025-06-23 23:34:39 | INFO | __main__:test_performance_comparison:234 |   平均效率提升: 61.3%
2025-06-23 23:35:50 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-06-23 23:35:50 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-06-23 23:35:50 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-06-23 23:35:50 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-06-23 23:35:50 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-06-23 23:35:50 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-06-23 23:35:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-06-23 23:35:52 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-06-23 23:35:52 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-06-23 23:35:52 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:35:52 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-06-23 23:35:52 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-06-23 23:35:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-06-23 23:35:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-06-23 23:35:53 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:35:53 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:35:53 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:35:53 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 23:35:53 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:35:53 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 23:35:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 23:35:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:35:54 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 23:35:54 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:35:54 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1893 | 检查蓝牙状态
2025-06-23 23:35:54 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1907 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:35:54 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:55 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:35:54 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:35:54 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:35:55 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:35:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:35:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:35:56 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:35:56 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:35:56 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:77 | ✅ 页面和输入框状态就绪
2025-06-23 23:35:56 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: 模拟语音输入_open bluetooth
2025-06-23 23:35:56 | INFO | pages.apps.ella.main_page:execute_voice_command:440 | 🎤 执行语音命令: 'open bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-06-23 23:35:56 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:35:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:35:57 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:35:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:35:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:35:58 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:35:58 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:35:58 | INFO | pages.apps.ella.main_page:start_voice_input:792 | 启动语音输入...
2025-06-23 23:35:59 | INFO | pages.apps.ella.main_page:start_voice_input:810 | 找到语音按钮: 语音按钮(备选)
2025-06-23 23:35:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 23:35:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:35:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 23:36:00 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 23:36:00 | INFO | pages.apps.ella.main_page:start_voice_input:812 | ✅ 语音按钮点击成功
2025-06-23 23:36:03 | INFO | pages.apps.ella.main_page:start_voice_input:825 | 尝试长按输入框启动语音输入...
2025-06-23 23:36:03 | INFO | pages.apps.ella.main_page:start_voice_input:834 | 尝试通过坐标点击语音按钮区域...
2025-06-23 23:36:04 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (972, 2160)
2025-06-23 23:36:06 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (918, 2160)
2025-06-23 23:36:08 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (540, 2280)
2025-06-23 23:36:11 | WARNING | pages.apps.ella.main_page:start_voice_input:852 | ❌ 无法启动语音输入
2025-06-23 23:36:11 | WARNING | pages.apps.ella.main_page:execute_voice_command:454 | 无法启动语音输入，回退到文本输入
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:36:11 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:36:12 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:36:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:12 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:12 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:36:13 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 23:36:13 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 23:36:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:14 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 23:36:14 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 23:36:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 23:36:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 23:36:14 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 23:36:14 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 23:36:15 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 23:36:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:15 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:17 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 23:36:17 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 23:36:17 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:18 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 23:36:18 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:36:19 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 23:36:19 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:91 | ✅ 模拟语音命令执行成功，耗时: 22.50秒
2025-06-23 23:36:19 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: 模拟语音命令执行 耗时 22.505秒 
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 13
2025-06-23 23:36:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:21 | INFO | pages.apps.ella.main_page:_is_ai_response:1615 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:36:21 | INFO | pages.apps.ella.main_page:_check_response_elements:1486 | 通过元素检查找到AI响应: 蓝牙 已打开
2025-06-23 23:36:21 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1419 | 检测到响应相关元素
2025-06-23 23:36:21 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-06-23 23:36:23 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-06-23 23:36:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:24 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 13
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:_is_ai_response:1615 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:get_response_text:1800 | 找到AI响应: 蓝牙 已打开
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:get_response_text:1813 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 23:36:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:120 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: open bluetooth
2025-06-23 23:36:26 | INFO | pages.apps.ella.main_page:verify_command_in_response:1959 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 23:36:26 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:129 | ✅ 响应内容与命令相关
2025-06-23 23:36:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1728 | 智能检查蓝牙状态...
2025-06-23 23:36:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1893 | 检查蓝牙状态
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1907 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:139 | 🔵 蓝牙最终状态: 开启
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:149 | ✅ 蓝牙已成功开启
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:153 | 📊 蓝牙状态变化: 否
2025-06-23 23:36:30 | INFO | core.logger:log_test_end:195 | 🏁 测试结束: 模拟语音输入_open bluetooth - ✅ 成功, 耗时: 22.50秒
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_simulated_voice_open_bluetooth:178 | 🎉 模拟语音输入open bluetooth测试完成
2025-06-23 23:36:30 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 23:36:30 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:36:30 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-06-23 23:36:30 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:27 | 🚀 启动Ella应用...
2025-06-23 23:36:30 | INFO | pages.apps.ella.main_page:start_app_with_activity:127 | 启动Ella应用（指定Activity）
2025-06-23 23:36:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 8秒
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:start_app_with_activity:138 | ✅ Ella应用启动成功
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-06-23 23:36:32 | INFO | testcases.test_ella.test_open_bluetooth_voice:setup_method:35 | ✅ Ella应用启动成功
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1893 | 检查蓝牙状态
2025-06-23 23:36:32 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1907 | 蓝牙状态: 开启 (值: 1)
2025-06-23 23:36:32 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:190 | 🔵 蓝牙初始状态: 开启
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:36:33 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:36:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:34 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:34 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:36:34 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:206 | ✅ 页面和输入框状态就绪
2025-06-23 23:36:34 | INFO | core.logger:log_test_start:188 | 🧪 测试开始: TTS语音输入_open bluetooth
2025-06-23 23:36:34 | INFO | pages.apps.ella.main_page:execute_real_voice_command:637 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN, 音量: 1)
2025-06-23 23:36:35 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: edge_tts
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:_log_voice_file_cache_status:727 | 🎯 语音文件缓存状态: 已存在 - data\zh\open_bluetooth.wav (10.3KB)
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:36:35 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:36:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:36 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:36 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:36:37 | INFO | pages.apps.ella.main_page:start_voice_input:792 | 启动语音输入...
2025-06-23 23:36:37 | INFO | pages.apps.ella.main_page:start_voice_input:810 | 找到语音按钮: 语音按钮(备选)
2025-06-23 23:36:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-06-23 23:36:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-06-23 23:36:38 | INFO | core.base_element:click:129 | 点击元素成功 [语音按钮(备选)]
2025-06-23 23:36:38 | INFO | pages.apps.ella.main_page:start_voice_input:812 | ✅ 语音按钮点击成功
2025-06-23 23:36:41 | INFO | pages.apps.ella.main_page:start_voice_input:825 | 尝试长按输入框启动语音输入...
2025-06-23 23:36:41 | INFO | pages.apps.ella.main_page:start_voice_input:834 | 尝试通过坐标点击语音按钮区域...
2025-06-23 23:36:41 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (972, 2160)
2025-06-23 23:36:44 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (918, 2160)
2025-06-23 23:36:46 | INFO | pages.apps.ella.main_page:start_voice_input:845 | 尝试点击坐标 (540, 2280)
2025-06-23 23:36:49 | WARNING | pages.apps.ella.main_page:start_voice_input:852 | ❌ 无法启动语音输入
2025-06-23 23:36:49 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:654 | 无法启动语音输入，回退到文本输入
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: open bluetooth
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-06-23 23:36:49 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-06-23 23:36:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:50 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:50 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-06-23 23:36:50 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: open bluetooth
2025-06-23 23:36:50 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-06-23 23:36:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:51 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-06-23 23:36:51 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-06-23 23:36:52 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-06-23 23:36:52 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-06-23 23:36:52 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:53 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-06-23 23:36:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:54 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-06-23 23:36:54 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: open bluetooth
2025-06-23 23:36:55 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: open bluetooth
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-06-23 23:36:55 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: open bluetooth
2025-06-23 23:36:55 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-06-23 23:36:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-06-23 23:36:56 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-06-23 23:36:56 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-06-23 23:36:56 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-06-23 23:36:56 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-06-23 23:36:56 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:225 | ✅ TTS语音命令执行成功，耗时: 21.91秒
2025-06-23 23:36:56 | INFO | core.logger:log_performance:200 | ⚡ 性能记录: TTS语音命令执行 耗时 21.906秒 
2025-06-23 23:36:56 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 12秒
2025-06-23 23:36:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 13
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:36:59 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-06-23 23:36:59 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-06-23 23:36:59 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:37:03 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 13
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:_is_ai_response:1628 | 匹配到AI响应特征: 帮我消除图片中的人物
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:get_response_text:1800 | 找到AI响应: 帮我消除图片中的人物
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:_is_ai_response:1615 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙 已打开
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:get_response_text:1800 | 找到AI响应: 蓝牙 已打开
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:get_response_text:1813 | 获取到蓝牙相关响应: 蓝牙 已打开
2025-06-23 23:37:06 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:254 | 🤖 AI响应内容: '蓝牙 已打开'
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: open bluetooth
2025-06-23 23:37:06 | INFO | pages.apps.ella.main_page:verify_command_in_response:1959 | ✅ 响应包含蓝牙相关关键词: ['蓝牙', '已打开', '打开']
2025-06-23 23:37:06 | INFO | testcases.test_ella.test_open_bluetooth_voice:test_tts_voice_open_bluetooth:263 | ✅ TTS响应内容与命令相关
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:1728 | 智能检查蓝牙状态...
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-06-23 23:37:09 | INFO | pages.apps.ella.main_page:check_bluetooth_status:1893 | 检查蓝牙状态
2025-06-23 23:37:10 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-06-23 23:37:10 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-06-23 23:37:10 | INFO | testcases.test_ella.test_open_bluetooth_voice:teardown_method:41 | 🔚 Ella应用已关闭
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-06-23 23:37:10 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
