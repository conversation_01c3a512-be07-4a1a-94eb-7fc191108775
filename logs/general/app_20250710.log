2025-07-10 21:44:12 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 21:44:12 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 21:44:12 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 21:44:12 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 21:44:12 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 21:44:12 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 21:44:12 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 21:44:14 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 21:44:14 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 21:44:15 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:44:15 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 21:44:15 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:44:15 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:44:15 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 21:44:15 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:44:15 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:44:15 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:44:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 21:44:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:18 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 21:44:18 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:44:19 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:84 | 闹钟初始状态: 有闹钟
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:44:19 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:44:20 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:44:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:20 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:44:20 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:22 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:44:22 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:44:22 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:44:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:23 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:44:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:44:23 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:44:23 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 21:44:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:25 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 21:44:25 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 21:44:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 21:44:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 21:44:26 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 21:44:26 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 21:44:27 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 21:44:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:27 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:44:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:29 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:44:29 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: set an alarm at 10 am tomorrow
2025-07-10 21:44:29 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: set an alarm at 10 am tomorrow
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:44:30 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: set an alarm at 10 am tomorrow
2025-07-10 21:44:30 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:44:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:44:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:44:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:44:31 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 21:44:31 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:113 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:32 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 14
2025-07-10 21:44:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:38 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1406 | 检测到元素数量增加: 15 > 14
2025-07-10 21:44:39 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 21:44:39 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 21:44:39 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 21:44:43 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:141 | ✅ 收到AI响应
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:43 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 21:44:46 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 15
2025-07-10 21:44:46 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 21:44:46 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:169 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:44:46 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: set an alarm at 10 am tomorrow
2025-07-10 21:44:46 | WARNING | pages.apps.ella.main_page:verify_command_in_response:1992 | ⚠️ 响应包含的关键词不足: ['an']
2025-07-10 21:44:46 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:44:49 | INFO | pages.apps.ella.main_page:check_alarm_status_smart:2057 | 智能检查闹钟状态...
2025-07-10 21:44:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:189 | 闹钟最终状态: 有闹钟
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:verify_alarm_set:2084 | 验证闹钟设置: 10:00
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:44:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:193 | 闹钟设置验证: 成功
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:209 | ✅ 闹钟设置验证通过
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | 🎉 set alarm命令测试完成
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:44:50 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:44:50 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 21:44:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:44:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:45:40 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 21:45:40 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 21:45:40 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 21:45:40 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 21:45:40 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 21:45:40 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 21:45:40 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 21:46:15 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 21:46:15 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 21:46:16 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4485, 'temperature': 345, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:46:16 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 21:46:16 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:46:16 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4485, 'temperature': 345, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:46:16 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 21:46:16 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:46:16 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:46:16 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:46:19 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 21:46:19 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:19 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 21:46:19 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:46:20 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:84 | 闹钟初始状态: 有闹钟
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:46:20 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:46:21 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:46:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:21 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:46:21 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:22 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:46:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:46:23 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:46:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:24 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:46:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:46:25 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:46:25 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 21:46:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:26 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 21:46:26 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 21:46:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 21:46:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 21:46:27 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 21:46:27 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 21:46:28 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 21:46:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:29 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:46:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:32 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:46:32 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: set an alarm at 10 am tomorrow
2025-07-10 21:46:32 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: set an alarm at 10 am tomorrow
2025-07-10 21:46:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:46:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:46:33 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: set an alarm at 10 am tomorrow
2025-07-10 21:46:33 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 21:46:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:46:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:46:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:46:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:46:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:46:34 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 21:46:34 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 21:46:34 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 21:46:34 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 21:46:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:113 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 14
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:38 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 21:46:38 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 21:46:38 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 21:46:42 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:141 | ✅ 收到AI响应
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:42 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 21:46:46 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 14
2025-07-10 21:46:46 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 21:46:46 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:169 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:46:46 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: set an alarm at 10 am tomorrow
2025-07-10 21:46:46 | WARNING | pages.apps.ella.main_page:verify_command_in_response:1992 | ⚠️ 响应包含的关键词不足: ['an']
2025-07-10 21:46:46 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:46:49 | INFO | pages.apps.ella.main_page:check_alarm_status_smart:2057 | 智能检查闹钟状态...
2025-07-10 21:46:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:189 | 闹钟最终状态: 有闹钟
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:verify_alarm_set:2084 | 验证闹钟设置: 10:00
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:46:50 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:193 | 闹钟设置验证: 成功
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:209 | ✅ 闹钟设置验证通过
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | 🎉 set alarm命令测试完成
2025-07-10 21:46:50 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:46:50 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:46:50 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 21:46:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:46:51 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:54:47 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 21:54:47 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 21:54:47 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 21:54:47 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 21:54:47 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 21:54:47 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 21:54:47 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 21:54:48 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 21:54:48 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 21:54:48 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:54:48 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 21:54:48 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:54:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:54:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:54:49 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 21:54:49 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:54:49 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:54:49 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:54:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 21:54:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:54:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:54:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 21:54:51 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:54:52 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:54:52 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:54:52 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:54:53 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 3
2025-07-10 21:54:53 | INFO | pages.apps.ella.main_page:clear_all_alarms:2250 | 开始清空所有闹钟...
2025-07-10 21:54:53 | INFO | pages.apps.ella.main_page:clear_all_alarms:2258 | 启动时钟应用以清空闹钟
2025-07-10 21:54:53 | INFO | pages.apps.ella.main_page:clear_all_alarms:2267 | 时钟应用启动成功
2025-07-10 21:54:57 | INFO | pages.apps.ella.main_page:clear_all_alarms:2280 | ✅ 通过数据库删除闹钟成功
2025-07-10 21:54:58 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:54:58 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:54:59 | WARNING | pages.apps.ella.main_page:clear_all_alarms:2289 | ⚠️ 仍有 3 个闹钟未清空
2025-07-10 21:54:59 | INFO | pages.apps.ella.main_page:clear_all_alarms:2295 | 尝试通过清理应用数据清空闹钟
2025-07-10 21:54:59 | INFO | pages.apps.ella.main_page:clear_all_alarms:2304 | ✅ 时钟应用数据已清理
2025-07-10 21:55:01 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:55:01 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:55:02 | WARNING | pages.apps.ella.main_page:clear_all_alarms:2313 | ⚠️ 清理后仍有 3 个闹钟
2025-07-10 21:55:02 | WARNING | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:107 | ⚠️ 闹钟清空失败，但继续测试
2025-07-10 21:55:03 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:55:03 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:55:04 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 3 个闹钟
2025-07-10 21:55:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:55:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:05 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:55:05 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:55:05 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:55:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:05 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:55:05 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:07 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:55:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:55:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:55:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:08 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:55:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:55:08 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:55:08 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 21:55:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:09 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 21:55:09 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 21:55:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 21:55:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 21:55:10 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 21:55:10 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 21:55:11 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 21:55:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:12 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:55:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:14 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:55:14 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: set an alarm at 10 am tomorrow
2025-07-10 21:55:15 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: set an alarm at 10 am tomorrow
2025-07-10 21:55:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:55:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:55:16 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: set an alarm at 10 am tomorrow
2025-07-10 21:55:16 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:55:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:55:17 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 21:55:17 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 14
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:21 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1406 | 检测到元素数量增加: 15 > 14
2025-07-10 21:55:22 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 21:55:22 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 21:55:22 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 21:55:25 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 21:55:25 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 21:55:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:26 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 21:55:29 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 15
2025-07-10 21:55:29 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 21:55:29 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:55:29 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: set an alarm at 10 am tomorrow
2025-07-10 21:55:29 | WARNING | pages.apps.ella.main_page:verify_command_in_response:1992 | ⚠️ 响应包含的关键词不足: ['an']
2025-07-10 21:55:29 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:55:34 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:55:34 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:55:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 21:55:35 | INFO | pages.apps.ella.main_page:check_alarm_status_smart:2057 | 智能检查闹钟状态...
2025-07-10 21:55:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:55:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:verify_alarm_set:2084 | 验证闹钟设置: 10:00
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:55:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2332 | 验证闹钟是否在列表中: 10:00
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:55:36 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:55:37 | WARNING | pages.apps.ella.main_page:verify_alarm_in_list:2371 | ⚠️ 未找到匹配的闹钟: 10:00
2025-07-10 21:55:37 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 21:55:37 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:286 | ⚠️ 闹钟数量未增加，但继续验证其他指标
2025-07-10 21:55:37 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:55:38 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:55:38 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 21:55:38 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:55:38 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:57:26 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 21:57:26 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 21:57:26 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 21:57:26 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 21:57:26 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 21:57:26 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 21:57:26 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 21:57:27 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 21:57:27 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 21:57:28 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:57:28 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 21:57:28 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:57:28 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:57:29 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 21:57:29 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 21:57:29 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:57:29 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:57:29 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:57:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 21:57:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:57:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:57:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 21:57:31 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:57:32 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:57:32 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:57:32 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:57:33 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 3
2025-07-10 21:57:33 | INFO | pages.apps.ella.main_page:clear_all_alarms:2250 | 开始清空所有闹钟...
2025-07-10 21:57:33 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:57:33 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:57:34 | INFO | pages.apps.ella.main_page:clear_all_alarms:2256 | 清空前发现 3 个闹钟
2025-07-10 21:57:34 | INFO | pages.apps.ella.main_page:clear_all_alarms:2262 | 停止时钟应用
2025-07-10 21:57:36 | INFO | pages.apps.ella.main_page:clear_all_alarms:2271 | 清理时钟应用数据
2025-07-10 21:57:36 | INFO | pages.apps.ella.main_page:clear_all_alarms:2280 | ✅ 时钟应用数据已清理
2025-07-10 21:57:39 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:57:39 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 21:57:40 | INFO | pages.apps.ella.main_page:clear_all_alarms:2285 | 清理后剩余 0 个闹钟
2025-07-10 21:57:40 | INFO | pages.apps.ella.main_page:clear_all_alarms:2288 | ✅ 闹钟数量减少: 3 -> 0
2025-07-10 21:57:40 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:88 | ✅ 闹钟清空成功
2025-07-10 21:57:40 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:57:40 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 21:57:41 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:92 | 清空后闹钟数量: 0
2025-07-10 21:57:41 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:105 | ✅ 所有闹钟已成功清空
2025-07-10 21:57:42 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:57:42 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 21:57:43 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 0 个闹钟
2025-07-10 21:57:43 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:57:43 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:57:44 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:57:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:57:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:57:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:57:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:57:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:57:55 | ERROR | core.base_element:click:135 | 点击元素失败 [输入框]: {'code': -32002, 'data': "Selector [resourceId='com.transsion.aivoiceassistant:id/et_input']", 'method': 'wait'}
2025-07-10 21:57:55 | WARNING | pages.apps.ella.main_page:ensure_input_box_ready:1234 | 主输入框点击失败
2025-07-10 21:57:55 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1249 | 尝试通过坐标点击输入区域...
2025-07-10 21:57:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-10 21:57:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-10 21:57:59 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-10 21:57:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-10 21:58:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-10 21:58:01 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-10 21:58:01 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1265 | 尝试滑动到底部显示输入框...
2025-07-10 21:58:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-10 21:58:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1271 | ✅ 通过滑动显示了输入框
2025-07-10 21:58:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:05 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:58:05 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:58:05 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 21:58:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:58:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:58:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:58:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:58:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 21:58:06 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 21:58:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 21:58:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:07 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:58:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 21:58:08 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: set an alarm at 10 am tomorrow
2025-07-10 21:58:08 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 21:58:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:09 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 21:58:09 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 21:58:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 21:58:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 21:58:10 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 21:58:10 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 21:58:11 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 21:58:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:11 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 21:58:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:13 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 21:58:13 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: set an alarm at 10 am tomorrow
2025-07-10 21:58:14 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: set an alarm at 10 am tomorrow
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:58:15 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: set an alarm at 10 am tomorrow
2025-07-10 21:58:15 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:58:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 21:58:16 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 21:58:16 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 14
2025-07-10 21:58:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:58:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:58:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:58:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:58:19 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1406 | 检测到元素数量增加: 15 > 14
2025-07-10 21:58:21 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 21:58:21 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 21:58:21 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 21:58:24 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:58:24 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 21:58:27 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 15
2025-07-10 21:58:27 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 21:58:27 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 21:58:27 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: set an alarm at 10 am tomorrow
2025-07-10 21:58:27 | WARNING | pages.apps.ella.main_page:verify_command_in_response:1992 | ⚠️ 响应包含的关键词不足: ['an']
2025-07-10 21:58:27 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 21:58:32 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:58:33 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:58:34 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:check_alarm_status_smart:2057 | 智能检查闹钟状态...
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 21:58:34 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:verify_alarm_set:2084 | 验证闹钟设置: 10:00
2025-07-10 21:58:34 | INFO | pages.apps.ella.main_page:verify_alarm_set:2099 | ✅ 时钟应用已打开，可能正在设置闹钟
2025-07-10 21:58:35 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 21:58:35 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2341 | 验证闹钟是否在列表中: 10:00
2025-07-10 21:58:35 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:58:35 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 21:58:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2359 | 检查 3 个闹钟记录
2025-07-10 21:58:36 | WARNING | pages.apps.ella.main_page:verify_alarm_in_list:2420 | ⚠️ 未找到匹配的闹钟: 10:00
2025-07-10 21:58:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2421 | 闹钟列表详情:
2025-07-10 21:58:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 1: RTC_WAKEUP #58: Alarm{4bc8635 type 0 origWhen 1752195600000 whenElapsed 43818334 com.transsion.deskclock}
2025-07-10 21:58:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 2: RTC_WAKEUP #60: Alarm{7277aca type 0 origWhen 1752199200000 whenElapsed 47418333 com.transsion.deskclock}
2025-07-10 21:58:36 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 3: RTC_WAKEUP #61: Alarm{575043b type 0 origWhen 1752199200000 whenElapsed 47418334 com.transsion.deskclock}
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:276 | ✅ 闹钟数量增加: 0 -> 3
2025-07-10 21:58:36 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:283 | ⚠️ 闹钟已增加但未找到目标时间: 10:00
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 21:58:36 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 21:58:36 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:58:36 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 21:58:37 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 21:58:37 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 21:59:43 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 21:59:43 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 21:59:43 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 21:59:43 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 21:59:43 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 21:59:43 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 21:59:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 21:59:45 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 21:59:45 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 21:59:45 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 346, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:59:45 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 21:59:45 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 21:59:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 346, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-10 21:59:46 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 21:59:46 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 21:59:46 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 21:59:46 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 21:59:48 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 21:59:48 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 21:59:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 21:59:48 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 21:59:48 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 21:59:49 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:71 | 🧹 开始清空所有现有闹钟...
2025-07-10 21:59:49 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:59:49 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 5 个闹钟
2025-07-10 21:59:50 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:75 | 清空前闹钟数量: 5
2025-07-10 21:59:50 | INFO | pages.apps.ella.main_page:clear_all_alarms:2250 | 开始清空所有闹钟...
2025-07-10 21:59:50 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:59:50 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 5 个闹钟
2025-07-10 21:59:52 | INFO | pages.apps.ella.main_page:clear_all_alarms:2256 | 清空前发现 5 个闹钟
2025-07-10 21:59:52 | INFO | pages.apps.ella.main_page:clear_all_alarms:2262 | 停止时钟应用
2025-07-10 21:59:53 | INFO | pages.apps.ella.main_page:clear_all_alarms:2271 | 清理时钟应用数据
2025-07-10 21:59:53 | INFO | pages.apps.ella.main_page:clear_all_alarms:2280 | ✅ 时钟应用数据已清理
2025-07-10 21:59:56 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:59:56 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 21:59:57 | INFO | pages.apps.ella.main_page:clear_all_alarms:2285 | 清理后剩余 0 个闹钟
2025-07-10 21:59:57 | INFO | pages.apps.ella.main_page:clear_all_alarms:2288 | ✅ 闹钟数量减少: 5 -> 0
2025-07-10 21:59:57 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:88 | ✅ 闹钟清空成功
2025-07-10 21:59:57 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:59:57 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 21:59:59 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:92 | 清空后闹钟数量: 0
2025-07-10 21:59:59 | INFO | testcases.test_ella.test_set_alarm_command:_clear_all_alarms_before_test:105 | ✅ 所有闹钟已成功清空
2025-07-10 21:59:59 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 21:59:59 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 0 个闹钟
2025-07-10 22:00:00 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:134 | 闹钟初始列表: 0 个闹钟
2025-07-10 22:00:00 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 22:00:00 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 22:00:01 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 22:00:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:02 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:00:02 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 22:00:02 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: set an alarm at 10 am tomorrow
2025-07-10 22:00:02 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 22:00:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:03 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 22:00:03 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 22:00:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 22:00:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:04 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:00:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 22:00:05 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: set an alarm at 10 am tomorrow
2025-07-10 22:00:05 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 22:00:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:06 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 22:00:06 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 22:00:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 22:00:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 22:00:07 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 22:00:07 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 22:00:08 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 22:00:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:09 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:00:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:11 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: set an alarm at 10 am tomorrow
2025-07-10 22:00:11 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: set an alarm at 10 am tomorrow
2025-07-10 22:00:11 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: set an alarm at 10 am tomorrow
2025-07-10 22:00:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:00:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:00:13 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: set an alarm at 10 am tomorrow
2025-07-10 22:00:13 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:00:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 22:00:13 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 22:00:13 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 22:00:13 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 22:00:13 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 22:00:14 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:164 | ✅ 成功执行命令: set an alarm at 10 am tomorrow
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 10秒
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 14
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:15 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 22:00:15 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 22:00:15 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 22:00:18 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:192 | ✅ 收到AI响应
2025-07-10 22:00:18 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 22:00:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:19 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 22:00:24 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 15
2025-07-10 22:00:24 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 22:00:24 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:220 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 22:00:24 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: set an alarm at 10 am tomorrow
2025-07-10 22:00:24 | WARNING | pages.apps.ella.main_page:verify_command_in_response:1992 | ⚠️ 响应包含的关键词不足: ['an']
2025-07-10 22:00:24 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:230 | ⚠️ 响应未包含命令相关内容，但继续测试: set an alarm at 10 am tomorrow
2025-07-10 22:00:29 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 22:00:29 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 22:00:30 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:240 | 闹钟最终列表: 3 个闹钟
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:check_alarm_status_smart:2057 | 智能检查闹钟状态...
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 22:00:30 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:244 | 闹钟最终状态: 有闹钟
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:verify_alarm_set:2084 | 验证闹钟设置: 10:00
2025-07-10 22:00:30 | INFO | pages.apps.ella.main_page:check_alarm_status:2011 | 检查闹钟状态
2025-07-10 22:00:31 | INFO | pages.apps.ella.main_page:check_alarm_status:2036 | 检测到闹钟相关信息: com.transsion.deskclock
2025-07-10 22:00:31 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:248 | 闹钟设置验证: 成功
2025-07-10 22:00:31 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2341 | 验证闹钟是否在列表中: 10:00
2025-07-10 22:00:31 | INFO | pages.apps.ella.main_page:get_alarm_list:2138 | 获取闹钟列表
2025-07-10 22:00:31 | INFO | pages.apps.ella.main_page:get_alarm_list:2161 | 通过dumpsys找到 3 个闹钟
2025-07-10 22:00:32 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2359 | 检查 3 个闹钟记录
2025-07-10 22:00:32 | WARNING | pages.apps.ella.main_page:verify_alarm_in_list:2420 | ⚠️ 未找到匹配的闹钟: 10:00
2025-07-10 22:00:32 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2421 | 闹钟列表详情:
2025-07-10 22:00:32 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 1: RTC_WAKEUP #75: Alarm{43b26b7 type 0 origWhen 1752195600000 whenElapsed 43818333 com.transsion.deskclock}
2025-07-10 22:00:32 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 2: RTC_WAKEUP #77: Alarm{b856e24 type 0 origWhen 1752199200000 whenElapsed 47418333 com.transsion.deskclock}
2025-07-10 22:00:32 | INFO | pages.apps.ella.main_page:verify_alarm_in_list:2423 |   闹钟 3: RTC_WAKEUP #78: Alarm{3370d8d type 0 origWhen 1752199200000 whenElapsed 47418334 com.transsion.deskclock}
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:252 | 闹钟列表验证: 失败
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:276 | ✅ 闹钟数量增加: 0 -> 3
2025-07-10 22:00:32 | WARNING | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:283 | ⚠️ 闹钟已增加但未找到目标时间: 10:00
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:289 | ✅ 闹钟设置验证通过
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:test_set_alarm_command:315 | 🎉 set alarm命令测试完成
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 22:00:32 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 22:00:32 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 22:00:32 | INFO | testcases.test_ella.test_set_alarm_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 22:00:32 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-10 22:10:01 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-10 22:10:01 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-10 22:10:01 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-10 22:10:01 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-10 22:10:01 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-10 22:10:01 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-10 22:10:01 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-10 22:10:01 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-10 22:10:01 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-10 22:10:02 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 22:10:02 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-10 22:10:02 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-10 22:10:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-10 22:10:02 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-10 22:10:02 | INFO | testcases.test_ella.test_weather_query_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-10 22:10:02 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-10 22:10:02 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-10 22:10:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [Ella应用包]
2025-07-10 22:10:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:167 | ✅ Ella应用包已加载
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:171 | ✅ Ella输入框已加载
2025-07-10 22:10:05 | INFO | testcases.test_ella.test_weather_query_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2533 | 检查天气应用状态
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2562 | ✅ 检测到天气应用: weather
2025-07-10 22:10:06 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:85 | 天气应用初始状态: 已打开
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 22:10:06 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 22:10:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 22:10:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:07 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:10:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:execute_text_command:400 | 执行文本命令: what is the weather like in shanghai tomorrow
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:983 | 确保在对话页面...
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:10:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:10:09 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:994 | ✅ 已在对话页面
2025-07-10 22:10:09 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1222 | 确保输入框就绪...
2025-07-10 22:10:09 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1226 | 找到主输入框
2025-07-10 22:10:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:10 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:10:10 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1230 | ✅ 主输入框已激活
2025-07-10 22:10:10 | INFO | pages.apps.ella.main_page:input_text_command:199 | 输入文本命令: what is the weather like in shanghai tomorrow
2025-07-10 22:10:10 | INFO | pages.apps.ella.main_page:clear_input_box:291 | 清空输入框...
2025-07-10 22:10:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:11 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-10 22:10:11 | INFO | pages.apps.ella.main_page:clear_input_box:297 | 主输入框已清空
2025-07-10 22:10:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-10 22:10:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-10 22:10:12 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-10 22:10:12 | INFO | pages.apps.ella.main_page:clear_input_box:312 | 备选输入框已清空
2025-07-10 22:10:13 | INFO | pages.apps.ella.main_page:input_text_command:206 | 使用主输入框(et_input)
2025-07-10 22:10:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:14 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-10 22:10:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:17 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: what is the weather like in shanghai tomorrow
2025-07-10 22:10:17 | INFO | pages.apps.ella.main_page:input_text_command:218 | ✅ 通过send_keys输入成功: what is the weather like in shanghai tomorrow
2025-07-10 22:10:17 | INFO | pages.apps.ella.main_page:verify_input_text:330 | 验证输入文本: what is the weather like in shanghai tomorrow
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-10 22:10:18 | INFO | pages.apps.ella.main_page:verify_input_text:337 | ✅ 主输入框文本验证成功: what is the weather like in shanghai tomorrow
2025-07-10 22:10:18 | INFO | pages.apps.ella.main_page:send_command:368 | 发送命令
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-10 22:10:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-10 22:10:19 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:send_command:373 | 使用发送按钮(fl_btn_three_btn)
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:send_command:375 | ✅ 命令发送成功
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:execute_text_command:420 | ✅ 文本命令执行完成
2025-07-10 22:10:19 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:114 | ✅ 成功执行命令: what is the weather like in shanghai tomorrow
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:wait_for_response:1330 | 快速等待AI响应，超时时间: 15秒
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:wait_for_response:1345 | 初始元素数量: 28
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:10:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:10:20 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:1647 | 检测到TTS播放按钮
2025-07-10 22:10:20 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1414 | 检测到TTS按钮，表示有AI响应
2025-07-10 22:10:20 | INFO | pages.apps.ella.main_page:wait_for_response:1363 | ✅ 快速检测到AI响应
2025-07-10 22:10:23 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:142 | ✅ 收到AI响应
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:get_response_text_smart:1752 | 智能获取响应文本...
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1015 | 检查当前进程是否是Ella...
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1022 | 当前应用: com.transsion.aivoiceassistant
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1023 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1032 | ✅ 当前在Ella应用进程
2025-07-10 22:10:23 | INFO | pages.apps.ella.main_page:get_response_text:1776 | 获取AI响应文本
2025-07-10 22:10:29 | INFO | pages.apps.ella.main_page:get_response_text:1792 | 页面上所有文本元素数量: 23
2025-07-10 22:10:29 | INFO | pages.apps.ella.main_page:get_response_text:1831 | 获取到最新有意义文本: Feel free to ask me any questions…
2025-07-10 22:10:29 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:170 | AI响应内容: 'Feel free to ask me any questions…'
2025-07-10 22:10:29 | INFO | pages.apps.ella.main_page:verify_command_in_response:1929 | 验证响应是否包含命令: what is the weather like in shanghai tomorrow
2025-07-10 22:10:29 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2013 | ⚠️ 响应包含的关键词不足: []
2025-07-10 22:10:29 | WARNING | testcases.test_ella.test_weather_query_command:test_weather_query_command:180 | ⚠️ 响应未包含命令相关内容，但继续测试: what is the weather like in shanghai tomorrow
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2587 | 综合验证天气查询: shanghai
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_response:2469 | 验证天气响应: shanghai
2025-07-10 22:10:32 | WARNING | pages.apps.ella.main_page:verify_weather_response:2518 | ⚠️ 响应未包含明显的天气信息
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2533 | 检查天气应用状态
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2562 | ✅ 检测到天气应用: weather
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2642 | 天气查询验证结果: False
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2644 |   ✅ 收到有效响应
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2644 |   ⚠️ 响应未包含明显天气信息
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2644 |   ⚠️ 响应未明确提及位置: shanghai
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2644 |   ✅ 检测到天气应用打开
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:verify_weather_query_success:2644 |   ⚠️ 天气查询可能未完全成功
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2533 | 检查天气应用状态
2025-07-10 22:10:32 | INFO | pages.apps.ella.main_page:check_weather_app_opened:2562 | ✅ 检测到天气应用: weather
2025-07-10 22:10:32 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:207 | 天气应用最终状态: 已打开
2025-07-10 22:10:32 | WARNING | testcases.test_ella.test_weather_query_command:test_weather_query_command:213 | ⚠️ 天气查询验证未完全通过，但测试继续
2025-07-10 22:10:32 | INFO | testcases.test_ella.test_weather_query_command:test_weather_query_command:238 | 🎉 天气查询命令测试完成
2025-07-10 22:10:33 | INFO | testcases.test_ella.test_weather_query_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-10 22:10:33 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-10 22:10:33 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-10 22:10:33 | INFO | testcases.test_ella.test_weather_query_command:ella_app:64 | ✅ Ella应用已停止
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-10 22:10:33 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
