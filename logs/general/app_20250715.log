2025-07-15 20:18:31 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-15 20:18:31 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-15 20:18:31 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-15 20:18:31 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-15 20:18:31 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-15 20:18:31 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-15 20:18:31 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-15 20:18:36 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-15 20:18:36 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-15 20:18:36 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-15 20:18:36 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-15 20:18:36 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-15 20:18:37 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-15 20:18:37 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-15 20:18:37 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-15 20:18:37 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-15 20:18:41 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-15 20:18:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-15 20:18:41 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-15 20:18:41 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-15 20:18:41 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-15 20:18:42 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-15 20:18:42 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-15 20:18:42 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-15 20:18:42 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-15 20:18:43 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-15 20:18:43 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-15 20:18:43 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-15 20:18:43 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-15 20:18:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-15 20:18:44 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-15 20:18:44 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-15 20:18:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-15 20:18:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-15 20:18:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-15 20:18:45 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-15 20:18:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-15 20:18:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-15 20:18:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-15 20:18:45 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-15 20:18:45 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-15 20:18:45 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-15 20:18:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-15 20:18:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-15 20:18:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-15 20:18:47 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-15 20:18:47 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-15 20:18:47 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-15 20:18:47 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-15 20:18:47 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-15 20:18:47 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-15 20:18:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-15 20:18:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-15 20:18:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-15 20:18:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-15 20:18:48 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-15 20:18:50 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-15 20:18:50 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-15 20:18:50 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-15 20:18:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-15 20:18:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-15 20:18:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-15 20:18:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-15 20:18:53 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-15 20:18:54 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-15 20:18:54 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: False -> True
2025-07-15 20:18:55 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-15 20:18:55 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-15 20:18:55 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-15 20:18:55 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
