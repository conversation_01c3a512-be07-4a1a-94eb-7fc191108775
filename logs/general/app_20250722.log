2025-07-22 10:41:49 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 10:41:49 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 10:41:50 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 10:41:50 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 10:41:53 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 10:41:53 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 10:41:53 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 10:41:53 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 10:41:53 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 10:41:53 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 10:41:53 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 10:41:53 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 10:41:53 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 10:41:53 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 10:41:53 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 10:41:53 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 10:41:53 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 10:41:53 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 10:41:53 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 10:41:54 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 10:41:54 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 10:41:54 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 10:41:54 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 10:41:54 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 10:41:54 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 10:41:54 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 10:41:58 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 10:41:58 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 10:41:58 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 10:41:58 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 10:41:58 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 10:41:58 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 10:41:58 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 10:41:58 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 10:41:59 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 10:42:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 10:42:00 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 10:42:00 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 10:42:00 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 10:42:00 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 10:42:01 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 10:42:01 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 10:42:01 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 10:42:01 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 10:42:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 10:42:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 10:42:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 10:42:02 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 10:42:02 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 10:42:02 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 10:42:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 10:42:02 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 10:42:02 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-22 10:42:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 10:42:02 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 10:42:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 10:42:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-22 10:42:03 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:216 | 从TextView元素获取响应
2025-07-22 10:42:17 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:232 | 查找RecyclerView中的最新消息
2025-07-22 10:42:17 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:338 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 10:42:17 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:257 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 10:42:17 | INFO | pages.apps.ella.ella_response_handler:get_response_text:102 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 10:42:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:135 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:148 | ✅ 状态验证通过: True -> None
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:simple_command_test:245 | 🎉 open wifi 测试完成
2025-07-22 10:42:20 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:185 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:216 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 10:42:20 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:217 | 缺失内容: ['WI-FI']
2025-07-22 10:42:21 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 10:42:21 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 10:42:21 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 16:50:31 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:50:31 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:50:32 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:50:32 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:50:35 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:50:35 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:50:35 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:50:35 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:50:35 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:50:35 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:50:35 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:50:35 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:50:35 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:50:35 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:50:35 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:50:35 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:50:35 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:50:35 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:50:35 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:50:36 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:50:36 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:50:36 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:50:36 | INFO | __main__:get_response_text:83 | 获取AI响应文本
2025-07-22 16:50:36 | INFO | __main__:_get_response_from_check_area:228 | 从check_area节点获取响应文本
2025-07-22 16:50:36 | INFO | __main__:_get_response_from_check_area:240 | 从check_area直接获取文本: Which app should I open?
2025-07-22 16:50:36 | INFO | __main__:get_response_text:88 | ✅ 从check_area获取到响应文本: Which app should I open?
2025-07-22 16:51:02 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:51:02 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:51:02 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:51:02 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:51:04 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:51:04 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:51:04 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:51:04 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:51:04 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:51:04 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:51:04 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:51:04 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:51:04 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:51:04 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:51:04 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:51:04 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:51:04 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:51:05 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:51:05 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:51:05 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:51:05 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:51:05 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:51:06 | INFO | __main__:get_response_all_text:129 | 获取AI响应文本
2025-07-22 16:51:06 | WARNING | __main__:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 16:51:06 | INFO | __main__:_get_response_from_text_views:321 | 从TextView元素获取响应
2025-07-22 16:51:07 | INFO | __main__:_get_response_from_chat_list:337 | 查找RecyclerView中的最新消息
2025-07-22 16:51:08 | INFO | __main__:_is_ai_response:443 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 16:51:08 | INFO | __main__:_get_response_from_page_dump:362 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 16:51:08 | INFO | __main__:get_response_all_text:155 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 16:51:08 | WARNING | __main__:get_response_all_text:161 | 未获取到有效的响应文本
2025-07-22 16:52:18 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:52:18 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:52:19 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:52:19 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:52:21 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:52:21 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:52:21 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:52:21 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:52:21 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:52:21 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:52:21 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:52:21 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:52:21 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:52:21 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:52:21 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:52:21 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:52:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:52:22 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:52:22 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:52:22 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:52:22 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:52:22 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:52:46 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:52:46 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:52:46 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:52:46 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:52:49 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:52:49 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:52:49 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:52:49 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:52:49 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:52:49 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:52:49 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:52:49 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:52:49 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:52:49 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:52:49 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:52:49 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:52:49 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:52:50 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:52:50 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:52:50 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:52:50 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:52:50 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:57:23 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:57:23 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:57:23 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:57:23 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:57:25 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:57:25 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:57:25 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:57:25 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:57:25 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:57:25 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:57:25 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:57:25 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:57:25 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:57:25 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:57:25 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:57:25 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:57:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:57:26 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:57:26 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:57:27 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:57:27 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:57:27 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:58:13 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:58:13 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:58:13 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:58:13 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:58:16 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:58:16 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:58:16 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:58:16 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:58:16 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:58:16 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:58:16 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:58:16 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:58:16 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:58:16 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:58:16 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:58:16 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:58:16 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:58:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:58:17 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:58:17 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:58:17 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:58:17 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:58:17 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x00000262B94C26D0>
2025-07-22 16:58:30 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 16:58:30 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 16:58:30 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 16:58:30 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 16:58:33 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 16:58:33 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 16:58:33 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 16:58:33 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:58:33 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 16:58:33 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 16:58:33 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 16:58:33 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 16:58:33 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 16:58:33 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 16:58:33 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 16:58:33 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 16:58:33 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 16:58:33 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 16:58:33 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 16:58:34 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 16:58:34 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 16:58:34 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 16:59:51 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x000002176E5F2410>
2025-07-22 17:07:02 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:07:02 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:07:02 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:07:02 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:07:05 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:07:05 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:07:05 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:07:05 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:07:05 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:07:05 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:07:05 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:07:05 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:07:05 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:07:05 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:07:05 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:07:05 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:07:05 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:07:06 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:07:06 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:07:07 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:07:07 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:07:07 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:08:04 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x000001C359A241D0>
2025-07-22 17:10:59 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:10:59 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:11:00 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:11:00 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:11:03 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:11:03 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:11:03 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:11:03 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:11:03 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:11:03 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:11:03 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:11:03 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:11:03 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:11:03 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:11:03 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:11:03 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:11:03 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:11:04 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:11:04 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:11:04 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:11:04 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:11:04 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:11:22 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x0000029073787CD0>
2025-07-22 17:13:50 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:13:50 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:13:50 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:13:50 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:13:53 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:13:53 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:13:53 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:13:53 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:13:53 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:13:53 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:13:53 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:13:53 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:13:53 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:13:53 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:13:53 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:13:53 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:13:53 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:13:54 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:13:54 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:13:54 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:13:54 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:13:54 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:14:10 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:14:10 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:14:10 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:14:10 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:14:13 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:14:13 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:14:13 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:14:13 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:14:13 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:14:13 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:14:13 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:14:13 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:14:13 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:14:13 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:14:13 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:14:13 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:14:13 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:14:13 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:14:13 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:14:14 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:14:14 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:14:14 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:14:47 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x0000016FB0CE12D0>
2025-07-22 17:16:57 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:16:57 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:16:57 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:16:57 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:17:00 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:17:00 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:17:00 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:17:00 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:17:00 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:17:00 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:17:00 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:17:00 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:17:00 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:17:00 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:17:00 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:17:00 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:17:00 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:17:01 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:17:01 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:17:01 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:17:01 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:17:01 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:17:04 | INFO | __main__:get_response_from_asr_txt:176 | 从asr_txt节点获取响应:<uiautomator2._selector.UiObject object at 0x000001BC42634E50>
2025-07-22 17:29:26 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:26 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:26 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:26 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:27 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:28 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:29 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:254 | asr_txt节点不存在，已达到最大重试次数
2025-07-22 17:29:30 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:268 | robot_text节点文本为空，已达到最大重试次数
2025-07-22 17:29:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:279 | function_name节点文本清理后为空，已达到最大重试次数
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:297 | ✅ 从function_control成功获取文本: 蓝牙已关闭
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*关闭 -> 蓝牙已关闭
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从function_control成功获取响应: 蓝牙已关闭
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:31 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:32 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:33 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:33 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:33 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:33 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:33 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:34 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:35 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:35 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:35 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:35 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:36 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:37 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:37 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:37 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:37 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:39 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:39 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:39 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:39 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:39 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:40 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:41 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:41 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:41 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:41 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:41 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:42 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:43 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:43 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:43 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:43 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:43 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:44 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:45 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:45 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:45 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:45 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:45 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:46 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:47 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:47 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:47 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:48 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:49 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:49 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:49 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:49 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:49 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:50 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:51 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:51 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:51 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:51 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:51 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:52 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:53 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:53 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:53 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:53 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:53 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:54 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:55 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:55 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:55 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:55 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:55 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:57 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:57 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:57 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:57 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:57 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:29:58 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:29:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:29:59 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:29:59 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:29:59 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:29:59 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:30:00 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:30:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:30:01 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:30:01 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:30:01 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:30:01 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:30:02 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: 蓝牙，已达到最大重试次数
2025-07-22 17:30:03 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:293 | function_control文本不符合AI响应格式: 已打开，已达到最大重试次数
2025-07-22 17:30:03 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:536 | 匹配到AI响应特征: 打开蓝牙
2025-07-22 17:30:03 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从asr_txt成功获取响应: 打开蓝牙
2025-07-22 17:30:03 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:513 | 匹配到蓝牙响应模式: 蓝牙.*已.*打开 -> 蓝牙已打开
2025-07-22 17:30:03 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: 蓝牙已打开
2025-07-22 17:33:40 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:33:40 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:33:40 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:33:40 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:33:43 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:33:43 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:33:43 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:33:43 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:33:43 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:33:43 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:33:43 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:33:43 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:33:43 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:33:43 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:33:43 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:33:43 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:33:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:33:43 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:33:43 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:33:44 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:33:44 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:33:44 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:33:46 | WARNING | __main__:_get_element_text_with_retry:293 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 17:35:39 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:35:39 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:35:39 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:35:39 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:35:42 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:35:42 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:35:42 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:35:42 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:35:42 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:35:42 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:35:42 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:35:42 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:35:42 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:35:42 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:35:42 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:35:42 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:35:42 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:35:43 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:35:43 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:35:43 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:35:43 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:35:43 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:35:45 | WARNING | __main__:_get_element_text_with_retry:293 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 17:37:32 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:37:32 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:37:32 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:37:32 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:37:35 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:37:35 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:37:35 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:37:35 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:37:35 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:37:35 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:37:35 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:37:35 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:37:35 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:37:35 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:37:35 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:37:35 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:37:35 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:37:36 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:37:36 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:37:36 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:37:36 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:37:36 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:37:38 | WARNING | __main__:_get_element_text_with_retry:293 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 17:37:40 | WARNING | __main__:_get_element_text_with_retry:293 | robot_text文本不符合AI响应格式: Which app should I open?，已达到最大重试次数
2025-07-22 17:37:41 | WARNING | __main__:_get_element_text_with_retry:254 | function_name节点不存在，已达到最大重试次数
2025-07-22 17:37:43 | WARNING | __main__:_get_element_text_with_retry:254 | function_control节点不存在，已达到最大重试次数
2025-07-22 17:38:06 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:38:06 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:38:06 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:38:06 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:38:09 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:38:09 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:38:09 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:38:09 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:38:09 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:38:09 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:38:09 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:38:09 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:38:09 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:38:09 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:38:09 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:38:09 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:38:09 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:38:10 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:38:10 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:38:10 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:38:10 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:38:10 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:38:12 | WARNING | __main__:_get_element_text_with_retry:293 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 17:38:14 | WARNING | __main__:_get_element_text_with_retry:293 | robot_text文本不符合AI响应格式: Which app should I open?，已达到最大重试次数
2025-07-22 17:38:16 | WARNING | __main__:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: Bluetooth，已达到最大重试次数
2025-07-22 17:38:18 | WARNING | __main__:_get_element_text_with_retry:268 | function_control节点文本为空，已达到最大重试次数
2025-07-22 17:38:45 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 17:38:45 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 17:38:45 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 17:38:45 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 17:38:48 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 17:38:48 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 17:38:48 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 17:38:48 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:38:48 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 17:38:48 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 17:38:48 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 17:38:48 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 17:38:48 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 17:38:48 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 17:38:48 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 17:38:48 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 17:38:48 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 17:38:49 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 17:38:49 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 17:38:49 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 17:38:49 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 17:38:49 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 17:38:51 | WARNING | __main__:_get_element_text_with_retry:293 | asr_txt文本不符合AI响应格式: open Bluetooth，已达到最大重试次数
2025-07-22 17:38:51 | INFO | __main__:_is_ai_response:513 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-22 17:38:51 | INFO | __main__:_get_element_text_with_retry:285 | ✅ 从robot_text成功获取响应: Bluetooth is turned on now.
2025-07-22 17:38:53 | WARNING | __main__:_get_element_text_with_retry:293 | function_name文本不符合AI响应格式: Bluetooth，已达到最大重试次数
2025-07-22 17:38:55 | WARNING | __main__:_get_element_text_with_retry:268 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:31:16 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:31:16 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:31:16 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:31:16 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:31:21 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:31:21 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:31:21 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:31:21 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:31:21 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:31:21 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:31:21 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:31:21 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:31:21 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:31:21 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:31:21 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:31:21 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:31:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:31:21 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:31:21 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:31:22 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:31:22 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:31:22 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'str'>, 内容: 蓝牙已成功开启，设备可以被发现
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已成功开启'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'str'>, 内容: 蓝牙已成功开启，设备可以被发现，连接状态良好
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已成功开启'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备可以被发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '', '设备']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '设备可发现', '']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '   ', None]
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | ⚠️ 响应文本为空或只包含空白字符
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:234 | ⚠️ 响应未包含期望内容: '蓝牙'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:242 | 缺失内容: ['蓝牙']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:243 | 搜索文本: ''
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['蓝牙已打开', '设备可发现']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:234 | ⚠️ 响应未包含期望内容: 'WiFi'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '设备'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | ❌ 部分期望内容未找到 (2/3)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:242 | 缺失内容: ['WiFi']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:243 | 搜索文本: '蓝牙已打开 设备可发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 全部匹配
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:339 | ⚠️ 未找到期望内容: '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:308 | ✅ [合并模式] 找到期望内容: '设备'
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:346 | ❌ 部分期望内容未找到 (1/2)
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:347 | 缺失内容: ['蓝牙已打开']
2025-07-22 20:31:22 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:348 | 响应内容: ['蓝牙', '已打开', '设备可发现']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: individual, 匹配模式: 全部匹配
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:315 | ✅ [独立模式] 在响应项1中找到期望内容: '蓝牙' -> '蓝牙已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:315 | ✅ [独立模式] 在响应项2中找到期望内容: '设备' -> '设备可发现'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:344 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:266 | verify_expected_in_response_advanced 响应类型: <class 'list'>, 搜索模式: combined, 匹配模式: 任意匹配
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:308 | ✅ [合并模式] 找到期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response_advanced:335 | 🎉 [任意匹配模式] 找到期望内容，验证通过: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['打开蓝牙', '蓝牙已打开', '蓝牙', '已打开', '']
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '蓝牙'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: '已打开'
2025-07-22 20:31:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:32:50 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:32:50 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:32:50 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:32:50 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:32:53 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:32:53 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:32:53 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:32:53 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:32:53 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:32:53 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:32:53 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:32:53 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:32:53 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:32:53 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:32:53 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:32:53 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:32:53 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:32:54 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:32:54 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:32:54 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:32:54 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:32:54 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:32:54 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:32:55 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:32:55 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:32:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:32:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:32:58 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:32:58 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:32:58 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:32:58 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:32:59 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:32:59 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:32:59 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:32:59 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:32:59 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:33:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 20:33:00 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:33:00 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:33:01 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 20:33:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:33:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:33:01 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:33:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:33:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:33:02 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 20:33:02 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:33:02 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:33:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:33:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:33:02 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:33:02 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:33:02 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:33:02 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:33:02 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:33:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:33:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:33:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:04 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:33:05 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | asr_txt文本不符合AI响应格式: open wifi，已达到最大重试次数
2025-07-22 20:33:07 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now.，已达到最大重试次数
2025-07-22 20:33:09 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | function_name文本不符合AI响应格式: Wi-Fi，已达到最大重试次数
2025-07-22 20:33:11 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:266 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:33:11 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:33:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:33:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:33:14 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:33:14 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:33:14 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:33:34 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:33:34 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:33:34 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:33:34 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:33:37 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:33:37 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:33:37 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:33:37 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:33:37 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:33:37 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:33:37 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:33:37 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:33:37 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:33:37 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:33:37 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:33:37 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:33:37 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:33:38 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:33:38 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:33:38 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:33:38 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:33:38 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:33:38 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:33:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:33:39 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:33:39 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:33:39 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:42 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:33:42 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:33:42 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:33:42 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:33:42 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:33:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:33:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:33:43 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:33:43 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:33:44 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 20:33:44 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:33:44 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:44 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:33:44 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:33:45 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:33:45 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 20:33:45 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:33:45 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:33:45 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:33:45 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:33:46 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 20:33:46 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:33:46 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:33:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:33:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:33:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:33:46 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:33:46 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:33:46 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:33:46 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:33:46 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-22 20:33:47 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:33:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:33:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:33:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:33:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:33:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:33:48 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:33:49 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | asr_txt文本不符合AI响应格式: open wifi，已达到最大重试次数
2025-07-22 20:33:51 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:252 | robot_text节点不存在，已达到最大重试次数
2025-07-22 20:33:52 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | function_name文本不符合AI响应格式: Wi-Fi，已达到最大重试次数
2025-07-22 20:33:54 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:266 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:33:54 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 20:33:54 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:412 | 从TextView元素获取响应
2025-07-22 20:33:55 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:428 | 查找RecyclerView中的最新消息
2025-07-22 20:33:56 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:534 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 20:33:56 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:453 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 20:33:56 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:154 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-22 20:33:56 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:159 | 未获取到有效的响应文本
2025-07-22 20:33:56 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', '', 'Wi-Fi', '', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />']'
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', '', 'Wi-Fi', '', '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />']
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:33:59 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:33:59 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:33:59 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:33:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:43:46 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:43:46 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:43:47 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:43:47 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:43:50 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:43:50 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:43:50 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:43:50 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:43:50 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:43:50 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:43:50 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:43:50 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:43:50 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:43:50 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:43:50 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:43:50 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:43:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:43:50 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:43:50 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:43:51 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:43:51 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:43:51 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:43:51 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:43:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:43:51 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:43:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:43:55 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:43:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:43:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:43:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:43:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:43:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:43:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:43:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:44:12 | WARNING | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:236 | ⚠️ UI元素加载超时 (15秒)
2025-07-22 20:44:12 | ERROR | testcases.test_ella.base_ella_test:ella_app:30 | ❌ Ella应用启动异常: Ella页面加载失败
assert False
 +  where False = wait_for_page_load(timeout=15)
 +    where wait_for_page_load = <pages.apps.ella.main_page_refactored.EllaMainPageRefactored object at 0x000002B883170A50>.wait_for_page_load
2025-07-22 20:44:12 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:44:12 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:44:12 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:44:44 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:44:44 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:44:44 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:44:44 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:44:47 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:44:47 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:44:47 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:44:47 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:44:47 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:44:47 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:44:47 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:44:47 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:44:47 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:44:47 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:44:47 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:44:47 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:44:47 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:44:48 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:44:48 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:44:48 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:44:48 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:44:48 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:44:48 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:44:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:44:49 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:44:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:44:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:44:52 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:44:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:44:52 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:44:52 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:44:53 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:44:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:44:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:44:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:44:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:44:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 20:44:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:44:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:44:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:44:54 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:44:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:44:55 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 20:44:55 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:44:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:44:55 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:44:55 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:44:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:44:56 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 20:44:56 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:44:56 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:44:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:44:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:44:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:44:56 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:44:56 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:44:56 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:44:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-22 20:44:56 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:44:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:44:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:44:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:44:57 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:44:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open wifi，已达到最大重试次数
2025-07-22 20:45:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now.，已达到最大重试次数
2025-07-22 20:45:02 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | function_name文本不符合AI响应格式: Wi-Fi，已达到最大重试次数
2025-07-22 20:45:04 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:267 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:45:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:137 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:45:07 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:150 | ✅ 状态验证通过: True -> None
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:simple_command_test:375 | 🎉 open wifi 测试完成
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:187 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:231 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:45:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:239 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:45:08 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:45:08 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:45:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:47:19 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:47:19 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:47:19 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:47:19 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:47:22 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:47:22 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:47:22 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:47:22 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:47:22 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:47:22 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:47:22 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:47:22 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:47:22 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:47:22 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:47:22 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:47:22 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:47:22 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:47:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:47:23 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:47:23 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:47:23 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:47:23 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:47:23 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:47:24 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:47:24 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:47:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:47:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:47:27 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:47:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:47:27 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:47:27 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:47:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:47:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:47:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:47:28 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:47:28 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:47:29 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:47:29 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 20:47:30 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-22 20:47:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:47:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:47:30 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:47:31 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:47:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:47:31 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 20:47:31 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:47:31 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:47:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:47:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:47:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:47:32 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:47:32 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:47:32 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:47:32 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open wifi
2025-07-22 20:47:32 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:47:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:47:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:47:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:47:33 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:47:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open wifi，已达到最大重试次数
2025-07-22 20:47:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now.，已达到最大重试次数
2025-07-22 20:47:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | function_name文本不符合AI响应格式: Wi-Fi，已达到最大重试次数
2025-07-22 20:47:40 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:267 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:47:40 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 20:47:43 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 20:47:43 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 20:47:43 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: True -> True
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open wifi 测试完成
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 20:47:44 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:47:44 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:47:44 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:47:45 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:48:04 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:48:04 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:48:04 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:48:04 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:48:07 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:48:07 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:48:07 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:48:07 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:48:07 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:48:07 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:48:07 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:48:07 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:48:07 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:48:07 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:48:07 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:48:07 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:48:07 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:48:08 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:48:08 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:48:09 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:48:09 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:48:09 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:48:09 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:48:09 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:48:09 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:48:09 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:48:12 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:48:12 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:48:12 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:48:12 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:48:13 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:48:13 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:48:13 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:48:13 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:48:14 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:48:15 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:48:15 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app打开应用，状态: None
2025-07-22 20:48:15 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:48:15 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open app
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:48:15 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open app
2025-07-22 20:48:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:48:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:48:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:48:16 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:48:16 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open app
2025-07-22 20:48:16 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:48:16 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:48:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:48:17 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:48:17 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open app
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:48:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:48:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:48:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:48:17 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:48:19 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 20:48:21 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | robot_text文本不符合AI响应格式: Which app should I open?，已达到最大重试次数
2025-07-22 20:48:22 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 20:48:23 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 20:48:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open app', 'Which app should I open?', '', '']'
2025-07-22 20:48:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: None -> None
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open app 测试完成
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open app', 'Which app should I open?', '', '']
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-22 20:48:27 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:48:27 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:48:27 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:48:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:49:44 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:49:44 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:49:44 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:49:44 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:49:47 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:49:47 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:49:47 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:49:47 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:49:47 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:49:47 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:49:47 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:49:47 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:49:47 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:49:47 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:49:47 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:49:47 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:49:47 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:49:48 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:49:48 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:49:48 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:49:48 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:49:48 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:49:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:49:49 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:49:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:49:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:49:52 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:49:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:49:52 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:49:52 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:49:53 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:49:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:49:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:49:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:49:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:49:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:49:54 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-22 20:49:54 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-22 20:49:54 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-22 20:49:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: True
2025-07-22 20:49:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:49:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:49:55 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:49:55 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-22 20:49:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:49:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:49:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:49:55 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:49:56 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-22 20:49:56 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:49:56 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:49:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:49:57 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:49:57 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open bluetooth
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:49:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:49:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:49:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:49:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:49:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:49:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:49:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:49:58 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:49:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open bluetooth，已达到最大重试次数
2025-07-22 20:49:59 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:640 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-22 20:49:59 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:284 | ✅ 从robot_text成功获取响应: Bluetooth is turned on now.
2025-07-22 20:50:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | function_name文本不符合AI响应格式: Bluetooth，已达到最大重试次数
2025-07-22 20:50:03 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:267 | function_control节点文本为空，已达到最大重试次数
2025-07-22 20:50:03 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']'
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-22 20:50:06 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-22 20:50:06 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: True -> True
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open bluetooth 测试完成
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'bluetooth'
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'is turned on now'
2025-07-22 20:50:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 20:50:07 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:50:07 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:50:07 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:52:35 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:52:35 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:52:36 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:52:36 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:52:39 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:52:39 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:52:39 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:52:39 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:52:39 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:52:39 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:52:39 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:52:39 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:52:39 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:52:39 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:52:39 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:52:39 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:52:39 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:52:39 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:52:39 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:52:40 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:52:40 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:52:40 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:52:40 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:52:40 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:52:40 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:52:40 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:44 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:52:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:52:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:52:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:52:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:52:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:52:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:52:44 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:52:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:52:46 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6a64b5a #101 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{84ef84e u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t101}
    * Hist  #0
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-22 20:52:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:52:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-22 20:52:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:52:47 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:52:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:52:47 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:52:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:52:48 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-22 20:52:48 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:52:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:52:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:52:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:52:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:52:48 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:52:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:52:48 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:52:48 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-22 20:52:51 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-22 20:52:51 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:52:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 20:52:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 20:52:51 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 20:52:51 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-22 20:52:51 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 20:52:51 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 20:52:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:52:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:52:53 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 20:52:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-22 20:52:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:52:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:320 | 从check_area节点获取响应文本
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:332 | 从check_area直接获取文本: Done!
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-22 20:52:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6a64b5a #101 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{84ef84e u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t101}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-22 20:52:55 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'done'
2025-07-22 20:52:55 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:52:55 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:52:55 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:52:55 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 20:53:24 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 20:53:24 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 20:53:24 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 20:53:24 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 20:53:28 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 20:53:28 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 20:53:28 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 20:53:28 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:53:28 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 20:53:28 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 20:53:28 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 20:53:28 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 20:53:28 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 20:53:28 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 20:53:28 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 20:53:28 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 20:53:28 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 20:53:28 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:53:28 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 20:53:29 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:53:29 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 20:53:29 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 20:53:29 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 20:53:29 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 20:53:29 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 20:53:30 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:53:33 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 20:53:33 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 20:53:33 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 20:53:33 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 20:53:33 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 20:53:34 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 20:53:34 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 20:53:34 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 20:53:34 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 20:53:35 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 20:53:35 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella打开应用，状态: None
2025-07-22 20:53:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:53:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open ella
2025-07-22 20:53:35 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 20:53:36 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 20:53:36 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open ella
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:53:36 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:53:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 20:53:36 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open ella
2025-07-22 20:53:36 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 20:53:36 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 20:53:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 20:53:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 20:53:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 20:53:37 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 20:53:37 | INFO | testcases.test_ella.base_ella_test:_execute_command:114 | ✅ 成功执行命令: open ella
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 20:53:37 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:126 | 确保返回到Ella应用以获取响应文本
2025-07-22 20:53:37 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:53:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:53:38 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 20:53:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 20:53:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 20:53:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 20:53:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 20:53:38 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 20:53:39 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open ella，已达到最大重试次数
2025-07-22 20:53:40 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:650 | 匹配到完成响应模式: done -> Done!
2025-07-22 20:53:40 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:284 | ✅ 从robot_text成功获取响应: Done!
2025-07-22 20:53:41 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 20:53:42 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 20:53:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:139 | AI响应: '['open ella', 'Done!', '', '']'
2025-07-22 20:53:45 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:152 | ✅ 状态验证通过: None -> None
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:simple_command_test:377 | 🎉 open ella 测试完成
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:189 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open ella', 'Done!', '', '']
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:233 | ✅ 响应包含期望内容: 'Done'
2025-07-22 20:53:46 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:241 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 20:53:46 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 20:53:46 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 20:53:46 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:07:55 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:07:55 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:07:55 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:07:55 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:08:00 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:08:00 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:08:00 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:08:00 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:08:00 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:08:00 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:08:00 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:08:00 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:08:00 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:08:00 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:08:00 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:08:00 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:08:00 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:08:00 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:08:00 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:08:01 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:08:01 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:08:01 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:08:02 | INFO | testcases.test_ella.base_ella_test:add_custom_status_check:258 | 添加自定义状态检查配置: calculator -> 计算器应用状态
2025-07-22 21:13:20 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:13:20 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:13:20 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:13:20 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:13:23 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:13:23 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:13:23 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:13:23 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:13:23 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:13:23 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:13:23 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:13:23 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:13:23 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:13:23 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:13:23 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:13:23 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:13:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:13:24 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:13:24 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:13:25 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:13:25 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:13:25 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:13:25 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:13:25 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 21:13:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 21:13:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:13:28 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 21:13:28 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 21:13:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 21:13:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 21:13:29 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:13:29 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 21:13:29 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 21:13:29 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 21:13:29 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 21:13:30 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:13:30 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{ec5b185 #104 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{f6153f5 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t104}
    * Hist  #0
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-22 21:13:31 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone打开应用，状态: True
2025-07-22 21:13:31 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:13:31 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open phone
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:13:31 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open phone
2025-07-22 21:13:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:13:32 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:13:32 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open phone
2025-07-22 21:13:32 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 21:13:32 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:13:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 21:13:33 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 21:13:33 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 21:13:33 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 21:13:33 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open phone
2025-07-22 21:13:33 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 21:13:33 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 21:13:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:13:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:13:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:13:34 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:13:34 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 21:13:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | asr_txt节点不存在，已达到最大重试次数
2025-07-22 21:13:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | robot_text节点不存在，已达到最大重试次数
2025-07-22 21:13:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 21:13:39 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 21:13:39 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 21:13:39 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:541 | 从TextView元素获取响应
2025-07-22 21:13:41 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:557 | 查找RecyclerView中的最新消息
2025-07-22 21:13:42 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:389 | 从dump正则提取文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13
2025-07-22 21:13:42 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13
2025-07-22 21:13:42 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-22 21:13:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13']'
2025-07-22 21:13:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:13:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 21:13:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 21:13:45 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 21:13:45 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 21:13:45 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 21:13:45 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 21:13:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:13:47 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{ec5b185 #104 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{f6153f5 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t104}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-22 21:13:47 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open phone 测试完成
2025-07-22 21:13:47 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13']
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:13:47 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:13'
2025-07-22 21:13:47 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 21:13:47 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:13:47 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:23:35 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:23:35 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:23:35 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:23:35 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:23:38 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:23:38 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:23:38 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:23:38 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:23:38 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:23:38 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:23:38 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:23:38 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:23:38 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:23:38 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:23:38 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:23:38 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:23:38 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:23:39 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:23:39 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:23:39 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:23:39 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:23:39 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:23:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:23:39 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 21:23:40 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 21:23:40 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:23:43 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 21:23:43 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 21:23:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 21:23:43 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 21:23:43 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:23:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 21:23:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 21:23:44 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 21:23:44 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 21:23:45 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:23:45 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:23:45 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:23:45 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{31ad024 #106 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{565bf8f u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t106}
    * Hist  #0
2025-07-22 21:23:45 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:23:45 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone打开应用，状态: True
2025-07-22 21:23:45 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:23:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:23:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open phone
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:23:46 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open phone
2025-07-22 21:23:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:23:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:23:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:23:47 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:23:47 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open phone
2025-07-22 21:23:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 21:23:47 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:23:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 21:23:48 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 21:23:48 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open phone
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 21:23:48 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:23:48 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:23:48 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:23:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:23:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:23:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:23:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:23:49 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 21:23:52 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | robot_text节点不存在，已达到最大重试次数
2025-07-22 21:23:53 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 21:23:54 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 21:23:54 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 21:23:54 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:541 | 从TextView元素获取响应
2025-07-22 21:23:56 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:557 | 查找RecyclerView中的最新消息
2025-07-22 21:23:57 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:389 | 从dump正则提取文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23
2025-07-22 21:23:57 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23
2025-07-22 21:23:57 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-22 21:23:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23']'
2025-07-22 21:24:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:24:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 21:24:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 21:24:00 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 21:24:00 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 21:24:00 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 21:24:00 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:24:02 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{31ad024 #106 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{565bf8f u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t106}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 21:24:02 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open phone 测试完成
2025-07-22 21:24:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23']
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:24:02 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:23'
2025-07-22 21:24:03 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 21:24:03 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:24:03 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:26:02 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:26:02 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:26:02 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:26:02 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:26:06 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:26:06 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:26:06 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:26:06 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:26:06 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:26:06 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:26:06 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:26:06 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:26:06 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:26:06 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:26:06 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:26:06 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:26:06 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:26:06 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:26:06 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:26:07 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:26:07 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:26:07 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:26:07 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:26:07 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 21:26:07 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 21:26:07 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:11 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 21:26:11 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 21:26:11 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 21:26:11 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 21:26:11 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:26:11 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 21:26:11 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 21:26:11 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 21:26:12 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 21:26:13 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{5d7d271 #109 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{ef0f240 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t109}
    * Hist  #0
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-22 21:26:13 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:26:13 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:26:13 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:26:14 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:26:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:26:14 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-22 21:26:14 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 21:26:14 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 21:26:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 21:26:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:26:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 21:26:15 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 21:26:15 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 21:26:15 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 21:26:15 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-22 21:26:18 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-22 21:26:18 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:26:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 21:26:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 21:26:18 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 21:26:18 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-22 21:26:18 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 21:26:18 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:26:20 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 21:26:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-22 21:26:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:26:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:26:21 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-22 21:26:21 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:320 | 从check_area节点获取响应文本
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:332 | 从check_area直接获取文本: Done!
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{5d7d271 #109 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{ef0f240 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t109}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-22 21:26:22 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:416 | ✅ 响应包含期望内容: 'done'
2025-07-22 21:26:22 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:424 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 21:26:22 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 21:26:22 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:26:23 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:27:14 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:27:14 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:27:14 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:27:14 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:27:17 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:27:17 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:27:17 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:27:17 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:27:17 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:27:17 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:27:17 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:27:17 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:27:17 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:27:17 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:27:17 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:27:17 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:27:17 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:27:18 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:27:18 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:27:18 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:27:18 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:27:18 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:27:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:27:19 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:27:19 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 21:27:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 21:27:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:27:22 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 21:27:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 21:27:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 21:27:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 21:27:23 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:27:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 21:27:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 21:27:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 21:27:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 21:27:24 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:27:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:27:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:27:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{611b4fc #110 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{535abd0 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t110}
    * Hist  #0
2025-07-22 21:27:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:27:24 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contact打开应用，状态: True
2025-07-22 21:27:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:27:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:27:25 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:27:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-22 21:27:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:27:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:27:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:27:26 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 21:27:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:27:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:27:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:27:26 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-22 21:27:26 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 21:27:26 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 21:27:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 21:27:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:27:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 21:27:27 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 21:27:27 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 21:27:27 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 21:27:27 | INFO | testcases.test_ella.base_ella_test:_execute_command:297 | ✅ 成功执行命令: open contact
2025-07-22 21:27:27 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 21:27:27 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 21:27:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:309 | 确保返回到Ella应用以获取响应文本
2025-07-22 21:27:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:27:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:27:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:27:28 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 21:27:29 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | asr_txt节点不存在，已达到最大重试次数
2025-07-22 21:27:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | robot_text节点不存在，已达到最大重试次数
2025-07-22 21:27:32 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 21:27:33 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 21:27:33 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 21:27:33 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:541 | 从TextView元素获取响应
2025-07-22 21:27:35 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:557 | 查找RecyclerView中的最新消息
2025-07-22 21:27:36 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:389 | 从dump正则提取文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27
2025-07-22 21:27:36 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27
2025-07-22 21:27:36 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-22 21:27:36 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:322 | AI响应: '['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27']'
2025-07-22 21:27:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:27:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 21:27:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 21:27:39 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 21:27:39 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 21:27:39 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 21:27:39 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:27:41 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{611b4fc #110 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{535abd0 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t110}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 21:27:41 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:27:41 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:335 | ✅ 状态验证通过: True -> True
2025-07-22 21:27:42 | INFO | testcases.test_ella.base_ella_test:simple_command_test:560 | 🎉 open contact 测试完成
2025-07-22 21:27:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:372 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27']
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:419 | ⚠️ 响应未包含期望内容: 'done'
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:426 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:427 | 缺失内容: ['done']
2025-07-22 21:27:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:428 | 搜索文本: 'ABC DEF GHI JKL MNO PQRS TUV WXYZ 通话记录 联系人 21:27'
2025-07-22 21:27:42 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 21:27:42 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:27:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:42:36 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:42:36 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:42:37 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:42:37 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:42:41 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:42:41 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:42:41 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:42:41 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:42:41 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:42:41 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:42:41 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:42:41 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:42:41 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:42:41 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:42:41 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:42:41 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:42:41 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:42:41 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:42:41 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:42:42 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:42:42 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:42:42 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: False
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: True (当前页面: ella)
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contacts，状态: False
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: True (当前页面: ella)
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: False
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: True (当前页面: ella)
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: False (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第1次尝试返回
2025-07-22 21:42:42 | INFO | __main__:return_to_ella_app:36 | 模拟返回Ella应用 (第1次尝试)
2025-07-22 21:42:42 | WARNING | __main__:return_to_ella_app:40 | 模拟返回失败 (第1次)
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第1次返回Ella应用失败
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第2次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: False (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第2次尝试返回
2025-07-22 21:42:42 | INFO | __main__:return_to_ella_app:36 | 模拟返回Ella应用 (第2次尝试)
2025-07-22 21:42:42 | WARNING | __main__:return_to_ella_app:40 | 模拟返回失败 (第2次)
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第2次返回Ella应用失败
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第3次尝试确保在Ella页面以获取响应
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: False (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:102 | 不在Ella对话页面，第3次尝试返回
2025-07-22 21:42:42 | INFO | __main__:return_to_ella_app:36 | 模拟返回Ella应用 (第3次尝试)
2025-07-22 21:42:42 | WARNING | __main__:return_to_ella_app:40 | 模拟返回失败 (第3次)
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:112 | 第3次返回Ella应用失败
2025-07-22 21:42:42 | ERROR | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:115 | 经过3次尝试仍无法返回Ella页面，强制获取响应
2025-07-22 21:42:42 | WARNING | __main__:get_response_all_text:52 | 不在Ella页面，无法获取响应文本 (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:417 | get_response_all_text()返回空，尝试备用方法
2025-07-22 21:42:42 | WARNING | __main__:get_response_text:60 | 不在Ella页面，无法获取响应文本 (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:422 | 所有响应获取方法都返回空
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '[]'
2025-07-22 21:42:42 | WARNING | __main__:get_response_all_text:52 | 不在Ella页面，无法获取响应文本 (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:417 | get_response_all_text()返回空，尝试备用方法
2025-07-22 21:42:42 | WARNING | __main__:get_response_text:60 | 不在Ella页面，无法获取响应文本 (当前页面: contacts)
2025-07-22 21:42:42 | WARNING | testcases.test_ella.base_ella_test:_safe_get_response_text:422 | 所有响应获取方法都返回空
2025-07-22 21:42:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open contacts，状态: False
2025-07-22 21:42:42 | INFO | __main__:ensure_on_chat_page:30 | 检查是否在Ella页面: True (当前页面: ella)
2025-07-22 21:44:14 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 21:44:14 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 21:44:14 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 21:44:14 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 21:44:17 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 21:44:17 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 21:44:17 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 21:44:17 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:44:17 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 21:44:17 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 21:44:17 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 21:44:17 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 21:44:17 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 21:44:17 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 21:44:17 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 21:44:17 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 21:44:17 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 21:44:18 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:44:18 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 21:44:18 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:44:18 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 21:44:18 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 21:44:19 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 21:44:19 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 21:44:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 21:44:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:44:22 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 21:44:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 21:44:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 21:44:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 21:44:23 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 21:44:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 21:44:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 21:44:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 21:44:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 21:44:24 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 21:44:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:44:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:44:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{44095fb #111 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{3ba2adf u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t111}
    * Hist  #0
2025-07-22 21:44:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:44:24 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone，状态: True
2025-07-22 21:44:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:44:25 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open phone
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 21:44:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open phone
2025-07-22 21:44:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:44:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:44:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:44:26 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 21:44:26 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open phone
2025-07-22 21:44:26 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 21:44:26 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 21:44:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 21:44:27 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 21:44:27 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 21:44:27 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 21:44:27 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open phone
2025-07-22 21:44:27 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 21:44:27 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 21:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 21:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 21:44:30 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 21:44:30 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 21:44:30 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 21:44:30 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:44:32 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{44095fb #111 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{3ba2adf u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t111}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 21:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 21:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 21:44:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 21:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:44:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 21:44:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 21:44:33 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 21:44:35 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | asr_txt文本不符合AI响应格式: open phone，已达到最大重试次数
2025-07-22 21:44:35 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:650 | 匹配到完成响应模式: done -> Done!
2025-07-22 21:44:35 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:284 | ✅ 从robot_text成功获取响应: Done!
2025-07-22 21:44:37 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_name节点不存在，已达到最大重试次数
2025-07-22 21:44:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:253 | function_control节点不存在，已达到最大重试次数
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open phone', 'Done!', '', '']'
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open phone 测试完成
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open phone', 'Done!', '', '']
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 21:44:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 21:44:38 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 21:44:38 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 21:44:39 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 21:50:34 | INFO | __main__:run_ella_tests:164 | 运行设置测试
2025-07-22 21:50:34 | INFO | __main__:run_tests:44 | 开始执行测试
2025-07-22 21:50:35 | WARNING | __main__:run_tests:79 | 测试执行完成，存在失败用例，退出码: 1
2025-07-22 21:50:35 | INFO | __main__:generate_allure_report:99 | 生成Allure报告
2025-07-22 21:50:35 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 21:58:31 | INFO | __main__:run_ella_tests:164 | 运行设置测试
2025-07-22 21:58:31 | INFO | __main__:run_tests:44 | 开始执行测试
2025-07-22 21:58:31 | WARNING | __main__:run_tests:79 | 测试执行完成，存在失败用例，退出码: 1
2025-07-22 21:58:31 | INFO | __main__:generate_allure_report:99 | 生成Allure报告
2025-07-22 21:58:31 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 22:02:31 | INFO | __main__:run_ella_tests:164 | 运行设置测试
2025-07-22 22:02:31 | INFO | __main__:run_tests:44 | 开始执行测试
2025-07-22 22:02:32 | WARNING | __main__:run_tests:79 | 测试执行完成，存在失败用例，退出码: 1
2025-07-22 22:02:32 | INFO | __main__:generate_allure_report:99 | 生成Allure报告
2025-07-22 22:02:32 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-22 22:04:11 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:04:11 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:04:11 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:04:11 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:04:16 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:04:16 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:04:16 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:04:16 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:04:16 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:04:16 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:04:16 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:04:16 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:04:16 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:04:16 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:04:16 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:04:16 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:04:16 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:04:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:04:17 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:04:17 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:04:17 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:04:17 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:04:20 | WARNING | core.data_driven_test:parametrize_from_excel:326 | 没有找到匹配的测试数据
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:04:20 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:04:21 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:04:21 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:04:21 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-22 22:04:21 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:21 | INFO | core.base_driver:start_app:233 | 启动应用: com.transsion.aivoiceassistant
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-22 22:04:23 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-22 22:04:23 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-22 22:04:24 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-22 22:04:25 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-22 22:04:26 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:04:27 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:27 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:28 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:28 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:29 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:29 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:29 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:580 | 清空输入框...
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:30 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:586 | 主输入框已清空
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-22 22:04:30 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-22 22:04:30 | INFO | pages.apps.ella.history.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-22 22:04:31 | INFO | pages.apps.ella.history.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:31 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:32 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-22 22:04:32 | INFO | pages.apps.ella.history.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-22 22:04:33 | INFO | pages.apps.ella.history.main_page:send_command:657 | 发送命令
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:34 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:send_command:664 | ✅ 命令发送成功
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-22 22:04:34 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:wait_for_response:2008 | 初始元素数量: 13
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:34 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:_check_tts_button_appeared:2323 | 检测到TTS播放按钮
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-22 22:04:36 | INFO | pages.apps.ella.history.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-22 22:04:40 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:get_response_text_smart:2428 | 智能获取响应文本...
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:40 | INFO | pages.apps.ella.history.main_page:get_response_text:2452 | 获取AI响应文本
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2468 | 页面上所有文本元素数量: 13
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:_is_ai_response:2286 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2476 | 找到AI响应: Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:get_response_text:2489 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:verify_command_in_response:2605 | 验证响应是否包含命令: open bluetooth
2025-07-22 22:04:42 | INFO | pages.apps.ella.history.main_page:verify_command_in_response:2635 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-22 22:04:42 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-22 22:04:44 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status_smart:2404 | 智能检查蓝牙状态...
2025-07-22 22:04:44 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-22 22:04:45 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-22 22:04:45 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:45 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-22 22:04:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:04:45 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-22 22:04:45 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:45 | INFO | core.base_driver:start_app:233 | 启动应用: com.transsion.aivoiceassistant
2025-07-22 22:04:48 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-22 22:04:48 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-22 22:04:48 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:04:48 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:04:48 | INFO | pages.apps.ella.history.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-22 22:04:48 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-22 22:04:48 | INFO | pages.apps.ella.history.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-22 22:04:49 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:04:50 | INFO | testcases.test_ella.history.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 开启
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:50 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:51 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:51 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:51 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:execute_text_command:728 | 执行文本命令: close bluetooth
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-22 22:04:52 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-22 22:04:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:53 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:53 | INFO | pages.apps.ella.history.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-22 22:04:53 | INFO | pages.apps.ella.history.main_page:input_text_command:483 | 输入文本命令: close bluetooth
2025-07-22 22:04:53 | INFO | pages.apps.ella.history.main_page:clear_input_box:580 | 清空输入框...
2025-07-22 22:04:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:54 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:04:54 | INFO | pages.apps.ella.history.main_page:clear_input_box:586 | 主输入框已清空
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-22 22:04:54 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-22 22:04:54 | INFO | pages.apps.ella.history.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-22 22:04:54 | INFO | pages.apps.ella.history.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:55 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-22 22:04:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:56 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-22 22:04:56 | INFO | pages.apps.ella.history.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: close bluetooth
2025-07-22 22:04:56 | INFO | pages.apps.ella.history.main_page:verify_input_text:619 | 验证输入文本: close bluetooth
2025-07-22 22:04:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:04:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: close bluetooth
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:send_command:657 | 发送命令
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:04:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:04:57 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:send_command:664 | ✅ 命令发送成功
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-22 22:04:57 | INFO | testcases.test_ella.history.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 20秒
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:04:57 | INFO | pages.apps.ella.history.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-22 22:04:58 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-22 22:04:58 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:04:58 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:04:59 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:04:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:05:38 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:05:38 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:05:39 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:05:39 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:05:42 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:05:42 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:05:42 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:05:42 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:05:42 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:05:42 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:05:42 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:05:42 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:05:42 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:05:42 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:05:42 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:05:42 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:05:42 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:05:43 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:05:43 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:05:43 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:05:43 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:05:43 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:05:44 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:05:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:05:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:47 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:05:47 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:05:47 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:05:47 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:05:48 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:05:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app，状态: None
2025-07-22 22:05:50 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:50 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open app
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open app
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open app
2025-07-22 22:05:51 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:05:51 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:05:53 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open app
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:05:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:05:54 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:05:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 22:05:57 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | robot_text节点不存在，已达到最大重试次数
2025-07-22 22:05:58 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-22 22:05:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-22 22:05:59 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 22:05:59 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:539 | 从TextView元素获取响应
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:555 | 查找RecyclerView中的最新消息
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:387 | 从dump正则提取文本: Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06
2025-07-22 22:06:01 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]'
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: None -> None
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open app 测试完成
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:02 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:02 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:06:03 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:03 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:03 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:06 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:06 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:06 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:07 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:07 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:07 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:07 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:07 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:08 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-22 22:06:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-22 22:06:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:06:08 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth，状态: True
2025-07-22 22:06:08 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:09 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:09 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-22 22:06:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:09 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:10 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-22 22:06:10 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:10 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:11 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:11 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:11 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:11 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open bluetooth
2025-07-22 22:06:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:06:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:06:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:06:15 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:15 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:15 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:06:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:16 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:06:17 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open bluetooth，已达到最大重试次数
2025-07-22 22:06:17 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:638 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-22 22:06:17 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:282 | ✅ 从robot_text成功获取响应: Bluetooth is turned on now.
2025-07-22 22:06:19 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Bluetooth，已达到最大重试次数
2025-07-22 22:06:21 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open bluetooth 测试完成
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open bluetooth', 'Bluetooth is turned on now.', 'Bluetooth', '']
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'bluetooth'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'is turned on now'
2025-07-22 22:06:21 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (2/2)
2025-07-22 22:06:21 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:21 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:06:22 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:25 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:25 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:25 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:26 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:27 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6f57a88 #116 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{9142d96 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t116}
    * Hist  #0
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-22 22:06:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:28 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:29 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:29 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-22 22:06:32 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-22 22:06:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 22:06:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 22:06:33 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 22:06:33 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-22 22:06:33 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 22:06:33 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:34 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:318 | 从check_area节点获取响应文本
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:330 | 从check_area直接获取文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6f57a88 #116 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{9142d96 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t116}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:36 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:36 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:06:37 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:37 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:37 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:40 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:40 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:40 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:40 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:41 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:41 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:41 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:41 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:41 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:42 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:42 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella，状态: None
2025-07-22 22:06:42 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:42 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:42 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:43 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:43 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open ella
2025-07-22 22:06:43 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:43 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:43 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open ella
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:43 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:44 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open ella
2025-07-22 22:06:44 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:44 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:45 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open ella
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:06:45 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:45 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:45 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:06:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:46 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:06:47 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open ella，已达到最大重试次数
2025-07-22 22:06:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:648 | 匹配到完成响应模式: done -> Done!
2025-07-22 22:06:47 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:282 | ✅ 从robot_text成功获取响应: Done!
2025-07-22 22:06:49 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-22 22:06:50 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-22 22:06:50 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open ella', 'Done!', '', '']'
2025-07-22 22:06:50 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: None -> None
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open ella 测试完成
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open ella', 'Done!', '', '']
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'Done'
2025-07-22 22:06:51 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:51 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:51 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:06:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:51 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:51 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:55 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{182c4d3 #118 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{f32110c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t118}
    * Hist  #0
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:57 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open phone，状态: True
2025-07-22 22:06:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:57 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open phone
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:57 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open phone
2025-07-22 22:06:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:58 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open phone
2025-07-22 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:59 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:59 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open phone
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:06:59 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:07:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 22:07:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 22:07:03 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 22:07:03 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:416 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-22 22:07:03 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 22:07:03 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:04 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{182c4d3 #118 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{f32110c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t118}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:07:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:07:04 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:07:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:05 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:07:05 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:05 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:07:07 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open phone，已达到最大重试次数
2025-07-22 22:07:07 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:648 | 匹配到完成响应模式: done -> Done!
2025-07-22 22:07:07 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:282 | ✅ 从robot_text成功获取响应: Done!
2025-07-22 22:07:08 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-22 22:07:10 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open phone', 'Done!', '', '']'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open phone 测试完成
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open phone', 'Done!', '', '']
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:07:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:07:11 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:07:11 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:07:11 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:07:11 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:07:11 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:14 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:07:14 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:07:14 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:07:14 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:07:15 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:07:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:07:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:07:15 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:07:15 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:07:16 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:07:16 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 22:07:17 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi，状态: True
2025-07-22 22:07:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:07:17 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-22 22:07:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:07:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:07:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:07:18 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:07:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:07:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:07:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:07:18 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-22 22:07:18 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:07:18 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:07:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:07:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:07:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:07:19 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:07:19 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:07:19 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:07:19 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open wifi
2025-07-22 22:07:19 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:07:20 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:07:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:07:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:07:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:07:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:07:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:07:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:07:24 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:07:25 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open wifi，已达到最大重试次数
2025-07-22 22:07:27 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Wi-Fi is turned on now.，已达到最大重试次数
2025-07-22 22:07:29 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Wi-Fi，已达到最大重试次数
2025-07-22 22:07:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: True -> True
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open wifi 测试完成
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'WI-FI'
2025-07-22 22:07:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:07:31 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:07:31 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:07:32 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:25:19 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:25:19 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:25:19 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:25:19 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:25:23 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:25:23 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:25:23 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:25:23 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:25:23 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:25:23 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:25:23 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:25:23 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:25:23 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:25:23 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:25:23 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:25:23 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:25:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:25:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:25:23 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:25:24 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:25:24 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:25:24 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:25:24 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-22 22:25:24 | INFO | __main__:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-22 22:25:24 | INFO | __main__:get_flashlight_detailed_status:229 | 获取手电筒详细状态
2025-07-22 22:25:24 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-22 22:25:25 | INFO | __main__:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-22 22:25:36 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:25:36 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:25:36 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:25:36 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:25:39 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:25:39 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:25:39 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:25:39 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:25:39 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:25:39 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:25:39 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:25:39 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:25:39 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:25:39 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:25:39 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:25:39 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:25:39 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:25:40 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:25:40 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:25:40 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:25:40 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:25:40 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:25:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:25:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:25:41 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:25:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:25:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:25:44 | INFO | pages.apps.ella.ella_status_checker:check_app_started:524 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:25:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:25:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:25:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:481 | 检查UIAutomator2服务健康状态
2025-07-22 22:25:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:497 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:25:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:25:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:25:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:25:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:25:46 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:25:46 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:25:46 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: None
2025-07-22 22:25:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:25:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:25:46 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:25:47 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:25:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:25:47 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-22 22:25:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:25:47 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:25:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:25:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:25:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:25:48 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:25:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:25:48 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:25:48 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:25:48 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:25:49 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:25:52 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:25:52 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:25:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:25:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:25:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:25:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:25:52 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:25:52 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:25:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:25:53 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:25:55 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-22 22:25:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-22 22:25:58 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-22 22:26:00 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: None -> None
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:26:00 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:537 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:544 | ❌ 部分期望内容未找到 (0/1)
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:545 | 缺失内容: ['WI-FI']
2025-07-22 22:26:00 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:546 | 搜索文本: 'open flashlight Flashlight is turned on now. Flashlight'
2025-07-22 22:26:00 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:26:00 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:26:01 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:26:30 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:26:30 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:26:30 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:26:30 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:26:34 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:26:34 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:26:34 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:26:34 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:26:34 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:26:34 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:26:34 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:26:34 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:26:34 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:26:34 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:26:34 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:26:34 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:26:34 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:26:34 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:26:34 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:26:35 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:26:35 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:26:35 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:26:44 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:26:44 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:26:44 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:26:44 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:26:47 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:26:47 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:26:47 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:26:47 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:26:47 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:26:47 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:26:47 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:26:47 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:26:47 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:26:47 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:26:47 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:26:47 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:26:47 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:26:48 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:26:48 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:26:48 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:26:48 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:26:49 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:26:49 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:26:49 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:26:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:26:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:26:52 | INFO | pages.apps.ella.ella_status_checker:check_app_started:524 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:26:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:26:52 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:26:52 | INFO | pages.apps.ella.ella_status_checker:check_service_health:481 | 检查UIAutomator2服务健康状态
2025-07-22 22:26:53 | INFO | pages.apps.ella.ella_status_checker:check_service_health:497 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:26:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:26:53 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:26:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:26:53 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:26:54 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:26:54 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:26:54 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: None
2025-07-22 22:26:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:26:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:26:55 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:26:55 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-22 22:26:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:26:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:26:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:26:55 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:26:56 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-22 22:26:56 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:26:56 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:26:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:26:56 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:26:56 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:26:56 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:26:56 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:26:56 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:26:57 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:27:00 | WARNING | testcases.test_ella.base_ella_test:_get_status_by_type:217 | 方法不存在: check_flashlight_status
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:27:00 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:27:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:27:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:27:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:27:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:27:00 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:27:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:27:00 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:27:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:27:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:27:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:27:01 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:27:02 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-22 22:27:04 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-22 22:27:05 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-22 22:27:07 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: None -> None
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-22 22:27:07 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:27:08 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:27:08 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:27:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-22 22:37:40 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-22 22:37:40 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-22 22:37:40 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-22 22:37:40 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-22 22:37:43 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-22 22:37:43 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-22 22:37:43 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-22 22:37:43 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:37:43 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-22 22:37:43 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-22 22:37:43 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-22 22:37:43 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-22 22:37:43 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-22 22:37:43 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-22 22:37:43 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-22 22:37:43 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-22 22:37:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-22 22:37:44 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:37:44 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-22 22:37:45 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:37:45 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-22 22:37:45 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:37:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:37:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:37:45 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:37:45 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:37:49 | INFO | pages.apps.ella.ella_status_checker:check_app_started:524 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:37:49 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:37:49 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:37:49 | INFO | pages.apps.ella.ella_status_checker:check_service_health:481 | 检查UIAutomator2服务健康状态
2025-07-22 22:37:49 | INFO | pages.apps.ella.ella_status_checker:check_service_health:497 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:37:50 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:37:50 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:37:50 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:37:50 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:37:51 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-22 22:37:51 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-22 22:37:51 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:37:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:37:52 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:37:52 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:37:52 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:37:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:37:53 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-22 22:37:53 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:37:53 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:37:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:37:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:37:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:37:54 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:37:54 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:37:54 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:37:54 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-22 22:37:54 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:37:54 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-22 22:37:57 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:37:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:37:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:37:58 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-22 22:37:58 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:38:00 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-22 22:38:02 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-22 22:38:03 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-22 22:38:05 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> False
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-22 22:38:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:38:06 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:38:06 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-22 22:38:06 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
