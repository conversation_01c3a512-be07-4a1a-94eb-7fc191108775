2025-07-14 19:24:45 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 19:24:45 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 19:24:45 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 19:24:45 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 19:24:45 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 19:24:45 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 19:24:45 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 19:24:55 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 19:24:55 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 19:24:56 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:24:56 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 19:24:56 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 19:24:56 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:24:56 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:24:56 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:24:56 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:24:56 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:24:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:24:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:24:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:24:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:24:59 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:24:59 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:24:59 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:25:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:25:01 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:25:02 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 关闭
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:25:02 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:25:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:03 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:03 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:25:03 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:25:03 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:25:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:25:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:25:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:05 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:05 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:25:05 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:25:05 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:25:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:06 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:25:06 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:25:06 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:25:06 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:25:06 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:07 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:08 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:25:08 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:25:08 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:25:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:09 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:25:09 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:25:09 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:25:09 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:25:09 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:25:09 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:25:10 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 8
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:18 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:25:18 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:25:18 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:25:21 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:25:21 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:25:21 | WARNING | pages.apps.ella.main_page:get_response_text_smart:2419 | 获取响应文本时不在Ella进程，尝试返回
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:25:21 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:25:23 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:25:25 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 7
2025-07-14 19:25:25 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:25:25 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: '已执行!'
2025-07-14 19:25:25 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:25:25 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2695 | ⚠️ 响应包含的关键词不足: []
2025-07-14 19:25:25 | WARNING | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:25:27 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:25:27 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:28 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:25:28 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 关闭
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:25:28 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:25:28 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:25:28 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:25:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:25:28 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:25:28 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:25:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:25:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:25:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:25:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:25:31 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:25:31 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:25:31 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:25:32 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:25:33 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 关闭
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:25:33 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:25:34 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:25:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:34 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:34 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: close bluetooth
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:25:35 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:25:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:36 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:36 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:25:36 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: close bluetooth
2025-07-14 19:25:36 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:37 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:25:37 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:25:37 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:25:37 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:25:38 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:25:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:38 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:25:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:39 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 19:25:39 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: close bluetooth
2025-07-14 19:25:40 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: close bluetooth
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:25:40 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: close bluetooth
2025-07-14 19:25:40 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:25:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:25:41 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:25:41 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 20秒
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:46 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2324 | 检测到播放相关按钮
2025-07-14 19:25:46 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:25:46 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:25:49 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:292 | ✅ 收到AI响应
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:25:49 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:25:49 | WARNING | pages.apps.ella.main_page:get_response_text_smart:2419 | 获取响应文本时不在Ella进程，尝试返回
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:25:49 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:25:51 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:25:54 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:25:54 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:25:54 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:308 | AI响应内容: '已执行!'
2025-07-14 19:25:54 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: close bluetooth
2025-07-14 19:25:54 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2695 | ⚠️ 响应包含的关键词不足: []
2025-07-14 19:25:54 | WARNING | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:318 | ⚠️ 响应未包含命令相关内容，但继续测试: close bluetooth
2025-07-14 19:25:56 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:25:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:25:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:25:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:25:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:25:57 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:25:57 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:328 | 蓝牙最终状态: 关闭
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:333 | ✅ 蓝牙已成功关闭
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:341 | 🎉 close bluetooth命令测试完成
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:25:57 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:25:57 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:25:57 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:25:57 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:25:57 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:25:57 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:26:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:26:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:26:00 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:26:00 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:26:00 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:26:00 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:26:00 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:26:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:26:01 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:26:01 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:26:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:02 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:26:02 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: bluetooth status
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:26:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:26:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:26:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:26:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:04 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:26:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:26:05 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: bluetooth status
2025-07-14 19:26:05 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:06 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:26:06 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:26:07 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:26:07 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:26:07 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:26:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:07 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:26:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:09 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: bluetooth status
2025-07-14 19:26:09 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: bluetooth status
2025-07-14 19:26:09 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: bluetooth status
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:10 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: bluetooth status
2025-07-14 19:26:10 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:26:11 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:14 | WARNING | pages.apps.ella.main_page:wait_for_response:2032 | 快速等待AI响应超时
2025-07-14 19:26:14 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:26:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:26:15 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:26:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:16 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: is bluetooth on
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:26:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:26:17 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:26:17 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:26:17 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:26:17 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:26:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:26:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:26:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:26:27 | ERROR | core.base_element:click:237 | 点击元素失败 [输入框]: {'code': -32002, 'data': "Selector [resourceId='com.transsion.aivoiceassistant:id/et_input']", 'method': 'wait'}
2025-07-14 19:26:27 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1687 | 检查通用输入框元素...
2025-07-14 19:26:28 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1721 | 尝试通过坐标激活输入区域...
2025-07-14 19:26:28 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2160)
2025-07-14 19:26:29 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2040)
2025-07-14 19:26:31 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 1920)
2025-07-14 19:26:33 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (864, 2160)
2025-07-14 19:26:34 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1752 | 尝试通过滑动激活输入框...
2025-07-14 19:26:40 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1794 | 检查可交互元素...
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1805 | 找到 1 个图像按钮元素
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1647 | ✅ 检测到可交互元素，假设输入功能可用
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: is bluetooth on
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:input_text_command:566 | 尝试基于坐标的输入方案
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1872 | 使用基于坐标的输入方案: is bluetooth on
2025-07-14 19:26:41 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1885 | 点击输入区域坐标: (540, 2160)
2025-07-14 19:26:56 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1903 | ✅ 基于坐标输入成功: is bluetooth on
2025-07-14 19:26:56 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:26:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:26:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-14 19:26:59 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [发送按钮]
2025-07-14 19:26:59 | INFO | pages.apps.ella.main_page:send_command:669 | 未找到发送按钮，尝试其他发送方法
2025-07-14 19:26:59 | INFO | pages.apps.ella.main_page:send_command:673 | 尝试使用回车键发送
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:send_command:676 | ✅ 回车键发送成功
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:27:00 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:27:00 | WARNING | pages.apps.ella.main_page:wait_for_response:1997 | 等待响应时发现不在Ella进程，尝试返回
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:27:00 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:27:02 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:27:03 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:27:06 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:27:06 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:27:06 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'is bluetooth on' 响应: 已执行!
2025-07-14 19:27:06 | WARNING | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:387 | ⚠️ 命令 'is bluetooth on' 响应未包含蓝牙相关内容
2025-07-14 19:27:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:27:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:27:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:07 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:27:07 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: check bluetooth
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:27:08 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:27:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:27:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:09 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:27:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:27:10 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: check bluetooth
2025-07-14 19:27:10 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:27:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:11 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:27:11 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:27:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:27:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:27:11 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:27:11 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:27:12 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:27:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:12 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:27:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:14 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: check bluetooth
2025-07-14 19:27:14 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: check bluetooth
2025-07-14 19:27:14 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: check bluetooth
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: check bluetooth
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:27:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:27:15 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:27:15 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:27:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:27:17 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:27:17 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:27:17 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:27:17 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:28:37 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 7
2025-07-14 19:28:37 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 月光照明导航原理？
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'check bluetooth' 响应: 月光照明导航原理？
2025-07-14 19:28:37 | WARNING | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:387 | ⚠️ 命令 'check bluetooth' 响应未包含蓝牙相关内容
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:28:37 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:28:37 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:28:37 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:28:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:28:37 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:28:37 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:28:40 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:28:40 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:28:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:28:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:28:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:28:40 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:28:40 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:28:41 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:28:42 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:407 | 蓝牙初始状态: 关闭
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:28:42 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:28:43 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:28:43 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:28:43 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:28:43 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:28:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:28:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:28:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:28:43 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:28:43 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:execute_voice_command:768 | 🎤 执行语音命令: 'open bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:28:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:28:45 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:28:45 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:28:45 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:28:45 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:28:45 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:28:45 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:28:45 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:28:45 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:28:45 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:28:46 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:28:46 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:28:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:28:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:28:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:28:47 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:28:47 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:28:50 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:28:50 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:28:51 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:28:53 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:28:55 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:28:58 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:28:58 | WARNING | pages.apps.ella.main_page:execute_voice_command:782 | 无法启动语音输入，回退到文本输入
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:28:58 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:28:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:28:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:28:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:28:59 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:28:59 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:28:59 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:28:59 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:28:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:00 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:29:00 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:29:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:29:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:29:00 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:29:00 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:29:01 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:29:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:01 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:29:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:02 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:29:02 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:29:03 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:03 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:29:03 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:29:04 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:29:04 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:436 | ✅ 成功执行语音命令: open bluetooth
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 10秒
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:29:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:07 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2324 | 检测到播放相关按钮
2025-07-14 19:29:07 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:29:07 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:29:11 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:464 | ✅ 收到AI响应
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:29:11 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:29:11 | WARNING | pages.apps.ella.main_page:get_response_text_smart:2419 | 获取响应文本时不在Ella进程，尝试返回
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:29:11 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:29:13 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:29:16 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:29:16 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:29:16 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:487 | AI响应内容: '已执行!'
2025-07-14 19:29:16 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:29:16 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2695 | ⚠️ 响应包含的关键词不足: []
2025-07-14 19:29:16 | WARNING | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:497 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:29:18 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:29:18 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:507 | 蓝牙最终状态: 关闭
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:29:19 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:29:19 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:29:19 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:29:19 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:29:19 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:29:19 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:29:21 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:29:21 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:29:21 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:29:22 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:29:22 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:29:22 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:29:22 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:29:23 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:29:23 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:29:24 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:557 | 蓝牙初始状态: 关闭
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:29:24 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:29:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:25 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:29:25 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:29:25 | INFO | pages.apps.ella.main_page:execute_voice_command:768 | 🎤 执行语音命令: 'close bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-07-14 19:29:25 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:29:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:29:26 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:29:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:27 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:29:27 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:29:27 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:29:28 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:29:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:29:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:29:28 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:29:28 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:29:32 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:29:32 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:29:32 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:29:34 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:29:37 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:29:39 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:29:39 | WARNING | pages.apps.ella.main_page:execute_voice_command:782 | 无法启动语音输入，回退到文本输入
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: close bluetooth
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:29:39 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:29:40 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:29:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:40 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:29:40 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:29:41 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: close bluetooth
2025-07-14 19:29:41 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:41 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:29:41 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:29:42 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:29:42 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:29:42 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:29:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:42 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:29:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:43 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 19:29:43 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: close bluetooth
2025-07-14 19:29:44 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: close bluetooth
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:29:44 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: close bluetooth
2025-07-14 19:29:44 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:29:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:29:45 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:29:45 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:573 | ✅ 成功执行语音命令: close bluetooth
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 10秒
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:48 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2324 | 检测到播放相关按钮
2025-07-14 19:29:48 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:29:48 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:29:51 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:591 | ✅ 收到AI响应
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:29:51 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:29:51 | WARNING | pages.apps.ella.main_page:get_response_text_smart:2419 | 获取响应文本时不在Ella进程，尝试返回
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:29:51 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:29:53 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:29:56 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:29:56 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:29:56 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:597 | AI响应内容: '已执行!'
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:29:58 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:29:59 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:607 | 蓝牙最终状态: 关闭
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:612 | ✅ 蓝牙已成功关闭
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:620 | 🎉 语音输入close bluetooth命令测试完成
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:29:59 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:29:59 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:29:59 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:29:59 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:29:59 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:29:59 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:30:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:30:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:30:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:30:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:30:02 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:30:02 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:30:02 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:30:03 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:30:04 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:638 | 蓝牙初始状态: 关闭
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:30:04 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:30:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:05 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:30:05 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:execute_real_voice_command:965 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN, 音量: 0.8)
2025-07-14 19:30:06 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: None
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:_log_voice_file_cache_status:1055 | 🎯 语音文件缓存状态: 已存在 - data\zh\open_bluetooth.wav (10.3KB)
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:30:06 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:30:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:07 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:30:07 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:30:07 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:30:08 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:30:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:30:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:30:09 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:30:09 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:30:12 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:30:12 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:30:12 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:30:15 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:30:17 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:30:19 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:30:19 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:982 | 无法启动语音输入，回退到文本输入
2025-07-14 19:30:19 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:30:19 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:30:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:30:20 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:30:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:21 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:30:21 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:30:21 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:30:21 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:30:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:22 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:30:22 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:30:22 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:30:22 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:30:22 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:23 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:30:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:24 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:30:24 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:30:25 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:30:25 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:30:25 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:30:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:30:26 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:30:26 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:672 | ✅ 成功执行TTS语音命令: open bluetooth
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 12秒
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:30:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:27 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:27 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:27 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:31 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2324 | 检测到播放相关按钮
2025-07-14 19:30:31 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:30:31 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:30:34 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:700 | ✅ 收到AI响应
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.settings.bluetooth
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-14 19:30:34 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-14 19:30:34 | WARNING | pages.apps.ella.main_page:get_response_text_smart:2419 | 获取响应文本时不在Ella进程，尝试返回
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-14 19:30:34 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:return_to_ella_app:1466 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-14 19:30:36 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:30:39 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:30:39 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: 已执行!
2025-07-14 19:30:39 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:723 | AI响应内容: '已执行!'
2025-07-14 19:30:39 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:30:39 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2695 | ⚠️ 响应包含的关键词不足: []
2025-07-14 19:30:39 | WARNING | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:733 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:30:41 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:30:42 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:743 | 蓝牙最终状态: 关闭
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:30:42 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:30:42 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:30:42 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 19:30:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 19:36:00 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 19:36:00 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 19:36:00 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 19:36:00 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 19:36:00 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 19:36:00 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 19:36:00 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 19:36:02 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 19:36:02 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 19:36:02 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:36:02 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 19:36:02 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 19:36:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 19:36:03 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 19:36:03 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:36:03 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:03 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:36:03 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:36:05 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:05 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:36:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:36:05 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:36:05 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:36:06 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:36:07 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:07 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:36:08 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:08 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:09 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:09 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:36:09 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:10 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:11 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:11 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:11 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:36:11 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:36:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:12 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:36:12 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:36:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:36:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:36:12 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:36:12 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:36:12 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:36:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:13 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:14 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:36:14 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:36:15 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:15 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:36:15 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:36:16 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:36:16 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:36:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:36:17 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:36:20 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-14 19:36:20 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:36:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:21 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 13
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned on now.
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned on now.
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-14 19:36:24 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:36:24 | INFO | pages.apps.ella.main_page:verify_command_in_response:2622 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-14 19:36:24 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:36:26 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-14 19:36:26 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:36:26 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:36:26 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:36:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:36:27 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:36:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:27 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:36:27 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:36:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:36:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:36:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:36:29 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:36:30 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:36:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:31 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:36:31 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:36:31 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:238 | 蓝牙初始状态: 开启
2025-07-14 19:36:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:32 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: close bluetooth
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:33 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:34 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:34 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:34 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: close bluetooth
2025-07-14 19:36:34 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:35 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:36:35 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:36:35 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:36:35 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:36:36 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:36:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:36 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:37 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 19:36:37 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: close bluetooth
2025-07-14 19:36:38 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: close bluetooth
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:38 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: close bluetooth
2025-07-14 19:36:38 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:36:39 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:36:39 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:257 | ✅ 成功执行命令: close bluetooth
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 20秒
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:42 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:36:42 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:36:42 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:36:45 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:292 | ✅ 收到AI响应
2025-07-14 19:36:45 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:36:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:46 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 13
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*off -> Bluetooth is turned off now.
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned off now.
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned off now.
2025-07-14 19:36:48 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:308 | AI响应内容: 'Bluetooth is turned off now.'
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: close bluetooth
2025-07-14 19:36:48 | INFO | pages.apps.ella.main_page:verify_command_in_response:2622 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-14 19:36:48 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:316 | ✅ 响应包含命令相关内容: close bluetooth
2025-07-14 19:36:50 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:36:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:51 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:51 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:36:51 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:328 | 蓝牙最终状态: 关闭
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:333 | ✅ 蓝牙已成功关闭
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:test_close_bluetooth_command:341 | 🎉 close bluetooth命令测试完成
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:36:51 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:36:51 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:36:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:36:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:36:51 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:36:51 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:36:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:36:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:36:54 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:36:54 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:36:54 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:36:54 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:36:54 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:36:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:36:55 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:56 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:57 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: bluetooth status
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:36:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:36:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:36:58 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:36:58 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:36:58 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:36:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:36:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:36:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:36:59 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:36:59 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:36:59 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: bluetooth status
2025-07-14 19:36:59 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:36:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:00 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:37:00 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:37:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:37:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:37:01 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:37:01 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:37:01 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:37:01 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:01 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:01 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:02 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:03 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: bluetooth status
2025-07-14 19:37:03 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: bluetooth status
2025-07-14 19:37:03 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: bluetooth status
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:04 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: bluetooth status
2025-07-14 19:37:04 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:05 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:08 | WARNING | pages.apps.ella.main_page:wait_for_response:2032 | 快速等待AI响应超时
2025-07-14 19:37:08 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:37:08 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:37:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:09 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:09 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: is bluetooth on
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:10 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:37:11 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:37:11 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:37:11 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:37:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:11 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:11 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:11 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:37:12 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: is bluetooth on
2025-07-14 19:37:12 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:37:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:13 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:37:13 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:37:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:37:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:37:14 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:37:14 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:37:14 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:37:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:15 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:16 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: is bluetooth on
2025-07-14 19:37:16 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: is bluetooth on
2025-07-14 19:37:17 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: is bluetooth on
2025-07-14 19:37:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: is bluetooth on
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:18 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 13
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:37:19 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 11
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> is bluetooth on
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: is bluetooth on
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: is bluetooth on
2025-07-14 19:37:44 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'is bluetooth on' 响应: is bluetooth on
2025-07-14 19:37:44 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:385 | ✅ 命令 'is bluetooth on' 响应包含蓝牙相关内容
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:37:44 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:37:44 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:45 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:45 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:37:45 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: check bluetooth
2025-07-14 19:37:45 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:37:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:47 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:37:47 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:37:47 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:37:47 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:37:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:47 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:47 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:37:48 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: check bluetooth
2025-07-14 19:37:48 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:37:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:49 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:37:49 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:37:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:37:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:37:50 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:37:50 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:37:51 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:37:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:51 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:37:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:53 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: check bluetooth
2025-07-14 19:37:53 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: check bluetooth
2025-07-14 19:37:53 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: check bluetooth
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:37:54 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: check bluetooth
2025-07-14 19:37:54 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:37:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:37:55 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 3秒
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 10
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:37:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:37:56 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2069 | 检测到元素数量增加: 11 > 10
2025-07-14 19:38:08 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:38:08 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:38:08 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:38:08 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:38:11 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 10
2025-07-14 19:38:11 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> is bluetooth on
2025-07-14 19:38:11 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: is bluetooth on
2025-07-14 19:38:11 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: is bluetooth on
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:377 | 命令 'check bluetooth' 响应: is bluetooth on
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:test_bluetooth_status_query:385 | ✅ 命令 'check bluetooth' 响应包含蓝牙相关内容
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:38:11 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:38:11 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:38:11 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:38:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:38:11 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:38:11 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:38:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:38:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:38:13 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:38:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:38:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:38:14 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:38:14 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:38:15 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:38:15 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:38:16 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:407 | 蓝牙初始状态: 关闭
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:38:16 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:38:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:17 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:execute_voice_command:768 | 🎤 执行语音命令: 'open bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:38:18 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:38:18 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:38:18 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:38:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:19 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:19 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:38:19 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:38:20 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:38:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:38:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:38:20 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:38:20 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:38:23 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:38:23 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:38:24 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:38:26 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:38:28 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:38:30 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:38:30 | WARNING | pages.apps.ella.main_page:execute_voice_command:782 | 无法启动语音输入，回退到文本输入
2025-07-14 19:38:30 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:38:30 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:38:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:38:31 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:38:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:32 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:38:32 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:38:32 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:38:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:33 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:38:33 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:38:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:38:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:38:34 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:38:34 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:38:34 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:38:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:35 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:36 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:38:36 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:38:36 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:37 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:38:37 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:38:38 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:38:38 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:436 | ✅ 成功执行语音命令: open bluetooth
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 10秒
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:41 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:38:41 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:38:41 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:38:44 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:464 | ✅ 收到AI响应
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:44 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 13
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned on now.
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-14 19:38:47 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:487 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:38:47 | INFO | pages.apps.ella.main_page:verify_command_in_response:2622 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-14 19:38:47 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:495 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:38:50 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:507 | 蓝牙最终状态: 开启
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:518 | ✅ 蓝牙已成功开启
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_open_bluetooth_command:539 | 🎉 语音输入open bluetooth命令测试完成
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:38:50 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:38:50 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:38:50 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:38:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:38:50 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:38:50 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:38:52 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:38:52 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:38:52 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:38:53 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:38:53 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:38:53 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:38:53 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:38:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:38:55 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:557 | 蓝牙初始状态: 开启
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:38:55 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:38:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:56 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:56 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:38:56 | INFO | pages.apps.ella.main_page:execute_voice_command:768 | 🎤 执行语音命令: 'close bluetooth' (语言: zh-CN, 持续时间: 3.0秒)
2025-07-14 19:38:56 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:38:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:38:57 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:38:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:38:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:38:58 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:38:58 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:38:58 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:38:59 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:38:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:38:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:38:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:39:00 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:39:00 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:39:03 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:39:03 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:39:04 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:39:06 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:39:08 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:39:11 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:39:11 | WARNING | pages.apps.ella.main_page:execute_voice_command:782 | 无法启动语音输入，回退到文本输入
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: close bluetooth
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:39:11 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:39:12 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:39:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:12 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:12 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:39:13 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: close bluetooth
2025-07-14 19:39:13 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:13 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:39:13 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:39:14 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:39:14 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:39:14 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:39:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:14 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:15 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 19:39:15 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: close bluetooth
2025-07-14 19:39:16 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: close bluetooth
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:16 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: close bluetooth
2025-07-14 19:39:16 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:39:17 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:39:17 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:573 | ✅ 成功执行语音命令: close bluetooth
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 10秒
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:21 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:39:21 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:39:21 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:39:24 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:591 | ✅ 收到AI响应
2025-07-14 19:39:24 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:39:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:25 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:39:28 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 14
2025-07-14 19:39:28 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*off -> Bluetooth is turned off now.
2025-07-14 19:39:28 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned off now.
2025-07-14 19:39:28 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned off now.
2025-07-14 19:39:28 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:597 | AI响应内容: 'Bluetooth is turned off now.'
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:39:30 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:39:30 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:607 | 蓝牙最终状态: 关闭
2025-07-14 19:39:30 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:612 | ✅ 蓝牙已成功关闭
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:test_voice_close_bluetooth_command:620 | 🎉 语音输入close bluetooth命令测试完成
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:39:31 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:39:31 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:39:31 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 19:39:31 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-14 19:39:31 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:39:31 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-14 19:39:33 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-14 19:39:33 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-14 19:39:33 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 19:39:34 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 19:39:34 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-14 19:39:34 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-14 19:39:34 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-14 19:39:35 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-14 19:39:35 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:39:35 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 19:39:35 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:638 | 蓝牙初始状态: 关闭
2025-07-14 19:39:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:39:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:39:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:36 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:36 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:execute_real_voice_command:965 | 🎤 执行真实语音命令: 'open bluetooth' (语言: zh-CN, 音量: 0.8)
2025-07-14 19:39:37 | INFO | utils.tts_utils:__init__:88 | 选择的TTS服务: None
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:_log_voice_file_cache_status:1055 | 🎯 语音文件缓存状态: 已存在 - data\zh\open_bluetooth.wav (10.3KB)
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:39:37 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:39:38 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:39:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:38 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:38 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:39:39 | INFO | pages.apps.ella.main_page:start_voice_input:1120 | 启动语音输入...
2025-07-14 19:39:39 | INFO | pages.apps.ella.main_page:start_voice_input:1138 | 找到语音按钮: 语音按钮(备选)
2025-07-14 19:39:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [语音按钮(备选)], 超时时间: 5秒
2025-07-14 19:39:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [语音按钮(备选)]
2025-07-14 19:39:40 | INFO | core.base_element:click:231 | 点击元素成功 [语音按钮(备选)]
2025-07-14 19:39:40 | INFO | pages.apps.ella.main_page:start_voice_input:1140 | ✅ 语音按钮点击成功
2025-07-14 19:39:43 | INFO | pages.apps.ella.main_page:start_voice_input:1153 | 尝试长按输入框启动语音输入...
2025-07-14 19:39:43 | INFO | pages.apps.ella.main_page:start_voice_input:1162 | 尝试通过坐标点击语音按钮区域...
2025-07-14 19:39:43 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (972, 2160)
2025-07-14 19:39:45 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (918, 2160)
2025-07-14 19:39:47 | INFO | pages.apps.ella.main_page:start_voice_input:1173 | 尝试点击坐标 (540, 2280)
2025-07-14 19:39:50 | WARNING | pages.apps.ella.main_page:start_voice_input:1180 | ❌ 无法启动语音输入
2025-07-14 19:39:50 | WARNING | pages.apps.ella.main_page:execute_real_voice_command:982 | 无法启动语音输入，回退到文本输入
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-14 19:39:50 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-14 19:39:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:51 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:51 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-14 19:39:51 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-14 19:39:51 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:52 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 19:39:52 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-14 19:39:53 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-14 19:39:53 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-14 19:39:53 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-14 19:39:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:54 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-14 19:39:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:55 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 19:39:55 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-14 19:39:56 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 19:39:56 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-14 19:39:56 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 19:39:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 19:39:57 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-14 19:39:57 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:672 | ✅ 成功执行TTS语音命令: open bluetooth
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 12秒
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-14 19:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:39:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:39:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:39:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:40:03 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-14 19:40:03 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-14 19:40:03 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-14 19:40:06 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:700 | ✅ 收到AI响应
2025-07-14 19:40:06 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-14 19:40:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:40:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:40:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:40:07 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:40:07 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 13
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned on now.
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-14 19:40:10 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:723 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-14 19:40:10 | INFO | pages.apps.ella.main_page:verify_command_in_response:2622 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-14 19:40:10 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:731 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-14 19:40:12 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:743 | 蓝牙最终状态: 开启
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:754 | ✅ 蓝牙已成功开启
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:test_real_tts_voice_open_bluetooth_command:777 | 🎉 TTS真实语音输入open bluetooth命令测试完成
2025-07-14 19:40:12 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-14 19:40:12 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 19:40:12 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 19:40:13 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 19:40:13 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:268 | 🚀 开始验证所有优化后的测试用例...
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:00:16 | INFO | __main__:validate_all_tests:287 | 
📊 验证完成: 7/8 个文件通过验证
2025-07-14 21:00:16 | WARNING | __main__:validate_all_tests:293 | ⚠️ 1 个文件验证失败
2025-07-14 21:00:16 | WARNING | __main__:main:371 | ⚠️ 部分测试文件验证失败，请查看报告
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:268 | 🚀 开始验证所有优化后的测试用例...
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:274 | 
============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:276 | ============================================================
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:287 | 
📊 验证完成: 8/8 个文件通过验证
2025-07-14 21:01:22 | INFO | __main__:validate_all_tests:290 | 🎉 所有测试文件验证成功！
2025-07-14 21:01:22 | INFO | __main__:main:369 | 🎉 所有测试文件验证完成！
2025-07-14 21:05:29 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:05:29 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:05:29 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:05:29 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:05:29 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:05:29 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:05:29 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:05:30 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:05:30 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:05:31 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:05:31 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:05:31 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:05:31 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:05:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:05:32 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:05:32 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:24 | 🚀 开始启动Ella应用（优化版本）...
2025-07-14 21:05:32 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:05:32 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:05:35 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:05:35 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:05:35 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:28 | ✅ Ella应用启动成功
2025-07-14 21:05:35 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:31 | ⏳ 等待Ella页面加载...
2025-07-14 21:05:35 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:05:35 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:05:36 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:05:36 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:05:36 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:05:36 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:05:37 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:05:38 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:33 | ✅ Ella页面加载完成
2025-07-14 21:05:38 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:05:39 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:85 | 蓝牙初始状态: 开启
2025-07-14 21:05:39 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:05:39 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:05:39 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:05:40 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:05:40 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-14 21:05:40 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:05:40 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:05:40 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:05:40 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:05:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:05:41 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 21:05:41 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:05:41 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:05:41 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:05:41 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:05:41 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:05:41 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:05:41 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:05:41 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:05:41 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:114 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:05:41 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:05:42 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:05:45 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:142 | ✅ 收到AI响应
2025-07-14 21:05:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:05:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:05:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:05:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:05:45 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:05:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:200 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-14 21:05:47 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Bluetooth is turned on now.
2025-07-14 21:05:47 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:170 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-14 21:05:47 | INFO | pages.apps.ella.ella_response_handler:verify_command_in_response:282 | 响应包含命令关键词: bluetooth
2025-07-14 21:05:47 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:178 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:05:49 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:05:49 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:190 | 蓝牙最终状态: 开启
2025-07-14 21:05:49 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:201 | ✅ 蓝牙已成功开启
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:test_open_bluetooth_command_optimized:223 | 🎉 open bluetooth命令测试完成（优化版本）
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:63 | 🧹 清理Ella应用...
2025-07-14 21:05:50 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:05:50 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:05:50 | INFO | testcases.test_ella.test_bluetooth_simple_command:ella_app:65 | ✅ Ella应用已停止
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:05:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:17:00 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:17:00 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:17:00 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:17:00 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:17:00 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:17:00 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:17:00 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:17:01 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:17:01 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:17:02 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:17:02 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:17:02 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:17:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:17:02 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:17:02 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:17:02 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:17:06 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:17:06 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:17:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:17:06 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:17:06 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:17:07 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:17:07 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:17:07 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:17:07 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:17:08 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:17:08 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:17:09 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:17:09 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:17:09 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:17:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:17:10 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 21:17:10 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:17:10 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:17:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:17:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:17:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:17:10 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:17:10 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:17:10 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:17:10 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:17:10 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:17:11 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:17:13 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:17:13 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:17:13 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:17:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:17:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:17:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:17:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:17:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:17:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:17:17 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> True
2025-07-14 21:17:17 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-14 21:17:17 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:17:17 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:17:17 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:18:59 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:18:59 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:18:59 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:18:59 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:18:59 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:18:59 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:18:59 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:19:01 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:19:01 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:19:01 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:19:01 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:19:01 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:19:01 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:19:02 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:19:02 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:19:02 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:19:02 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:19:05 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:19:05 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:19:05 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:19:05 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:19:06 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:19:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:19:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:19:06 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:19:07 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:19:08 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:19:08 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:19:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:19:09 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: close bluetooth
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:19:09 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: close bluetooth
2025-07-14 21:19:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:19:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:19:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:19:09 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:19:10 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 21:19:10 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:19:10 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:19:10 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:19:11 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:19:11 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: close bluetooth
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:19:11 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:19:14 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:19:14 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:19:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:19:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:19:18 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:19:18 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:19:18 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:29:38 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:29:38 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:29:38 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:29:38 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:29:38 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:29:38 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:29:38 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:29:40 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:29:40 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:29:40 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:29:40 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:29:40 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:29:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:29:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:29:41 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:29:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:29:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:29:44 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:29:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:29:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 10秒
2025-07-14 21:29:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:29:45 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:29:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:29:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:29:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:29:46 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:29:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:29:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:29:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:29:48 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:29:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:29:48 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 21:29:48 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:29:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:29:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:29:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:29:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:29:49 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:29:49 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:29:49 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:29:49 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 5秒
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:29:50 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:29:52 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:29:52 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:29:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:29:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:29:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:29:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:29:53 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:29:53 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 21:29:53 | ERROR | testcases.test_ella.test_bluetooth_simple_command_concise:_test_command:165 | ❌ open bluetooth 测试失败: 蓝牙未开启
assert False
2025-07-14 21:29:53 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:29:53 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:29:54 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:30:42 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:30:42 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:30:42 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:30:42 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:30:42 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:30:42 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:30:42 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:30:43 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:30:43 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:30:44 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:30:44 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:30:44 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:30:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:30:44 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:30:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:30:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:30:48 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:30:48 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:30:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:30:48 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:30:48 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:30:49 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:30:49 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:30:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:30:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:30:50 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:30:50 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:30:50 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 21:30:50 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:30:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:30:51 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:30:51 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-14 21:30:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:30:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:30:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:30:52 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:30:52 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-14 21:30:52 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:30:52 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:30:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:30:53 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:30:53 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:30:53 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:30:56 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:30:56 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:30:56 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:30:59 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:30:59 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: False -> True
2025-07-14 21:31:00 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-14 21:31:00 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:31:00 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:31:00 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:31:13 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:31:13 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:31:13 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:31:13 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:31:13 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:31:13 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:31:13 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:31:15 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:31:15 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:31:15 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:31:15 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:31:15 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:31:15 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:31:16 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:31:16 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:31:16 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:31:16 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:31:19 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:31:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:31:19 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:31:19 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:31:20 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:31:20 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:31:20 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:31:20 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:31:21 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:31:22 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-14 21:31:22 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:31:22 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: close bluetooth
2025-07-14 21:31:22 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:31:23 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:31:23 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: close bluetooth
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:31:23 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:31:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:31:23 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: close bluetooth
2025-07-14 21:31:23 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:31:23 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:31:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:31:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:31:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:31:24 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:31:24 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:31:24 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:31:24 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: close bluetooth
2025-07-14 21:31:24 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:31:25 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:31:39 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:31:39 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:31:39 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:31:42 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 21:31:42 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> False
2025-07-14 21:31:43 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 close bluetooth 测试完成
2025-07-14 21:31:43 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:31:43 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:31:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-14 21:32:25 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-14 21:32:25 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-14 21:32:25 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-14 21:32:25 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-14 21:32:25 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-14 21:32:25 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-14 21:32:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-14 21:32:26 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:32:26 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-14 21:32:27 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:32:27 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-14 21:32:27 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-14 21:32:27 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-14 21:32:27 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-14 21:32:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-14 21:32:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:32:31 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-14 21:32:31 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-14 21:32:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-14 21:32:31 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-14 21:32:31 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-14 21:32:32 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-14 21:32:32 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-14 21:32:32 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-14 21:32:32 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-14 21:32:33 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-14 21:32:33 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:32:33 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 21:32:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-14 21:32:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:32:34 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: what is bluetooth status
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-14 21:32:34 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: what is bluetooth status
2025-07-14 21:32:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:32:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:32:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:32:35 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-14 21:32:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-14 21:32:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:32:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-14 21:32:35 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: what is bluetooth status
2025-07-14 21:32:35 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-14 21:32:35 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-14 21:32:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-14 21:32:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-14 21:32:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-14 21:32:36 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-14 21:32:36 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-14 21:32:36 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-14 21:32:36 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: what is bluetooth status
2025-07-14 21:32:36 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-14 21:32:36 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-14 21:32:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:32:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:32:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:32:37 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:32:37 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-14 21:32:39 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:218 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:32:39 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-14 21:32:39 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-14 21:32:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-14 21:32:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-14 21:32:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-14 21:32:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-14 21:32:42 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-14 21:32:43 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-14 21:32:43 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 what is bluetooth status 测试完成
2025-07-14 21:32:43 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-14 21:32:43 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-14 21:32:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
