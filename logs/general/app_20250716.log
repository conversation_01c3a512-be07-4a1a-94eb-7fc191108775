2025-07-16 16:39:06 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-16 16:39:06 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 16:39:06 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-16 16:39:06 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-16 16:39:06 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-16 16:39:06 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 16:39:06 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:39:17 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:39:17 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-16 16:39:17 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-16 16:39:17 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:39:17 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:39:17 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-16 16:39:19 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:39:36 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:39:47 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:39:47 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-16 16:39:47 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-16 16:39:47 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-16 16:39:47 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-16 16:39:57 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 13764254B4001229 not online
2025-07-16 16:40:11 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-16 16:40:11 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 16:40:11 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-16 16:40:11 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-16 16:40:11 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-16 16:40:11 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 16:40:11 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:40:21 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:40:21 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-16 16:40:21 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-16 16:40:21 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:40:21 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:40:22 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-16 16:40:24 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:40:36 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-16 16:40:36 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 16:40:36 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-16 16:40:36 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-16 16:40:36 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-16 16:40:36 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 16:40:36 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:40:46 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:40:46 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-16 16:40:46 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-16 16:40:46 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:40:46 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:40:46 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-16 16:40:48 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:41:05 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:41:15 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:41:15 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-16 16:41:15 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-16 16:41:15 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-16 16:41:15 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-16 16:41:26 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 13764254B4001229 not online
2025-07-16 16:42:16 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-16 16:42:16 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 16:42:16 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-16 16:42:16 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-16 16:42:16 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-16 16:42:16 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 16:42:16 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:42:27 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:42:27 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-16 16:42:27 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-16 16:42:27 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:42:27 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:42:27 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-16 16:42:29 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:42:46 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:42:56 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:42:56 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-16 16:42:56 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-16 16:42:56 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-16 16:42:56 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-16 16:43:06 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 13764254B4001229 not online
2025-07-16 16:56:43 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-16 16:56:43 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 16:56:43 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-16 16:56:43 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-16 16:56:43 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-16 16:56:43 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 16:56:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:57:01 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:57:01 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-16 16:57:01 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-16 16:57:01 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:57:01 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:57:02 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-16 16:57:04 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-16 16:57:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-16 16:57:31 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 13764254B4001229 not online
2025-07-16 16:57:31 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-16 16:57:31 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-16 16:57:31 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-16 16:57:31 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-16 16:57:42 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 13764254B4001229 not online
2025-07-16 17:00:24 | INFO | __main__:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-16 17:00:24 | INFO | __main__:get_connected_devices:49 | 发现 1 个连接的设备: ['14016254CJ000032']
2025-07-16 17:00:24 | INFO | __main__:discover_device:243 | 正在发现设备: 14016254CJ000032
2025-07-16 17:00:28 | INFO | __main__:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.016(OP001PF001AZ)_SU)
2025-07-16 17:00:28 | INFO | __main__:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-16 17:00:28 | INFO | __main__:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-16 17:01:28 | INFO | __main__:auto_discover_and_update:207 | 开始自动发现和更新设备配置...
2025-07-16 17:01:28 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-16 17:01:28 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['14016254CJ000032']
2025-07-16 17:01:28 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 14016254CJ000032
2025-07-16 17:01:31 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.016(OP001PF001AZ)_SU)
2025-07-16 17:01:31 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-16 17:01:31 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:325 | ================================================================================
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:326 | 📱 发现的设备摘要
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:327 | ================================================================================
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:330 | 
设备 1:
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:331 |   设备ID: 14016254CJ000032
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:332 |   设备名称: TECNO CM8
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:333 |   HiOS版本: 15.1.2
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:334 |   Android版本: 15
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:335 |   品牌: TECNO
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:336 |   型号: TECNO CM8
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:337 |   屏幕分辨率: 1080x2400
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:338 |   CPU架构: arm64-v8a
2025-07-16 17:01:31 | INFO | utils.device_discovery:print_device_summary:339 |   发现时间: 2025-07-16 17:01:31
2025-07-16 17:01:31 | INFO | __main__:update_config_with_discovered_devices:180 | 添加设备配置: tecno_tecno_cm8_0032 -> TECNO CM8
2025-07-16 17:01:31 | INFO | __main__:update_config_with_discovered_devices:185 | 设置当前设备: tecno_tecno_cm8_0032
2025-07-16 17:01:31 | INFO | __main__:update_config_with_discovered_devices:190 | 设备配置更新完成，共更新 1 个设备
2025-07-16 17:01:31 | INFO | __main__:auto_discover_and_update:223 | ✅ 设备配置自动更新完成
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:235 | 
============================================================
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:236 | 📋 更新后的设备配置
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:237 | ============================================================
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:240 | 当前设备: tecno_tecno_cm8_0032
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:243 | 配置的设备数量: 5
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:247 | 
[ ] default:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0.3
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:247 | 
[ ] emulator:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:248 |     设备ID: emulator-5554
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:249 |     设备名称: Android Emulator
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:250 |     HiOS版本: 11.0
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:247 | 
[ ] real_device:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:249 |     设备名称: Real Android Device
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:247 | 
[✓] tecno_tecno_cm8_0032:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:248 |     设备ID: 14016254CJ000032
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.1.2
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:251 |     Android版本: 15
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 1080x2400
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:255 |     最后更新: 2025-07-16 17:01:31
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:247 | 
[ ] tecno_tecno_cm8_1229:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0.3
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:251 |     Android版本: 15
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 1260x2800
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:255 |     最后更新: 2025-06-12 15:47:31
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:259 | 
自动发现状态:
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:260 |   启用: True
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:261 |   最后更新: 2025-07-16 17:01:31
2025-07-16 17:01:31 | INFO | __main__:print_updated_config:262 |   发现设备数: 1
2025-07-16 17:06:46 | INFO | __main__:auto_discover_and_update:207 | 开始自动发现和更新设备配置...
2025-07-16 17:06:46 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-16 17:06:46 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['14016254CJ000032']
2025-07-16 17:06:46 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 14016254CJ000032
2025-07-16 17:06:49 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.016(OP001PF001AZ)_SU)
2025-07-16 17:06:49 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-16 17:06:49 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:325 | ================================================================================
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:326 | 📱 发现的设备摘要
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:327 | ================================================================================
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:330 | 
设备 1:
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:331 |   设备ID: 14016254CJ000032
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:332 |   设备名称: TECNO CM8
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:333 |   HiOS版本: 15.1.2
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:334 |   Android版本: 15
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:335 |   品牌: TECNO
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:336 |   型号: TECNO CM8
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:337 |   屏幕分辨率: 1080x2400
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:338 |   CPU架构: arm64-v8a
2025-07-16 17:06:49 | INFO | utils.device_discovery:print_device_summary:339 |   发现时间: 2025-07-16 17:06:49
2025-07-16 17:06:49 | INFO | __main__:update_config_with_discovered_devices:180 | 添加设备配置: tecno_tecno_cm8_0032 -> TECNO CM8
2025-07-16 17:06:49 | INFO | __main__:update_config_with_discovered_devices:185 | 设置当前设备: tecno_tecno_cm8_0032
2025-07-16 17:06:49 | INFO | __main__:update_config_with_discovered_devices:190 | 设备配置更新完成，共更新 1 个设备
2025-07-16 17:06:49 | INFO | __main__:auto_discover_and_update:223 | ✅ 设备配置自动更新完成
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:235 | 
============================================================
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:236 | 📋 更新后的设备配置
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:237 | ============================================================
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:240 | 当前设备: tecno_tecno_cm8_0032
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:243 | 配置的设备数量: 5
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:247 | 
[ ] current_device:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0.3
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:251 |     Android版本: 15
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 1260x2800
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:255 |     最后更新: 2025-06-12 15:47:31
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:247 | 
[ ] default:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0.3
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:247 | 
[ ] emulator:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:248 |     设备ID: emulator-5554
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:249 |     设备名称: Android Emulator
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:250 |     HiOS版本: 11.0
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:247 | 
[ ] real_device:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:248 |     设备ID: 13764254B4001229
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:249 |     设备名称: Real Android Device
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.0
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:251 |     Android版本: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:247 | 
[✓] tecno_tecno_cm8_0032:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:248 |     设备ID: 14016254CJ000032
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:249 |     设备名称: TECNO CM8
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:250 |     HiOS版本: 15.1.2
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:251 |     Android版本: 15
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:252 |     屏幕分辨率: 1080x2400
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:255 |     最后更新: 2025-07-16 17:06:49
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:259 | 
自动发现状态:
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:260 |   启用: True
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:261 |   最后更新: 2025-07-16 17:06:49
2025-07-16 17:06:49 | INFO | __main__:print_updated_config:262 |   发现设备数: 1
2025-07-16 17:07:58 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:07:58 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:07:58 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:07:58 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:07:58 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:07:58 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:07:58 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:08:00 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:08:00 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:08:00 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:08:00 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:08:00 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:08:00 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:08:00 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:08:00 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:08:00 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:08:00 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:08:00 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:08:01 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:04 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:08:04 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:08:04 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:08:04 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:08:06 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:08:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:08:06 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:08:06 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:08:12 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 发送按钮
2025-07-16 17:08:13 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:08:13 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:13 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:08:13 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:08:14 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:08:14 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:08:14 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:08:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:08:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:08:15 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:08:15 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:08:15 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:08:15 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-16 17:08:15 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:08:15 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-16 17:08:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:16 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:08:17 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: 哎呀，好像有什么遗漏了。让我们再试一次吧！
2025-07-16 17:08:17 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: 哎呀，好像有什么遗漏了。让我们再试一次吧！
2025-07-16 17:08:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '哎呀，好像有什么遗漏了。让我们再试一次吧！'
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:08:20 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:08:21 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:08:21 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:08:21 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:08:21 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:08:21 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:08:24 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:08:24 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:08:24 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:08:24 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:08:24 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:08:24 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:08:24 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:08:25 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:08:26 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:08:26 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:08:26 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:08:26 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:08:26 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:08:26 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:08:26 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:08:26 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:08:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:08:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:08:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:30 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:08:30 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:08:30 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:08:30 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:08:31 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:08:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:08:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:08:31 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:08:31 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:08:32 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:08:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:08:33 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:08:33 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:08:34 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-16 17:08:34 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:08:34 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:08:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:08:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:08:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:08:35 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:08:35 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:08:35 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:08:35 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-16 17:08:35 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:08:35 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-16 17:08:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:36 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:08:37 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: 打开蓝牙
2025-07-16 17:08:37 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: 打开蓝牙
2025-07-16 17:08:37 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '打开蓝牙'
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:08:40 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:08:41 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:08:41 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:08:41 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:08:41 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:08:41 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:09:39 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:09:39 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:09:39 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:09:39 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:09:39 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:09:39 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:09:39 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:09:40 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:09:40 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:09:41 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:09:41 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:09:41 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:09:41 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:09:41 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:09:41 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:09:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:09:41 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:09:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:09:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:09:44 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:09:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:09:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:09:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:09:45 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:09:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:09:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:09:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:09:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:09:47 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:09:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:09:47 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:09:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:09:48 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:09:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:09:48 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-16 17:09:48 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:09:48 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:09:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:09:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:09:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:09:49 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:09:49 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:09:49 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:09:49 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-16 17:09:49 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:09:49 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-16 17:09:49 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:09:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:09:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:09:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:09:50 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:09:51 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: 你好！很高兴见到你。我是一名22岁的人工智能专业的大学生。有什么问题需要我解答吗？
2025-07-16 17:09:51 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: 你好！很高兴见到你。我是一名22岁的人工智能专业的大学生。有什么问题需要我解答吗？
2025-07-16 17:09:51 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '你好！很高兴见到你。我是一名22岁的人工智能专业的大学生。有什么问题需要我解答吗？'
2025-07-16 17:09:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:09:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.settings.bluetooth
2025-07-16 17:09:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-16 17:09:54 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:128 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-16 17:09:54 | WARNING | pages.apps.ella.main_page_refactored:check_bluetooth_status_smart:404 | 检查蓝牙状态时不在Ella进程，尝试返回
2025-07-16 17:09:54 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-16 17:09:54 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:09:56 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:09:56 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:09:57 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:09:57 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:09:57 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:09:57 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:09:57 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:10:18 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:10:18 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:10:18 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:10:18 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:10:18 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:10:18 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:10:18 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:10:19 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:10:19 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:10:19 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:10:19 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:10:19 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:10:19 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:10:19 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:10:19 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:10:20 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:10:20 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:10:20 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:10:20 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:23 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:10:23 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:10:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:10:23 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:10:24 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:10:24 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:10:24 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:10:24 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:10:25 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:10:26 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-16 17:10:26 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:10:26 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-16 17:10:26 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:10:27 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:10:27 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:10:27 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:10:28 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-16 17:10:28 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:10:28 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:10:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:10:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:10:28 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:10:28 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:10:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:10:28 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open bluetooth
2025-07-16 17:10:28 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:10:29 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:10:30 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-16 17:10:30 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Bluetooth is turned on now.
2025-07-16 17:10:30 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: 'Bluetooth is turned on now.'
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-16 17:10:33 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-16 17:10:33 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: False -> True
2025-07-16 17:10:34 | INFO | testcases.test_ella.base_ella_test:simple_command_test:187 | 🎉 open bluetooth 测试完成
2025-07-16 17:10:34 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:10:34 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:10:34 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:10:34 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:10:34 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:10:44 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:10:44 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:10:44 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:10:44 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:10:44 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:10:44 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:10:44 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:10:45 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:10:45 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:10:46 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:10:46 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:10:46 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:10:46 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:10:46 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:10:46 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:10:46 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:10:46 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:10:46 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:10:47 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:50 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:10:50 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:10:50 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:10:50 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:10:50 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:10:51 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:10:51 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:10:51 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:10:51 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:10:52 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:10:52 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:10:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:10:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:10:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:52 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:10:52 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:10:52 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:10:53 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:10:53 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 17:10:53 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:10:53 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:10:53 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:10:53 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:10:54 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 17:10:54 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:10:54 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:10:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:10:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:10:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:10:54 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:10:54 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:10:54 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:10:54 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open contact
2025-07-16 17:10:54 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:10:55 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:10:57 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:10:57 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:10:57 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />'
2025-07-16 17:11:00 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: None -> None
2025-07-16 17:11:00 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:11:00 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:11:01 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:11:01 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:11:01 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:15:09 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:15:09 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:15:09 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:15:09 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:15:09 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:15:09 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:15:09 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:15:10 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:15:10 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:15:11 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:15:11 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:15:11 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:15:11 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:15:11 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:15:11 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:15:11 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:15:11 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:15:12 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:15:12 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:15:15 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:15:15 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:15:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:15:15 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:15:15 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:15:16 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:15:16 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:15:16 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:15:16 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:15:17 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:15:17 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:15:17 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:154 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 17:15:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:15:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:15:18 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:15:18 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 17:15:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:15:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:15:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:15:19 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:15:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:15:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:15:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:15:19 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 17:15:19 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:15:19 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:15:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:15:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:15:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:15:20 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:15:20 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:15:20 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:15:20 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open contact
2025-07-16 17:15:20 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:15:21 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:15:23 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:15:23 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:15:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />'
2025-07-16 17:15:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> None
2025-07-16 17:15:26 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:15:26 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:15:26 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:15:26 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:15:26 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:17:04 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:17:04 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:17:04 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:17:04 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:17:04 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:17:04 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:17:04 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:17:06 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:17:06 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:17:06 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:17:06 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:17:06 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:17:06 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:17:06 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:17:06 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:17:07 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:17:07 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:17:07 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-16 17:17:07 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:17:07 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-16 17:17:09 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-16 17:17:09 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-16 17:17:09 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:17:10 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:17:10 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-16 17:17:10 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-16 17:17:10 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-16 17:17:11 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-16 17:17:12 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-16 17:17:12 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-16 17:17:13 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-16 17:17:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:13 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-16 17:17:13 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-16 17:17:14 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-16 17:17:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:15 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-16 17:17:15 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-16 17:17:15 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-16 17:17:15 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-16 17:17:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:16 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:17:16 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-16 17:17:16 | INFO | core.base_element:clear_text:325 | 清空文本成功 [文本输入框(备选)]
2025-07-16 17:17:16 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-16 17:17:16 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:17 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-16 17:17:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:17 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:17 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:18 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-16 17:17:18 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-16 17:17:18 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:19 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-16 17:17:19 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:17:19 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:17:19 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-16 17:17:19 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-16 17:17:19 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-16 17:17:20 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2323 | 检测到TTS播放按钮
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-16 17:17:20 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-16 17:17:24 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:get_response_text_smart:2428 | 智能获取响应文本...
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:24 | INFO | pages.apps.ella.main_page:get_response_text:2452 | 获取AI响应文本
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:get_response_text:2468 | 页面上所有文本元素数量: 13
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:_is_ai_response:2286 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 找到AI响应: Bluetooth is turned on now.
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:get_response_text:2489 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-16 17:17:27 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:verify_command_in_response:2605 | 验证响应是否包含命令: open bluetooth
2025-07-16 17:17:27 | INFO | pages.apps.ella.main_page:verify_command_in_response:2635 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-16 17:17:27 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2404 | 智能检查蓝牙状态...
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2569 | 检查蓝牙状态
2025-07-16 17:17:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2583 | 蓝牙状态: 开启 (值: 1)
2025-07-16 17:17:29 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-16 17:17:29 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-16 17:17:30 | INFO | testcases.test_ella.history.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-16 17:17:30 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-16 17:17:30 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:17:30 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:17:30 | INFO | testcases.test_ella.history.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-16 17:17:30 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:17:30 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:17:30 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:17:50 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:17:50 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:17:50 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:17:50 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:17:50 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:17:50 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:17:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:17:51 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:17:51 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:17:52 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:17:52 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:17:52 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:17:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:17:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:17:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:17:52 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:17:52 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:17:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:17:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:17:56 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:17:56 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:17:56 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:37 | Ella应用启动成功
2025-07-16 17:17:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 10秒
2025-07-16 17:17:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:17:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:17:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:17:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:17:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:17:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:17:58 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:41 | Ella页面加载完成
2025-07-16 17:17:58 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:156 | 时钟应用初始状态: 未运行
2025-07-16 17:17:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:59 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-16 17:17:59 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:168 | ✅ 输入框点击成功
2025-07-16 17:17:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:17:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:17:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:17:59 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open clock
2025-07-16 17:17:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:18:00 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:183 | ✅ 成功输入命令，发送按钮已可用: open clock
2025-07-16 17:18:00 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:186 | ✅ 成功输入命令: open clock
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:18:00 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:18:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [TTS播放按钮], 超时时间: 8秒
2025-07-16 17:18:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-16 17:18:08 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [TTS播放按钮]
2025-07-16 17:18:08 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:204 | ✅ 命令已发送，继续执行（未检测到TTS但不影响流程）
2025-07-16 17:18:08 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:205 | ✅ 命令发送成功
2025-07-16 17:18:08 | INFO | testcases.test_ella.history.test_open_clock_command:_wait_for_clock_app_launch:122 | 智能等待时钟应用启动，超时时间: 10秒
2025-07-16 17:18:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [时钟应用包], 超时时间: 10秒
2025-07-16 17:18:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-16 17:18:19 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [时钟应用包]
2025-07-16 17:18:19 | WARNING | testcases.test_ella.history.test_open_clock_command:_wait_for_clock_app_launch:136 | ⚠️ 未检测到时钟应用包，尝试其他验证方式
2025-07-16 17:18:19 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:220 | ⚠️ 未直接检测到应用启动，将通过后续验证确认
2025-07-16 17:18:19 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:226 | 时钟应用最终状态: 未运行
2025-07-16 17:18:19 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:230 | UI验证状态: 未通过
2025-07-16 17:18:20 | ERROR | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:248 | ❌ 时钟应用未打开: com.transsion.deskclock
2025-07-16 17:18:20 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:18:20 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:18:20 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:64 | Ella应用已停止
2025-07-16 17:18:20 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:18:20 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:18:20 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:18:24 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:18:24 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:18:24 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:18:24 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:18:24 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:18:24 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:18:24 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:18:26 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:18:26 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:18:26 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:18:26 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:18:26 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:18:26 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:18:26 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:18:26 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:18:27 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:18:27 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:18:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:18:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:18:30 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:18:30 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:18:30 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:37 | Ella应用启动成功
2025-07-16 17:18:30 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 10秒
2025-07-16 17:18:30 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:18:31 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:18:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:18:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:18:31 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:18:32 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:18:33 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:41 | Ella页面加载完成
2025-07-16 17:18:33 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:156 | 时钟应用初始状态: 未运行
2025-07-16 17:18:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:18:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:18:34 | INFO | core.base_element:click:231 | 点击元素成功 [输入框]
2025-07-16 17:18:34 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:168 | ✅ 输入框点击成功
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:18:34 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open clock
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:18:34 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:183 | ✅ 成功输入命令，发送按钮已可用: open clock
2025-07-16 17:18:34 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:186 | ✅ 成功输入命令: open clock
2025-07-16 17:18:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:18:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:18:35 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:18:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [TTS播放按钮], 超时时间: 8秒
2025-07-16 17:18:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [TTS播放按钮]
2025-07-16 17:18:38 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:202 | ✅ 检测到TTS响应，命令处理完成
2025-07-16 17:18:38 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:205 | ✅ 命令发送成功
2025-07-16 17:18:38 | INFO | testcases.test_ella.history.test_open_clock_command:_wait_for_clock_app_launch:122 | 智能等待时钟应用启动，超时时间: 10秒
2025-07-16 17:18:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [时钟应用包], 超时时间: 10秒
2025-07-16 17:18:38 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:18:38 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [时钟应用包]
2025-07-16 17:18:38 | INFO | testcases.test_ella.history.test_open_clock_command:_wait_for_clock_app_launch:133 | ✅ 检测到时钟应用包，应用已启动
2025-07-16 17:18:38 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:218 | ✅ 时钟应用启动检测成功
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:226 | 时钟应用最终状态: 运行中
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:230 | UI验证状态: 通过
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:251 | ✅ 时钟应用已成功打开: com.transsion.deskclock
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:267 | ✅ 输入命令得到正确响应（时钟应用已打开）
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:test_open_clock_command:295 | 🎉 open clock命令测试完成
2025-07-16 17:18:39 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:18:39 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:18:39 | INFO | testcases.test_ella.history.test_open_clock_command:ella_app:64 | Ella应用已停止
2025-07-16 17:18:39 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:18:39 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:18:39 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:19:23 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:19:23 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:19:23 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:19:23 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:19:23 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:19:23 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:19:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:19:24 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:19:24 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:19:25 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:19:25 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:19:25 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:19:25 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:19:25 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:19:25 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:19:25 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:19:25 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:19:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:19:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:19:28 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:19:28 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:19:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:19:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:19:29 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:19:29 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:19:29 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:19:29 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:19:30 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:19:31 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:154 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 17:19:31 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:19:31 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:19:31 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:19:32 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:19:32 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 17:19:32 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:19:32 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:19:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:19:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:19:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:19:33 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:19:33 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:19:33 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:19:33 | INFO | testcases.test_ella.base_ella_test:_execute_command:109 | ✅ 成功执行命令: open contact
2025-07-16 17:19:33 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-16 17:19:33 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-16 17:19:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:19:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:19:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:19:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:19:34 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:19:36 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:19:36 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:19:36 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:125 | AI响应: '<node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />'
2025-07-16 17:19:39 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:138 | ✅ 状态验证通过: True -> None
2025-07-16 17:19:39 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:19:39 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:19:39 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:19:39 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:19:39 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:37:15 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:37:15 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:37:15 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:37:15 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:37:15 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:37:15 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:37:15 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:37:16 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:37:16 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:37:16 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:37:16 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:37:16 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:37:16 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:37:16 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:37:16 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:37:17 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:37:17 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:37:17 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:37:17 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:37:20 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:37:20 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:37:20 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:37:20 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:37:21 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:37:21 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:37:21 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:37:21 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:37:21 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:37:22 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:37:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:154 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 17:37:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:37:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:37:23 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:37:24 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:37:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:37:24 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 17:37:24 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:37:24 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:37:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:37:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:37:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:37:25 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:37:25 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:37:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:37:25 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:74 | ✅ 成功执行命令: open contact
2025-07-16 17:37:26 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:82 | 尝试获取响应文本 (第1次)
2025-07-16 17:37:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:37:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:37:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:37:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:37:26 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:37:28 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:37:28 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:37:28 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:91 | ✅ 成功获取响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 17:37:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:37:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.sh.smart.caller
2025-07-16 17:37:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-16 17:37:31 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:128 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-16 17:37:31 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:413 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-16 17:37:31 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-16 17:37:31 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:37:33 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:37:33 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:157 | 未检测到专门的联系人应用
2025-07-16 17:37:33 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:108 | 命令执行完成: 初始状态=True, 最终状态=False, 响应='<node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />'
2025-07-16 17:37:34 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:37:34 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:37:34 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:37:34 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:37:34 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 17:47:09 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 17:47:09 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 17:47:09 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 17:47:09 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 17:47:09 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 17:47:09 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 17:47:09 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 17:47:10 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:47:10 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 17:47:10 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:47:10 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 17:47:10 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 17:47:10 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 17:47:10 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 17:47:10 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 17:47:11 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 17:47:11 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 17:47:11 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 17:47:11 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:14 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 17:47:14 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 17:47:14 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 17:47:14 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 17:47:15 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 17:47:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 17:47:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 17:47:15 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 17:47:16 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 17:47:17 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:157 | 未检测到专门的联系人应用
2025-07-16 17:47:17 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:67 | 初始状态 - Dalier应用打开: False
2025-07-16 17:47:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:47:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 17:47:17 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:47:18 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:47:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 17:47:18 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 17:47:18 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 17:47:18 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 17:47:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 17:47:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 17:47:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 17:47:19 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 17:47:19 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 17:47:19 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 17:47:19 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:76 | ✅ 成功执行命令: open contact
2025-07-16 17:47:22 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:82 | 检查执行命令后的当前页面状态...
2025-07-16 17:47:22 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:47:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:47:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:47:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:47:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:47:23 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:97 | ✅ 当前仍在Ella页面
2025-07-16 17:47:23 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:102 | 尝试获取响应文本 (第1次)
2025-07-16 17:47:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:47:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:47:23 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: done -> Done!
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-16 17:47:24 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:117 | ✅ 成功获取响应文本: Done!
2025-07-16 17:47:24 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:124 | 检查最终状态 - Dalier应用是否已打开
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 17:47:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 17:47:25 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:157 | 未检测到专门的联系人应用
2025-07-16 17:47:25 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:127 | 命令执行完成: 初始状态=False, 最终状态=False, 响应='Done!'
2025-07-16 17:47:25 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:34 | ✅ 响应包含Done: Done!
2025-07-16 17:47:25 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 17:47:25 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 17:47:25 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 17:47:25 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 17:47:25 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 18:03:03 | INFO | __main__:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 18:03:03 | INFO | __main__:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-16 18:03:03 | INFO | __main__:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{c7ceeca #38 type=standard A=10218:android.task.contacts U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{29e978a u0 com.sh.smart.caller/com.android.dialer.main.impl.MainActivity t38}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{29e978a u0 com.sh.sma
2025-07-16 18:03:03 | INFO | __main__:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 18:03:16 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 18:03:16 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 18:03:16 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 18:03:16 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 18:03:16 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 18:03:16 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 18:03:16 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 18:03:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 18:03:17 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 18:03:18 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 18:03:18 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 18:03:18 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 18:03:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 18:03:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 18:03:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 18:03:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 18:03:18 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 18:03:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 18:03:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:22 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 18:03:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 18:03:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 18:03:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 18:03:23 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 18:03:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 18:03:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 18:03:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 18:03:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 18:03:24 | INFO | testcases.test_ella.base_ella_test:ella_app:25 | ✅ Ella应用启动成功
2025-07-16 18:03:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 18:03:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-16 18:03:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{1153b76 #47 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{33fdeaa u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t47}
    * Hist  #0: 
2025-07-16 18:03:24 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 18:03:24 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:88 | 初始状态 - Dalier应用打开: True
2025-07-16 18:03:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:03:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:03:25 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 18:03:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 18:03:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 18:03:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:03:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 18:03:25 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 18:03:26 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 18:03:26 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 18:03:26 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:03:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 18:03:27 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 18:03:27 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 18:03:27 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 18:03:27 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:97 | ✅ 成功执行命令: open contact
2025-07-16 18:03:30 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:103 | 检查执行命令后的当前页面状态...
2025-07-16 18:03:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:03:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:03:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:03:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:03:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 18:03:30 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:118 | ✅ 当前仍在Ella页面
2025-07-16 18:03:30 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:123 | 尝试获取响应文本 (第1次)
2025-07-16 18:03:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:03:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:03:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:03:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:03:31 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:346 | 按返回键回到主页...
2025-07-16 18:03:33 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:352 | ✅ 通过返回键回到对话页面
2025-07-16 18:03:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:269 | ✅ 成功返回到对话页面
2025-07-16 18:03:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:03:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:03:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:03:33 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 18:03:34 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: done -> Done!
2025-07-16 18:03:34 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-16 18:03:34 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:138 | ✅ 成功获取响应文本: Done!
2025-07-16 18:03:34 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:145 | 检查最终状态 - Dalier应用是否已打开
2025-07-16 18:03:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{1153b76 #47 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{33fdeaa u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t47}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumed
2025-07-16 18:03:35 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 18:03:35 | INFO | testcases.test_ella.test_open_contact:_execute_contact_command_with_retry:148 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-16 18:03:35 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:34 | ✅ 响应包含Done: Done!
2025-07-16 18:03:35 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:38 | 应用状态检查结果: 初始=True, 最终=True
2025-07-16 18:03:35 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:59 | ✅ Dalier应用已成功打开
2025-07-16 18:03:35 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:69 | 🎉 open contact 测试完成 - 响应包含Done且Dalier应用已打开
2025-07-16 18:03:35 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 18:03:35 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 18:03:36 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 18:03:36 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 18:03:36 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 18:14:02 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 18:14:02 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 18:14:02 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 18:14:02 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 18:14:02 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 18:14:02 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 18:14:02 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-16 18:14:04 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-16 18:14:04 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-16 18:14:04 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 18:14:04 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-16 18:14:04 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-16 18:14:05 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-16 18:14:05 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-16 18:14:05 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-16 18:14:05 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '14016254CJ000032', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-16 18:14:05 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-16 18:14:05 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-16 18:14:05 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:08 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-16 18:14:08 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-16 18:14:08 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-16 18:14:08 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-16 18:14:09 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-16 18:14:09 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-16 18:14:09 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-16 18:14:09 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-16 18:14:10 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-16 18:14:11 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{98f033d #48 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{a3ce801 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t48}
    * Hist  #0: 
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-16 18:14:11 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:14:11 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-16 18:14:11 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 18:14:12 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:14:12 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-16 18:14:12 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-16 18:14:12 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-16 18:14:12 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-16 18:14:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-16 18:14:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-16 18:14:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-16 18:14:13 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-16 18:14:13 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-16 18:14:13 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-16 18:14:13 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-16 18:14:16 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:14:16 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-16 18:14:16 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-16 18:14:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:14:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:14:17 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:205 | 匹配到蓝牙响应模式: bluetooth.*on -> <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.sh.smart.caller
2025-07-16 18:14:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-16 18:14:20 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:128 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-16 18:14:20 | WARNING | pages.apps.ella.main_page_refactored:check_contacts_app_opened_smart:413 | 检查联系人应用状态时不在Ella进程，尝试返回
2025-07-16 18:14:20 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-16 18:14:20 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-16 18:14:22 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{98f033d #48 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{a3ce801 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t48}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumed
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='<node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Bluetooth on." checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[814,41][837,102]" drawing-order="13" hint="" display-id="0" />'
2025-07-16 18:14:22 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-16 18:14:23 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-16 18:14:23 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-16 18:14:23 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-16 18:14:23 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-16 18:14:23 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-16 21:46:25 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-16 21:46:25 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-16 21:46:25 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-16 21:46:25 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-16 21:46:25 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-16 21:46:25 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-16 21:46:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
