2025-07-11 09:27:12 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 09:27:12 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 09:27:12 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 09:27:12 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 09:27:12 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 09:27:12 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 09:27:12 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 09:27:22 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 09:27:22 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 09:27:23 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4469, 'temperature': 290, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:27:23 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 09:27:23 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 09:27:23 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 09:27:23 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 09:27:23 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 09:27:23 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4469, 'temperature': 290, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:27:23 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 09:27:23 | INFO | testcases.test_ella.test_weather_query_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 09:27:23 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:27:23 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 09:27:26 | INFO | testcases.test_ella.test_weather_query_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 09:27:26 | INFO | testcases.test_ella.test_weather_query_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 09:27:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 09:27:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 09:27:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 09:27:31 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 09:27:31 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:27:31 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:27:32 | INFO | testcases.test_ella.test_weather_query_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 09:27:32 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:27:32 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 09:27:32 | INFO | testcases.test_ella.test_weather_query_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 09:27:32 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 09:27:32 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 09:27:32 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 09:27:52 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 09:27:52 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 09:27:52 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 09:27:52 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 09:27:52 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 09:27:52 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 09:27:52 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 09:28:02 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 09:28:02 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 09:28:03 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4469, 'temperature': 292, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:28:03 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 09:28:03 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 09:28:03 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 09:28:03 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 09:28:03 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 09:28:03 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4469, 'temperature': 292, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:28:03 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 09:28:03 | INFO | testcases.test_ella.test_weather_query_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 09:28:03 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:28:03 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 09:28:06 | INFO | testcases.test_ella.test_weather_query_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 09:28:06 | INFO | testcases.test_ella.test_weather_query_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 09:28:06 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 09:28:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 09:28:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 09:28:11 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 09:28:11 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:28:11 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:28:11 | INFO | testcases.test_ella.test_weather_query_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 09:28:11 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:28:11 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 09:28:11 | INFO | testcases.test_ella.test_weather_query_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 09:28:11 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 09:28:11 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 09:28:11 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 09:54:11 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 09:54:11 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 09:54:11 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 09:54:11 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 09:54:11 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 09:54:11 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 09:54:11 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 09:54:20 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 09:54:20 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 09:54:21 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 313, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:54:21 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 09:54:21 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 09:54:21 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 09:54:21 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 09:54:21 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 09:54:21 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 313, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 09:54:21 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 09:54:21 | INFO | testcases.test_ella.test_weather_query_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 09:54:21 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:54:21 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 09:54:24 | INFO | testcases.test_ella.test_weather_query_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 09:54:24 | INFO | testcases.test_ella.test_weather_query_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 09:54:24 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 09:54:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 09:54:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 09:54:29 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 09:54:29 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 09:54:29 | ERROR | testcases.test_ella.test_weather_query_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 09:54:30 | INFO | testcases.test_ella.test_weather_query_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 09:54:30 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 09:54:30 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 09:54:30 | INFO | testcases.test_ella.test_weather_query_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 09:54:30 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 09:54:30 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 09:54:30 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 10:11:25 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 10:11:25 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 10:11:25 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 10:11:25 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 10:11:25 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 10:11:25 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 10:11:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 10:11:26 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 10:11:26 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 10:11:27 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 10:11:27 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 10:11:27 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 10:11:27 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 10:11:27 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 10:11:27 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 10:11:28 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 10:11:28 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 10:11:28 | INFO | testcases.test_ella.test_take_photo_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 10:11:28 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 10:11:28 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 10:11:30 | INFO | testcases.test_ella.test_take_photo_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 10:11:30 | INFO | testcases.test_ella.test_take_photo_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 10:11:30 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 10:11:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 10:11:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 10:11:35 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 10:11:35 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 10:11:35 | ERROR | testcases.test_ella.test_take_photo_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 10:11:37 | INFO | testcases.test_ella.test_take_photo_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 10:11:37 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 10:11:37 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 10:11:37 | INFO | testcases.test_ella.test_take_photo_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 10:11:37 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 10:11:37 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 10:11:37 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 10:11:49 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 10:11:49 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 10:11:49 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 10:11:49 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 10:11:49 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 10:11:49 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 10:11:49 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 10:11:50 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 10:11:50 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 10:11:51 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 10:11:51 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 10:11:51 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 10:11:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 10:11:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 10:11:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 10:11:51 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 10:11:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 10:11:51 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 10:11:51 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 10:11:51 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 10:11:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 10:11:54 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 10:11:54 | INFO | pages.apps.ella.main_page:wait_for_page_load:162 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 10:11:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 10:11:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 10:11:59 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 10:11:59 | ERROR | pages.apps.ella.main_page:wait_for_page_load:181 | ❌ Ella应用包未加载
2025-07-11 10:11:59 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 10:12:00 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 10:12:00 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 10:12:00 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 10:12:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 10:12:01 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 10:12:01 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 10:12:01 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 12:08:54 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 12:08:54 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 12:08:54 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 12:08:54 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 12:08:54 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 12:08:54 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 12:08:54 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 12:09:04 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 12:09:04 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 12:09:05 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4484, 'temperature': 326, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 12:09:05 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 12:09:05 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 12:09:05 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 12:09:05 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 12:09:05 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 12:09:05 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4484, 'temperature': 326, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 12:09:05 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 12:09:05 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 12:09:05 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 12:09:05 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 12:09:08 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 12:09:08 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 12:09:08 | INFO | pages.apps.ella.main_page:wait_for_page_load:163 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 12:09:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [Ella应用包], 超时时间: 5秒
2025-07-11 12:09:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 12:09:13 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [Ella应用包]
2025-07-11 12:09:13 | ERROR | pages.apps.ella.main_page:wait_for_page_load:182 | ❌ Ella应用包未加载
2025-07-11 12:09:13 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 12:09:14 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 12:09:14 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 12:09:14 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 12:09:14 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 12:09:14 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 12:09:14 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 12:09:14 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 12:11:45 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 12:11:45 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 12:11:45 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 12:11:45 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 12:11:45 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 12:11:45 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 12:11:45 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 12:11:46 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 12:11:46 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 12:11:47 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 12:11:47 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 12:11:47 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 12:11:47 | INFO | __main__:diagnose_ella_app:27 | 🔍 开始诊断Ella应用状态
2025-07-11 12:11:47 | INFO | __main__:_check_device_connection:52 | 📱 检查设备连接状态
2025-07-11 12:11:47 | INFO | __main__:_check_device_connection:55 | 设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 12:11:47 | INFO | __main__:_check_device_connection:59 | 屏幕尺寸: (1080, 2400)
2025-07-11 12:11:49 | INFO | __main__:_check_device_connection:65 | ✅ 设备连接正常
2025-07-11 12:11:49 | INFO | __main__:_check_app_installation:73 | 📦 检查Ella应用安装状态
2025-07-11 12:11:50 | INFO | __main__:_check_app_installation:80 | ✅ Ella应用已安装: {'versionName': '4.6.0.026', 'versionCode': 40600026}
2025-07-11 12:11:50 | INFO | __main__:_check_app_running_status:94 | 🏃 检查应用运行状态
2025-07-11 12:11:50 | INFO | __main__:_check_app_running_status:98 | 当前前台应用: {'package': 'com.transsion.aivoiceassistant', 'activity': 'com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'pid': 10696}
2025-07-11 12:11:50 | INFO | __main__:_check_app_running_status:103 | ✅ Ella应用正在运行
2025-07-11 12:11:50 | INFO | __main__:_try_start_app:116 | 🚀 尝试启动Ella应用
2025-07-11 12:11:54 | INFO | __main__:_try_start_app:122 | 方法1: 标准启动完成
2025-07-11 12:11:57 | INFO | __main__:_try_start_app:131 | 方法2: 指定Activity启动完成
2025-07-11 12:12:00 | INFO | __main__:_try_start_app:140 | 方法3: Shell命令启动完成
2025-07-11 12:12:00 | INFO | __main__:_try_start_app:147 | ✅ Ella应用启动成功
2025-07-11 12:12:00 | INFO | __main__:_check_page_elements:157 | 🔍 检查页面元素
2025-07-11 12:12:00 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 EditText 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 TextView 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 Button 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 ImageView 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 LinearLayout 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:191 | ⚠️ 未找到 RecyclerView 元素
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:213 | ⚠️ 未找到Ella元素: 输入框
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:213 | ⚠️ 未找到Ella元素: 语音按钮
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:213 | ⚠️ 未找到Ella元素: 欢迎消息
2025-07-11 12:12:01 | INFO | __main__:_check_page_elements:213 | ⚠️ 未找到Ella元素: Ella文本
2025-07-11 12:12:01 | INFO | __main__:_take_debug_screenshot:223 | 📸 获取调试截图
2025-07-11 12:12:02 | INFO | __main__:diagnose_ella_app:47 | 🏁 Ella应用诊断完成
2025-07-11 12:12:02 | INFO | __main__:get_detailed_app_info:240 | 📋 获取详细应用信息
2025-07-11 12:12:02 | INFO | __main__:get_detailed_app_info:244 | 应用详细信息: {'versionName': '4.6.0.026', 'versionCode': 40600026}
2025-07-11 14:08:35 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:08:35 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:08:35 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:08:35 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:08:35 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:08:35 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:08:35 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:08:39 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: USB device 13764254B4001229 is offline
2025-07-11 14:08:39 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 14:08:39 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 14:08:39 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:08:39 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:08:40 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 14:08:42 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:09:00 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:09:04 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: USB device 13764254B4001229 is offline
2025-07-11 14:09:04 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:09:04 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:09:04 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:09:04 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 14:09:08 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: USB device 13764254B4001229 is offline
2025-07-11 14:09:33 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:09:33 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:09:33 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:09:33 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:09:33 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:09:33 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:09:33 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:09:43 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:09:43 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:09:44 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4494, 'temperature': 298, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:09:44 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:09:44 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:09:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:09:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:09:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:09:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4494, 'temperature': 298, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:09:44 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:09:44 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:09:44 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:09:44 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:09:47 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:09:47 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:09:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:09:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:09:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:09:47 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:10:04 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:338 | ⚠️ UI元素等待超时，但可能页面已加载
2025-07-11 14:10:04 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 14:10:05 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2050 | 检查蓝牙状态
2025-07-11 14:10:05 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2064 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:10:05 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 14:10:05 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1140 | 确保在对话页面...
2025-07-11 14:10:05 | INFO | pages.apps.ella.main_page:ensure_ella_process:1172 | 检查当前进程是否是Ella...
2025-07-11 14:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1179 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1180 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:10:06 | INFO | pages.apps.ella.main_page:ensure_ella_process:1189 | ✅ 当前在Ella应用进程
2025-07-11 14:10:06 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1155 | 不在对话页面，尝试回到主页...
2025-07-11 14:10:06 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1316 | 方法1: 按返回键回到主页...
2025-07-11 14:10:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 3秒
2025-07-11 14:10:11 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:11 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:10:11 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 3秒
2025-07-11 14:10:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:14 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:10:14 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1326 | 方法2: 尝试点击主页按钮...
2025-07-11 14:10:14 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1348 | 方法3: 尝试多次返回键...
2025-07-11 14:10:17 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-11 14:10:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:19 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:10:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-11 14:10:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:21 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:10:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-11 14:10:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:25 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:10:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-11 14:10:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:27 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:10:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-11 14:10:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:31 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:10:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-11 14:10:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:10:33 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:10:33 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1358 | 方法4: 重新启动应用到主页...
2025-07-11 14:10:33 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:10:33 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 10秒
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:10:37 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:10:48 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:338 | ⚠️ UI元素等待超时，但可能页面已加载
2025-07-11 14:10:48 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1361 | ✅ 重新启动应用成功
2025-07-11 14:10:48 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1379 | 确保输入框就绪...
2025-07-11 14:10:49 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1406 | 尝试通过坐标点击输入区域...
2025-07-11 14:10:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-11 14:11:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:11:00 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:11:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-11 14:11:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:11:02 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:11:02 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1422 | 尝试滑动到底部显示输入框...
2025-07-11 14:11:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 2秒
2025-07-11 14:11:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:11:05 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-07-11 14:11:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 2秒
2025-07-11 14:11:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 14:11:07 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-07-11 14:11:07 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1437 | ❌ 无法确保输入框就绪
2025-07-11 14:11:08 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:11:08 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:11:08 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:11:08 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:11:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:11:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:11:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:13:27 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:13:27 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:13:27 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:13:27 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:13:27 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:13:27 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:13:27 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:13:27 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:13:27 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:13:28 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:13:28 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:13:28 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:13:28 | INFO | __main__:test_ella_input_fix:19 | 🧪 开始测试Ella输入框修复
2025-07-11 14:13:28 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:13:28 | INFO | __main__:test_ella_input_fix:26 | 1️⃣ 启动Ella应用
2025-07-11 14:13:28 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:13:28 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:13:32 | INFO | __main__:test_ella_input_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:13:32 | INFO | __main__:test_ella_input_fix:34 | 2️⃣ 等待页面加载
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:13:32 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:13:48 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:338 | ⚠️ UI元素等待超时，但可能页面已加载
2025-07-11 14:13:48 | INFO | __main__:test_ella_input_fix:38 | ✅ 页面加载成功
2025-07-11 14:13:48 | INFO | __main__:test_ella_input_fix:41 | 3️⃣ 获取当前页面截图
2025-07-11 14:13:49 | ERROR | core.base_driver:screenshot:305 | 截图失败: unknown file extension: 
2025-07-11 14:13:49 | ERROR | __main__:test_ella_input_fix:85 | 测试过程中发生异常: unknown file extension: 
2025-07-11 14:13:49 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:13:49 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:13:49 | INFO | __main__:test_ella_input_fix:92 | ✅ 应用已停止
2025-07-11 14:13:49 | ERROR | __main__:main:177 | 💥 Ella输入框修复测试失败
2025-07-11 14:14:45 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:14:45 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:14:45 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:14:45 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:14:45 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:14:45 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:14:45 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:14:46 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:14:46 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:14:46 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:14:46 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:14:46 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:14:46 | INFO | __main__:test_ella_input_fix:19 | 🧪 开始测试Ella输入框修复
2025-07-11 14:14:47 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:14:47 | INFO | __main__:test_ella_input_fix:26 | 1️⃣ 启动Ella应用
2025-07-11 14:14:47 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:14:47 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:14:50 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:14:50 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:14:50 | INFO | __main__:test_ella_input_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:14:50 | INFO | __main__:test_ella_input_fix:34 | 2️⃣ 等待页面加载
2025-07-11 14:14:50 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:14:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:14:51 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:14:51 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:15:06 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:338 | ⚠️ UI元素等待超时，但可能页面已加载
2025-07-11 14:15:06 | INFO | __main__:test_ella_input_fix:38 | ✅ 页面加载成功
2025-07-11 14:15:06 | INFO | __main__:test_ella_input_fix:41 | 3️⃣ 获取当前页面截图
2025-07-11 14:15:06 | INFO | __main__:test_ella_input_fix:46 | 4️⃣ 测试输入框检测和激活
2025-07-11 14:15:06 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1379 | 确保输入框就绪...
2025-07-11 14:15:06 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1412 | 检查已知输入框元素...
2025-07-11 14:15:07 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1439 | 检查通用输入框元素...
2025-07-11 14:15:07 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1473 | 尝试通过坐标激活输入区域...
2025-07-11 14:15:07 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1486 | 尝试点击坐标 (540, 2160)
2025-07-11 14:15:09 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1486 | 尝试点击坐标 (540, 2040)
2025-07-11 14:15:10 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1486 | 尝试点击坐标 (540, 1920)
2025-07-11 14:15:11 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1486 | 尝试点击坐标 (864, 2160)
2025-07-11 14:15:13 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1504 | 尝试通过滑动激活输入框...
2025-07-11 14:15:15 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1546 | 检查可交互元素...
2025-07-11 14:15:16 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1402 | ❌ 无法确保输入框就绪
2025-07-11 14:15:16 | ERROR | __main__:test_ella_input_fix:72 | ❌ 输入框检测失败
2025-07-11 14:15:16 | INFO | __main__:test_ella_input_fix:79 | 7️⃣ 输出诊断信息
2025-07-11 14:15:16 | INFO | __main__:_output_diagnostic_info:100 | 📋 诊断信息:
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:104 | 当前应用: {'package': 'com.transsion.aivoiceassistant', 'activity': 'com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'pid': 16063}
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:108 | 屏幕尺寸: (1080, 2400)
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:111 | 页面元素检查:
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:115 |   - EditText元素: 0 个
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:119 |   - TextView元素: 0 个
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:123 |   - Button元素: 0 个
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:127 |   - ImageButton元素: 0 个
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:130 | Ella特定元素检查:
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:136 |   ❌ 主输入框不存在
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:142 |   ❌ 备选输入框不存在
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:148 |   ❌ 语音按钮不存在
2025-07-11 14:15:17 | INFO | __main__:_output_diagnostic_info:154 |   ❌ 发送按钮不存在
2025-07-11 14:15:18 | INFO | __main__:_output_diagnostic_info:163 |   ❌ 未找到包含'Ella'的元素
2025-07-11 14:15:18 | INFO | __main__:_output_diagnostic_info:163 |   ❌ 未找到包含'Hi，我是Ella'的元素
2025-07-11 14:15:18 | INFO | __main__:_output_diagnostic_info:163 |   ❌ 未找到包含'语音助手'的元素
2025-07-11 14:15:18 | INFO | __main__:_output_diagnostic_info:163 |   ❌ 未找到包含'输入'的元素
2025-07-11 14:15:18 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:15:18 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:15:18 | INFO | __main__:test_ella_input_fix:92 | ✅ 应用已停止
2025-07-11 14:15:18 | ERROR | __main__:main:177 | 💥 Ella输入框修复测试失败
2025-07-11 14:16:52 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:16:52 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:16:52 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:16:52 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:16:52 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:16:52 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:16:52 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:16:53 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:16:53 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:16:54 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:16:54 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:16:54 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:16:54 | INFO | __main__:analyze_ella_page:20 | 🔍 开始深度分析Ella页面
2025-07-11 14:16:54 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:16:54 | INFO | __main__:analyze_ella_page:26 | 1️⃣ 启动Ella应用
2025-07-11 14:16:54 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:16:54 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:16:58 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:16:58 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:16:58 | INFO | __main__:analyze_ella_page:32 | 2️⃣ 等待页面稳定
2025-07-11 14:17:03 | INFO | __main__:analyze_ella_page:36 | 3️⃣ 获取页面源码
2025-07-11 14:17:03 | INFO | __main__:get_page_source:68 | 获取页面源码...
2025-07-11 14:17:03 | INFO | __main__:analyze_ella_page:40 | 4️⃣ 分析页面结构
2025-07-11 14:17:03 | INFO | __main__:analyze_page_structure:97 | 分析页面结构...
2025-07-11 14:17:03 | INFO | __main__:analyze_page_structure:145 | 页面总元素数量: 0
2025-07-11 14:17:03 | WARNING | __main__:analyze_page_structure:148 | ⚠️ 页面没有检测到任何UI元素，可能是:
2025-07-11 14:17:03 | WARNING | __main__:analyze_page_structure:149 |   1. 页面还在加载中
2025-07-11 14:17:03 | WARNING | __main__:analyze_page_structure:150 |   2. 应用启动到了错误的页面
2025-07-11 14:17:03 | WARNING | __main__:analyze_page_structure:151 |   3. 应用需要特殊权限或设置
2025-07-11 14:17:03 | WARNING | __main__:analyze_page_structure:152 |   4. UIAutomator2无法访问该页面
2025-07-11 14:17:03 | INFO | __main__:analyze_ella_page:44 | 5️⃣ 尝试不同的交互方式
2025-07-11 14:17:03 | INFO | __main__:try_different_interactions:161 | 尝试不同的交互方式...
2025-07-11 14:17:03 | INFO | __main__:try_different_interactions:164 | 1. 尝试点击屏幕中央
2025-07-11 14:17:06 | INFO | __main__:try_different_interactions:177 | 2. 尝试按返回键
2025-07-11 14:17:09 | INFO | __main__:try_different_interactions:182 | 3. 尝试按Home键再重新进入
2025-07-11 14:17:14 | INFO | __main__:try_different_interactions:191 | 4. 尝试滑动操作
2025-07-11 14:17:16 | INFO | __main__:try_different_interactions:201 | 5. 尝试长按屏幕
2025-07-11 14:17:19 | WARNING | __main__:try_different_interactions:210 | ⚠️ 所有交互尝试后仍未检测到UI元素
2025-07-11 14:17:19 | INFO | __main__:analyze_ella_page:48 | 6️⃣ 检查应用状态
2025-07-11 14:17:19 | INFO | __main__:check_app_state:219 | 检查应用状态...
2025-07-11 14:17:19 | INFO | __main__:check_app_state:223 | 当前应用: {'package': 'com.transsion.aivoiceassistant', 'activity': 'com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity', 'pid': 16956}
2025-07-11 14:17:20 | INFO | __main__:check_app_state:228 | ✅ Ella应用正在运行
2025-07-11 14:17:20 | INFO | __main__:check_app_state:235 | 应用信息: {'versionName': '4.6.0.026', 'versionCode': 40600026}
2025-07-11 14:17:20 | INFO | __main__:check_app_state:241 | 设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:17:28 | INFO | __main__:check_app_state:245 | 屏幕状态: 开启
2025-07-11 14:17:28 | INFO | __main__:analyze_ella_page:52 | 7️⃣ 保存分析结果
2025-07-11 14:17:28 | INFO | __main__:save_analysis_results:254 | 保存分析结果...
2025-07-11 14:17:30 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:17:30 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:18:44 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:18:44 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:18:44 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:18:44 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:18:44 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:18:44 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:18:44 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:18:45 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:18:45 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:18:45 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:18:45 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:18:45 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:18:45 | INFO | __main__:fix_ella_accessibility:19 | 🔧 开始修复Ella应用无障碍访问问题
2025-07-11 14:18:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:18:45 | INFO | __main__:fix_ella_accessibility:25 | 1️⃣ 重启UIAutomator2服务
2025-07-11 14:18:45 | INFO | __main__:restart_uiautomator2_service:53 | 重启UIAutomator2服务...
2025-07-11 14:18:48 | INFO | __main__:restart_uiautomator2_service:59 | ✅ UIAutomator2服务已停止
2025-07-11 14:18:48 | INFO | __main__:restart_uiautomator2_service:66 | ✅ 设备重新连接成功
2025-07-11 14:18:48 | INFO | __main__:fix_ella_accessibility:29 | 2️⃣ 检查无障碍服务设置
2025-07-11 14:18:48 | INFO | __main__:check_accessibility_settings:77 | 检查无障碍服务设置...
2025-07-11 14:18:50 | INFO | __main__:check_accessibility_settings:87 | 已打开设置应用
2025-07-11 14:18:54 | INFO | __main__:fix_ella_accessibility:33 | 3️⃣ 尝试不同的启动方式
2025-07-11 14:18:54 | INFO | __main__:try_different_launch_methods:103 | 尝试不同的启动方式...
2025-07-11 14:18:54 | INFO | __main__:try_different_launch_methods:115 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:19:00 | INFO | __main__:test_basic_interaction:172 | ✅ 基本交互测试通过
2025-07-11 14:19:00 | INFO | __main__:try_different_launch_methods:121 | ✅ Activity com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity 启动成功并可交互
2025-07-11 14:19:00 | INFO | __main__:fix_ella_accessibility:37 | 4️⃣ 使用替代检测方案
2025-07-11 14:19:00 | INFO | __main__:use_alternative_detection:187 | 使用替代检测方案...
2025-07-11 14:19:00 | INFO | __main__:use_alternative_detection:190 | 1. 测试基于坐标的交互
2025-07-11 14:19:25 | INFO | __main__:test_coordinate_based_interaction:237 | ✅ 在坐标 (540, 2160) 找到可交互区域
2025-07-11 14:19:25 | INFO | __main__:use_alternative_detection:193 | ✅ 基于坐标的交互方案可用
2025-07-11 14:19:25 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:19:25 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:21:43 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:21:43 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:21:43 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:21:43 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:21:43 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:21:43 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:21:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:21:44 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:21:44 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:21:45 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:21:45 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:21:45 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:21:45 | INFO | __main__:main:231 | 🚀 开始Ella基于坐标的修复方案测试
2025-07-11 14:21:45 | INFO | __main__:test_ella_coordinate_fix:19 | 🧪 开始测试Ella基于坐标的修复方案
2025-07-11 14:21:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:21:45 | INFO | __main__:test_ella_coordinate_fix:26 | 1️⃣ 启动Ella应用
2025-07-11 14:21:45 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:21:45 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:21:48 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:21:48 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:21:48 | INFO | __main__:test_ella_coordinate_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:21:48 | INFO | __main__:test_ella_coordinate_fix:34 | 2️⃣ 等待页面加载
2025-07-11 14:21:48 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:21:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:21:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:21:49 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:21:49 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:318 | ✅ 检测到页面元素: 输入框
2025-07-11 14:21:50 | INFO | __main__:test_ella_coordinate_fix:38 | ✅ 页面加载成功
2025-07-11 14:21:50 | INFO | __main__:test_ella_coordinate_fix:41 | 3️⃣ 获取当前页面截图
2025-07-11 14:21:51 | INFO | __main__:test_ella_coordinate_fix:46 | 4️⃣ 测试改进后的输入框检测
2025-07-11 14:21:51 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1423 | 确保输入框就绪...
2025-07-11 14:21:51 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1456 | 检查已知输入框元素...
2025-07-11 14:21:51 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1460 | 找到主输入框
2025-07-11 14:21:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:21:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:21:52 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:21:52 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1462 | ✅ 主输入框已激活
2025-07-11 14:21:52 | INFO | __main__:test_ella_coordinate_fix:50 | ✅ 输入框检测成功
2025-07-11 14:21:52 | INFO | __main__:test_ella_coordinate_fix:53 | 5️⃣ 测试基于坐标的文本输入
2025-07-11 14:21:52 | INFO | __main__:test_ella_coordinate_fix:62 | 测试命令 1: 你好
2025-07-11 14:21:52 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: 你好
2025-07-11 14:21:52 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:21:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:21:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:21:53 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:21:53 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:21:53 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:21:53 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:21:53 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:21:54 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:21:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:21:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:21:55 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: 你好
2025-07-11 14:21:55 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: 你好
2025-07-11 14:21:55 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: 你好
2025-07-11 14:21:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:21:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:21:56 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: 你好
2025-07-11 14:21:56 | INFO | __main__:test_ella_coordinate_fix:66 | ✅ 命令输入成功: 你好
2025-07-11 14:21:57 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:21:57 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:21:58 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:21:58 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:21:58 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:21:58 | INFO | __main__:test_ella_coordinate_fix:73 | ✅ 命令发送成功: 你好
2025-07-11 14:22:03 | INFO | __main__:test_ella_coordinate_fix:62 | 测试命令 2: 测试输入
2025-07-11 14:22:03 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: 测试输入
2025-07-11 14:22:03 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:03 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:22:03 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:22:04 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:22:04 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:22:04 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:22:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:04 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:22:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:05 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: 测试输入
2025-07-11 14:22:05 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: 测试输入
2025-07-11 14:22:06 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: 测试输入
2025-07-11 14:22:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:06 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: 测试输入
2025-07-11 14:22:06 | INFO | __main__:test_ella_coordinate_fix:66 | ✅ 命令输入成功: 测试输入
2025-07-11 14:22:07 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:08 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:22:08 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:22:08 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:22:08 | INFO | __main__:test_ella_coordinate_fix:73 | ✅ 命令发送成功: 测试输入
2025-07-11 14:22:13 | INFO | __main__:test_ella_coordinate_fix:62 | 测试命令 3: open bluetooth
2025-07-11 14:22:13 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: open bluetooth
2025-07-11 14:22:13 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:22:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:13 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:13 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:13 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:22:13 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:22:13 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:22:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:22:14 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:22:14 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:22:14 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:22:14 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:14 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:14 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:15 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:22:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:15 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-07-11 14:22:15 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-11 14:22:16 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: open bluetooth
2025-07-11 14:22:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:17 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-11 14:22:17 | INFO | __main__:test_ella_coordinate_fix:66 | ✅ 命令输入成功: open bluetooth
2025-07-11 14:22:18 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:18 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:22:18 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:22:18 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:22:18 | INFO | __main__:test_ella_coordinate_fix:73 | ✅ 命令发送成功: open bluetooth
2025-07-11 14:22:24 | INFO | __main__:test_ella_coordinate_fix:62 | 测试命令 4: what time is it
2025-07-11 14:22:24 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: what time is it
2025-07-11 14:22:24 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:24 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:22:24 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:22:25 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:22:25 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:22:25 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:22:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:25 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:22:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:26 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: what time is it
2025-07-11 14:22:26 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: what time is it
2025-07-11 14:22:27 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: what time is it
2025-07-11 14:22:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:27 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: what time is it
2025-07-11 14:22:27 | INFO | __main__:test_ella_coordinate_fix:66 | ✅ 命令输入成功: what time is it
2025-07-11 14:22:28 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:29 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:22:29 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:22:29 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:22:29 | INFO | __main__:test_ella_coordinate_fix:73 | ✅ 命令发送成功: what time is it
2025-07-11 14:22:34 | INFO | __main__:test_ella_coordinate_fix:91 | 6️⃣ 测试完整的命令执行流程
2025-07-11 14:22:34 | INFO | pages.apps.ella.main_page:execute_text_command:601 | 执行文本命令: 测试完整流程
2025-07-11 14:22:34 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1184 | 确保在对话页面...
2025-07-11 14:22:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1195 | ✅ 已在对话页面
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1423 | 确保输入框就绪...
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1456 | 检查已知输入框元素...
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1460 | 找到主输入框
2025-07-11 14:22:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:35 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:22:35 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1462 | ✅ 主输入框已激活
2025-07-11 14:22:36 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: 测试完整流程
2025-07-11 14:22:36 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:22:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:37 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:22:37 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:22:37 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:22:37 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:22:37 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:38 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:22:38 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:39 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: 测试完整流程
2025-07-11 14:22:39 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: 测试完整流程
2025-07-11 14:22:39 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: 测试完整流程
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:22:40 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: 测试完整流程
2025-07-11 14:22:40 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:22:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:22:41 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:22:41 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:22:41 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:22:41 | INFO | pages.apps.ella.main_page:execute_text_command:621 | ✅ 文本命令执行完成
2025-07-11 14:22:41 | INFO | __main__:test_ella_coordinate_fix:95 | ✅ 完整流程测试成功: 测试完整流程
2025-07-11 14:22:44 | INFO | __main__:test_ella_coordinate_fix:115 | 7️⃣ 输出测试总结
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:136 | 📋 测试总结:
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:142 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:143 | 屏幕尺寸: (1080, 2400)
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:147 | ✅ 输入框检测: 成功
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:148 | ✅ 基于坐标的交互方案: 可用
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:149 | ✅ 修复方案: 有效
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:156 | 💡 建议:
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:158 |   - 可以使用基于坐标的交互方案进行Ella测试
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:159 |   - 建议在实际测试中验证响应内容
2025-07-11 14:22:44 | INFO | __main__:_output_test_summary:160 |   - 可以考虑添加更多的错误处理机制
2025-07-11 14:22:44 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:22:44 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:22:45 | INFO | __main__:test_ella_coordinate_fix:128 | ✅ 应用已停止
2025-07-11 14:22:45 | INFO | __main__:test_specific_coordinate_interaction:172 | 🎯 测试特定坐标交互
2025-07-11 14:22:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:22:45 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:22:45 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:22:48 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:22:48 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:22:51 | INFO | __main__:test_specific_coordinate_interaction:185 | 屏幕尺寸: 1080 x 2400
2025-07-11 14:22:51 | INFO | __main__:test_specific_coordinate_interaction:196 | 测试坐标 1: (540, 2160)
2025-07-11 14:22:54 | INFO | __main__:test_specific_coordinate_interaction:211 | ✅ 坐标 (540, 2160) 交互成功
2025-07-11 14:22:54 | INFO | __main__:test_specific_coordinate_interaction:196 | 测试坐标 2: (540, 2160)
2025-07-11 14:22:57 | INFO | __main__:test_specific_coordinate_interaction:211 | ✅ 坐标 (540, 2160) 交互成功
2025-07-11 14:22:57 | INFO | __main__:test_specific_coordinate_interaction:196 | 测试坐标 3: (540, 2040)
2025-07-11 14:23:00 | INFO | __main__:test_specific_coordinate_interaction:211 | ✅ 坐标 (540, 2040) 交互成功
2025-07-11 14:23:00 | INFO | __main__:test_specific_coordinate_interaction:196 | 测试坐标 4: (864, 2160)
2025-07-11 14:23:02 | INFO | __main__:test_specific_coordinate_interaction:211 | ✅ 坐标 (864, 2160) 交互成功
2025-07-11 14:23:02 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:23:02 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:23:02 | INFO | __main__:main:241 | 🎉 Ella基于坐标的修复方案测试全部通过
2025-07-11 14:23:21 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:23:21 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:23:21 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:23:21 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:23:21 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:23:21 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:23:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:23:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:23:23 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:23:24 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:23:24 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:23:24 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:23:24 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:23:24 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:23:24 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:23:24 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:23:24 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:23:24 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:23:24 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:23:24 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:23:26 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:23:26 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:23:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:23:27 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:23:27 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:23:27 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:23:27 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:318 | ✅ 检测到页面元素: 输入框
2025-07-11 14:23:28 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 14:23:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2352 | 检查蓝牙状态
2025-07-11 14:23:29 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2366 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:23:29 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 14:23:29 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1184 | 确保在对话页面...
2025-07-11 14:23:29 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1195 | ✅ 已在对话页面
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1423 | 确保输入框就绪...
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1456 | 检查已知输入框元素...
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1460 | 找到主输入框
2025-07-11 14:23:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:30 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:23:30 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1462 | ✅ 主输入框已激活
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:execute_text_command:601 | 执行文本命令: open bluetooth
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1184 | 确保在对话页面...
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1195 | ✅ 已在对话页面
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1423 | 确保输入框就绪...
2025-07-11 14:23:31 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1456 | 检查已知输入框元素...
2025-07-11 14:23:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1460 | 找到主输入框
2025-07-11 14:23:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:32 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:23:32 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1462 | ✅ 主输入框已激活
2025-07-11 14:23:33 | INFO | pages.apps.ella.main_page:input_text_command:356 | 输入文本命令: open bluetooth
2025-07-11 14:23:33 | INFO | pages.apps.ella.main_page:clear_input_box:453 | 清空输入框...
2025-07-11 14:23:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:33 | INFO | core.base_element:clear_text:223 | 清空文本成功 [输入框]
2025-07-11 14:23:33 | INFO | pages.apps.ella.main_page:clear_input_box:459 | 主输入框已清空
2025-07-11 14:23:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:23:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:23:34 | INFO | core.base_element:clear_text:223 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:23:34 | INFO | pages.apps.ella.main_page:clear_input_box:474 | 备选输入框已清空
2025-07-11 14:23:34 | INFO | pages.apps.ella.main_page:input_text_command:363 | 使用主输入框(et_input)
2025-07-11 14:23:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:35 | INFO | core.base_element:click:129 | 点击元素成功 [输入框]
2025-07-11 14:23:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:36 | INFO | core.base_element:send_keys:200 | 输入文本成功 [输入框]: open bluetooth
2025-07-11 14:23:36 | INFO | pages.apps.ella.main_page:input_text_command:375 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-11 14:23:36 | INFO | pages.apps.ella.main_page:verify_input_text:492 | 验证输入文本: open bluetooth
2025-07-11 14:23:36 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:23:36 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:36 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:verify_input_text:499 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:send_command:530 | 发送命令
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:23:37 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:23:37 | INFO | core.base_element:click:129 | 点击元素成功 [发送按钮]
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:send_command:535 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:send_command:537 | ✅ 命令发送成功
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:execute_text_command:621 | ✅ 文本命令执行完成
2025-07-11 14:23:37 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:wait_for_response:1789 | 快速等待AI响应，超时时间: 8秒
2025-07-11 14:23:37 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:wait_for_response:1804 | 初始元素数量: 14
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:40 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2106 | 检测到TTS播放按钮
2025-07-11 14:23:40 | INFO | pages.apps.ella.main_page:_quick_check_for_response:1873 | 检测到TTS按钮，表示有AI响应
2025-07-11 14:23:40 | INFO | pages.apps.ella.main_page:wait_for_response:1822 | ✅ 快速检测到AI响应
2025-07-11 14:23:44 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:get_response_text_smart:2211 | 智能获取响应文本...
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:44 | INFO | pages.apps.ella.main_page:get_response_text:2235 | 获取AI响应文本
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:get_response_text:2251 | 页面上所有文本元素数量: 13
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:_is_ai_response:2074 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:get_response_text:2259 | 找到AI响应: Bluetooth is turned on now.
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:get_response_text:2272 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-11 14:23:47 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:verify_command_in_response:2388 | 验证响应是否包含命令: open bluetooth
2025-07-11 14:23:47 | INFO | pages.apps.ella.main_page:verify_command_in_response:2418 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-11 14:23:47 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2187 | 智能检查蓝牙状态...
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1216 | 检查当前进程是否是Ella...
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1223 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1224 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1233 | ✅ 当前在Ella应用进程
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2352 | 检查蓝牙状态
2025-07-11 14:23:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2366 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:23:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-11 14:23:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-11 14:23:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-11 14:23:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:23:49 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:23:49 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:23:50 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:23:50 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:23:50 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:23:50 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:24:34 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:24:34 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:24:34 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:24:34 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:24:34 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:24:34 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:24:34 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:24:37 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:24:37 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 14:24:37 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 14:24:37 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:24:37 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:24:37 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 14:24:39 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:24:41 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 14:24:47 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:24:52 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:24:52 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:24:52 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:24:52 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:24:52 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 14:24:52 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4488, 'temperature': 328, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:24:52 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:24:52 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:24:53 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:24:53 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:24:53 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:24:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4488, 'temperature': 328, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:24:53 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:24:53 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:24:53 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:24:53 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:24:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:24:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:24:55 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:24:56 | INFO | pages.apps.ella.main_page:wait_for_page_load:267 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:24:56 | INFO | pages.apps.ella.main_page:wait_for_page_load:270 | ✅ Ella应用包已确认
2025-07-11 14:24:56 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:294 | 等待Ella页面UI元素加载...
2025-07-11 14:24:56 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [输入框]: Invalid version: ''
2025-07-11 14:24:57 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:24:58 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:24:59 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:24:59 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [发送按钮]: Invalid version: ''
2025-07-11 14:25:00 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:25:01 | ERROR | core.base_element:is_exists:112 | 检查元素存在性失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:25:02 | ERROR | pages.apps.ella.main_page:_wait_for_ui_elements:342 | 等待UI元素失败: Invalid version: ''
2025-07-11 14:25:02 | ERROR | testcases.test_ella.test_bluetooth_command:ella_app:40 | ❌ Ella页面加载失败
2025-07-11 14:25:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:25:02 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:25:02 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:25:02 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:25:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:25:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:25:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:30:20 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:30:20 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:30:20 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:30:20 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:30:20 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:30:20 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:30:20 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:30:22 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:30:22 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:30:22 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:30:22 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:30:22 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:30:22 | INFO | __main__:main:206 | 🚀 开始错误处理修复测试
2025-07-11 14:30:22 | INFO | __main__:test_error_handling_fix:19 | 🧪 开始测试错误处理修复
2025-07-11 14:30:22 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:30:22 | INFO | __main__:test_error_handling_fix:26 | 1️⃣ 测试应用启动
2025-07-11 14:30:22 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:30:22 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:30:26 | INFO | __main__:test_error_handling_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:30:26 | INFO | __main__:test_error_handling_fix:34 | 2️⃣ 测试页面加载（错误处理改进）
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 20秒
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:30:26 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:30:27 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:30:28 | INFO | __main__:test_error_handling_fix:38 | ✅ 页面加载成功，没有出现Invalid version错误
2025-07-11 14:30:28 | INFO | __main__:test_error_handling_fix:43 | 3️⃣ 测试元素检测的错误处理
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:90 | 测试元素检测的错误处理...
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:107 |   输入框: 存在
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:107 |   文本输入框: 存在
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:107 |   语音按钮: 不存在
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:107 |   发送按钮: 存在
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:107 |   欢迎消息: 不存在
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:113 | 元素检测结果: 成功 5, 错误 0
2025-07-11 14:30:28 | INFO | __main__:test_element_error_handling:116 | ✅ 所有元素检测都没有出现错误
2025-07-11 14:30:28 | INFO | __main__:test_error_handling_fix:47 | 4️⃣ 测试输入框检测
2025-07-11 14:30:28 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1550 | 确保输入框就绪...
2025-07-11 14:30:28 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1583 | 检查已知输入框元素...
2025-07-11 14:30:29 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1587 | 找到主输入框
2025-07-11 14:30:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:29 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:30:29 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1589 | ✅ 主输入框已激活
2025-07-11 14:30:30 | INFO | __main__:test_error_handling_fix:51 | ✅ 输入框检测成功
2025-07-11 14:30:30 | INFO | __main__:test_error_handling_fix:54 | 5️⃣ 测试简单的文本输入
2025-07-11 14:30:30 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: 测试错误处理
2025-07-11 14:30:30 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:30 | INFO | core.base_element:clear_text:265 | 清空文本成功 [输入框]
2025-07-11 14:30:30 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:30:30 | INFO | core.base_element:clear_text:265 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:30:30 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-11 14:30:31 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-11 14:30:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:31 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:30:32 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:32 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:32 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:32 | INFO | core.base_element:send_keys:242 | 输入文本成功 [输入框]: 测试错误处理
2025-07-11 14:30:32 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: 测试错误处理
2025-07-11 14:30:33 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: 测试错误处理
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:33 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: 测试错误处理
2025-07-11 14:30:33 | INFO | __main__:test_error_handling_fix:56 | ✅ 文本输入成功
2025-07-11 14:30:33 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:30:34 | INFO | core.base_element:click:171 | 点击元素成功 [发送按钮]
2025-07-11 14:30:34 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:30:34 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-11 14:30:34 | INFO | __main__:test_error_handling_fix:60 | ✅ 发送功能正常
2025-07-11 14:30:34 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:30:34 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:30:34 | INFO | __main__:test_error_handling_fix:82 | ✅ 应用已停止
2025-07-11 14:30:34 | INFO | __main__:test_service_health_check:128 | 🔍 测试服务健康检查
2025-07-11 14:30:34 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:30:34 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:30:35 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:30:38 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:30:38 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:30:38 | INFO | __main__:test_service_health_check:138 | 测试UIAutomator2服务健康检查...
2025-07-11 14:30:38 | INFO | __main__:test_service_health_check:146 | 健康检查 1: 通过
2025-07-11 14:30:39 | INFO | __main__:test_service_health_check:146 | 健康检查 2: 通过
2025-07-11 14:30:40 | INFO | __main__:test_service_health_check:146 | 健康检查 3: 通过
2025-07-11 14:30:41 | INFO | __main__:test_service_health_check:146 | 健康检查 4: 通过
2025-07-11 14:30:42 | INFO | __main__:test_service_health_check:146 | 健康检查 5: 通过
2025-07-11 14:30:43 | INFO | __main__:test_service_health_check:153 | 服务健康检查成功率: 100.0%
2025-07-11 14:30:43 | INFO | __main__:test_service_health_check:156 | ✅ 服务健康检查表现良好
2025-07-11 14:30:43 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:30:43 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:30:43 | INFO | __main__:test_multiple_runs:177 | 🔄 测试多次运行的稳定性
2025-07-11 14:30:43 | INFO | __main__:test_multiple_runs:183 | 开始第 1 次运行...
2025-07-11 14:30:43 | INFO | __main__:test_error_handling_fix:19 | 🧪 开始测试错误处理修复
2025-07-11 14:30:43 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:30:44 | INFO | __main__:test_error_handling_fix:26 | 1️⃣ 测试应用启动
2025-07-11 14:30:44 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:30:44 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:30:47 | INFO | __main__:test_error_handling_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:30:47 | INFO | __main__:test_error_handling_fix:34 | 2️⃣ 测试页面加载（错误处理改进）
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 20秒
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:30:47 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:30:48 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:30:49 | INFO | __main__:test_error_handling_fix:38 | ✅ 页面加载成功，没有出现Invalid version错误
2025-07-11 14:30:49 | INFO | __main__:test_error_handling_fix:43 | 3️⃣ 测试元素检测的错误处理
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:90 | 测试元素检测的错误处理...
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:107 |   输入框: 存在
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:107 |   文本输入框: 存在
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:107 |   语音按钮: 不存在
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:107 |   发送按钮: 存在
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:107 |   欢迎消息: 不存在
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:113 | 元素检测结果: 成功 5, 错误 0
2025-07-11 14:30:49 | INFO | __main__:test_element_error_handling:116 | ✅ 所有元素检测都没有出现错误
2025-07-11 14:30:49 | INFO | __main__:test_error_handling_fix:47 | 4️⃣ 测试输入框检测
2025-07-11 14:30:49 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1550 | 确保输入框就绪...
2025-07-11 14:30:49 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1583 | 检查已知输入框元素...
2025-07-11 14:30:49 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1587 | 找到主输入框
2025-07-11 14:30:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:50 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:30:50 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1589 | ✅ 主输入框已激活
2025-07-11 14:30:50 | INFO | __main__:test_error_handling_fix:51 | ✅ 输入框检测成功
2025-07-11 14:30:50 | INFO | __main__:test_error_handling_fix:54 | 5️⃣ 测试简单的文本输入
2025-07-11 14:30:50 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: 测试错误处理
2025-07-11 14:30:50 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 14:30:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:51 | INFO | core.base_element:clear_text:265 | 清空文本成功 [输入框]
2025-07-11 14:30:51 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:30:52 | INFO | core.base_element:clear_text:265 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:30:52 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-11 14:30:52 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:53 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:30:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:54 | INFO | core.base_element:send_keys:242 | 输入文本成功 [输入框]: 测试错误处理
2025-07-11 14:30:54 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: 测试错误处理
2025-07-11 14:30:55 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: 测试错误处理
2025-07-11 14:30:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:30:55 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:55 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:30:55 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: 测试错误处理
2025-07-11 14:30:55 | INFO | __main__:test_error_handling_fix:56 | ✅ 文本输入成功
2025-07-11 14:30:55 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 14:30:55 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:30:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:30:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:30:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:30:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:30:56 | INFO | core.base_element:click:171 | 点击元素成功 [发送按钮]
2025-07-11 14:30:56 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:30:56 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-11 14:30:56 | INFO | __main__:test_error_handling_fix:60 | ✅ 发送功能正常
2025-07-11 14:30:56 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:30:56 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:30:56 | INFO | __main__:test_error_handling_fix:82 | ✅ 应用已停止
2025-07-11 14:30:56 | INFO | __main__:test_multiple_runs:188 | ✅ 第 1 次运行成功
2025-07-11 14:30:59 | INFO | __main__:test_multiple_runs:183 | 开始第 2 次运行...
2025-07-11 14:30:59 | INFO | __main__:test_error_handling_fix:19 | 🧪 开始测试错误处理修复
2025-07-11 14:30:59 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:30:59 | INFO | __main__:test_error_handling_fix:26 | 1️⃣ 测试应用启动
2025-07-11 14:30:59 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:31:00 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:31:03 | INFO | __main__:test_error_handling_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:31:03 | INFO | __main__:test_error_handling_fix:34 | 2️⃣ 测试页面加载（错误处理改进）
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 20秒
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:31:03 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:31:04 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:31:05 | INFO | __main__:test_error_handling_fix:38 | ✅ 页面加载成功，没有出现Invalid version错误
2025-07-11 14:31:05 | INFO | __main__:test_error_handling_fix:43 | 3️⃣ 测试元素检测的错误处理
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:90 | 测试元素检测的错误处理...
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:107 |   输入框: 存在
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:107 |   文本输入框: 存在
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:107 |   语音按钮: 不存在
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:107 |   发送按钮: 存在
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:107 |   欢迎消息: 不存在
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:113 | 元素检测结果: 成功 5, 错误 0
2025-07-11 14:31:05 | INFO | __main__:test_element_error_handling:116 | ✅ 所有元素检测都没有出现错误
2025-07-11 14:31:05 | INFO | __main__:test_error_handling_fix:47 | 4️⃣ 测试输入框检测
2025-07-11 14:31:05 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1550 | 确保输入框就绪...
2025-07-11 14:31:05 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1583 | 检查已知输入框元素...
2025-07-11 14:31:05 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1587 | 找到主输入框
2025-07-11 14:31:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:06 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:31:06 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1589 | ✅ 主输入框已激活
2025-07-11 14:31:06 | INFO | __main__:test_error_handling_fix:51 | ✅ 输入框检测成功
2025-07-11 14:31:06 | INFO | __main__:test_error_handling_fix:54 | 5️⃣ 测试简单的文本输入
2025-07-11 14:31:06 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: 测试错误处理
2025-07-11 14:31:06 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 14:31:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:06 | INFO | core.base_element:clear_text:265 | 清空文本成功 [输入框]
2025-07-11 14:31:06 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:31:07 | INFO | core.base_element:clear_text:265 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:31:07 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-11 14:31:07 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:08 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:31:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:08 | INFO | core.base_element:send_keys:242 | 输入文本成功 [输入框]: 测试错误处理
2025-07-11 14:31:08 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: 测试错误处理
2025-07-11 14:31:09 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: 测试错误处理
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:09 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: 测试错误处理
2025-07-11 14:31:09 | INFO | __main__:test_error_handling_fix:56 | ✅ 文本输入成功
2025-07-11 14:31:09 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:09 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:31:10 | INFO | core.base_element:click:171 | 点击元素成功 [发送按钮]
2025-07-11 14:31:10 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:31:10 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-11 14:31:10 | INFO | __main__:test_error_handling_fix:60 | ✅ 发送功能正常
2025-07-11 14:31:10 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:31:10 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:31:10 | INFO | __main__:test_error_handling_fix:82 | ✅ 应用已停止
2025-07-11 14:31:10 | INFO | __main__:test_multiple_runs:188 | ✅ 第 2 次运行成功
2025-07-11 14:31:13 | INFO | __main__:test_multiple_runs:183 | 开始第 3 次运行...
2025-07-11 14:31:13 | INFO | __main__:test_error_handling_fix:19 | 🧪 开始测试错误处理修复
2025-07-11 14:31:13 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:31:13 | INFO | __main__:test_error_handling_fix:26 | 1️⃣ 测试应用启动
2025-07-11 14:31:13 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:31:13 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:31:17 | INFO | __main__:test_error_handling_fix:31 | ✅ Ella应用启动成功
2025-07-11 14:31:17 | INFO | __main__:test_error_handling_fix:34 | 2️⃣ 测试页面加载（错误处理改进）
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 20秒
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:31:17 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:31:18 | INFO | __main__:test_error_handling_fix:38 | ✅ 页面加载成功，没有出现Invalid version错误
2025-07-11 14:31:18 | INFO | __main__:test_error_handling_fix:43 | 3️⃣ 测试元素检测的错误处理
2025-07-11 14:31:18 | INFO | __main__:test_element_error_handling:90 | 测试元素检测的错误处理...
2025-07-11 14:31:18 | INFO | __main__:test_element_error_handling:107 |   输入框: 存在
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:107 |   文本输入框: 存在
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:107 |   语音按钮: 不存在
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:107 |   发送按钮: 存在
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:107 |   欢迎消息: 不存在
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:113 | 元素检测结果: 成功 5, 错误 0
2025-07-11 14:31:19 | INFO | __main__:test_element_error_handling:116 | ✅ 所有元素检测都没有出现错误
2025-07-11 14:31:19 | INFO | __main__:test_error_handling_fix:47 | 4️⃣ 测试输入框检测
2025-07-11 14:31:19 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1550 | 确保输入框就绪...
2025-07-11 14:31:19 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1583 | 检查已知输入框元素...
2025-07-11 14:31:19 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1587 | 找到主输入框
2025-07-11 14:31:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:20 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:31:20 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1589 | ✅ 主输入框已激活
2025-07-11 14:31:20 | INFO | __main__:test_error_handling_fix:51 | ✅ 输入框检测成功
2025-07-11 14:31:20 | INFO | __main__:test_error_handling_fix:54 | 5️⃣ 测试简单的文本输入
2025-07-11 14:31:20 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: 测试错误处理
2025-07-11 14:31:20 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 14:31:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:20 | INFO | core.base_element:clear_text:265 | 清空文本成功 [输入框]
2025-07-11 14:31:20 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:31:21 | INFO | core.base_element:clear_text:265 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:31:21 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-11 14:31:21 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:22 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:31:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:23 | INFO | core.base_element:send_keys:242 | 输入文本成功 [输入框]: 测试错误处理
2025-07-11 14:31:23 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: 测试错误处理
2025-07-11 14:31:23 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: 测试错误处理
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:31:23 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: 测试错误处理
2025-07-11 14:31:23 | INFO | __main__:test_error_handling_fix:56 | ✅ 文本输入成功
2025-07-11 14:31:23 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:31:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:31:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:31:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:31:24 | INFO | core.base_element:click:171 | 点击元素成功 [发送按钮]
2025-07-11 14:31:24 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:31:24 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-11 14:31:24 | INFO | __main__:test_error_handling_fix:60 | ✅ 发送功能正常
2025-07-11 14:31:24 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:31:24 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:31:24 | INFO | __main__:test_error_handling_fix:82 | ✅ 应用已停止
2025-07-11 14:31:24 | INFO | __main__:test_multiple_runs:188 | ✅ 第 3 次运行成功
2025-07-11 14:31:24 | INFO | __main__:test_multiple_runs:199 | 多次运行成功率: 100.0% (3/3)
2025-07-11 14:31:24 | INFO | __main__:main:220 | 📊 测试结果总结:
2025-07-11 14:31:24 | INFO | __main__:main:221 |   基本错误处理: ✅ 通过
2025-07-11 14:31:24 | INFO | __main__:main:222 |   服务健康检查: ✅ 通过
2025-07-11 14:31:24 | INFO | __main__:main:223 |   多次运行稳定性: ✅ 通过
2025-07-11 14:31:24 | INFO | __main__:main:224 |   总体通过率: 3/3
2025-07-11 14:31:24 | INFO | __main__:main:227 | 🎉 错误处理修复测试总体通过
2025-07-11 14:32:51 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:32:51 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:32:51 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:32:51 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:32:51 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:32:51 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:32:51 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:32:53 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:32:53 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 14:32:53 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 14:32:53 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:32:53 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:32:54 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 14:32:56 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:32:58 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 14:33:04 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:33:08 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:33:08 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:33:08 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:33:08 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:33:08 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 14:33:09 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4486, 'temperature': 332, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:33:09 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:33:09 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:33:09 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:33:09 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:33:09 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:33:09 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4486, 'temperature': 332, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:33:09 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:33:09 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:33:09 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:33:09 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:33:12 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:33:12 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:33:12 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:33:12 | WARNING | pages.apps.ella.main_page:_ensure_service_health:308 | UIAutomator2服务健康检查异常: Invalid version: ''
2025-07-11 14:33:12 | WARNING | pages.apps.ella.main_page:wait_for_page_load:266 | UIAutomator2服务状态异常，但继续尝试
2025-07-11 14:33:13 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:33:13 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:33:13 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:33:14 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:15 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:16 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:33:17 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:18 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:19 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:33:20 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:22 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:23 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:33:24 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:25 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:26 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:33:27 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:28 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:30 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:33:31 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:32 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:33 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:33:34 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:35 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:36 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:33:38 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:405 | ⚠️ UI元素等待超时，使用备选检测方案
2025-07-11 14:33:38 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:447 | 使用备选UI检测方案...
2025-07-11 14:33:39 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:452 | ✅ Ella应用在前台，假设UI已加载
2025-07-11 14:33:39 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2479 | 检查蓝牙状态
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2493 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:33:40 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1343 | 检查当前进程是否是Ella...
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1350 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1351 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:33:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1360 | ✅ 当前在Ella应用进程
2025-07-11 14:33:41 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:43 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:44 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:33:45 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:33:46 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:33:47 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:33:47 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1326 | 不在对话页面，尝试回到主页...
2025-07-11 14:33:47 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1487 | 方法1: 按返回键回到主页...
2025-07-11 14:33:48 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1539 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 14:33:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:33:49 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:33:49 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:33:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:33:49 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:33:49 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:33:49 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:39:08 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:39:08 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:39:08 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:39:08 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:39:08 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:39:08 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:39:08 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:39:10 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:39:10 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:39:10 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:39:10 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:39:10 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:39:10 | INFO | __main__:main:232 | 🚀 开始对话页面检测修复测试
2025-07-11 14:39:10 | INFO | __main__:test_chat_page_detection:19 | 🧪 开始测试对话页面检测修复
2025-07-11 14:39:10 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:39:10 | INFO | __main__:test_chat_page_detection:26 | 1️⃣ 启动Ella应用
2025-07-11 14:39:10 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:39:10 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:39:14 | INFO | __main__:test_chat_page_detection:31 | ✅ Ella应用启动成功
2025-07-11 14:39:14 | INFO | __main__:test_chat_page_detection:34 | 2️⃣ 等待页面加载
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:39:14 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:39:15 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:38 | ✅ 页面加载成功
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:41 | 3️⃣ 测试对话页面指示器检测
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:43 | 对话页面指示器检测结果: True
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:46 | 4️⃣ 测试ensure_on_chat_page方法
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:50 | ✅ ensure_on_chat_page 返回 True
2025-07-11 14:39:16 | INFO | __main__:test_chat_page_detection:55 | 5️⃣ 测试多次调用的稳定性
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:17 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:17 | INFO | __main__:test_chat_page_detection:64 | 第 1 次调用: 成功
2025-07-11 14:39:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:19 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:19 | INFO | __main__:test_chat_page_detection:64 | 第 2 次调用: 成功
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:20 | INFO | __main__:test_chat_page_detection:64 | 第 3 次调用: 成功
2025-07-11 14:39:21 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:21 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:22 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:22 | INFO | __main__:test_chat_page_detection:64 | 第 4 次调用: 成功
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:23 | INFO | __main__:test_chat_page_detection:64 | 第 5 次调用: 成功
2025-07-11 14:39:24 | INFO | __main__:test_chat_page_detection:70 | 多次调用成功率: 100.0% (5/5)
2025-07-11 14:39:24 | INFO | __main__:test_chat_page_detection:73 | 6️⃣ 测试在不同状态下的检测
2025-07-11 14:39:24 | INFO | __main__:test_different_states:98 | 测试在不同状态下的对话页面检测...
2025-07-11 14:39:24 | INFO | __main__:test_different_states:101 | 状态1: 当前状态
2025-07-11 14:39:24 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:25 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:25 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:25 | INFO | __main__:test_different_states:103 |   当前状态检测: 成功
2025-07-11 14:39:25 | INFO | __main__:test_different_states:106 | 状态2: 按Home键后
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:28 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:28 | WARNING | pages.apps.ella.main_page:ensure_on_chat_page:1315 | 当前不在Ella进程，尝试返回Ella
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:return_to_ella_app:1455 | 尝试返回Ella应用...
2025-07-11 14:39:28 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第1次按返回键...
2025-07-11 14:39:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:31 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:31 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第2次按返回键...
2025-07-11 14:39:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:33 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:33 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第3次按返回键...
2025-07-11 14:39:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:36 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:36 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:36 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第4次按返回键...
2025-07-11 14:39:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:38 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:38 | INFO | pages.apps.ella.main_page:return_to_ella_app:1460 | 第5次按返回键...
2025-07-11 14:39:39 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:40 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:40 | INFO | pages.apps.ella.main_page:return_to_ella_app:1470 | 尝试通过最近任务切换到Ella...
2025-07-11 14:39:40 | INFO | pages.apps.ella.main_page:_switch_to_ella_via_recent_tasks:1496 | 通过最近任务切换到Ella...
2025-07-11 14:39:42 | INFO | pages.apps.ella.main_page:_switch_to_ella_via_recent_tasks:1515 | 找到Ella任务卡片，点击切换
2025-07-11 14:39:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:45 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:45 | INFO | pages.apps.ella.main_page:_switch_to_ella_via_recent_tasks:1525 | 未找到Ella特定卡片，尝试点击任务卡片...
2025-07-11 14:39:48 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.hilauncher
2025-07-11 14:39:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.android.launcher3.uioverrides.QuickstepLauncher
2025-07-11 14:39:49 | WARNING | pages.apps.ella.main_page:ensure_ella_process:1440 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.hilauncher
2025-07-11 14:39:52 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:53 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:53 | INFO | pages.apps.ella.main_page:_switch_to_ella_via_recent_tasks:1535 | ✅ 通过第2个任务卡片切换到Ella
2025-07-11 14:39:53 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:53 | INFO | __main__:test_different_states:111 |   Home键后检测: 成功
2025-07-11 14:39:53 | INFO | __main__:test_different_states:114 | 状态3: 重新进入应用后
2025-07-11 14:39:56 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:57 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:57 | INFO | __main__:test_different_states:119 |   重新进入后检测: 成功
2025-07-11 14:39:57 | INFO | __main__:test_different_states:122 | 状态4: 点击屏幕后
2025-07-11 14:39:58 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:39:58 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:39:59 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:39:59 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:39:59 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:39:59 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:39:59 | INFO | __main__:test_different_states:128 |   点击屏幕后检测: 成功
2025-07-11 14:39:59 | INFO | __main__:test_different_states:133 | 不同状态检测总结: 4/4 成功
2025-07-11 14:40:00 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:40:00 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:40:00 | INFO | __main__:test_chat_page_detection:90 | ✅ 应用已停止
2025-07-11 14:40:00 | INFO | __main__:test_chat_page_indicators:144 | 🎯 专门测试对话页面指示器
2025-07-11 14:40:00 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:40:00 | INFO | pages.apps.ella.main_page:start_app_with_activity:128 | 启动Ella应用（指定Activity）
2025-07-11 14:40:00 | INFO | pages.apps.ella.main_page:start_app_with_activity:137 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:40:03 | INFO | pages.apps.ella.main_page:_check_app_started:195 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-11 14:40:03 | INFO | pages.apps.ella.main_page:start_app_with_activity:142 | ✅ Ella应用启动成功（指定Activity）
2025-07-11 14:40:06 | INFO | __main__:test_chat_page_indicators:156 | 测试各种对话页面指示器...
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:207 | 对话页面指示器检测结果:
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:209 |   输入框检测: ✅ 成功
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:209 |   发送按钮检测: ✅ 成功
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:209 |   语音按钮检测: ✅ 成功
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:209 |   通用UI元素检测: ✅ 成功
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:209 |   页面文本检测: ✅ 成功
2025-07-11 14:40:07 | INFO | __main__:test_chat_page_indicators:215 | 指示器检测成功率: 100.0% (5/5)
2025-07-11 14:40:07 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:40:07 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:40:07 | INFO | __main__:main:243 | 📊 测试结果总结:
2025-07-11 14:40:07 | INFO | __main__:main:244 |   基本对话页面检测: ✅ 通过
2025-07-11 14:40:07 | INFO | __main__:main:245 |   对话页面指示器: ✅ 通过
2025-07-11 14:40:07 | INFO | __main__:main:246 |   总体通过率: 2/2
2025-07-11 14:40:07 | INFO | __main__:main:249 | 🎉 对话页面检测修复测试总体通过
2025-07-11 14:41:09 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:41:09 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:41:09 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:41:09 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:41:09 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:41:09 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:41:09 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:41:11 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 14:41:11 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 14:41:11 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:41:11 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:41:11 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:41:11 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:41:11 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:41:11 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:41:12 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-11 14:41:12 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:41:12 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:41:12 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:41:12 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:41:14 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:41:14 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:41:14 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:41:15 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:41:15 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:41:15 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:41:15 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 输入框
2025-07-11 14:41:16 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:41:17 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:17 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:41:18 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 14:41:18 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 14:41:18 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-11 14:41:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:18 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:41:18 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:19 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:20 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 14:41:20 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 14:41:20 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 14:41:20 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1664 | 找到主输入框
2025-07-11 14:41:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:21 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:41:21 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1666 | ✅ 主输入框已激活
2025-07-11 14:41:21 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-11 14:41:21 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 14:41:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:23 | INFO | core.base_element:clear_text:265 | 清空文本成功 [输入框]
2025-07-11 14:41:23 | INFO | pages.apps.ella.main_page:clear_input_box:586 | 主输入框已清空
2025-07-11 14:41:23 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-07-11 14:41:23 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:23 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [文本输入框(备选)]
2025-07-11 14:41:24 | INFO | core.base_element:clear_text:265 | 清空文本成功 [文本输入框(备选)]
2025-07-11 14:41:24 | INFO | pages.apps.ella.main_page:clear_input_box:601 | 备选输入框已清空
2025-07-11 14:41:24 | INFO | pages.apps.ella.main_page:input_text_command:490 | 使用主输入框(et_input)
2025-07-11 14:41:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:26 | INFO | core.base_element:click:171 | 点击元素成功 [输入框]
2025-07-11 14:41:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:27 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:27 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:27 | INFO | core.base_element:send_keys:242 | 输入文本成功 [输入框]: open bluetooth
2025-07-11 14:41:27 | INFO | pages.apps.ella.main_page:input_text_command:502 | ✅ 通过send_keys输入成功: open bluetooth
2025-07-11 14:41:28 | INFO | pages.apps.ella.main_page:verify_input_text:619 | 验证输入文本: open bluetooth
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-11 14:41:29 | INFO | pages.apps.ella.main_page:verify_input_text:626 | ✅ 主输入框文本验证成功: open bluetooth
2025-07-11 14:41:29 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-11 14:41:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-11 14:41:30 | INFO | core.base_element:click:171 | 点击元素成功 [发送按钮]
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:send_command:662 | 使用发送按钮(fl_btn_three_btn)
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:send_command:664 | ✅ 命令发送成功
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-11 14:41:30 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 14
2025-07-11 14:41:30 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2310 | 检测到TTS播放按钮
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-11 14:41:31 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-11 14:41:34 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-11 14:41:34 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-11 14:41:34 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:35 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:35 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 13
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:_is_ai_response:2278 | 匹配到蓝牙响应模式: bluetooth.*on -> Bluetooth is turned on now.
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:get_response_text:2463 | 找到AI响应: Bluetooth is turned on now.
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:get_response_text:2476 | 获取到蓝牙相关响应: Bluetooth is turned on now.
2025-07-11 14:41:38 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Bluetooth is turned on now.'
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-11 14:41:38 | INFO | pages.apps.ella.main_page:verify_command_in_response:2622 | ✅ 响应包含蓝牙相关关键词: ['bluetooth']
2025-07-11 14:41:38 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:177 | ✅ 响应包含命令相关内容: open bluetooth
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 14:41:40 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:41:40 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-11 14:41:40 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-11 14:41:41 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-11 14:41:41 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:41:41 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:41:41 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:41:41 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:41:41 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:41:41 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:41:41 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:42:18 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:42:18 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:42:18 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:42:18 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:42:18 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:42:18 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:42:18 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:42:21 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:42:21 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 14:42:21 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 14:42:21 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:42:21 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:42:21 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 14:42:23 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:42:26 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 14:42:32 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:42:36 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:42:36 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:42:36 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:42:36 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:42:36 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 14:42:36 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 331, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:42:36 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:42:36 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:42:36 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:42:36 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:42:36 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:42:36 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4504, 'temperature': 331, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:42:36 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:42:37 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:42:37 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:42:37 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:42:39 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:42:39 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:42:39 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:42:40 | WARNING | pages.apps.ella.main_page:_ensure_service_health:308 | UIAutomator2服务健康检查异常: Invalid version: ''
2025-07-11 14:42:40 | WARNING | pages.apps.ella.main_page:wait_for_page_load:266 | UIAutomator2服务状态异常，但继续尝试
2025-07-11 14:42:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:42:40 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:42:40 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:42:41 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:42:42 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:42:44 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:42:44 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:42:46 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:42:47 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:42:48 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:42:49 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:42:51 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:42:51 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:42:53 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:42:54 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:42:55 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:43:56 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=64289): Read timed out. (read timeout=60)
2025-07-11 14:43:57 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:43:58 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 1/3): Invalid version: ''
2025-07-11 14:43:59 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:44:01 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:44:01 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:44:01 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:44:01 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:44:04 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:44:04 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:44:04 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 14:44:08 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 14:44:08 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 14:44:08 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 14:44:08 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 14:44:08 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 14:44:08 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 14:44:08 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:44:11 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:44:11 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 14:44:11 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 14:44:11 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:44:11 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:44:12 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 14:44:14 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 14:44:16 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 14:44:22 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 14:44:26 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 14:44:26 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 14:44:26 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 14:44:26 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 14:44:26 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 14:44:26 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4489, 'temperature': 331, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:44:26 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 14:44:26 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 14:44:26 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 14:44:26 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 14:44:26 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 14:44:26 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4489, 'temperature': 331, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 14:44:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 14:44:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 14:44:27 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:44:27 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 14:44:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 14:44:29 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 14:44:29 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 14:44:30 | WARNING | pages.apps.ella.main_page:_ensure_service_health:308 | UIAutomator2服务健康检查异常: Invalid version: ''
2025-07-11 14:44:30 | WARNING | pages.apps.ella.main_page:wait_for_page_load:266 | UIAutomator2服务状态异常，但继续尝试
2025-07-11 14:44:30 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 14:44:30 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 14:44:30 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 14:44:31 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:32 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:33 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:44:34 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:35 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:36 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:44:37 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:39 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:40 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:44:40 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:42 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:43 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:44:44 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:45 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:46 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:44:47 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:48 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:49 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 14:44:50 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:51 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 2/3): Invalid version: ''
2025-07-11 14:44:53 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 14:44:55 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:405 | ⚠️ UI元素等待超时，使用备选检测方案
2025-07-11 14:44:55 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:447 | 使用备选UI检测方案...
2025-07-11 14:44:55 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:452 | ✅ Ella应用在前台，假设UI已加载
2025-07-11 14:44:55 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 14:44:56 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 14:44:56 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 14:44:56 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 14:44:56 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 14:44:56 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:44:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:44:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:44:57 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:44:57 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:44:59 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:45:01 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:02 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:03 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:45:04 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:05 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:07 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 14:45:07 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:09 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:10 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 14:45:11 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:12 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:13 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 14:45:15 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1326 | 不在对话页面，尝试回到主页...
2025-07-11 14:45:15 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1564 | 方法1: 按返回键回到主页...
2025-07-11 14:45:16 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1616 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1335 | ✅ 在Ella应用中，使用宽松策略认为在对话页面
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 14:45:16 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 14:45:17 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:18 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:19 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 14:45:20 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 14:45:22 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 14:45:23 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 14:45:23 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1687 | 检查通用输入框元素...
2025-07-11 14:45:24 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1721 | 尝试通过坐标激活输入区域...
2025-07-11 14:45:25 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1752 | 尝试通过滑动激活输入框...
2025-07-11 14:45:26 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1794 | 检查可交互元素...
2025-07-11 14:45:27 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1650 | ❌ 无法确保输入框就绪
2025-07-11 14:45:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 14:45:27 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 14:45:27 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 14:45:27 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 14:45:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 14:45:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 14:45:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 15:47:29 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 15:47:29 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 15:47:29 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 15:47:29 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 15:47:29 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 15:47:29 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 15:47:29 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 15:47:31 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 15:47:31 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 15:47:31 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 15:47:31 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 15:47:31 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 15:47:32 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 15:47:34 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 15:47:36 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 15:47:42 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 15:47:46 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 15:47:46 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 15:47:46 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 15:47:46 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 15:47:46 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 15:47:47 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4484, 'temperature': 312, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 15:47:47 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 15:47:47 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 15:47:47 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 15:47:47 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 15:47:47 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 15:47:47 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4484, 'temperature': 312, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 15:47:47 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 15:47:47 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 15:47:47 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 15:47:47 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 15:47:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 15:47:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 15:47:49 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 15:47:50 | WARNING | pages.apps.ella.main_page:_ensure_service_health:308 | UIAutomator2服务健康检查异常: Invalid version: ''
2025-07-11 15:47:50 | WARNING | pages.apps.ella.main_page:wait_for_page_load:266 | UIAutomator2服务状态异常，但继续尝试
2025-07-11 15:47:50 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 15:47:50 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 15:47:50 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 15:47:51 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 15:47:53 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 15:47:54 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:47:55 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 15:47:56 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 15:47:57 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:47:58 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 15:47:59 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:00 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 15:48:01 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:02 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:04 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 15:48:05 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:06 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:07 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 15:48:08 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:09 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [Ella欢迎消息] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:10 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [Ella欢迎消息]: Invalid version: ''
2025-07-11 15:48:11 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:13 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [聊天消息列表] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:14 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [聊天消息列表]: Invalid version: ''
2025-07-11 15:48:16 | WARNING | pages.apps.ella.main_page:_wait_for_ui_elements:405 | ⚠️ UI元素等待超时，使用备选检测方案
2025-07-11 15:48:16 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:447 | 使用备选UI检测方案...
2025-07-11 15:48:17 | INFO | pages.apps.ella.main_page:_fallback_ui_detection:452 | ✅ Ella应用在前台，假设UI已加载
2025-07-11 15:48:17 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 15:48:18 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 15:48:18 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 15:48:19 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:20 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:22 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:48:23 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:24 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:25 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:48:26 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:27 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [发送按钮] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:28 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [发送按钮]: Invalid version: ''
2025-07-11 15:48:29 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:31 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音输入按钮] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:32 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音输入按钮]: Invalid version: ''
2025-07-11 15:48:32 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:34 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [语音按钮(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:35 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [语音按钮(备选)]: Invalid version: ''
2025-07-11 15:48:37 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1326 | 不在对话页面，尝试回到主页...
2025-07-11 15:48:37 | INFO | pages.apps.ella.main_page:_try_return_to_chat_page:1564 | 方法1: 按返回键回到主页...
2025-07-11 15:48:38 | ERROR | pages.apps.ella.main_page:_try_return_to_chat_page:1616 | 尝试返回对话页面失败: Invalid version: ''
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1335 | ✅ 在Ella应用中，使用宽松策略认为在对话页面
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 15:48:38 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 15:48:39 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:40 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [输入框] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:41 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [输入框]: Invalid version: ''
2025-07-11 15:48:42 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 1/3): Invalid version: ''
2025-07-11 15:48:44 | WARNING | core.base_element:is_exists:122 | 元素检查遇到可重试错误 [文本输入框(备选)] (尝试 2/3): Invalid version: ''
2025-07-11 15:48:45 | ERROR | core.base_element:is_exists:126 | 元素检查重试失败 [文本输入框(备选)]: Invalid version: ''
2025-07-11 15:48:45 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1687 | 检查通用输入框元素...
2025-07-11 15:48:46 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1721 | 尝试通过坐标激活输入区域...
2025-07-11 15:48:47 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1752 | 尝试通过滑动激活输入框...
2025-07-11 15:48:48 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1794 | 检查可交互元素...
2025-07-11 15:48:49 | ERROR | pages.apps.ella.main_page:ensure_input_box_ready:1650 | ❌ 无法确保输入框就绪
2025-07-11 15:48:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 15:48:49 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 15:48:49 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 15:48:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 15:48:49 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 15:48:49 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 15:48:49 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-11 16:48:48 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 16:48:48 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 16:48:48 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 16:48:48 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 16:48:48 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 16:48:48 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 16:48:48 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:48:52 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:48:52 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-11 16:48:52 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-11 16:48:52 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:48:52 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:48:53 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 16:48:55 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:48:57 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 16:49:03 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:49:07 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:49:07 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:49:07 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-11 16:49:07 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-11 16:49:07 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-11 16:49:08 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4482, 'temperature': 318, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 16:49:08 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 16:49:08 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 16:49:08 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-11 16:49:08 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-11 16:49:08 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-11 16:49:08 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4482, 'temperature': 318, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 16:49:08 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-11 16:49:08 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:23 | 🚀 开始启动Ella应用...
2025-07-11 16:49:08 | INFO | core.base_page:start_app:65 | 启动应用: ella (com.transsion.aivoiceassistant)
2025-07-11 16:49:08 | INFO | core.base_driver:start_app:228 | 启动应用: com.transsion.aivoiceassistant
2025-07-11 16:49:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:27 | ✅ Ella应用启动成功
2025-07-11 16:49:11 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:30 | ⏳ 等待Ella页面加载...
2025-07-11 16:49:11 | INFO | pages.apps.ella.main_page:wait_for_page_load:261 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-11 16:49:11 | WARNING | pages.apps.ella.main_page:_ensure_service_health:308 | UIAutomator2服务健康检查异常: Invalid version: ''
2025-07-11 16:49:11 | WARNING | pages.apps.ella.main_page:wait_for_page_load:266 | UIAutomator2服务状态异常，但继续尝试
2025-07-11 16:49:12 | INFO | pages.apps.ella.main_page:wait_for_page_load:271 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-11 16:49:12 | INFO | pages.apps.ella.main_page:wait_for_page_load:274 | ✅ Ella应用包已确认
2025-07-11 16:49:12 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:347 | 等待Ella页面UI元素加载...
2025-07-11 16:49:13 | WARNING | core.base_element:is_exists:122 | 检测到版本错误，尝试自动修复 [输入框]: Invalid version: ''
2025-07-11 16:49:13 | WARNING | core.base_element:_handle_version_error:184 | 检测到版本错误，尝试自动修复 [输入框]
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:fix_version_issue:192 | 修复UIAutomator2版本问题 (设备: 13764254B4001229)
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:fix_version_issue:195 | 尝试策略1: 标准重启服务
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:fix_version_issue:197 | 第 1 次尝试修复...
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:13 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 16:49:15 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:17 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 16:49:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:49:27 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:49:27 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:49:27 | INFO | utils.uiautomator2_manager:fix_version_issue:206 | 等待 2 秒后重试...
2025-07-11 16:49:29 | INFO | utils.uiautomator2_manager:fix_version_issue:197 | 第 2 次尝试修复...
2025-07-11 16:49:29 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:29 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:30 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 16:49:32 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:34 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 16:49:40 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:49:44 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:49:44 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:49:44 | INFO | utils.uiautomator2_manager:fix_version_issue:206 | 等待 4 秒后重试...
2025-07-11 16:49:48 | INFO | utils.uiautomator2_manager:fix_version_issue:197 | 第 3 次尝试修复...
2025-07-11 16:49:48 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:48 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:49 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-11 16:49:51 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 13764254B4001229)
2025-07-11 16:49:52 | INFO | utils.uiautomator2_manager:start_service:168 | UIAutomator2初始化成功
2025-07-11 16:49:58 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:50:03 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:50:03 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-11 16:50:03 | INFO | utils.uiautomator2_manager:fix_version_issue:210 | 尝试策略2: 强制清理和重新初始化
2025-07-11 16:50:03 | INFO | utils.uiautomator2_manager:_force_cleanup_and_reinit:241 | 执行强制清理和重新初始化...
2025-07-11 16:50:21 | INFO | utils.uiautomator2_manager:_force_cleanup_and_reinit:296 | 强制清理和重新初始化完成
2025-07-11 16:50:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:50:26 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: Invalid version: ''
2025-07-11 16:50:26 | INFO | utils.uiautomator2_manager:fix_version_issue:217 | 尝试策略3: 重新安装UIAutomator2
2025-07-11 16:50:26 | INFO | utils.uiautomator2_manager:_reinstall_uiautomator2:314 | 重新安装UIAutomator2...
2025-07-11 16:50:37 | INFO | utils.uiautomator2_manager:_reinstall_uiautomator2:344 | UIAutomator2重新安装完成
2025-07-11 16:50:37 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:51:15 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 16:51:15 | INFO | utils.uiautomator2_manager:fix_version_issue:220 | ✅ 重新安装修复成功
2025-07-11 16:51:15 | INFO | core.base_element:_handle_version_error:201 | 版本问题修复成功，重新获取驱动 [输入框]
2025-07-11 16:51:15 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_1229
2025-07-11 16:51:15 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-11 16:51:15 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 13764254B4001229
2025-07-11 16:51:15 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.0.3
2025-07-11 16:51:15 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-11 16:51:15 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-11 16:51:15 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-11 16:51:16 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-11 16:51:16 | INFO | core.base_driver:_ensure_uiautomator2_service:149 | ✅ UIAutomator2服务状态正常
2025-07-11 16:51:17 | INFO | core.base_driver:_connect_device:51 | 设备连接成功: {'udid': '13764254B4001229-ca:3d:07:68:68:0a-TECNO_CM8', 'version': '15', 'serial': '13764254B4001229', 'brand': 'TECNO', 'model': 'TECNO CM8', 'hwaddr': 'ca:3d:07:68:68:0a', 'sdk': 35, 'agentVersion': '0.10.0', 'display': {'width': 1080, 'height': 2400}, 'battery': {'acPowered': True, 'usbPowered': False, 'wirelessPowered': False, 'status': 2, 'health': 2, 'present': True, 'level': 100, 'scale': 100, 'voltage': 4481, 'temperature': 321, 'technology': 'Li-ion'}, 'memory': {'total': 11767992, 'around': '11 GB'}, 'cpu': {'cores': 7, 'hardware': ''}, 'arch': '', 'owner': None, 'presenceChangedAt': '0001-01-01T00:00:00Z', 'usingBeganAt': '0001-01-01T00:00:00Z', 'product': None, 'provider': None}
2025-07-11 16:51:17 | INFO | core.base_driver:_verify_device_info:129 | ✅ 设备验证通过
2025-07-11 16:51:17 | INFO | core.base_driver:_setup_implicit_wait:184 | 设置隐式等待时间: 10秒
2025-07-11 16:51:21 | INFO | pages.apps.ella.main_page:_wait_for_ui_elements:375 | ✅ 检测到页面元素: 聊天消息列表
2025-07-11 16:51:22 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:32 | ✅ Ella页面加载完成
2025-07-11 16:51:23 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 16:51:23 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 16:51:23 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:84 | 蓝牙初始状态: 开启
2025-07-11 16:51:23 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 16:51:23 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:51:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:51:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingsMainActivity
2025-07-11 16:51:24 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:51:26 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 16:51:26 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 16:51:26 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 16:51:26 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1687 | 检查通用输入框元素...
2025-07-11 16:51:27 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1721 | 尝试通过坐标激活输入区域...
2025-07-11 16:51:28 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2160)
2025-07-11 16:51:29 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2040)
2025-07-11 16:51:31 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 1920)
2025-07-11 16:51:34 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (864, 2160)
2025-07-11 16:51:36 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1752 | 尝试通过滑动激活输入框...
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1794 | 检查可交互元素...
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1805 | 找到 1 个图像按钮元素
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1647 | ✅ 检测到可交互元素，假设输入功能可用
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:execute_text_command:728 | 执行文本命令: open bluetooth
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1311 | 确保在对话页面...
2025-07-11 16:51:46 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:51:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:51:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingVoiceWakeupActivity2
2025-07-11 16:51:47 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:51:48 | INFO | pages.apps.ella.main_page:ensure_on_chat_page:1322 | ✅ 已在对话页面
2025-07-11 16:51:48 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1627 | 确保输入框就绪...
2025-07-11 16:51:48 | INFO | pages.apps.ella.main_page:_check_known_input_elements:1660 | 检查已知输入框元素...
2025-07-11 16:51:49 | INFO | pages.apps.ella.main_page:_check_generic_input_elements:1687 | 检查通用输入框元素...
2025-07-11 16:51:49 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1721 | 尝试通过坐标激活输入区域...
2025-07-11 16:51:50 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2160)
2025-07-11 16:51:51 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 2040)
2025-07-11 16:51:53 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (540, 1920)
2025-07-11 16:51:55 | INFO | pages.apps.ella.main_page:_activate_input_by_coordinates:1734 | 尝试点击坐标 (864, 2160)
2025-07-11 16:51:57 | INFO | pages.apps.ella.main_page:_activate_input_by_scrolling:1752 | 尝试通过滑动激活输入框...
2025-07-11 16:52:07 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1794 | 检查可交互元素...
2025-07-11 16:52:07 | INFO | pages.apps.ella.main_page:_check_any_interactive_elements:1805 | 找到 1 个图像按钮元素
2025-07-11 16:52:07 | INFO | pages.apps.ella.main_page:ensure_input_box_ready:1647 | ✅ 检测到可交互元素，假设输入功能可用
2025-07-11 16:52:07 | INFO | pages.apps.ella.main_page:input_text_command:483 | 输入文本命令: open bluetooth
2025-07-11 16:52:07 | INFO | pages.apps.ella.main_page:clear_input_box:580 | 清空输入框...
2025-07-11 16:52:08 | INFO | pages.apps.ella.main_page:input_text_command:566 | 尝试基于坐标的输入方案
2025-07-11 16:52:08 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1872 | 使用基于坐标的输入方案: open bluetooth
2025-07-11 16:52:08 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1885 | 点击输入区域坐标: (540, 2160)
2025-07-11 16:52:26 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1911 | 尝试使用Shell命令输入
2025-07-11 16:52:27 | INFO | pages.apps.ella.main_page:_coordinate_based_input:1915 | ✅ Shell命令输入成功: open bluetooth
2025-07-11 16:52:27 | INFO | pages.apps.ella.main_page:send_command:657 | 发送命令
2025-07-11 16:52:27 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 3秒
2025-07-11 16:52:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-07-11 16:52:31 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [发送按钮]
2025-07-11 16:52:31 | INFO | pages.apps.ella.main_page:send_command:669 | 未找到发送按钮，尝试其他发送方法
2025-07-11 16:52:31 | INFO | pages.apps.ella.main_page:send_command:673 | 尝试使用回车键发送
2025-07-11 16:52:32 | INFO | pages.apps.ella.main_page:send_command:676 | ✅ 回车键发送成功
2025-07-11 16:52:32 | INFO | pages.apps.ella.main_page:execute_text_command:748 | ✅ 文本命令执行完成
2025-07-11 16:52:32 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:113 | ✅ 成功执行命令: open bluetooth
2025-07-11 16:52:32 | INFO | pages.apps.ella.main_page:wait_for_response:1993 | 快速等待AI响应，超时时间: 8秒
2025-07-11 16:52:32 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingVoiceWakeupActivity2
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:wait_for_response:2008 | 初始元素数量: 2
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingVoiceWakeupActivity2
2025-07-11 16:52:33 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:52:39 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2069 | 检测到元素数量增加: 4 > 2
2025-07-11 16:52:42 | INFO | pages.apps.ella.main_page:_check_tts_button_appeared:2324 | 检测到播放相关按钮
2025-07-11 16:52:42 | INFO | pages.apps.ella.main_page:_quick_check_for_response:2077 | 检测到TTS按钮，表示有AI响应
2025-07-11 16:52:42 | INFO | pages.apps.ella.main_page:wait_for_response:2026 | ✅ 快速检测到AI响应
2025-07-11 16:52:45 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:141 | ✅ 收到AI响应
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:get_response_text_smart:2415 | 智能获取响应文本...
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingsMainActivity
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:52:45 | INFO | pages.apps.ella.main_page:get_response_text:2439 | 获取AI响应文本
2025-07-11 16:52:47 | INFO | pages.apps.ella.main_page:get_response_text:2455 | 页面上所有文本元素数量: 4
2025-07-11 16:52:47 | INFO | pages.apps.ella.main_page:get_response_text:2494 | 获取到最新有意义文本: Digital assistant
2025-07-11 16:52:47 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:169 | AI响应内容: 'Digital assistant'
2025-07-11 16:52:47 | INFO | pages.apps.ella.main_page:verify_command_in_response:2592 | 验证响应是否包含命令: open bluetooth
2025-07-11 16:52:47 | WARNING | pages.apps.ella.main_page:verify_command_in_response:2695 | ⚠️ 响应包含的关键词不足: []
2025-07-11 16:52:47 | WARNING | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:179 | ⚠️ 响应未包含命令相关内容，但继续测试: open bluetooth
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status_smart:2391 | 智能检查蓝牙状态...
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1420 | 检查当前进程是否是Ella...
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1427 | 当前应用: com.transsion.aivoiceassistant
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1428 | 当前Activity: com.transsion.ella.pages.settings.SettingsMainActivity
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:ensure_ella_process:1437 | ✅ 当前在Ella应用进程
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2556 | 检查蓝牙状态
2025-07-11 16:52:49 | INFO | pages.apps.ella.main_page:check_bluetooth_status:2570 | 蓝牙状态: 开启 (值: 1)
2025-07-11 16:52:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:189 | 蓝牙最终状态: 开启
2025-07-11 16:52:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:200 | ✅ 蓝牙已成功开启
2025-07-11 16:52:49 | INFO | testcases.test_ella.test_bluetooth_command:test_open_bluetooth_command:220 | 🎉 open bluetooth命令测试完成
2025-07-11 16:52:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:62 | 🧹 清理Ella应用...
2025-07-11 16:52:49 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-11 16:52:49 | INFO | core.base_driver:stop_app:242 | 停止应用: com.transsion.aivoiceassistant
2025-07-11 16:52:49 | INFO | testcases.test_ella.test_bluetooth_command:ella_app:64 | ✅ Ella应用已停止
2025-07-11 16:52:49 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-11 16:52:49 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-11 16:52:49 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
