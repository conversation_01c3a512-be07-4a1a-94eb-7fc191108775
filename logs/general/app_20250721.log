2025-07-21 15:01:03 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-21 15:01:03 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-21 15:01:03 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-21 15:01:03 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-21 15:01:03 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-21 15:01:03 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-21 15:01:03 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-21 15:01:14 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 14016254CJ000032 not online
2025-07-21 15:01:14 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-21 15:01:14 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-21 15:01:14 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:01:14 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:01:15 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-21 15:01:17 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:01:34 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-21 15:01:44 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 14016254CJ000032 not online
2025-07-21 15:01:44 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-21 15:01:44 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-21 15:01:44 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-21 15:01:44 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-21 15:01:55 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 14016254CJ000032 not online
2025-07-21 15:02:14 | INFO | core.base_driver:_load_device_config:93 | 加载设备配置: tecno_tecno_cm8_0032
2025-07-21 15:02:14 | INFO | core.base_driver:_load_device_config:94 |   设备名称: TECNO CM8
2025-07-21 15:02:14 | INFO | core.base_driver:_load_device_config:95 |   设备ID: 14016254CJ000032
2025-07-21 15:02:14 | INFO | core.base_driver:_load_device_config:96 |   HiOS版本: 15.1.2
2025-07-21 15:02:14 | INFO | core.base_driver:_connect_device:41 | 正在连接指定设备: TECNO CM8 (14016254CJ000032)
2025-07-21 15:02:14 | INFO | core.base_driver:_ensure_uiautomator2_service:142 | 检查UIAutomator2服务状态...
2025-07-21 15:02:14 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-21 15:02:25 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 14016254CJ000032 not online
2025-07-21 15:02:25 | WARNING | core.base_driver:_ensure_uiautomator2_service:146 | UIAutomator2服务状态异常，尝试重启...
2025-07-21 15:02:25 | INFO | core.base_driver:_restart_uiautomator2_service:162 | 重启UIAutomator2服务...
2025-07-21 15:02:25 | INFO | utils.uiautomator2_manager:restart_service:71 | 重启UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:02:25 | INFO | utils.uiautomator2_manager:stop_service:109 | 停止UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:02:25 | INFO | utils.uiautomator2_manager:stop_service:134 | UIAutomator2服务已停止
2025-07-21 15:02:27 | INFO | utils.uiautomator2_manager:start_service:152 | 启动UIAutomator2服务 (设备: 14016254CJ000032)
2025-07-21 15:02:44 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 14016254CJ000032)
2025-07-21 15:02:54 | WARNING | utils.uiautomator2_manager:check_service_health:57 | UIAutomator2服务健康检查失败: device 14016254CJ000032 not online
2025-07-21 15:02:54 | ERROR | utils.uiautomator2_manager:restart_service:88 | 服务重启后健康检查失败
2025-07-21 15:02:54 | ERROR | core.base_driver:_restart_uiautomator2_service:168 | ❌ UIAutomator2服务重启失败
2025-07-21 15:02:54 | ERROR | core.base_driver:_restart_uiautomator2_service:172 | 重启UIAutomator2服务失败: UIAutomator2服务重启失败
2025-07-21 15:02:54 | WARNING | core.base_driver:_ensure_uiautomator2_service:152 | 检查UIAutomator2服务状态失败: UIAutomator2服务重启失败
2025-07-21 15:03:04 | ERROR | core.base_driver:_connect_device:60 | 设备连接失败: device 14016254CJ000032 not online
2025-07-21 15:15:21 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 15:15:21 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 15:15:21 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 15:15:21 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 15:15:25 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 15:15:25 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 15:15:25 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 15:15:25 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:15:25 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 15:15:25 | INFO | core.base_driver:_discover_and_update_current_device:348 | ✅ 已将设备 'current_device' 设置为当前设备
2025-07-21 15:15:25 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 15:15:25 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 15:15:25 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 15:15:25 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 15:15:25 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:15:25 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 15:15:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 15:15:25 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:15:25 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 15:15:26 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:15:26 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 15:15:26 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:15:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 15:15:26 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 15:15:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:15:30 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 15:15:30 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 15:15:30 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 15:15:30 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-21 15:15:30 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:15:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 15:15:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 15:15:31 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 15:15:31 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 15:15:32 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 15:15:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:15:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:15:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:15:33 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:15:33 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:15:33 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:15:34 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 15:15:34 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 15:15:34 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 15:15:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 15:15:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:15:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 15:15:34 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 15:15:34 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 15:15:34 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 15:15:34 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 15:15:34 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 15:15:35 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 15:15:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:15:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:15:35 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 15:15:37 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: 好的 -> 好的，为你跳转到设置页面
2025-07-21 15:15:37 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: 好的，为你跳转到设置页面
2025-07-21 15:15:37 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '好的，为你跳转到设置页面'
2025-07-21 15:15:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:15:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.settings.bluetooth
2025-07-21 15:15:40 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: .ui.BluetoothSettingsActivity
2025-07-21 15:15:40 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:128 | ❌ 当前不在Ella应用进程，当前包名: com.transsion.settings.bluetooth
2025-07-21 15:15:40 | WARNING | pages.apps.ella.main_page_refactored:check_bluetooth_status_smart:404 | 检查蓝牙状态时不在Ella进程，尝试返回
2025-07-21 15:15:40 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-21 15:15:40 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:15:42 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 15:15:42 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 15:15:43 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 15:15:43 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 15:16:07 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 15:16:07 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 15:16:07 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 15:16:07 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 15:16:10 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 15:16:10 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 15:16:10 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 15:16:10 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:16:10 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 15:16:10 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 15:16:10 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 15:16:10 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 15:16:10 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 15:16:10 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 15:16:10 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:16:10 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 15:16:10 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 15:16:11 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:16:11 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 15:16:11 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:16:11 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 15:16:11 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:16:12 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:16:12 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 15:16:12 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 15:16:12 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:16:15 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 15:16:15 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 15:16:15 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 15:16:15 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-21 15:16:16 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:16:16 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 15:16:16 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 15:16:16 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 15:16:16 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 15:16:17 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 15:16:17 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:16:17 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:16:18 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:16:18 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:16:18 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:16:18 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:16:19 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 15:16:19 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 15:16:19 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 15:16:19 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 15:16:19 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:16:19 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 15:16:19 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 15:16:19 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 15:16:19 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 15:16:19 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 15:16:19 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-21 15:16:20 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 15:16:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:16:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:16:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:16:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:16:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:16:21 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:16:21 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 15:16:23 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 15:16:23 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 15:16:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 15:16:26 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-21 15:16:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 15:16:27 | INFO | testcases.test_ella.base_ella_test:simple_command_test:195 | 🎉 open bluetooth 测试完成
2025-07-21 15:16:27 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 15:16:27 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 15:19:48 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 15:19:48 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 15:19:48 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 15:19:48 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 15:19:51 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 15:19:51 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 15:19:51 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 15:19:51 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:19:51 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 15:19:51 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 15:19:51 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 15:19:51 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 15:19:51 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 15:19:51 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 15:19:51 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 15:19:51 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 15:19:51 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 15:19:52 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:19:52 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 15:19:52 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:19:52 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 15:19:52 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:19:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:19:53 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 15:19:53 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 15:19:53 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:19:56 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 15:19:56 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 15:19:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 15:19:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-21 15:19:57 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-21 15:19:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 15:19:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 15:19:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 15:19:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 15:19:58 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{a2fd2cb #56 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{b903aaf u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t56}
    * Hist  #0: 
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 15:19:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:19:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:19:59 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 15:19:59 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:19:59 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:19:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 15:20:00 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 15:20:00 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 15:20:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 15:20:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 15:20:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 15:20:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 15:20:00 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 15:20:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 15:20:00 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 15:20:00 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 15:20:03 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 15:20:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:20:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:20:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.sh.smart.caller
2025-07-21 15:20:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-21 15:20:04 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:128 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-21 15:20:04 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-21 15:20:04 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-21 15:20:04 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-21 15:20:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:20:06 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-21 15:20:06 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 15:20:06 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:20:06 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:20:06 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: done -> Done!
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 15:20:07 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{a2fd2cb #56 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{b903aaf u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t56}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumed
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-21 15:20:08 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:34 | ✅ 响应包含Done: Done!
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:38 | ✅ Dalier应用已成功打开
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:48 | 🎉 open contact 测试完成 - 响应包含Done且Dalier应用已打开
2025-07-21 15:20:08 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 15:20:08 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 19:16:21 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:16:21 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:16:21 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:16:21 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:16:24 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:16:24 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:16:24 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:16:24 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:16:24 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:16:24 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:16:24 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:16:24 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:16:24 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:16:24 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:16:24 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:16:24 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:16:24 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:16:25 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:16:25 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:16:25 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:16:25 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:16:25 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:16:25 | INFO | __main__:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 19:16:25 | INFO | __main__:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-21 19:16:33 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:16:33 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:16:33 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:16:33 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:16:36 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:16:36 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:16:36 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:16:36 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:16:36 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:16:36 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:16:36 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:16:36 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:16:36 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:16:36 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:16:36 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:16:36 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:16:36 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:16:36 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:16:36 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:16:37 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:16:37 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:16:37 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:16:37 | INFO | __main__:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 19:16:37 | INFO | __main__:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 19:19:20 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:19:20 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:19:20 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:19:20 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:19:23 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:19:23 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:19:23 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:19:23 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:19:23 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:19:23 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:19:23 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:19:23 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:19:23 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:19:23 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:19:23 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:19:23 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:19:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:19:24 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:19:24 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:19:24 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:19:24 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:19:24 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:19:24 | INFO | __main__:clear_all_alarms:280 | 清除所有闹钟
2025-07-21 19:19:24 | INFO | __main__:clear_all_alarms:298 | 闹钟清除成功
2025-07-21 19:32:22 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:32:22 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:32:22 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:32:22 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:32:25 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:32:25 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:32:25 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:32:25 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:32:25 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:32:25 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:32:25 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:32:25 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:32:25 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:32:25 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:32:25 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:32:25 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:32:25 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:32:26 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:32:26 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:32:26 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:32:26 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:32:26 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:32:26 | INFO | __main__:get_alarm_list:216 | 获取闹钟列表
2025-07-21 19:32:26 | INFO | __main__:get_alarm_list:232 | 找到 5 个闹钟
2025-07-21 19:40:54 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:40:54 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:40:55 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:40:55 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:40:58 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:40:58 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:40:58 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:40:58 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:40:58 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:40:58 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:40:58 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:40:58 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:40:58 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:40:58 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:40:58 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:40:58 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:40:58 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:40:58 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:40:58 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:40:59 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:40:59 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:40:59 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:40:59 | INFO | __main__:get_alarm_list:216 | 获取闹钟列表
2025-07-21 19:40:59 | INFO | __main__:get_alarm_list:232 | 找到 5 个闹钟
2025-07-21 19:44:19 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 19:44:19 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 19:44:19 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 19:44:19 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 19:44:22 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 19:44:22 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 19:44:22 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 19:44:22 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:44:22 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 19:44:22 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 19:44:22 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 19:44:22 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 19:44:22 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 19:44:22 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 19:44:22 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 19:44:22 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 19:44:22 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 19:44:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:44:23 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 19:44:23 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:44:23 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 19:44:23 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:44:24 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 19:44:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 19:44:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 19:44:27 | INFO | pages.apps.ella.ella_status_checker:check_app_started:190 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 19:44:27 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 19:44:27 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 19:44:27 | INFO | pages.apps.ella.ella_status_checker:check_service_health:147 | 检查UIAutomator2服务健康状态
2025-07-21 19:44:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:163 | ✅ UIAutomator2服务健康状态良好
2025-07-21 19:44:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 19:44:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 19:44:28 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 19:44:28 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 19:44:29 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 19:44:29 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 19:44:29 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 19:44:29 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 19:44:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 19:44:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 19:44:30 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 19:44:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 19:44:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 19:44:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 19:44:30 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 19:44:31 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 19:44:31 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 19:44:31 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 19:44:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 19:44:32 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 19:44:32 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 19:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 19:44:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 19:44:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 19:44:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 19:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 19:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 19:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 19:44:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 19:44:33 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 19:44:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 19:44:47 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 19:44:47 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:108 | 检查当前进程是否是Ella...
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:115 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:125 | ✅ 当前在Ella应用进程
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 19:44:50 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-21 19:44:50 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 19:44:51 | INFO | testcases.test_ella.base_ella_test:simple_command_test:195 | 🎉 open bluetooth 测试完成
2025-07-21 19:44:51 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 19:44:51 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 19:52:24 | INFO | __main__:generate_allure_report:99 | 生成Allure报告
2025-07-21 19:52:24 | ERROR | __main__:generate_allure_report:123 | Allure命令未找到，请确保已安装Allure并添加到PATH
2025-07-21 20:05:29 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:05:29 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:05:29 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:05:29 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:05:34 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:05:34 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:05:34 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:05:34 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:05:34 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:05:34 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:05:34 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:05:34 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:05:34 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:05:34 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:05:34 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:05:34 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:05:34 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:05:34 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:05:34 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:05:35 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:05:35 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:05:35 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:05:35 | INFO | __main__:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 20:05:35 | INFO | __main__:check_bluetooth_status:43 | 蓝牙状态: 开启 (值: 1)
2025-07-21 20:06:17 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:06:17 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:06:17 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:06:17 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:06:22 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:06:22 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:06:22 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:06:22 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:06:22 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:06:22 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:06:22 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:06:22 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:06:22 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:06:22 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:06:22 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:06:22 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:06:22 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:06:22 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:06:22 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:06:23 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:06:23 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:06:23 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:06:23 | INFO | __main__:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 20:06:23 | INFO | __main__:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 20:06:23 | INFO | __main__:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 20:06:34 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:06:34 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:06:34 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:06:34 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:06:37 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:06:37 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:06:37 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:06:37 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:06:37 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:06:37 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:06:37 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:06:37 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:06:37 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:06:37 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:06:37 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:06:37 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:06:37 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:06:38 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:06:38 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:06:38 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:06:38 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:06:38 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:06:38 | INFO | __main__:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 20:06:38 | INFO | __main__:check_bluetooth_status:42 | 蓝牙状态: (值: 0)
2025-07-21 20:06:38 | INFO | __main__:check_bluetooth_status:44 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 20:17:56 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:17:56 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:17:56 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:17:56 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:17:59 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:17:59 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:17:59 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:17:59 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:17:59 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:17:59 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:17:59 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:17:59 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:17:59 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:17:59 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:17:59 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:17:59 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:17:59 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:17:59 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:17:59 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:18:00 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:00 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:18:00 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:00 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:18:00 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:18:01 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:04 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:18:04 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:18:04 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:18:04 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:18:04 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:18:05 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:18:05 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:18:05 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:18:05 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:18:06 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{5528d5 #60 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{2351d94 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t60}
    * Hist  #0: A
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:18:06 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:07 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:18:07 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:18:07 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:18:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:18:07 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:18:08 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:18:08 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:18:08 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:08 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:18:08 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:18:08 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:18:08 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:18:08 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:18:11 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:18:11 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:11 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:12 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.sh.smart.caller
2025-07-21 20:18:12 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-21 20:18:12 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:129 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-21 20:18:12 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-21 20:18:12 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-21 20:18:12 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:14 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-21 20:18:14 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:18:14 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:14 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:15 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:15 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:15 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: done -> Done!
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{5528d5 #60 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{2351d94 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t60}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedA
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-21 20:18:16 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:18:16 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:175 | ✅ 响应包含期望内容: done
2025-07-21 20:18:17 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:18:17 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:18:40 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:18:40 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:18:40 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:18:40 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:18:43 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:18:43 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:18:43 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:18:43 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:18:43 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:18:43 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:18:43 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:18:43 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:18:43 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:18:43 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:18:43 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:18:43 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:18:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:18:44 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:18:44 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:18:44 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:44 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:18:44 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:18:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:45 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:18:45 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:18:45 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:48 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:18:48 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:18:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:18:48 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:18:49 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:18:49 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:18:49 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:18:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:18:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:18:50 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:18:50 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{522e7a1 #62 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{c328e25 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t62}
    * Hist  #0: 
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:18:51 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:51 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:18:51 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:18:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:18:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:18:51 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:18:52 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:18:52 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:18:52 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:18:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:18:52 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:18:52 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:18:52 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:18:52 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:18:55 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:18:55 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:55 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:56 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:18:56 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:56 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:18:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:57 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:57 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:18:58 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:18:58 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:18:58 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:18:58 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:18:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{522e7a1 #62 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{c328e25 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t62}
    * Hist  #0: 
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 20:18:59 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:18:59 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:178 | ⚠️ 响应未包含期望内容: done111111
2025-07-21 20:18:59 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:18:59 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:19:38 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:19:38 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:19:38 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:19:38 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:19:41 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:19:41 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:19:41 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:19:41 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:19:41 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:19:41 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:19:41 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:19:41 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:19:41 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:19:41 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:19:41 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:19:41 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:19:41 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:19:42 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:19:42 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:19:42 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:19:42 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:19:42 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:19:42 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:19:43 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:19:43 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:46 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:19:46 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:19:46 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:19:46 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:19:47 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:19:47 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:19:47 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:19:47 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:19:47 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:19:48 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{55c386e #63 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{fae7c22 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t63}
    * Hist  #0: 
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:19:48 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:19:48 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:19:48 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:19:49 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:19:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:19:49 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:19:49 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:19:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:19:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:19:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:19:50 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:19:50 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:19:50 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:19:50 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:19:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:19:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:19:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:19:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:19:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:19:54 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:228 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{55c386e #63 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{fae7c22 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t63}
    * Hist  #0: 
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 20:19:56 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:19:56 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:178 | ⚠️ 响应未包含期望内容: done
2025-07-21 20:19:57 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:19:57 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:22:47 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:22:47 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:22:47 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:22:47 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:22:50 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:22:50 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:22:50 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:22:50 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:22:50 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:22:50 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:22:50 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:22:50 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:22:50 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:22:50 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:22:50 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:22:50 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:22:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:22:51 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:22:51 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:22:51 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:22:51 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:22:51 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:22:52 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:22:52 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:22:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:22:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:22:55 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:22:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:22:55 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:22:55 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:22:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:22:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:22:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:22:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:22:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:22:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:22:57 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{8015ea5 #64 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{80dce9 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t64}
    * Hist  #0: A
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:22:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:22:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:22:58 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:22:58 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:22:58 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:22:58 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:22:59 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:22:59 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:22:59 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:22:59 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:22:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:23:00 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:23:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:23:00 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:23:00 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:23:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:23:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:23:03 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:23:03 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:23:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:23:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:23:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:23:04 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:346 | 按返回键回到主页...
2025-07-21 20:23:06 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:352 | ✅ 通过返回键回到对话页面
2025-07-21 20:23:06 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:269 | ✅ 成功返回到对话页面
2025-07-21 20:23:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:23:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:23:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:23:06 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:23:06 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:215 | 匹配到完成响应模式: done -> Done!
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{8015ea5 #64 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{80dce9 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t64}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedA
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-21 20:23:08 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:23:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:23:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:176 | ✅ 响应包含期望内容: done
2025-07-21 20:23:09 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:23:09 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:28:28 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:28:28 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:28:28 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:28:28 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:28:31 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:28:31 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:28:31 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:28:31 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:28:31 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:28:31 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:28:31 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:28:31 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:28:31 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:28:31 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:28:31 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:28:31 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:28:31 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:28:32 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:28:32 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:28:32 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:28:32 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:28:32 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:28:32 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:28:32 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:28:33 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:36 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:28:36 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:28:36 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:28:36 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:28:36 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:28:37 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:28:37 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:28:37 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:28:37 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:28:38 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{4a83648 #66 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{2f6705c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t66}
    * Hist  #0: 
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:28:38 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:28:38 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:28:38 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:28:39 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:28:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:28:39 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:28:39 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:28:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:28:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:28:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:28:40 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:28:40 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:28:40 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:28:40 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:28:43 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:28:43 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:28:43 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:28:43 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:28:44 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.sh.smart.caller
2025-07-21 20:28:44 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-21 20:28:44 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:129 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-21 20:28:44 | WARNING | pages.apps.ella.main_page_refactored:get_response_text_smart:430 | 获取响应文本时不在Ella进程，尝试返回
2025-07-21 20:28:44 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-21 20:28:44 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:28:46 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:28:46 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:112 | 从TextView元素获取响应
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:221 | 匹配到完成响应模式: done -> Done!
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:117 | 从TextView获取响应: Done!
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:28:47 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{4a83648 #66 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{2f6705c u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t66}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumed
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-21 20:28:48 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:28:48 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:28:48 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:176 | ✅ 响应包含期望内容: done
2025-07-21 20:28:48 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:28:48 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:30:10 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:30:10 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:30:10 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:30:10 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:30:13 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:30:13 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:30:13 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:30:13 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:30:13 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:30:13 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:30:13 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:30:13 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:30:13 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:30:13 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:30:13 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:30:13 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:30:13 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:30:13 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:30:13 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:30:14 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:30:14 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:30:14 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:30:14 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:30:14 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:30:14 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:18 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:30:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:30:18 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:30:18 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:30:18 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:30:19 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:30:19 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:30:19 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:30:19 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:30:20 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{f005486 #68 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{b6086ba u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t68}
    * Hist  #0: 
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-21 20:30:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:30:20 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:30:20 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:30:21 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:30:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:30:21 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:30:21 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:30:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:30:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:30:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:30:22 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:30:22 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:30:22 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:30:22 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-21 20:30:25 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-21 20:30:25 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:30:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.sh.smart.caller
2025-07-21 20:30:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-21 20:30:25 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:129 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-21 20:30:25 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-21 20:30:25 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-21 20:30:25 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-21 20:30:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:30:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:30:27 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-21 20:30:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-21 20:30:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:30:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:30:28 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:112 | 从TextView元素获取响应
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:221 | 匹配到完成响应模式: done -> Done!
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:117 | 从TextView获取响应: Done!
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: Done!
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:30:29 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{f005486 #68 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{b6086ba u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t68}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumed
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:153 | === 尝试宽松匹配 ===
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:159 | ✅ 通过宽松匹配检测到 caller
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-21 20:30:30 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-21 20:30:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:30:30 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:179 | ⚠️ 响应未包含期望内容: done11111
2025-07-21 20:30:30 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:30:30 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:55:13 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 20:55:13 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 20:55:13 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 20:55:13 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 20:55:16 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 20:55:16 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 20:55:16 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 20:55:16 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:55:16 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 20:55:16 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 20:55:16 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 20:55:16 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 20:55:16 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 20:55:16 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 20:55:16 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 20:55:16 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 20:55:16 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 20:55:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:55:17 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 20:55:17 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:55:17 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 20:55:17 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:55:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:55:18 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 20:55:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 20:55:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:55:21 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 20:55:21 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 20:55:21 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 20:55:21 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 20:55:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 20:55:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 20:55:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 20:55:22 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 20:55:22 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 20:55:23 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:55:23 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 20:55:23 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 0)
2025-07-21 20:55:23 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 关闭 (值: 0)
2025-07-21 20:55:23 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: False
2025-07-21 20:55:23 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:55:23 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:55:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 20:55:24 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:55:24 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:55:24 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 20:55:25 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 20:55:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 20:55:25 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 20:55:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 20:55:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 20:55:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 20:55:25 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 20:55:25 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 20:55:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 20:55:25 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 20:55:25 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 20:55:26 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 20:55:26 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:55:26 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_response_handler:get_response_text:82 | 获取AI响应文本
2025-07-21 20:55:26 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:112 | 从TextView元素获取响应
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:128 | 查找RecyclerView中的最新消息
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:234 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:153 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:get_response_text:95 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:55:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 20:55:31 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 20:55:32 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:simple_command_test:241 | 🎉 open bluetooth 测试完成
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'done'
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:213 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | 缺失内容: ['done']
2025-07-21 20:55:32 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 20:55:32 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 21:06:48 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 21:06:48 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 21:06:48 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 21:06:48 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 21:06:51 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 21:06:51 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 21:06:51 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 21:06:51 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 21:06:51 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 21:06:51 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 21:06:51 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 21:06:51 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 21:06:51 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 21:06:51 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 21:06:51 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 21:06:51 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 21:06:51 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 21:06:52 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 21:06:52 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 21:06:52 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 21:06:52 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 21:06:52 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 21:06:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 21:06:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 21:06:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 21:06:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 21:06:53 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 21:06:53 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 21:06:53 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:06:56 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 21:06:56 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 21:06:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 21:06:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 21:06:57 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 21:06:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 21:06:57 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 21:06:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 21:06:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 21:06:58 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 21:06:58 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 21:06:58 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 21:06:58 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 21:06:58 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: True
2025-07-21 21:06:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 21:06:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:06:59 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 21:06:59 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 21:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 21:06:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:06:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 21:06:59 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 21:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 21:07:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:07:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 21:07:00 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 21:07:00 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 21:07:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 21:07:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 21:07:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:07:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 21:07:00 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 21:07:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 21:07:00 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 21:07:00 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 21:07:00 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 21:07:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 21:07:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:07:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 21:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:143 | 从robot_text子节点获取文本: Bluetooth is turned on now.
2025-07-21 21:07:02 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Bluetooth is turned on now.
2025-07-21 21:07:02 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: 'Bluetooth is turned on now.'
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 21:07:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 21:07:05 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: True -> True
2025-07-21 21:07:06 | INFO | testcases.test_ella.base_ella_test:simple_command_test:241 | 🎉 open bluetooth 测试完成
2025-07-21 21:07:06 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: bluetooth is turned on now.
2025-07-21 21:07:06 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:203 | ✅ 响应包含期望内容: 'bluetooth'
2025-07-21 21:07:06 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:203 | ✅ 响应包含期望内容: 'is turned on now'
2025-07-21 21:07:06 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:211 | 🎉 所有期望内容都已找到 (2/2)
2025-07-21 21:07:06 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 21:07:06 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 21:07:06 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 21:07:06 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 21:07:06 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 21:12:17 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 21:12:17 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 21:12:17 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 21:12:17 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 21:12:20 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 21:12:20 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 21:12:20 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 21:12:20 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 21:12:20 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 21:12:20 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 21:12:20 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 21:12:20 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 21:12:20 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 21:12:20 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 21:12:20 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 21:12:20 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 21:12:20 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 21:12:21 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 21:12:21 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 21:12:21 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 21:12:21 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 21:12:21 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 21:12:21 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 21:12:21 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 21:12:21 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 21:12:22 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 21:12:22 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 21:12:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 21:12:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:12:25 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 21:12:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 21:12:25 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 21:12:25 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 21:12:26 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 21:12:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 21:12:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 21:12:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 21:12:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 21:12:27 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 21:12:27 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app打开应用，状态: None
2025-07-21 21:12:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 21:12:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:12:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:12:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:12:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:12:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open app
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 21:12:28 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open app
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 21:12:28 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:12:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 21:12:29 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open app
2025-07-21 21:12:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 21:12:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 21:12:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 21:12:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 21:12:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 21:12:29 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 21:12:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 21:12:29 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 21:12:29 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open app
2025-07-21 21:12:29 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 21:12:30 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 21:12:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:12:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 21:12:30 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 21:12:31 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:157 | 从TextView子节点收集文本: open app
2025-07-21 21:12:31 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: open app
2025-07-21 21:12:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: 'open app'
2025-07-21 21:12:34 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: None -> None
2025-07-21 21:12:34 | INFO | testcases.test_ella.base_ella_test:simple_command_test:241 | 🎉 open app 测试完成
2025-07-21 21:12:34 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: open app
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'YouTube'
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'Instagram'
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'Visha'
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'which app should i open'
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:213 | ❌ 部分期望内容未找到 (0/4)
2025-07-21 21:12:34 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | 缺失内容: ['YouTube', 'Instagram', 'Visha', 'which app should i open']
2025-07-21 21:12:34 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 21:12:34 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 21:12:34 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 21:12:34 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 21:12:34 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:04:14 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:04:14 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:04:14 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:04:14 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:04:17 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:04:17 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:04:17 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:04:17 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:04:17 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:04:17 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:04:17 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:04:17 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:04:17 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:04:17 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:04:17 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:04:17 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:04:17 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:04:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:04:17 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:04:18 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:04:18 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:04:18 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:04:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:04:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:04:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:04:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:04:18 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:04:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:04:18 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:04:22 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:04:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:04:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:04:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 22:04:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:04:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:04:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:04:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:04:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:04:24 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:04:24 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app打开应用，状态: None
2025-07-21 22:04:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:04:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:04:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:04:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:04:24 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:04:24 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:04:24 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open app
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open app
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:04:25 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:04:25 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:04:25 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open app
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:04:25 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:04:26 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:04:26 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:04:26 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:04:26 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:04:26 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:04:26 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:04:26 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open app
2025-07-21 22:04:26 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 22:04:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:04:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:04:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:135 | 从check_area直接获取文本: Which app should I open?
2025-07-21 22:04:27 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Which app should I open?
2025-07-21 22:04:27 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: 'Which app should I open?'
2025-07-21 22:04:30 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: None -> None
2025-07-21 22:04:31 | INFO | testcases.test_ella.base_ella_test:simple_command_test:243 | 🎉 open app 测试完成
2025-07-21 22:04:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: which app should i open?
2025-07-21 22:04:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:203 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-21 22:04:31 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:211 | 🎉 所有期望内容都已找到 (1/1)
2025-07-21 22:04:31 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:04:31 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:04:31 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:04:31 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:04:31 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:06:48 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:06:48 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:06:48 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:06:48 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:06:50 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:06:50 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:06:50 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:06:50 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:06:50 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:06:50 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:06:50 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:06:50 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:06:50 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:06:50 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:06:50 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:06:50 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:06:50 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:06:51 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:06:51 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:06:52 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:06:52 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:06:52 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:06:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:06:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:06:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:06:52 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:06:52 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:06:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:06:52 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:06:56 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:06:56 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:06:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:06:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 22:06:56 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:06:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:06:56 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:06:56 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:06:57 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:06:58 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:06:58 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella打开应用，状态: None
2025-07-21 22:06:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:06:58 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open ella
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:06:58 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open ella
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:06:59 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:06:59 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:06:59 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open ella
2025-07-21 22:06:59 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:06:59 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:07:00 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:07:00 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:07:00 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:07:00 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:07:00 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:07:00 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:07:00 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open ella
2025-07-21 22:07:00 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 22:07:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:07:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:07:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:135 | 从check_area直接获取文本: Done!
2025-07-21 22:07:01 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-21 22:07:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: 'Done!'
2025-07-21 22:07:04 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: None -> None
2025-07-21 22:07:05 | INFO | testcases.test_ella.base_ella_test:simple_command_test:243 | 🎉 open ella 测试完成
2025-07-21 22:07:05 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: done!
2025-07-21 22:07:05 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'Done'
2025-07-21 22:07:05 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 22:07:05 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:215 | 缺失内容: ['Done']
2025-07-21 22:07:05 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:07:05 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:07:05 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:07:05 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:07:05 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:09:24 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:09:24 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:09:24 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:09:24 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:09:26 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:09:26 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:09:26 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:09:26 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:09:26 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:09:26 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:09:26 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:09:26 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:09:26 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:09:26 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:09:26 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:09:26 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:09:26 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:09:27 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:09:27 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:09:28 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:09:28 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:09:28 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:09:28 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:09:28 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:09:28 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:09:28 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:09:28 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:09:28 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:09:28 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:09:31 | INFO | pages.apps.ella.ella_status_checker:check_app_started:191 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:09:31 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:09:31 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:09:31 | INFO | pages.apps.ella.ella_status_checker:check_service_health:148 | 检查UIAutomator2服务健康状态
2025-07-21 22:09:32 | INFO | pages.apps.ella.ella_status_checker:check_service_health:164 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:09:32 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:09:32 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:09:32 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:09:32 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:09:33 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:09:33 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open ella打开应用，状态: None
2025-07-21 22:09:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:09:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:09:34 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open ella
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:09:34 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open ella
2025-07-21 22:09:34 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:09:34 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:09:34 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:09:34 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:09:35 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open ella
2025-07-21 22:09:35 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:09:35 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:09:35 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:09:35 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:09:35 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:09:35 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:09:35 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open ella
2025-07-21 22:09:35 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 22:09:36 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:09:36 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:09:36 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:109 | 检查当前进程是否是Ella...
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:116 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:117 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:126 | ✅ 当前在Ella应用进程
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:09:36 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:216 | 从TextView元素获取响应
2025-07-21 22:09:38 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:232 | 查找RecyclerView中的最新消息
2025-07-21 22:09:38 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:338 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:09:38 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:257 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:09:38 | INFO | pages.apps.ella.ella_response_handler:get_response_text:102 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:09:38 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 22:09:41 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: None -> None
2025-07-21 22:09:42 | INFO | testcases.test_ella.base_ella_test:simple_command_test:243 | 🎉 open ella 测试完成
2025-07-21 22:09:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:09:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'Done'
2025-07-21 22:09:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 22:09:42 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:215 | 缺失内容: ['Done']
2025-07-21 22:09:42 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:09:42 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:09:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:09:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:09:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:20:36 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:20:36 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:20:37 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:20:37 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:20:39 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:20:39 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:20:39 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:20:39 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:20:39 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:20:39 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:20:39 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:20:39 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:20:39 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:20:39 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:20:39 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:20:39 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:20:39 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:20:40 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:20:40 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:20:40 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:20:40 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:20:40 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:20:40 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:20:40 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:20:40 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:20:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:20:41 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:20:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:20:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:20:44 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:20:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:20:44 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:20:44 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-21 22:20:45 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:20:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:20:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:20:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:20:45 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:20:46 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:20:47 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:20:47 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:20:47 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:20:47 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:20:47 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:23:55 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:23:55 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:23:55 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:23:55 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:23:57 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:23:57 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:23:57 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:23:57 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:23:57 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:23:57 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:23:57 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:23:57 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:23:57 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:23:57 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:23:57 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:23:57 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:23:57 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:23:58 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:23:58 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:23:59 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:23:59 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:23:59 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:23:59 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:23:59 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:23:59 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:23:59 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:23:59 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:23:59 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:23:59 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:24:03 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:24:03 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:24:03 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:24:03 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-21 22:24:03 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:24:03 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:24:03 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:24:03 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:24:04 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:24:05 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 22:24:05 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: True
2025-07-21 22:24:05 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:24:05 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open bluetooth
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:24:05 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open bluetooth
2025-07-21 22:24:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:24:06 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:24:06 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open bluetooth
2025-07-21 22:24:06 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:24:06 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:24:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:24:07 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:24:07 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:24:07 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:24:07 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:24:07 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:24:07 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open bluetooth
2025-07-21 22:24:07 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-21 22:24:08 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:24:08 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:24:08 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:24:08 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:216 | 从TextView元素获取响应
2025-07-21 22:24:21 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:232 | 查找RecyclerView中的最新消息
2025-07-21 22:24:22 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:338 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:24:22 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:257 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:24:22 | INFO | pages.apps.ella.ella_response_handler:get_response_text:102 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:24:22 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:135 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:30 | 检查蓝牙状态
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:42 | 蓝牙状态: (值: 1)
2025-07-21 22:24:25 | INFO | pages.apps.ella.ella_status_checker:check_bluetooth_status:44 | 蓝牙状态: 开启 (值: 1)
2025-07-21 22:24:25 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:148 | ✅ 状态验证通过: True -> True
2025-07-21 22:24:25 | INFO | testcases.test_ella.base_ella_test:simple_command_test:245 | 🎉 open bluetooth 测试完成
2025-07-21 22:24:25 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:185 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:24:25 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'bluetooth'
2025-07-21 22:24:25 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'is turned on now'
2025-07-21 22:24:25 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:216 | ❌ 部分期望内容未找到 (0/2)
2025-07-21 22:24:25 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:217 | 缺失内容: ['bluetooth', 'is turned on now']
2025-07-21 22:24:26 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:24:26 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:24:26 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:24:26 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:24:26 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:25:37 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:25:37 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:25:38 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:25:38 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:25:40 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:25:40 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:25:40 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:25:40 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:25:40 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:25:40 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:25:40 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:25:40 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:25:40 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:25:40 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:25:40 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:25:40 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:25:40 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:25:41 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:25:41 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:25:41 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:25:41 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:25:41 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:25:41 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:25:41 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:25:41 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:25:41 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:25:41 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:25:41 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:25:42 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:25:45 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:25:45 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:25:45 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:25:45 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-21 22:25:45 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:25:46 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:25:46 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:25:46 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:25:46 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:25:47 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:25:47 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:25:47 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:25:47 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:25:47 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:25:47 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:26:20 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:26:20 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:26:20 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:26:20 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:26:23 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:26:23 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:26:23 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:26:23 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:26:23 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:26:23 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:26:23 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:26:23 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:26:23 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:26:23 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:26:23 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:26:23 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:26:23 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:26:23 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:26:23 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:26:24 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:26:24 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:26:24 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:26:24 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:26:24 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:26:24 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:26:24 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:26:24 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:26:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:26:24 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:26:28 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:26:28 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:26:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:26:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-21 22:26:28 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:26:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:26:28 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:26:28 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:26:29 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:26:30 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-21 22:26:30 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-21 22:26:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:26:30 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:26:30 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-21 22:26:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:26:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:26:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:26:31 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:26:31 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-21 22:26:31 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:26:31 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:26:31 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:26:32 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:26:32 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:26:32 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:26:32 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-21 22:26:32 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:26:32 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-21 22:26:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:26:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:26:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:26:33 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:26:33 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:216 | 从TextView元素获取响应
2025-07-21 22:26:34 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:232 | 查找RecyclerView中的最新消息
2025-07-21 22:26:35 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:338 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:26:35 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:257 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:26:35 | INFO | pages.apps.ella.ella_response_handler:get_response_text:102 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:26:35 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:135 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 22:26:38 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:148 | ✅ 状态验证通过: True -> None
2025-07-21 22:26:38 | INFO | testcases.test_ella.base_ella_test:simple_command_test:245 | 🎉 open wifi 测试完成
2025-07-21 22:26:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:185 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:26:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-21 22:26:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:216 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 22:26:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:217 | 缺失内容: ['WI-FI']
2025-07-21 22:26:38 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:26:38 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:26:39 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:26:39 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:26:39 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 22:27:52 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-21 22:27:52 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-21 22:27:52 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-21 22:27:52 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-21 22:27:55 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-21 22:27:55 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-21 22:27:55 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-21 22:27:55 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:27:55 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-21 22:27:55 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-21 22:27:55 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-21 22:27:55 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-21 22:27:55 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-21 22:27:55 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-21 22:27:55 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-21 22:27:55 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-21 22:27:55 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-21 22:27:55 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:27:55 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-21 22:27:56 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:27:56 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-21 22:27:56 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-21 22:27:56 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 22:27:56 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 22:27:56 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 22:27:56 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 22:27:56 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-21 22:27:56 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-21 22:27:57 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:28:00 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-21 22:28:00 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-21 22:28:00 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-21 22:28:00 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-21 22:28:00 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-21 22:28:01 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-21 22:28:01 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-21 22:28:01 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-21 22:28:01 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-21 22:28:02 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:61 | 检查WiFi状态
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:check_wifi_status:74 | WiFi状态: 开启 (值: 1)
2025-07-21 22:28:02 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open wifi打开应用，状态: True
2025-07-21 22:28:02 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:28:02 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open wifi
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-21 22:28:02 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open wifi
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:28:03 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:28:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-21 22:28:03 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open wifi
2025-07-21 22:28:03 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-21 22:28:03 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-21 22:28:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-21 22:28:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-21 22:28:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-21 22:28:04 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-21 22:28:04 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-21 22:28:04 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-21 22:28:04 | INFO | testcases.test_ella.base_ella_test:_execute_command:112 | ✅ 成功执行命令: open wifi
2025-07-21 22:28:04 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-21 22:28:04 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-21 22:28:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:124 | 确保返回到Ella应用以获取响应文本
2025-07-21 22:28:04 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-21 22:28:04 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:28:05 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:123 | 从check_area节点获取响应文本
2025-07-21 22:28:05 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:216 | 从TextView元素获取响应
2025-07-21 22:28:06 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:232 | 查找RecyclerView中的最新消息
2025-07-21 22:28:07 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:338 | 匹配到AI响应特征: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:28:07 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:257 | 从页面dump获取响应: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:28:07 | INFO | pages.apps.ella.ella_response_handler:get_response_text:102 | ✅ 获取到响应文本: <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:28:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:135 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 22:28:10 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:148 | ✅ 状态验证通过: True -> None
2025-07-21 22:28:10 | INFO | testcases.test_ella.base_ella_test:simple_command_test:245 | 🎉 open wifi 测试完成
2025-07-21 22:28:10 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:185 | verify_expected_in_response 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 22:28:10 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:208 | ⚠️ 响应未包含期望内容: 'WI-FI'
2025-07-21 22:28:10 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:216 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 22:28:10 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:217 | 缺失内容: ['WI-FI']
2025-07-21 22:28:10 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-21 22:28:10 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-21 22:28:11 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 22:28:11 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 22:28:11 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
