2025-06-23 21:11:23 | INFO | __main__:main:202 | 🖼️ 截图管理工具
2025-06-23 21:11:23 | INFO | __main__:main:203 | ================================================================================
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 7 天前的截图
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:11:23 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager_tool.py --cleanup --confirm
2025-06-23 21:11:23 | INFO | __main__:main:223 | 
================================================================================
2025-06-23 21:11:23 | INFO | __main__:main:224 | 🎯 截图功能优化总结:
2025-06-23 21:11:23 | INFO | __main__:main:225 | ✅ 1. 截图按测试类名称自动分文件夹保存
2025-06-23 21:11:23 | INFO | __main__:main:226 | ✅ 2. 支持自动生成包含测试方法名的文件名
2025-06-23 21:11:23 | INFO | __main__:main:227 | ✅ 3. 提供完整的截图统计和管理功能
2025-06-23 21:11:23 | INFO | __main__:main:228 | ✅ 4. 保持向后兼容性，不影响现有代码
2025-06-23 21:11:23 | INFO | __main__:main:229 | ✅ 5. 集成到pytest和Allure报告系统
2025-06-23 21:11:23 | INFO | __main__:main:231 | 
💡 使用建议:
2025-06-23 21:11:23 | INFO | __main__:main:232 | - 定期运行 --cleanup 清理旧截图
2025-06-23 21:11:23 | INFO | __main__:main:233 | - 重要测试前运行 --backup 备份截图
2025-06-23 21:11:23 | INFO | __main__:main:234 | - 使用 --structure 查看测试覆盖情况
2025-06-23 21:12:34 | INFO | __main__:main:202 | 🖼️ 截图管理工具
2025-06-23 21:12:34 | INFO | __main__:main:203 | ================================================================================
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:12:34 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager_tool.py --cleanup --confirm
2025-06-23 21:12:34 | INFO | __main__:main:223 | 
================================================================================
2025-06-23 21:12:34 | INFO | __main__:main:224 | 🎯 截图功能优化总结:
2025-06-23 21:12:34 | INFO | __main__:main:225 | ✅ 1. 截图按测试类名称自动分文件夹保存
2025-06-23 21:12:34 | INFO | __main__:main:226 | ✅ 2. 支持自动生成包含测试方法名的文件名
2025-06-23 21:12:34 | INFO | __main__:main:227 | ✅ 3. 提供完整的截图统计和管理功能
2025-06-23 21:12:34 | INFO | __main__:main:228 | ✅ 4. 保持向后兼容性，不影响现有代码
2025-06-23 21:12:34 | INFO | __main__:main:229 | ✅ 5. 集成到pytest和Allure报告系统
2025-06-23 21:12:34 | INFO | __main__:main:231 | 
💡 使用建议:
2025-06-23 21:12:34 | INFO | __main__:main:232 | - 定期运行 --cleanup 清理旧截图
2025-06-23 21:12:34 | INFO | __main__:main:233 | - 重要测试前运行 --backup 备份截图
2025-06-23 21:12:34 | INFO | __main__:main:234 | - 使用 --structure 查看测试覆盖情况
2025-06-23 21:13:16 | INFO | __main__:main:202 | 🖼️ 截图管理工具
2025-06-23 21:13:16 | INFO | __main__:main:203 | ================================================================================
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:13:16 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager_tool.py --cleanup --confirm
2025-06-23 21:13:16 | INFO | __main__:main:223 | 
================================================================================
2025-06-23 21:13:16 | INFO | __main__:main:224 | 🎯 截图功能优化总结:
2025-06-23 21:13:16 | INFO | __main__:main:225 | ✅ 1. 截图按测试类名称自动分文件夹保存
2025-06-23 21:13:16 | INFO | __main__:main:226 | ✅ 2. 支持自动生成包含测试方法名的文件名
2025-06-23 21:13:16 | INFO | __main__:main:227 | ✅ 3. 提供完整的截图统计和管理功能
2025-06-23 21:13:16 | INFO | __main__:main:228 | ✅ 4. 保持向后兼容性，不影响现有代码
2025-06-23 21:13:16 | INFO | __main__:main:229 | ✅ 5. 集成到pytest和Allure报告系统
2025-06-23 21:13:16 | INFO | __main__:main:231 | 
💡 使用建议:
2025-06-23 21:13:16 | INFO | __main__:main:232 | - 定期运行 --cleanup 清理旧截图
2025-06-23 21:13:16 | INFO | __main__:main:233 | - 重要测试前运行 --backup 备份截图
2025-06-23 21:13:16 | INFO | __main__:main:234 | - 使用 --structure 查看测试覆盖情况
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:14:19 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager_tool.py --cleanup --confirm
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:19:25 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager_tool.py --cleanup --confirm
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:100 | ⚠️ 这是预览模式，不会实际删除文件
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:24:22 | INFO | __main__:cleanup_old_screenshots:147 | 
💡 要实际删除这些文件，请运行: python screenshot_manager.py --cleanup --confirm
2025-06-23 21:25:10 | INFO | __main__:main:202 | 🖼️ 截图管理工具
2025-06-23 21:25:10 | INFO | __main__:main:203 | ================================================================================
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:96 | 
🧹 清理 1 天前的截图
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:97 | ============================================================
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: device_connection_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: device_discovery_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: ella_center_click_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: ella_exploration_completed.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: ella_initial_exploration.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: ella_text_input_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: framework_complete_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: global_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\ella_page_load_failed.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_204315.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_204454.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_204543.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_215952.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaOpenClockCommand\ella_clock_command_input.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaOpenClockCommand\ella_clock_command_sent.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaOpenClockCommand\ella_clock_initial_state.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaOpenClockCommand\ella_clock_test_completed.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestEllaOpenClockCommand\ella_clock_wait_complete.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestElla_Simple\ella_basic_test_start.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestElla_Simple\ella_bluetooth_response.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestElla_Simple\ella_hello_response.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestElla_Simple\ella_text_input.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestFramework_Complete\screenshot_manager_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestFramework_Complete\settings_page_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestReadmeExamples\custom_screenshot.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestReadmeExamples\page_verification.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestReadmeExamples\test_step.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestScreenshotAdvanced\manager_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestScreenshotAdvanced\settings_page.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestScreenshotBasic\basic_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestScreenshotBasic\screenshot_20250612_135756.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\after_swipe.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\back_to_main_settings.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\bluetooth_settings_page.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\failure_test_enter_about_phone_20250612_221458.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\failure_test_enter_apps_settings_20250612_221417.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\failure_test_enter_display_settings_20250612_221409.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\failure_test_search_function_20250612_221511.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: TestSettingsBasic\wifi_settings_page.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_close_final.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_command_sent.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_response_received.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_script_final.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_script_initial.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_script_input.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_script_sent.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_script_tts.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_test_final.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\bluetooth_test_initial.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\clock_command_input.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\clock_command_sent.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\clock_test_final.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\clock_test_initial.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\clock_tts_response.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\ella_clock_setup_complete.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\ella_script_setup_complete.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\ella_setup_complete.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\readme_example_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:134 | 删除: unknown\readme_manager_test.png
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:137 | 找到 60 个需要清理的文件:
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   device_connection_test.png (1371.5KB, 2025-06-12 15:56:46)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   device_discovery_test.png (179.8KB, 2025-06-12 15:47:33)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   ella_center_click_test.png (620.7KB, 2025-06-12 16:04:40)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   ella_exploration_completed.png (556.6KB, 2025-06-12 16:04:08)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   ella_initial_exploration.png (558.1KB, 2025-06-12 16:04:03)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   ella_text_input_test.png (744.7KB, 2025-06-12 16:04:16)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   framework_complete_test.png (178.6KB, 2025-06-12 15:51:20)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   global_test.png (1519.8KB, 2025-06-12 14:04:31)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\ella_page_load_failed.png (368.7KB, 2025-06-13 14:40:35)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:139 |   TestEllaBluetoothCommand\failure_test_open_bluetooth_command_20250612_160959.png (565.0KB, 2025-06-12 16:09:59)
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:142 |   ... 还有 50 个文件
2025-06-23 21:25:10 | INFO | __main__:cleanup_old_screenshots:144 | 总计: 36.41 MB
2025-06-23 21:25:10 | INFO | __main__:main:223 | 
================================================================================
2025-06-23 21:25:10 | INFO | __main__:main:224 | 🎯 截图功能优化总结:
2025-06-23 21:25:10 | INFO | __main__:main:225 | ✅ 1. 截图按测试类名称自动分文件夹保存
2025-06-23 21:25:10 | INFO | __main__:main:226 | ✅ 2. 支持自动生成包含测试方法名的文件名
2025-06-23 21:25:10 | INFO | __main__:main:227 | ✅ 3. 提供完整的截图统计和管理功能
2025-06-23 21:25:10 | INFO | __main__:main:228 | ✅ 4. 保持向后兼容性，不影响现有代码
2025-06-23 21:25:10 | INFO | __main__:main:229 | ✅ 5. 集成到pytest和Allure报告系统
2025-06-23 21:25:10 | INFO | __main__:main:231 | 
💡 使用建议:
2025-06-23 21:25:10 | INFO | __main__:main:232 | - 定期运行 --cleanup 清理旧截图
2025-06-23 21:25:10 | INFO | __main__:main:233 | - 重要测试前运行 --backup 备份截图
2025-06-23 21:25:10 | INFO | __main__:main:234 | - 使用 --structure 查看测试覆盖情况
