#!/usr/bin/env python3
"""
WiFi状态检查测试脚本
用于测试优化后的WiFi状态检查功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pages.apps.ella.ella_status_checker import EllaStatusChecker
from core.logger import log


def test_wifi_status():
    """测试WiFi状态检查功能"""
    print("=" * 50)
    print("WiFi状态检查测试")
    print("=" * 50)
    
    # 创建状态检查器实例（不需要driver）
    checker = EllaStatusChecker()
    
    # 测试基本WiFi状态检查
    print("\n1. 基本WiFi状态检查:")
    wifi_enabled = checker.check_wifi_status()
    print(f"   WiFi是否开启: {wifi_enabled}")
    
    # 测试详细WiFi连接状态
    print("\n2. 详细WiFi连接状态:")
    wifi_info = checker.check_wifi_connection_status()
    print(f"   WiFi启用状态: {wifi_info['wifi_enabled']}")
    print(f"   连接状态: {wifi_info['connected']}")
    print(f"   SSID: {wifi_info['ssid']}")
    print(f"   信号强度: {wifi_info['signal_strength']} dBm" if wifi_info['signal_strength'] else "   信号强度: 未知")
    print(f"   IP地址: {wifi_info['ip_address']}")
    
    # 测试蓝牙状态（对比）
    print("\n3. 蓝牙状态检查（对比）:")
    bluetooth_enabled = checker.check_bluetooth_status()
    print(f"   蓝牙是否开启: {bluetooth_enabled}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


def test_adb_commands():
    """测试ADB命令是否可用"""
    import subprocess
    
    print("\n检查ADB命令可用性:")
    
    try:
        # 检查ADB是否可用
        result = subprocess.run(
            ["adb", "devices"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print("✅ ADB命令可用")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            if devices and any(device.strip() for device in devices):
                print(f"✅ 检测到设备: {len([d for d in devices if d.strip()])} 台")
            else:
                print("⚠️  未检测到连接的设备")
        else:
            print("❌ ADB命令不可用")
            
    except FileNotFoundError:
        print("❌ ADB命令未找到，请确保ADB已安装并添加到PATH")
    except Exception as e:
        print(f"❌ ADB检查失败: {e}")


if __name__ == "__main__":
    # 首先检查ADB可用性
    test_adb_commands()
    
    # 然后测试WiFi状态
    test_wifi_status()
