#!/usr/bin/env python3
"""
测试当前手电筒检测逻辑
"""

def test_raw_log_parsing():
    """测试原始日志解析"""
    print('=== 测试原始日志解析 ===')

    import subprocess

    try:
        # 获取原始日志
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "media.camera"],
            capture_output=True,
            text=True,
            timeout=10
        )

        if result.returncode == 0:
            camera_output = result.stdout
            lines = camera_output.split('\n')

            # 查找所有torch相关行
            all_torch_lines = [line.strip() for line in lines if 'torch' in line.lower()]
            print(f'\n1. 所有torch相关行 ({len(all_torch_lines)} 条):')
            for i, line in enumerate(all_torch_lines[:10]):  # 只显示前10条
                print(f'   [{i+1}] {line}')

            # 查找torch操作日志
            torch_operation_lines = [
                line.strip() for line in lines
                if 'torch for camera' in line.lower() and ('turned on' in line.lower() or 'turned off' in line.lower())
            ]
            print(f'\n2. torch操作日志 ({len(torch_operation_lines)} 条):')
            for i, line in enumerate(torch_operation_lines):
                print(f'   [{i+1}] {line}')

            if torch_operation_lines:
                latest = torch_operation_lines[-1]
                print(f'\n3. 最新操作: {latest}')
                if 'turned on' in latest.lower():
                    print('   -> 状态: 开启')
                elif 'turned off' in latest.lower():
                    print('   -> 状态: 关闭')

    except Exception as e:
        print(f'解析失败: {e}')

def test_current_detection():
    """测试当前的检测逻辑"""
    print('\n=== 测试当前手电筒检测逻辑 ===')

    try:
        from pages.apps.ella.ella_status_checker import EllaStatusChecker
        from core.base_driver import driver_manager

        checker = EllaStatusChecker(driver_manager.driver)

        print('\n1. 当前检测结果:')
        status = checker.check_flashlight_status()
        print(f'   手电筒状态: {status}')

        print('\n2. 详细状态:')
        detailed_status = checker.get_flashlight_detailed_status()
        for key, value in detailed_status.items():
            if key == 'torch_logs' and value:
                print(f'   {key}: {len(value)} 条日志')
                for i, log_line in enumerate(value):
                    print(f'     [{i+1}] {log_line}')
            else:
                print(f'   {key}: {value}')

        return status, detailed_status

    except Exception as e:
        print(f'测试失败: {e}')
        return False, {}

if __name__ == '__main__':
    test_raw_log_parsing()
    test_current_detection()
