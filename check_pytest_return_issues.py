#!/usr/bin/env python3
"""
检查pytest测试文件中的return语句问题
"""
import os
import re
from pathlib import Path


def check_pytest_return_issues():
    """检查pytest测试文件中的return语句问题"""
    
    # 测试文件目录
    test_dirs = ["testcases"]
    
    # 匹配pytest测试函数的return语句
    return_pattern = r'^\s*return\s+.*'
    test_function_pattern = r'^\s*def\s+test_.*\('
    
    issues_found = []
    
    for test_dir in test_dirs:
        if not os.path.exists(test_dir):
            continue
            
        # 遍历所有Python测试文件
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        
                        in_test_function = False
                        current_test_function = None
                        
                        for line_num, line in enumerate(lines, 1):
                            # 检查是否进入测试函数
                            if re.match(test_function_pattern, line):
                                in_test_function = True
                                current_test_function = line.strip()
                                continue
                            
                            # 检查是否离开函数（下一个函数或类定义）
                            if in_test_function and (line.startswith('def ') or line.startswith('class ') or 
                                                   (line.strip() and not line.startswith(' ') and not line.startswith('\t'))):
                                if not line.startswith('def test_') and not line.startswith('class Test'):
                                    in_test_function = False
                                    current_test_function = None
                            
                            # 在测试函数中检查return语句
                            if in_test_function and re.match(return_pattern, line):
                                # 排除return None的情况
                                if 'return None' not in line and 'return' in line and line.strip() != 'return':
                                    issues_found.append({
                                        'file': file_path,
                                        'line': line_num,
                                        'function': current_test_function,
                                        'return_statement': line.strip()
                                    })
                    
                    except Exception as e:
                        print(f"❌ 读取文件失败: {file_path}, 错误: {e}")
    
    return issues_found


def main():
    """主函数"""
    print("🔍 检查pytest测试文件中的return语句问题...")
    
    issues = check_pytest_return_issues()
    
    if not issues:
        print("✅ 没有发现pytest测试函数返回值的问题")
        return
    
    print(f"\n❌ 发现 {len(issues)} 个问题:")
    
    for issue in issues:
        print(f"\n📁 文件: {issue['file']}")
        print(f"📍 行号: {issue['line']}")
        print(f"🔧 函数: {issue['function']}")
        print(f"⚠️  返回语句: {issue['return_statement']}")
    
    print(f"\n📋 修复建议:")
    print("1. 删除测试函数中的return语句")
    print("2. 使用assert语句进行验证")
    print("3. 如果需要记录结果，使用日志或Allure报告")
    
    print(f"\n🔧 批量修复命令:")
    unique_files = set(issue['file'] for issue in issues)
    for file_path in unique_files:
        print(f"# 修复文件: {file_path}")
        print(f"# 将 'return initial_status, final_status, response_text' 替换为注释")


if __name__ == "__main__":
    main()
