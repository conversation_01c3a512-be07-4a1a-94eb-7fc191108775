#!/usr/bin/env python3
"""
测试优化后的响应验证方法
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


class TestResponseVerification:
    """测试响应验证功能"""
    
    def __init__(self):
        self.test_instance = BaseEllaTest()
    
    def test_string_response_with_string_expected(self):
        """测试字符串响应 + 字符串期望"""
        print("\n📋 测试1: 字符串响应 + 字符串期望")
        
        response_text = "蓝牙已成功开启，设备可以被发现"
        expected_text = "蓝牙已成功开启"
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_string_response_with_list_expected(self):
        """测试字符串响应 + 列表期望"""
        print("\n📋 测试2: 字符串响应 + 列表期望")
        
        response_text = "蓝牙已成功开启，设备可以被发现，连接状态良好"
        expected_text = ["蓝牙已成功开启", "设备可以被发现"]
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_list_response_with_string_expected(self):
        """测试列表响应 + 字符串期望"""
        print("\n📋 测试3: 列表响应 + 字符串期望")
        
        response_text = ["打开蓝牙", "蓝牙已打开", "", "设备"]
        expected_text = "蓝牙已打开"
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_list_response_with_list_expected(self):
        """测试列表响应 + 列表期望"""
        print("\n📋 测试4: 列表响应 + 列表期望")
        
        response_text = ["打开蓝牙", "蓝牙已打开", "设备可发现", ""]
        expected_text = ["蓝牙", "设备"]
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_empty_list_response(self):
        """测试空列表响应"""
        print("\n📋 测试5: 空列表响应")
        
        response_text = ["", "   ", None]  # 模拟空响应
        expected_text = "蓝牙"
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"❌ 应该失败但通过了: {result}")
        except Exception as e:
            print(f"✅ 正确失败: {e}")
    
    def test_partial_match_failure(self):
        """测试部分匹配失败"""
        print("\n📋 测试6: 部分匹配失败")
        
        response_text = ["蓝牙已打开", "设备可发现"]
        expected_text = ["蓝牙", "WiFi", "设备"]  # WiFi不存在
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"❌ 应该失败但通过了: {result}")
        except Exception as e:
            print(f"✅ 正确失败: {e}")
    
    def test_advanced_combined_mode(self):
        """测试高级方法 - 合并模式"""
        print("\n📋 测试7: 高级方法 - 合并模式")
        
        response_text = ["蓝牙", "已打开", "设备可发现"]
        expected_text = ["蓝牙已打开", "设备"]
        
        try:
            result = self.test_instance.verify_expected_in_response_advanced(
                expected_text, response_text, search_mode="combined"
            )
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_advanced_individual_mode(self):
        """测试高级方法 - 独立模式"""
        print("\n📋 测试8: 高级方法 - 独立模式")
        
        response_text = ["蓝牙已打开", "设备可发现", "连接成功"]
        expected_text = ["蓝牙", "设备"]
        
        try:
            result = self.test_instance.verify_expected_in_response_advanced(
                expected_text, response_text, search_mode="individual"
            )
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_advanced_any_match_mode(self):
        """测试高级方法 - 任意匹配模式"""
        print("\n📋 测试9: 高级方法 - 任意匹配模式")
        
        response_text = ["蓝牙已打开", "设备可发现"]
        expected_text = ["蓝牙", "WiFi", "网络"]  # 只有蓝牙存在
        
        try:
            result = self.test_instance.verify_expected_in_response_advanced(
                expected_text, response_text, search_mode="combined", match_any=True
            )
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def test_real_world_scenario(self):
        """测试真实场景"""
        print("\n📋 测试10: 真实场景模拟")
        
        # 模拟 get_response_all_text() 的返回值
        response_text = [
            "打开蓝牙",           # asr_txt
            "蓝牙已打开",         # robot_text  
            "蓝牙",              # function_name
            "已打开",            # function_control
            ""                   # 空值
        ]
        expected_text = ["蓝牙", "已打开"]
        
        try:
            result = self.test_instance.verify_expected_in_response(expected_text, response_text)
            print(f"✅ 测试通过: {result}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始测试优化后的响应验证方法...")
        
        test_methods = [
            self.test_string_response_with_string_expected,
            self.test_string_response_with_list_expected,
            self.test_list_response_with_string_expected,
            self.test_list_response_with_list_expected,
            self.test_empty_list_response,
            self.test_partial_match_failure,
            self.test_advanced_combined_mode,
            self.test_advanced_individual_mode,
            self.test_advanced_any_match_mode,
            self.test_real_world_scenario
        ]
        
        passed = 0
        total = len(test_methods)
        
        for test_method in test_methods:
            try:
                test_method()
                passed += 1
            except Exception as e:
                print(f"❌ 测试方法 {test_method.__name__} 执行异常: {e}")
        
        print(f"\n🎉 测试完成: {passed}/{total} 个测试通过")


if __name__ == "__main__":
    tester = TestResponseVerification()
    tester.run_all_tests()
