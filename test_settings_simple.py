#!/usr/bin/env python3
"""
简单的Settings应用检测测试
"""
import subprocess

def test_settings_detection_simple():
    """简单测试Settings应用检测"""
    print('=== Settings应用检测测试 ===')
    
    try:
        from pages.apps.ella.ella_app_detector import EllaAppDetector

        detector = EllaAppDetector()
        
        print('\n1. 当前Settings应用状态:')
        is_settings_open = detector.check_settings_opened()
        print(f'   检测结果: {is_settings_open}')
        
        if is_settings_open:
            print('   ✅ Settings应用正在运行')
        else:
            print('   ❌ Settings应用未运行')
            
        # 显示检测到的具体信息
        print('\n2. 检测详情:')
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "settings"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            processes = result.stdout.strip().split('\n')
            print(f'   Settings相关进程: {len(processes)} 个')
            for i, process in enumerate(processes[:3], 1):
                # 提取进程名
                parts = process.split()
                if len(parts) >= 9:
                    process_name = parts[8]
                    print(f'     {i}. {process_name}')
        else:
            print('   未找到Settings相关进程')
            
    except Exception as e:
        print(f'测试失败: {e}')

def close_settings_app():
    """关闭Settings应用"""
    print('\n=== 尝试关闭Settings应用 ===')
    try:
        # 强制停止Settings应用
        result = subprocess.run(
            ["adb", "shell", "am", "force-stop", "com.android.settings"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print('✅ 标准Settings应用已关闭')
        else:
            print(f'❌ 关闭标准Settings失败: {result.stderr}')
            
        # 关闭Transsion Settings
        result = subprocess.run(
            ["adb", "shell", "am", "force-stop", "com.transsion.settings"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print('✅ Transsion Settings应用已关闭')
        else:
            print(f'❌ 关闭Transsion Settings失败: {result.stderr}')
            
    except Exception as e:
        print(f'关闭失败: {e}')

if __name__ == '__main__':
    # 1. 测试当前状态
    test_settings_detection_simple()
    
    # 2. 尝试关闭Settings应用
    close_settings_app()
    
    # 3. 等待一下再测试
    import time
    print('\n等待2秒...')
    time.sleep(2)
    
    # 4. 再次测试
    print('\n=== 关闭后再次测试 ===')
    test_settings_detection_simple()
    
    print('\n=== 测试完成 ===')
