"""
Ella应用调试工具
用于诊断Ella应用启动和页面加载问题
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.base_driver import driver_manager
from core.logger import log
import time


class EllaDebugger:
    """Ella应用调试器"""
    
    def __init__(self):
        """初始化调试器"""
        self.driver = driver_manager.driver
        self.package_name = "com.transsion.aivoiceassistant"
    
    def diagnose_ella_app(self):
        """诊断Ella应用状态"""
        log.info("🔍 开始诊断Ella应用状态")
        
        # 1. 检查设备连接
        self._check_device_connection()
        
        # 2. 检查应用安装
        self._check_app_installation()
        
        # 3. 检查应用运行状态
        self._check_app_running_status()
        
        # 4. 尝试启动应用
        self._try_start_app()
        
        # 5. 检查页面元素
        self._check_page_elements()
        
        # 6. 获取页面截图
        self._take_debug_screenshot()
        
        log.info("🏁 Ella应用诊断完成")
    
    def _check_device_connection(self):
        """检查设备连接状态"""
        try:
            log.info("📱 检查设备连接状态")
            
            device_info = self.driver.device_info
            log.info(f"设备信息: {device_info}")
            
            # 检查设备基本功能
            screen_size = self.driver.window_size()
            log.info(f"屏幕尺寸: {screen_size}")
            
            # 检查设备是否响应
            self.driver.press("home")
            time.sleep(1)
            
            log.info("✅ 设备连接正常")
            
        except Exception as e:
            log.error(f"❌ 设备连接异常: {e}")
    
    def _check_app_installation(self):
        """检查应用安装状态"""
        try:
            log.info("📦 检查Ella应用安装状态")
            
            # 获取已安装应用列表
            installed_apps = self.driver.app_list()
            
            if self.package_name in installed_apps:
                app_info = self.driver.app_info(self.package_name)
                log.info(f"✅ Ella应用已安装: {app_info}")
            else:
                log.error(f"❌ Ella应用未安装: {self.package_name}")
                log.info("已安装的相关应用:")
                for app in installed_apps:
                    if "ella" in app.lower() or "voice" in app.lower() or "ai" in app.lower():
                        log.info(f"  - {app}")
            
        except Exception as e:
            log.error(f"检查应用安装状态失败: {e}")
    
    def _check_app_running_status(self):
        """检查应用运行状态"""
        try:
            log.info("🏃 检查应用运行状态")
            
            # 检查当前前台应用
            current_app = self.driver.app_current()
            log.info(f"当前前台应用: {current_app}")
            
            # 检查正在运行的应用
            running_apps = self.driver.app_list_running()
            if self.package_name in running_apps:
                log.info(f"✅ Ella应用正在运行")
            else:
                log.info(f"⚠️ Ella应用未在运行")
                log.info("正在运行的应用:")
                for app in running_apps[:10]:  # 只显示前10个
                    log.info(f"  - {app}")
            
        except Exception as e:
            log.error(f"检查应用运行状态失败: {e}")
    
    def _try_start_app(self):
        """尝试启动应用"""
        try:
            log.info("🚀 尝试启动Ella应用")
            
            # 方法1: 标准启动
            try:
                self.driver.app_start(self.package_name)
                time.sleep(3)
                log.info("方法1: 标准启动完成")
            except Exception as e:
                log.warning(f"方法1失败: {e}")
            
            # 方法2: 指定Activity启动
            try:
                activity = "com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"
                self.driver.app_start(self.package_name, activity)
                time.sleep(3)
                log.info("方法2: 指定Activity启动完成")
            except Exception as e:
                log.warning(f"方法2失败: {e}")
            
            # 方法3: Shell命令启动
            try:
                cmd = f"am start -n {self.package_name}/.MainActivity"
                self.driver.shell(cmd)
                time.sleep(3)
                log.info("方法3: Shell命令启动完成")
            except Exception as e:
                log.warning(f"方法3失败: {e}")
            
            # 检查启动结果
            current_app = self.driver.app_current()
            if current_app.get('package') == self.package_name:
                log.info("✅ Ella应用启动成功")
            else:
                log.error(f"❌ Ella应用启动失败，当前应用: {current_app}")
            
        except Exception as e:
            log.error(f"尝试启动应用失败: {e}")
    
    def _check_page_elements(self):
        """检查页面元素"""
        try:
            log.info("🔍 检查页面元素")
            
            # 获取页面源码
            try:
                page_source = self.driver.dump_hierarchy()
                if page_source:
                    log.info("✅ 成功获取页面源码")
                    # 检查是否包含Ella相关元素
                    if "ella" in page_source.lower() or "aivoiceassistant" in page_source.lower():
                        log.info("✅ 页面包含Ella相关元素")
                    else:
                        log.warning("⚠️ 页面不包含Ella相关元素")
                else:
                    log.error("❌ 无法获取页面源码")
            except Exception as e:
                log.error(f"获取页面源码失败: {e}")
            
            # 检查常见元素
            common_elements = [
                ("EditText", "android.widget.EditText"),
                ("TextView", "android.widget.TextView"),
                ("Button", "android.widget.Button"),
                ("ImageView", "android.widget.ImageView"),
                ("LinearLayout", "android.widget.LinearLayout"),
                ("RecyclerView", "androidx.recyclerview.widget.RecyclerView")
            ]
            
            for name, class_name in common_elements:
                try:
                    elements = self.driver(className=class_name)
                    if elements.exists():
                        count = elements.count
                        log.info(f"✅ 找到 {count} 个 {name} 元素")
                    else:
                        log.info(f"⚠️ 未找到 {name} 元素")
                except Exception as e:
                    log.warning(f"检查 {name} 元素失败: {e}")
            
            # 检查特定的Ella元素
            ella_elements = [
                ("com.transsion.aivoiceassistant:id/et_input", "输入框"),
                ("com.transsion.aivoiceassistant:id/iv_voice", "语音按钮"),
                ("Hi，我是Ella", "欢迎消息"),
                ("Ella", "Ella文本")
            ]
            
            for locator, description in ella_elements:
                try:
                    if ":" in locator:
                        element = self.driver(resourceId=locator)
                    else:
                        element = self.driver(text=locator)
                    
                    if element.exists():
                        log.info(f"✅ 找到Ella元素: {description}")
                    else:
                        log.info(f"⚠️ 未找到Ella元素: {description}")
                except Exception as e:
                    log.warning(f"检查Ella元素失败 {description}: {e}")
            
        except Exception as e:
            log.error(f"检查页面元素失败: {e}")
    
    def _take_debug_screenshot(self):
        """获取调试截图"""
        try:
            log.info("📸 获取调试截图")
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"ella_debug_{timestamp}.png"
            
            screenshot_path = driver_manager.screenshot(filename)
            log.info(f"✅ 调试截图已保存: {screenshot_path}")
            
            return screenshot_path
            
        except Exception as e:
            log.error(f"获取调试截图失败: {e}")
            return None
    
    def get_detailed_app_info(self):
        """获取详细的应用信息"""
        try:
            log.info("📋 获取详细应用信息")
            
            if self.package_name in self.driver.app_list():
                app_info = self.driver.app_info(self.package_name)
                log.info(f"应用详细信息: {app_info}")
                
                # 尝试获取应用权限
                try:
                    permissions = self.driver.shell(f"dumpsys package {self.package_name} | grep permission")
                    log.info(f"应用权限信息: {permissions}")
                except:
                    log.warning("无法获取权限信息")
                
                return app_info
            else:
                log.error("应用未安装")
                return None
                
        except Exception as e:
            log.error(f"获取应用信息失败: {e}")
            return None


def main():
    """主函数"""
    debugger = EllaDebugger()
    debugger.diagnose_ella_app()
    debugger.get_detailed_app_info()


if __name__ == "__main__":
    main()
