"""
测试Ella输入框修复
验证改进后的输入框检测和激活功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.history.main_page import EllaMainPage
from core.logger import log


def test_ella_input_fix():
    """测试Ella输入框修复"""
    log.info("🧪 开始测试Ella输入框修复")
    
    # 初始化页面对象
    ella_page = EllaMainPage()
    
    try:
        # 1. 启动应用
        log.info("1️⃣ 启动Ella应用")
        if not ella_page.start_app_with_activity():
            log.error("❌ Ella应用启动失败")
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 2. 等待页面加载
        log.info("2️⃣ 等待页面加载")
        if not ella_page.wait_for_page_load(timeout=15):
            log.warning("⚠️ 页面加载超时，但继续测试")
        else:
            log.info("✅ 页面加载成功")
        
        # 3. 获取页面截图
        log.info("3️⃣ 获取当前页面截图")
        screenshot_path = ella_page.screenshot("ella_before_input_test.png")
        log.info(f"📸 截图保存: {screenshot_path}")
        
        # 4. 测试输入框检测
        log.info("4️⃣ 测试输入框检测和激活")
        input_ready = ella_page.ensure_input_box_ready()
        
        if input_ready:
            log.info("✅ 输入框检测成功")
            
            # 5. 测试文本输入
            log.info("5️⃣ 测试文本输入功能")
            test_text = "测试输入"
            if ella_page.input_text_command(test_text):
                log.info(f"✅ 文本输入成功: {test_text}")
                
                # 6. 获取输入后截图
                screenshot_path2 = ella_page.screenshot("ella_after_input_test.png")
                log.info(f"📸 输入后截图: {screenshot_path2}")
                
                # 7. 测试发送功能
                log.info("6️⃣ 测试发送功能")
                if ella_page.send_command():
                    log.info("✅ 发送功能正常")
                else:
                    log.warning("⚠️ 发送功能异常")
                
            else:
                log.error("❌ 文本输入失败")
        else:
            log.error("❌ 输入框检测失败")
            
            # 获取失败时的截图
            screenshot_path3 = ella_page.screenshot("ella_input_failed.png")
            log.info(f"📸 失败截图: {screenshot_path3}")
        
        # 8. 输出诊断信息
        log.info("7️⃣ 输出诊断信息")
        _output_diagnostic_info(ella_page)
        
        return input_ready
        
    except Exception as e:
        log.error(f"测试过程中发生异常: {e}")
        return False
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
            log.info("✅ 应用已停止")
        except:
            log.warning("应用停止失败")


def _output_diagnostic_info(ella_page):
    """输出诊断信息"""
    try:
        log.info("📋 诊断信息:")
        
        # 检查当前应用
        current_app = ella_page.driver.app_current()
        log.info(f"当前应用: {current_app}")
        
        # 检查屏幕尺寸
        screen_size = ella_page.driver.window_size()
        log.info(f"屏幕尺寸: {screen_size}")
        
        # 检查页面元素
        log.info("页面元素检查:")
        
        # EditText元素
        edit_texts = ella_page.driver(className="android.widget.EditText")
        log.info(f"  - EditText元素: {edit_texts.count if edit_texts.exists() else 0} 个")
        
        # TextView元素
        text_views = ella_page.driver(className="android.widget.TextView")
        log.info(f"  - TextView元素: {text_views.count if text_views.exists() else 0} 个")
        
        # Button元素
        buttons = ella_page.driver(className="android.widget.Button")
        log.info(f"  - Button元素: {buttons.count if buttons.exists() else 0} 个")
        
        # ImageButton元素
        image_buttons = ella_page.driver(className="android.widget.ImageButton")
        log.info(f"  - ImageButton元素: {image_buttons.count if image_buttons.exists() else 0} 个")
        
        # 检查特定的Ella元素
        log.info("Ella特定元素检查:")
        
        # 主输入框
        if ella_page.input_box.is_exists():
            log.info("  ✅ 主输入框存在")
        else:
            log.info("  ❌ 主输入框不存在")
        
        # 备选输入框
        if ella_page.text_input_box.is_exists():
            log.info("  ✅ 备选输入框存在")
        else:
            log.info("  ❌ 备选输入框不存在")
        
        # 语音按钮
        if ella_page.voice_input_button.is_exists():
            log.info("  ✅ 语音按钮存在")
        else:
            log.info("  ❌ 语音按钮不存在")
        
        # 发送按钮
        if ella_page.send_button.is_exists():
            log.info("  ✅ 发送按钮存在")
        else:
            log.info("  ❌ 发送按钮不存在")
        
        # 检查是否有包含特定文本的元素
        ella_texts = ["Ella", "Hi，我是Ella", "语音助手", "输入"]
        for text in ella_texts:
            elements = ella_page.driver(textContains=text)
            if elements.exists():
                log.info(f"  ✅ 找到包含'{text}'的元素")
            else:
                log.info(f"  ❌ 未找到包含'{text}'的元素")
        
    except Exception as e:
        log.error(f"输出诊断信息失败: {e}")


def main():
    """主函数"""
    success = test_ella_input_fix()
    
    if success:
        log.info("🎉 Ella输入框修复测试通过")
        return 0
    else:
        log.error("💥 Ella输入框修复测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
