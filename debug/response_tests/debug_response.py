"""
调试响应检测问题
查看实际的页面内容和响应
"""
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.logger import log
from pages.apps.ella.history.main_page import EllaMainPage


def debug_ella_response():
    """调试Ella响应检测"""
    log.info("🔍 开始调试Ella响应检测...")
    
    try:
        # 创建Ella页面实例
        ella_page = EllaMainPage()
        
        # 启动应用
        log.info("📱 启动Ella应用...")
        if not ella_page.start_app_with_activity():
            log.error("应用启动失败")
            return False
        
        # 等待页面加载
        if not ella_page.wait_for_page_load(timeout=15):
            log.error("页面加载失败")
            return False
        
        log.info("✅ Ella应用启动成功")
        
        # 确保在对话页面
        if not ella_page.ensure_on_chat_page():
            log.error("无法确保在对话页面")
            return False
        
        # 确保输入框就绪
        if not ella_page.ensure_input_box_ready():
            log.error("输入框未就绪")
            return False
        
        # 获取发送命令前的页面快照
        log.info("📸 获取发送命令前的页面快照...")
        initial_snapshot = ella_page._get_page_text_snapshot()
        log.info(f"初始快照内容:\n{initial_snapshot}")
        log.info(f"初始快照长度: {len(initial_snapshot)}")
        
        # 执行命令
        command = "open bluetooth"
        log.info(f"🎯 执行命令: {command}")
        
        if not ella_page.execute_text_command(command):
            log.error("命令执行失败")
            return False
        
        log.info("✅ 命令执行成功")
        
        # 等待一段时间让响应出现
        log.info("⏳ 等待响应出现...")
        for i in range(20):  # 等待20秒，每秒检查一次
            time.sleep(1)
            
            # 获取当前页面快照
            current_snapshot = ella_page._get_page_text_snapshot()
            
            # 检查是否有变化
            if current_snapshot != initial_snapshot:
                log.info(f"🔄 第{i+1}秒检测到页面变化")
                log.info(f"当前快照内容:\n{current_snapshot}")
                log.info(f"当前快照长度: {len(current_snapshot)}")
                
                # 计算差异
                initial_lines = set(initial_snapshot.split('\n'))
                current_lines = set(current_snapshot.split('\n'))
                new_lines = current_lines - initial_lines
                
                if new_lines:
                    log.info(f"新增文本行数: {len(new_lines)}")
                    for j, new_line in enumerate(new_lines, 1):
                        log.info(f"新增文本 {j}: '{new_line}'")
                        
                        # 测试AI响应检测
                        is_ai_response = ella_page._is_ai_response(new_line)
                        log.info(f"  -> AI响应检测结果: {is_ai_response}")
                        
                        # 测试有意义文本检测
                        is_meaningful = ella_page._is_meaningful_text(new_line)
                        log.info(f"  -> 有意义文本检测结果: {is_meaningful}")
                
                # 检查TTS按钮
                tts_appeared = ella_page._check_tts_button_appeared()
                log.info(f"TTS按钮检测结果: {tts_appeared}")
                
                # 尝试获取响应文本
                response_text = ella_page.get_response_text()
                log.info(f"获取到的响应文本: '{response_text}'")
                
                break
            else:
                log.info(f"第{i+1}秒: 页面无变化")
        
        # 最终检查
        log.info("🔍 最终页面状态检查...")
        final_snapshot = ella_page._get_page_text_snapshot()
        log.info(f"最终快照内容:\n{final_snapshot}")
        
        # 获取所有TextView元素的详细信息
        log.info("📋 获取所有TextView元素详细信息...")
        text_views = ella_page.driver(className="android.widget.TextView")
        if text_views.exists():
            log.info(f"找到 {text_views.count} 个TextView元素")
            for i in range(min(text_views.count, 20)):  # 最多显示前20个
                try:
                    tv = text_views[i] if text_views.count > 1 else text_views
                    text = tv.get_text()
                    bounds = tv.info.get('bounds', {})
                    visible = tv.info.get('visible', False)
                    log.info(f"TextView {i+1}: '{text}' (visible={visible}, bounds={bounds})")
                except Exception as e:
                    log.debug(f"获取TextView {i+1}信息失败: {e}")
        else:
            log.warning("未找到任何TextView元素")
        
        # 检查蓝牙状态
        log.info("🔵 检查蓝牙状态...")
        bluetooth_status = ella_page.check_bluetooth_status()
        log.info(f"蓝牙状态: {'开启' if bluetooth_status else '关闭'}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 调试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
        except:
            pass


def debug_response_detection_logic():
    """调试响应检测逻辑"""
    log.info("🧪 调试响应检测逻辑...")
    
    try:
        ella_page = EllaMainPage()
        
        # 测试各种可能的响应文本
        test_responses = [
            "蓝牙 已打开",
            "蓝牙已打开", 
            "蓝牙已开启",
            "bluetooth enabled",
            "Bluetooth is now on",
            "我已经为您打开蓝牙",
            "蓝牙设置已更改",
            "好的，我已经打开蓝牙",
            "蓝牙功能已启用",
            "已成功开启蓝牙",
            "蓝牙",
            "已打开",
            "成功",
            "完成",
            "OK"
        ]
        
        log.info(f"测试 {len(test_responses)} 个可能的响应文本...")
        
        for i, response in enumerate(test_responses, 1):
            is_ai_response = ella_page._is_ai_response(response)
            is_meaningful = ella_page._is_meaningful_text(response)
            
            status_ai = "✅" if is_ai_response else "❌"
            status_meaningful = "✅" if is_meaningful else "❌"
            
            log.info(f"{i:2d}. '{response}' -> AI响应:{status_ai} 有意义:{status_meaningful}")
        
        return True
        
    except Exception as e:
        log.error(f"调试响应检测逻辑失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 80)
    print("🔍 Ella响应检测调试工具")
    print("=" * 80)
    
    # 选择调试模式
    print("请选择调试模式:")
    print("1. 实际应用响应调试")
    print("2. 响应检测逻辑调试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            print("\n🔍 开始实际应用响应调试...")
            success = debug_ella_response()
        elif choice == "2":
            print("\n🧪 开始响应检测逻辑调试...")
            success = debug_response_detection_logic()
        else:
            print("❌ 无效选择")
            return 1
        
        print("\n" + "=" * 80)
        if success:
            print("✅ 调试完成")
        else:
            print("❌ 调试失败")
        print("=" * 80)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断调试")
        return 1
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
