#!/usr/bin/env python3
"""
Ella测试用例生成示例
展示如何正确使用生成工具，特别是期望响应的设置
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.ella_test_generator_v2 import generate_with_custom_response, batch_generate_with_responses


def example_1_single_generation():
    """示例1: 单个测试用例生成，指定期望响应"""
    print("📝 示例1: 单个测试用例生成")
    print("=" * 50)
    
    # 场景：用户手动测试了"open bluetooth"命令
    # 实际响应：Ella说"蓝牙已成功开启，设备现在可以被发现"
    
    command = "open bluetooth"
    actual_response_from_manual_test = "蓝牙已成功开启，设备现在可以被发现"
    
    print(f"命令: {command}")
    print(f"手动测试的实际响应: {actual_response_from_manual_test}")
    
    # 基于实际响应设置期望内容
    expected_response = [
        "Done",           # 通用成功标识
        "蓝牙已成功开启",    # 核心功能确认
        "设备现在可以被发现"  # 状态描述
    ]
    
    print(f"设置的期望响应: {expected_response}")
    
    # 生成测试用例
    file_path = generate_with_custom_response(command, expected_response)
    print(f"✅ 生成完成: {file_path}")
    print()


def example_2_batch_generation():
    """示例2: 批量生成，每个命令都基于实际测试结果"""
    print("📝 示例2: 批量生成测试用例")
    print("=" * 50)
    
    # 场景：用户已经手动测试了多个命令，记录了实际响应
    commands_and_responses = [
        # 蓝牙命令 - 实际测试响应："蓝牙已开启"
        ("open bluetooth", ["Done", "蓝牙已开启"]),
        
        # 相机命令 - 实际测试响应："相机应用已打开，可以开始拍照"
        ("open camera", ["Done", "相机应用已打开", "可以开始拍照"]),
        
        # 联系人命令 - 实际测试响应："联系人应用已启动"
        ("open contacts", ["Done", "联系人应用已启动"]),
        
        # 导航命令 - 实际测试响应："正在为您规划到上海迪士尼乐园的路线"
        ("navigate to shanghai disneyland", ["Done", "正在为您规划", "上海迪士尼乐园"]),
        
        # 订餐命令 - 实际测试响应："抱歉，当前不支持订餐功能"
        ("order a burger", ["Sorry", "当前不支持订餐功能"]),
        
        # 性能模式 - 实际测试响应："抱歉，您的设备不支持性能模式"
        ("switch to performance mode", ["Sorry", "设备不支持性能模式"])
    ]
    
    print("批量生成以下测试用例:")
    for i, (command, expected) in enumerate(commands_and_responses, 1):
        print(f"{i}. {command} -> {expected}")
    
    print("\n开始生成...")
    generated_files = batch_generate_with_responses(commands_and_responses)
    
    print(f"\n✅ 批量生成完成，共生成 {len(generated_files)} 个测试文件")
    print()


def example_3_response_analysis():
    """示例3: 期望响应分析指南"""
    print("📝 示例3: 期望响应设置指南")
    print("=" * 50)
    
    examples = [
        {
            "command": "open bluetooth",
            "actual_response": "蓝牙已成功开启，设备现在可以被其他设备发现",
            "good_expected": ["Done", "蓝牙已成功开启", "设备现在可以被"],
            "bad_expected_1": ["蓝牙已成功开启，设备现在可以被其他设备发现"],  # 过于具体
            "bad_expected_2": ["Done"],  # 过于宽泛
            "reason": "包含关键信息，但不过于具体，避免因细微变化导致失败"
        },
        {
            "command": "navigate to disneyland",
            "actual_response": "正在为您规划到上海迪士尼乐园的最佳路线，预计需要45分钟",
            "good_expected": ["Done", "正在为您规划", "上海迪士尼乐园"],
            "bad_expected_1": ["正在为您规划到上海迪士尼乐园的最佳路线，预计需要45分钟"],  # 过于具体
            "bad_expected_2": ["规划"],  # 过于宽泛
            "reason": "关注核心功能（规划路线、目的地），忽略可变信息（时间）"
        },
        {
            "command": "order a pizza",
            "actual_response": "抱歉，我目前还不支持订餐功能，建议您使用外卖应用",
            "good_expected": ["Sorry", "不支持订餐功能"],
            "bad_expected_1": ["抱歉，我目前还不支持订餐功能，建议您使用外卖应用"],  # 过于具体
            "bad_expected_2": ["Sorry"],  # 过于宽泛
            "reason": "失败场景重点验证错误类型和核心原因"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. 命令: {example['command']}")
        print(f"   实际响应: {example['actual_response']}")
        print(f"   ✅ 推荐期望: {example['good_expected']}")
        print(f"   ❌ 过于具体: {example['bad_expected_1']}")
        print(f"   ❌ 过于宽泛: {example['bad_expected_2']}")
        print(f"   💡 原因: {example['reason']}")
    
    print()


def example_4_common_mistakes():
    """示例4: 常见错误和解决方案"""
    print("📝 示例4: 常见错误和解决方案")
    print("=" * 50)
    
    mistakes = [
        {
            "mistake": "完全依赖自动建议",
            "problem": "自动建议可能与实际响应不符",
            "solution": "始终先手动测试，记录实际响应",
            "example": "自动建议: ['Done', 'bluetooth'] vs 实际: '蓝牙已成功开启'"
        },
        {
            "mistake": "期望响应过于具体",
            "problem": "响应细微变化导致测试失败",
            "solution": "提取关键信息，忽略可变部分",
            "example": "避免: ['预计需要45分钟'] -> 改为: ['正在规划路线']"
        },
        {
            "mistake": "期望响应过于宽泛",
            "problem": "验证不充分，可能漏掉问题",
            "solution": "包含足够的关键信息进行验证",
            "example": "避免: ['Done'] -> 改为: ['Done', '蓝牙已开启']"
        },
        {
            "mistake": "忽略失败场景",
            "problem": "只考虑成功情况，忽略错误处理",
            "solution": "测试失败场景，设置合适的错误期望",
            "example": "不支持的功能应该期望: ['Sorry', '不支持']"
        }
    ]
    
    for i, mistake in enumerate(mistakes, 1):
        print(f"\n{i}. 错误: {mistake['mistake']}")
        print(f"   问题: {mistake['problem']}")
        print(f"   解决: {mistake['solution']}")
        print(f"   示例: {mistake['example']}")
    
    print()


def main():
    """主函数"""
    print("🚀 Ella测试用例生成示例")
    print("展示如何正确使用生成工具，特别是期望响应的设置")
    print("=" * 80)
    
    # 运行所有示例
    example_1_single_generation()
    example_2_batch_generation()
    example_3_response_analysis()
    example_4_common_mistakes()
    
    print("🎉 示例演示完成！")
    print("\n💡 关键要点:")
    print("1. 始终先手动测试命令，记录实际响应")
    print("2. 基于实际响应设置期望内容")
    print("3. 期望响应要包含关键信息，但不过于具体")
    print("4. 考虑失败场景，设置合适的错误期望")
    print("5. 生成后运行测试，根据结果调整期望响应")


if __name__ == "__main__":
    main()
