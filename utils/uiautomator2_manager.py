"""
UIAutomator2服务管理器
用于管理UIAutomator2服务的启动、停止、重启和健康检查
"""
import subprocess
import time
import uiautomator2 as u2
from typing import Optional, List, Dict, Any
from core.logger import log


class UIAutomator2Manager:
    """UIAutomator2服务管理器"""
    
    def __init__(self):
        """初始化UIAutomator2管理器"""
        self.service_timeout = 30
        self.retry_count = 3
    
    def check_service_health(self, device_id: Optional[str] = None) -> bool:
        """
        检查UIAutomator2服务健康状态
        
        Args:
            device_id: 设备ID，为None时检查默认设备
            
        Returns:
            bool: 服务是否健康
        """
        try:
            log.info(f"检查UIAutomator2服务健康状态 (设备: {device_id or 'default'})")
            
            # 尝试连接设备
            if device_id:
                device = u2.connect(device_id)
            else:
                device = u2.connect()
            
            # 检查设备信息是否可以正常获取
            device_info = device.device_info
            
            # 检查关键信息是否存在
            if not device_info or not device_info.get('serial'):
                log.warning("设备信息不完整")
                return False
            
            # 尝试获取屏幕信息
            window_size = device.window_size()
            if not window_size or len(window_size) != 2:
                log.warning("无法获取屏幕信息")
                return False
            
            log.info("✅ UIAutomator2服务健康状态良好")
            return True
            
        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查失败: {e}")
            return False
    
    def restart_service(self, device_id: Optional[str] = None) -> bool:
        """
        重启UIAutomator2服务
        
        Args:
            device_id: 设备ID，为None时重启默认设备服务
            
        Returns:
            bool: 重启是否成功
        """
        try:
            log.info(f"重启UIAutomator2服务 (设备: {device_id or 'default'})")
            
            # 停止服务
            if not self.stop_service(device_id):
                log.warning("停止服务失败，继续尝试启动")
            
            # 等待一下
            time.sleep(2)
            
            # 启动服务
            if self.start_service(device_id):
                # 验证服务是否正常
                time.sleep(3)
                if self.check_service_health(device_id):
                    log.info("✅ UIAutomator2服务重启成功")
                    return True
                else:
                    log.error("服务重启后健康检查失败")
                    return False
            else:
                log.error("启动服务失败")
                return False
                
        except Exception as e:
            log.error(f"重启UIAutomator2服务失败: {e}")
            return False
    
    def stop_service(self, device_id: Optional[str] = None) -> bool:
        """
        停止UIAutomator2服务
        
        Args:
            device_id: 设备ID，为None时停止默认设备服务
            
        Returns:
            bool: 停止是否成功
        """
        try:
            log.info(f"停止UIAutomator2服务 (设备: {device_id or 'default'})")
            
            # 构建adb命令前缀
            adb_prefix = ["adb"]
            if device_id:
                adb_prefix.extend(["-s", device_id])
            
            # 停止UIAutomator2应用
            stop_cmd = adb_prefix + ["shell", "am", "force-stop", "com.github.uiautomator"]
            result = subprocess.run(stop_cmd, capture_output=True, text=True, timeout=10)
            
            # 杀死uiautomator相关进程
            kill_commands = [
                adb_prefix + ["shell", "pkill", "-f", "uiautomator"],
                adb_prefix + ["shell", "pkill", "-f", "atx"],
                adb_prefix + ["shell", "pkill", "-f", "minicap"],
                adb_prefix + ["shell", "pkill", "-f", "minitouch"]
            ]
            
            for cmd in kill_commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                except:
                    pass
            
            log.info("UIAutomator2服务已停止")
            return True
            
        except Exception as e:
            log.error(f"停止UIAutomator2服务失败: {e}")
            return False
    
    def start_service(self, device_id: Optional[str] = None) -> bool:
        """
        启动UIAutomator2服务
        
        Args:
            device_id: 设备ID，为None时启动默认设备服务
            
        Returns:
            bool: 启动是否成功
        """
        try:
            log.info(f"启动UIAutomator2服务 (设备: {device_id or 'default'})")
            
            # 使用uiautomator2的init命令重新初始化
            if device_id:
                init_cmd = ["python", "-m", "uiautomator2", "init", "--serial", device_id]
            else:
                init_cmd = ["python", "-m", "uiautomator2", "init"]
            
            result = subprocess.run(
                init_cmd, 
                capture_output=True, 
                text=True, 
                timeout=self.service_timeout
            )
            
            if result.returncode == 0:
                log.info("UIAutomator2初始化成功")
            else:
                log.warning(f"UIAutomator2初始化警告: {result.stderr}")
            
            # 等待服务启动
            time.sleep(3)
            
            return True
            
        except Exception as e:
            log.error(f"启动UIAutomator2服务失败: {e}")
            return False
    
    def fix_version_issue(self, device_id: Optional[str] = None) -> bool:
        """
        修复版本相关问题 - 增强版本

        Args:
            device_id: 设备ID，为None时修复默认设备

        Returns:
            bool: 修复是否成功
        """
        try:
            log.info(f"修复UIAutomator2版本问题 (设备: {device_id or 'default'})")

            # 策略1: 标准重启服务
            log.info("尝试策略1: 标准重启服务")
            for attempt in range(self.retry_count):
                log.info(f"第 {attempt + 1} 次尝试修复...")

                if self.restart_service(device_id):
                    # 验证版本问题是否解决
                    if self.check_service_health(device_id):
                        log.info("✅ 标准修复成功")
                        return True

                if attempt < self.retry_count - 1:
                    log.info(f"等待 {(attempt + 1) * 2} 秒后重试...")
                    time.sleep((attempt + 1) * 2)

            # 策略2: 强制清理和重新初始化
            log.info("尝试策略2: 强制清理和重新初始化")
            if self._force_cleanup_and_reinit(device_id):
                if self.check_service_health(device_id):
                    log.info("✅ 强制修复成功")
                    return True

            # 策略3: 重新安装UIAutomator2
            log.info("尝试策略3: 重新安装UIAutomator2")
            if self._reinstall_uiautomator2(device_id):
                if self.check_service_health(device_id):
                    log.info("✅ 重新安装修复成功")
                    return True

            log.error("❌ 所有修复策略都失败了")
            return False

        except Exception as e:
            log.error(f"修复版本问题失败: {e}")
            return False

    def _force_cleanup_and_reinit(self, device_id: Optional[str] = None) -> bool:
        """
        强制清理和重新初始化

        Args:
            device_id: 设备ID

        Returns:
            bool: 是否成功
        """
        try:
            log.info("执行强制清理和重新初始化...")

            # 构建adb命令前缀
            adb_prefix = ["adb"]
            if device_id:
                adb_prefix.extend(["-s", device_id])

            # 1. 强制停止所有相关进程
            force_stop_commands = [
                adb_prefix + ["shell", "am", "force-stop", "com.github.uiautomator"],
                adb_prefix + ["shell", "am", "force-stop", "com.github.uiautomator.test"],
                adb_prefix + ["shell", "pkill", "-9", "-f", "uiautomator"],
                adb_prefix + ["shell", "pkill", "-9", "-f", "atx"],
                adb_prefix + ["shell", "pkill", "-9", "-f", "minicap"],
                adb_prefix + ["shell", "pkill", "-9", "-f", "minitouch"]
            ]

            for cmd in force_stop_commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                except:
                    pass

            # 2. 清理临时文件和缓存
            cleanup_commands = [
                adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/minicap*"],
                adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/minitouch*"],
                adb_prefix + ["shell", "rm", "-rf", "/data/local/tmp/atx*"],
                adb_prefix + ["shell", "pm", "clear", "com.github.uiautomator"],
                adb_prefix + ["shell", "pm", "clear", "com.github.uiautomator.test"]
            ]

            for cmd in cleanup_commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                except:
                    pass

            # 3. 重启ADB服务
            subprocess.run(["adb", "kill-server"], capture_output=True, text=True, timeout=10)
            time.sleep(2)
            subprocess.run(["adb", "start-server"], capture_output=True, text=True, timeout=10)
            time.sleep(3)

            # 4. 重新初始化UIAutomator2
            if device_id:
                init_cmd = ["python", "-m", "uiautomator2", "init", "--serial", device_id]
            else:
                init_cmd = ["python", "-m", "uiautomator2", "init"]

            result = subprocess.run(init_cmd, capture_output=True, text=True, timeout=60)

            # 5. 等待服务启动
            time.sleep(5)

            log.info("强制清理和重新初始化完成")
            return True

        except Exception as e:
            log.error(f"强制清理和重新初始化失败: {e}")
            return False

    def _reinstall_uiautomator2(self, device_id: Optional[str] = None) -> bool:
        """
        重新安装UIAutomator2

        Args:
            device_id: 设备ID

        Returns:
            bool: 是否成功
        """
        try:
            log.info("重新安装UIAutomator2...")

            # 构建adb命令前缀
            adb_prefix = ["adb"]
            if device_id:
                adb_prefix.extend(["-s", device_id])

            # 1. 卸载现有应用
            uninstall_commands = [
                adb_prefix + ["uninstall", "com.github.uiautomator"],
                adb_prefix + ["uninstall", "com.github.uiautomator.test"]
            ]

            for cmd in uninstall_commands:
                try:
                    subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                except:
                    pass

            # 2. 重新安装
            if device_id:
                install_cmd = ["python", "-m", "uiautomator2", "init", "--serial", device_id, "--reinstall"]
            else:
                install_cmd = ["python", "-m", "uiautomator2", "init", "--reinstall"]

            result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=120)

            # 3. 等待安装完成
            time.sleep(10)

            log.info("UIAutomator2重新安装完成")
            return True

        except Exception as e:
            log.error(f"重新安装UIAutomator2失败: {e}")
            return False
    
    def get_service_status(self, device_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取UIAutomator2服务状态
        
        Args:
            device_id: 设备ID，为None时获取默认设备状态
            
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        try:
            status = {
                'device_id': device_id or 'default',
                'service_running': False,
                'health_check': False,
                'device_info': None,
                'error': None
            }
            
            # 检查服务是否运行
            adb_prefix = ["adb"]
            if device_id:
                adb_prefix.extend(["-s", device_id])
            
            check_cmd = adb_prefix + ["shell", "ps", "|", "grep", "uiautomator"]
            result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=10)
            
            if "uiautomator" in result.stdout:
                status['service_running'] = True
            
            # 健康检查
            status['health_check'] = self.check_service_health(device_id)
            
            # 获取设备信息
            if status['health_check']:
                try:
                    if device_id:
                        device = u2.connect(device_id)
                    else:
                        device = u2.connect()
                    status['device_info'] = device.device_info
                except Exception as e:
                    status['error'] = str(e)
            
            return status
            
        except Exception as e:
            return {
                'device_id': device_id or 'default',
                'service_running': False,
                'health_check': False,
                'device_info': None,
                'error': str(e)
            }


# 全局UIAutomator2管理器实例
uiautomator2_manager = UIAutomator2Manager()
