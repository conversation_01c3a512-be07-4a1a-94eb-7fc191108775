"""
YAML工具类
用于读取和解析YAML配置文件
"""
import os
import yaml
from typing import Dict, Any


class YamlUtils:
    """YAML文件操作工具类"""
    
    @staticmethod
    def load_yaml(file_path: str) -> Dict[str, Any]:
        """
        加载YAML文件
        
        Args:
            file_path: YAML文件路径
            
        Returns:
            Dict: 解析后的字典数据
            
        Raises:
            FileNotFoundError: 文件不存在
            yaml.YAMLError: YAML格式错误
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"YAML文件不存在: {file_path}")
            
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file) or {}
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"YAML文件格式错误: {e}")
    
    @staticmethod
    def save_yaml(data: Dict[str, Any], file_path: str) -> None:
        """
        保存数据到YAML文件
        
        Args:
            data: 要保存的字典数据
            file_path: 保存的文件路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as file:
            yaml.safe_dump(data, file, default_flow_style=False, 
                          allow_unicode=True, indent=2)
    
    @staticmethod
    def get_project_root() -> str:
        """
        获取项目根目录
        
        Returns:
            str: 项目根目录路径
        """
        current_file = os.path.abspath(__file__)
        # 向上查找两级目录到项目根目录
        return os.path.dirname(os.path.dirname(current_file))
    
    @staticmethod
    def get_config_path(config_name: str) -> str:
        """
        获取配置文件的完整路径
        
        Args:
            config_name: 配置文件名
            
        Returns:
            str: 配置文件完整路径
        """
        project_root = YamlUtils.get_project_root()
        return os.path.join(project_root, "config", config_name)
