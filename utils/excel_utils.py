"""
Excel数据读取工具类
支持从Excel文件读取测试数据，包括step和except字段
"""
import pandas as pd
import os
from typing import List, Dict, Any, Optional
from core.logger import log


class ExcelUtils:
    """Excel数据处理工具类"""
    
    @staticmethod
    def read_test_data(file_path: str, sheet_name: str = None) -> List[Dict[str, Any]]:
        """
        从Excel文件读取测试数据
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，默认读取第一个工作表
            
        Returns:
            List[Dict[str, Any]]: 测试数据列表
        """
        try:
            if not os.path.exists(file_path):
                log.error(f"Excel文件不存在: {file_path}")
                return []
            
            log.info(f"读取Excel文件: {file_path}")
            
            # 读取Excel文件
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            
            # 检查必要的列是否存在
            required_columns = ['step', 'except']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                log.error(f"Excel文件缺少必要的列: {missing_columns}")
                log.info(f"当前列: {list(df.columns)}")
                return []
            
            # 过滤掉空行
            df = df.dropna(subset=['step'])
            
            # 转换为字典列表
            test_data = []
            for index, row in df.iterrows():
                data = {
                    'case_id': row.get('case_id', f"case_{index + 1}"),
                    'case_name': row.get('case_name', f"测试用例_{index + 1}"),
                    'step': str(row['step']).strip(),
                    'except': str(row['except']).strip(),
                    'priority': row.get('priority', 'normal'),
                    'enabled': row.get('enabled', True),
                    'description': row.get('description', ''),
                    'tags': row.get('tags', ''),
                    'timeout': row.get('timeout', 15)
                }
                
                # 只添加启用的测试用例
                if data['enabled'] and data['step']:
                    test_data.append(data)
                    log.debug(f"读取测试数据: {data['case_name']} - {data['step']}")
            
            log.info(f"成功读取 {len(test_data)} 条测试数据")
            return test_data
            
        except Exception as e:
            log.error(f"读取Excel文件失败: {e}")
            return []
    
    @staticmethod
    def read_ella_test_data(file_path: str = None) -> List[Dict[str, Any]]:
        """
        读取Ella测试数据
        
        Args:
            file_path: Excel文件路径，默认使用testcases/test_ella/Ella_Case.xlsx
            
        Returns:
            List[Dict[str, Any]]: Ella测试数据列表
        """
        if file_path is None:
            # 默认使用项目中的Ella测试数据文件
            file_path = "testcases/test_ella/Ella_Case.xlsx"
        
        return ExcelUtils.read_test_data(file_path)
    
    @staticmethod
    def validate_test_data(test_data: List[Dict[str, Any]]) -> bool:
        """
        验证测试数据的有效性
        
        Args:
            test_data: 测试数据列表
            
        Returns:
            bool: 数据是否有效
        """
        if not test_data:
            log.error("测试数据为空")
            return False
        
        for i, data in enumerate(test_data):
            # 检查必要字段
            if not data.get('step'):
                log.error(f"第{i+1}行数据缺少step字段")
                return False
            
            if not data.get('except'):
                log.error(f"第{i+1}行数据缺少except字段")
                return False
        
        log.info(f"测试数据验证通过，共{len(test_data)}条数据")
        return True
    
    @staticmethod
    def filter_test_data(test_data: List[Dict[str, Any]], 
                        priority: str = None, 
                        tags: str = None) -> List[Dict[str, Any]]:
        """
        根据条件过滤测试数据
        
        Args:
            test_data: 原始测试数据
            priority: 优先级过滤 (high, normal, low)
            tags: 标签过滤
            
        Returns:
            List[Dict[str, Any]]: 过滤后的测试数据
        """
        filtered_data = test_data.copy()
        
        if priority:
            filtered_data = [data for data in filtered_data 
                           if data.get('priority', 'normal').lower() == priority.lower()]
            log.info(f"按优先级'{priority}'过滤后剩余{len(filtered_data)}条数据")
        
        if tags:
            filtered_data = [data for data in filtered_data 
                           if tags.lower() in data.get('tags', '').lower()]
            log.info(f"按标签'{tags}'过滤后剩余{len(filtered_data)}条数据")
        
        return filtered_data
    
    @staticmethod
    def create_sample_excel(file_path: str) -> bool:
        """
        创建示例Excel文件
        
        Args:
            file_path: 要创建的Excel文件路径
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 示例数据
            sample_data = [
                {
                    'case_id': 'ELLA_001',
                    'case_name': '测试开启蓝牙',
                    'step': 'open bluetooth',
                    'except': '蓝牙已开启',
                    'priority': 'high',
                    'enabled': True,
                    'description': '通过语音命令开启蓝牙功能',
                    'tags': 'bluetooth,voice',
                    'timeout': 15
                },
                {
                    'case_id': 'ELLA_002',
                    'case_name': '测试关闭蓝牙',
                    'step': 'close bluetooth',
                    'except': '蓝牙已关闭',
                    'priority': 'high',
                    'enabled': True,
                    'description': '通过语音命令关闭蓝牙功能',
                    'tags': 'bluetooth,voice',
                    'timeout': 15
                },
                {
                    'case_id': 'ELLA_003',
                    'case_name': '测试打开时钟',
                    'step': 'open clock',
                    'except': 'com.android.deskclock',
                    'priority': 'normal',
                    'enabled': True,
                    'description': '通过语音命令打开时钟应用',
                    'tags': 'app,voice',
                    'timeout': 10
                },
                {
                    'case_id': 'ELLA_004',
                    'case_name': '测试查询天气',
                    'step': 'what is the weather today',
                    'except': '天气',
                    'priority': 'normal',
                    'enabled': True,
                    'description': '查询今天的天气情况',
                    'tags': 'weather,voice',
                    'timeout': 20
                }
            ]
            
            # 创建DataFrame
            df = pd.DataFrame(sample_data)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存到Excel文件
            df.to_excel(file_path, index=False, sheet_name='TestCases')
            
            log.info(f"成功创建示例Excel文件: {file_path}")
            return True
            
        except Exception as e:
            log.error(f"创建示例Excel文件失败: {e}")
            return False


if __name__ == '__main__':
    # 测试Excel工具类
    excel_utils = ExcelUtils()
    
    # 创建示例文件
    sample_file = "testcases/test_ella/Ella_Test_Sample.xlsx"
    excel_utils.create_sample_excel(sample_file)
    
    # 读取测试数据
    test_data = excel_utils.read_test_data(sample_file)
    print(f"读取到{len(test_data)}条测试数据")
    
    for data in test_data:
        print(f"- {data['case_name']}: {data['step']} -> {data['except']}")
