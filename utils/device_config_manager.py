"""
设备配置管理器
管理devices.yaml配置文件的更新和维护
"""
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from core.logger import log
from utils.yaml_utils import YamlUtils
from utils.device_discovery import device_discovery


class DeviceConfigManager:
    """设备配置管理器"""
    
    def __init__(self):
        """初始化设备配置管理器"""
        self.config_path = YamlUtils.get_config_path("devices.yaml")
        self.backup_dir = os.path.join(YamlUtils.get_project_root(), "config", "backups")
    
    def load_current_config(self) -> Dict[str, Any]:
        """
        加载当前设备配置
        
        Returns:
            Dict[str, Any]: 当前配置字典
        """
        try:
            return YamlUtils.load_yaml(self.config_path)
        except Exception as e:
            log.warning(f"加载设备配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置结构
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            'devices': {
                'default': {
                    'device_id': '',
                    'platform_version': '',
                    'device_name': 'Android Device'
                }
            },
            'current_device': 'default',
            'auto_discovery': {
                'enabled': True,
                'last_update': '',
                'discovered_devices': []
            }
        }
    
    def backup_config(self) -> str:
        """
        备份当前配置文件
        
        Returns:
            str: 备份文件路径
        """
        try:
            # 确保备份目录存在
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"devices_backup_{timestamp}.yaml"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # 复制当前配置到备份文件
            if os.path.exists(self.config_path):
                import shutil
                shutil.copy2(self.config_path, backup_path)
                log.info(f"配置文件已备份: {backup_path}")
            else:
                log.warning("原配置文件不存在，跳过备份")
            
            return backup_path
            
        except Exception as e:
            log.error(f"备份配置文件失败: {e}")
            return ""
    
    def generate_device_config_name(self, device_info: Dict[str, Any]) -> str:
        """
        生成设备配置名称
        
        Args:
            device_info: 设备信息字典
            
        Returns:
            str: 配置名称
        """
        # 使用品牌+型号+序列号后4位生成唯一名称
        brand = device_info.get('brand', '').lower()
        model = device_info.get('model', '').replace(' ', '_').lower()
        device_id = device_info.get('device_id', '')
        
        # 取序列号后4位
        serial_suffix = device_id[-4:] if len(device_id) >= 4 else device_id
        
        if brand and model:
            config_name = f"{brand}_{model}_{serial_suffix}"
        elif model:
            config_name = f"{model}_{serial_suffix}"
        else:
            config_name = f"device_{serial_suffix}"
        
        # 清理特殊字符
        config_name = config_name.replace('-', '_').replace('.', '_')
        
        return config_name
    
    def convert_device_info_to_config(self, device_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        将设备信息转换为配置格式
        
        Args:
            device_info: 设备发现信息
            
        Returns:
            Dict[str, Any]: 设备配置字典
        """
        return {
            'device_id': device_info.get('device_id', ''),
            'platform_version': device_info.get('platform_version', ''),
            'device_name': device_info.get('device_name', 'Unknown Device'),
            'brand': device_info.get('brand', ''),
            'model': device_info.get('model', ''),
            'android_version': device_info.get('android_version', ''),
            'screen_resolution': device_info.get('screen_resolution', ''),
            'cpu_abi': device_info.get('cpu_abi', ''),
            'last_updated': device_info.get('discovery_time', ''),
            'auto_discovered': True
        }
    
    def update_config_with_discovered_devices(self, discovered_devices: List[Dict[str, Any]], 
                                            set_as_current: bool = True) -> bool:
        """
        使用发现的设备更新配置文件
        
        Args:
            discovered_devices: 发现的设备列表
            set_as_current: 是否将第一个设备设为当前设备
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not discovered_devices:
                log.warning("没有发现的设备，跳过配置更新")
                return False
            
            # 备份当前配置
            self.backup_config()
            
            # 加载当前配置
            current_config = self.load_current_config()
            
            # 更新发现信息
            current_config['auto_discovery'] = {
                'enabled': True,
                'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'discovered_devices': len(discovered_devices)
            }
            
            # 添加或更新设备配置
            updated_devices = []
            for device_info in discovered_devices:
                config_name = self.generate_device_config_name(device_info)
                device_config = self.convert_device_info_to_config(device_info)
                
                # 添加到配置中
                current_config['devices'][config_name] = device_config
                updated_devices.append(config_name)
                
                log.info(f"添加设备配置: {config_name} -> {device_info['device_name']}")
            
            # 设置当前设备
            if set_as_current and updated_devices:
                current_config['current_device'] = updated_devices[0]
                log.info(f"设置当前设备: {updated_devices[0]}")
            
            # 保存配置
            YamlUtils.save_yaml(current_config, self.config_path)
            
            log.info(f"设备配置更新完成，共更新 {len(updated_devices)} 个设备")
            return True
            
        except Exception as e:
            log.error(f"更新设备配置失败: {e}")
            return False
    
    def auto_discover_and_update(self, set_as_current: bool = True) -> bool:
        """
        自动发现设备并更新配置
        
        Args:
            set_as_current: 是否将第一个发现的设备设为当前设备
            
        Returns:
            bool: 操作是否成功
        """
        log.info("开始自动发现和更新设备配置...")
        
        # 发现所有设备
        discovered_devices = device_discovery.discover_all_devices()
        
        if not discovered_devices:
            log.warning("未发现任何设备，配置未更新")
            return False
        
        # 打印发现的设备
        device_discovery.print_device_summary(discovered_devices)
        
        # 更新配置
        success = self.update_config_with_discovered_devices(discovered_devices, set_as_current)
        
        if success:
            log.info("✅ 设备配置自动更新完成")
            self.print_updated_config()
        else:
            log.error("❌ 设备配置更新失败")
        
        return success
    
    def print_updated_config(self) -> None:
        """打印更新后的配置信息"""
        try:
            config = self.load_current_config()
            
            log.info("\n" + "=" * 60)
            log.info("📋 更新后的设备配置")
            log.info("=" * 60)
            
            current_device = config.get('current_device', 'default')
            log.info(f"当前设备: {current_device}")
            
            devices = config.get('devices', {})
            log.info(f"配置的设备数量: {len(devices)}")
            
            for config_name, device_config in devices.items():
                is_current = "✓" if config_name == current_device else " "
                log.info(f"\n[{is_current}] {config_name}:")
                log.info(f"    设备ID: {device_config.get('device_id', '')}")
                log.info(f"    设备名称: {device_config.get('device_name', '')}")
                log.info(f"    HiOS版本: {device_config.get('platform_version', '')}")
                log.info(f"    Android版本: {device_config.get('android_version', '')}")
                log.info(f"    屏幕分辨率: {device_config.get('screen_resolution', '')}")
                
                if device_config.get('auto_discovered'):
                    log.info(f"    最后更新: {device_config.get('last_updated', '')}")
            
            auto_discovery = config.get('auto_discovery', {})
            if auto_discovery:
                log.info(f"\n自动发现状态:")
                log.info(f"  启用: {auto_discovery.get('enabled', False)}")
                log.info(f"  最后更新: {auto_discovery.get('last_update', '')}")
                log.info(f"  发现设备数: {auto_discovery.get('discovered_devices', 0)}")
            
        except Exception as e:
            log.error(f"打印配置信息失败: {e}")
    
    def switch_current_device(self, config_name: str) -> bool:
        """
        切换当前设备
        
        Args:
            config_name: 设备配置名称
            
        Returns:
            bool: 切换是否成功
        """
        try:
            config = self.load_current_config()
            
            if config_name not in config.get('devices', {}):
                log.error(f"设备配置 '{config_name}' 不存在")
                return False
            
            config['current_device'] = config_name
            YamlUtils.save_yaml(config, self.config_path)
            
            device_info = config['devices'][config_name]
            log.info(f"已切换到设备: {config_name} ({device_info.get('device_name', '')})")
            
            return True
            
        except Exception as e:
            log.error(f"切换设备失败: {e}")
            return False
    
    def list_available_devices(self) -> List[str]:
        """
        列出所有可用的设备配置
        
        Returns:
            List[str]: 设备配置名称列表
        """
        try:
            config = self.load_current_config()
            devices = config.get('devices', {})
            return list(devices.keys())
        except Exception as e:
            log.error(f"获取设备列表失败: {e}")
            return []
    
    def remove_device_config(self, config_name: str) -> bool:
        """
        删除设备配置
        
        Args:
            config_name: 设备配置名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            config = self.load_current_config()
            
            if config_name not in config.get('devices', {}):
                log.error(f"设备配置 '{config_name}' 不存在")
                return False
            
            # 不能删除当前设备
            if config.get('current_device') == config_name:
                log.error(f"不能删除当前使用的设备配置: {config_name}")
                return False
            
            # 删除配置
            del config['devices'][config_name]
            YamlUtils.save_yaml(config, self.config_path)
            
            log.info(f"已删除设备配置: {config_name}")
            return True
            
        except Exception as e:
            log.error(f"删除设备配置失败: {e}")
            return False


# 全局设备配置管理器实例
device_config_manager = DeviceConfigManager()

if __name__ == '__main__':
    device_config_manager.auto_discover_and_update()