"""
截图工具类
提供增强的截图功能，支持按测试类分文件夹管理
"""
import os
import inspect
from typing import Optional, Dict, Any
from datetime import datetime
from core.logger import log
from utils.file_utils import FileUtils
from utils.yaml_utils import YamlUtils


class ScreenshotManager:
    """截图管理器"""
    
    def __init__(self):
        """初始化截图管理器"""
        self.config = self._load_config()
        self.base_screenshot_dir = self.config.get("app", {}).get("screenshot_path", "reports/screenshots")
        self.project_root = YamlUtils.get_project_root()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            return YamlUtils.load_yaml(config_path)
        except Exception as e:
            log.warning(f"加载截图配置失败: {e}")
            return {}
    
    def get_test_context(self) -> Dict[str, str]:
        """
        获取测试上下文信息
        
        Returns:
            Dict: 包含测试类名、测试方法名等信息
        """
        context = {
            "test_class": "unknown",
            "test_method": "unknown",
            "test_file": "unknown"
        }
        
        try:
            # 获取调用栈
            for frame_info in inspect.stack():
                frame = frame_info.frame
                function_name = frame_info.function
                filename = frame_info.filename
                
                # 跳过当前文件和框架文件
                if 'screenshot_utils.py' in filename or 'conftest.py' in filename:
                    continue
                
                # 查找测试类实例
                if 'self' in frame.f_locals:
                    test_instance = frame.f_locals['self']
                    class_name = test_instance.__class__.__name__
                    if class_name.startswith('Test'):
                        context["test_class"] = class_name
                        if function_name.startswith('test_'):
                            context["test_method"] = function_name
                        context["test_file"] = os.path.basename(filename)
                        break
                
                # 查找测试函数
                if function_name.startswith('test_'):
                    context["test_method"] = function_name
                    context["test_file"] = os.path.basename(filename)
                    
                    # 从文件名推断测试类名
                    if 'test_' in filename:
                        base_name = os.path.basename(filename)
                        if base_name.startswith('test_'):
                            name_part = base_name.replace('test_', '').replace('.py', '')
                            context["test_class"] = f"Test{name_part.title()}"
                    break
            
            # 尝试从pytest获取信息
            try:
                import pytest
                if hasattr(pytest, 'current_item') and pytest.current_item:
                    item = pytest.current_item
                    if hasattr(item, 'cls') and item.cls:
                        context["test_class"] = item.cls.__name__
                    if hasattr(item, 'name'):
                        context["test_method"] = item.name
                    if hasattr(item, 'fspath'):
                        context["test_file"] = os.path.basename(str(item.fspath))
            except ImportError:
                pass
                
        except Exception as e:
            log.debug(f"获取测试上下文失败: {e}")
        
        return context
    
    def get_screenshot_directory(self, test_class: Optional[str] = None) -> str:
        """
        获取截图目录
        
        Args:
            test_class: 测试类名称，为None时自动获取
            
        Returns:
            str: 截图目录完整路径
        """
        if test_class is None:
            context = self.get_test_context()
            test_class = context["test_class"]
        
        # 创建按测试类分组的目录结构
        screenshot_dir = os.path.join(self.project_root, self.base_screenshot_dir, test_class)
        FileUtils.ensure_dir(screenshot_dir)
        
        return screenshot_dir
    
    def generate_screenshot_filename(self, prefix: str = "screenshot", 
                                   include_test_method: bool = True) -> str:
        """
        生成截图文件名
        
        Args:
            prefix: 文件名前缀
            include_test_method: 是否包含测试方法名
            
        Returns:
            str: 截图文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 包含毫秒
        
        if include_test_method:
            context = self.get_test_context()
            test_method = context["test_method"]
            if test_method != "unknown":
                return f"{prefix}_{test_method}_{timestamp}.png"
        
        return f"{prefix}_{timestamp}.png"
    
    def take_screenshot(self, driver, filename: Optional[str] = None, 
                       description: str = "") -> str:
        """
        执行截图
        
        Args:
            driver: UIAutomator2驱动实例
            filename: 截图文件名，为None时自动生成
            description: 截图描述
            
        Returns:
            str: 截图文件完整路径
        """
        try:
            # 生成文件名
            if filename is None:
                filename = self.generate_screenshot_filename()
            
            # 获取截图目录
            screenshot_dir = self.get_screenshot_directory()
            
            # 完整路径
            screenshot_path = os.path.join(screenshot_dir, filename)
            
            # 执行截图
            driver.screenshot(screenshot_path)
            
            # 记录日志
            context = self.get_test_context()
            log.info(f"截图保存: {screenshot_path}")
            log.debug(f"截图上下文: 类={context['test_class']}, 方法={context['test_method']}, 描述={description}")
            
            return screenshot_path
            
        except Exception as e:
            log.error(f"截图失败: {e}")
            raise
    
    def cleanup_old_screenshots(self, days: int = 7) -> None:
        """
        清理旧截图
        
        Args:
            days: 保留天数
        """
        try:
            import time
            from pathlib import Path
            
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            base_dir = os.path.join(self.project_root, self.base_screenshot_dir)
            
            if not os.path.exists(base_dir):
                return
            
            deleted_count = 0
            for root, dirs, files in os.walk(base_dir):
                for file in files:
                    if file.endswith('.png'):
                        file_path = os.path.join(root, file)
                        if os.path.getmtime(file_path) < cutoff_time:
                            os.remove(file_path)
                            deleted_count += 1
            
            log.info(f"清理了 {deleted_count} 个超过 {days} 天的截图文件")
            
        except Exception as e:
            log.error(f"清理旧截图失败: {e}")
    
    def get_screenshot_summary(self) -> Dict[str, Any]:
        """
        获取截图统计信息
        
        Returns:
            Dict: 截图统计信息
        """
        try:
            base_dir = os.path.join(self.project_root, self.base_screenshot_dir)
            
            if not os.path.exists(base_dir):
                return {"total_files": 0, "test_classes": [], "total_size": 0}
            
            summary = {
                "total_files": 0,
                "test_classes": [],
                "total_size": 0,
                "by_class": {}
            }
            
            for item in os.listdir(base_dir):
                item_path = os.path.join(base_dir, item)
                if os.path.isdir(item_path):
                    # 这是一个测试类目录
                    class_info = {
                        "name": item,
                        "file_count": 0,
                        "size": 0
                    }
                    
                    for file in os.listdir(item_path):
                        if file.endswith('.png'):
                            file_path = os.path.join(item_path, file)
                            file_size = os.path.getsize(file_path)
                            class_info["file_count"] += 1
                            class_info["size"] += file_size
                    
                    summary["test_classes"].append(item)
                    summary["by_class"][item] = class_info
                    summary["total_files"] += class_info["file_count"]
                    summary["total_size"] += class_info["size"]
                
                elif item.endswith('.png'):
                    # 根目录下的截图文件
                    file_size = os.path.getsize(item_path)
                    summary["total_files"] += 1
                    summary["total_size"] += file_size
            
            return summary
            
        except Exception as e:
            log.error(f"获取截图统计失败: {e}")
            return {"total_files": 0, "test_classes": [], "total_size": 0}


# 全局截图管理器实例
screenshot_manager = ScreenshotManager()
