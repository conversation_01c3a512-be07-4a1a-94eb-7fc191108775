"""
文件工具类
提供文件和目录操作的通用方法
"""
import os
import shutil
from datetime import datetime
from typing import Optional


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_dir(dir_path: str) -> None:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            dir_path: 目录路径
        """
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
    
    @staticmethod
    def clean_dir(dir_path: str) -> None:
        """
        清空目录内容
        
        Args:
            dir_path: 目录路径
        """
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
        os.makedirs(dir_path, exist_ok=True)
    
    @staticmethod
    def get_timestamp() -> str:
        """
        获取当前时间戳字符串
        
        Returns:
            str: 格式化的时间戳
        """
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    @staticmethod
    def get_screenshot_name(prefix: str = "screenshot") -> str:
        """
        生成截图文件名

        Args:
            prefix: 文件名前缀

        Returns:
            str: 截图文件名
        """
        timestamp = FileUtils.get_timestamp()
        return f"{prefix}_{timestamp}.png"

    @staticmethod
    def get_test_class_name() -> str:
        """
        获取当前运行的测试类名称

        Returns:
            str: 测试类名称，如果无法获取则返回'unknown'
        """
        import inspect
        import pytest

        try:
            # 获取当前调用栈
            for frame_info in inspect.stack():
                frame = frame_info.frame

                # 查找pytest相关的frame
                if 'pytest' in frame.f_globals.get('__name__', ''):
                    continue

                # 查找测试类
                if 'self' in frame.f_locals:
                    test_instance = frame.f_locals['self']
                    class_name = test_instance.__class__.__name__
                    if class_name.startswith('Test'):
                        return class_name

                # 查找测试函数所在的模块
                if frame_info.function.startswith('test_'):
                    # 从文件名推断测试类名
                    filename = frame_info.filename
                    if 'test_' in filename:
                        # 提取文件名中的测试类信息
                        import os
                        base_name = os.path.basename(filename)
                        if base_name.startswith('test_'):
                            # 将 test_calculator.py 转换为 TestCalculator
                            name_part = base_name.replace('test_', '').replace('.py', '')
                            return f"Test{name_part.title()}"

            # 如果无法从调用栈获取，尝试从pytest当前项目获取
            if hasattr(pytest, 'current_item') and pytest.current_item:
                item = pytest.current_item
                if hasattr(item, 'cls') and item.cls:
                    return item.cls.__name__
                elif hasattr(item, 'parent') and hasattr(item.parent, 'name'):
                    return item.parent.name

            return "unknown"

        except Exception:
            return "unknown"

    @staticmethod
    def get_screenshot_dir_by_test_class(base_dir: str = "reports/screenshots") -> str:
        """
        根据测试类名称获取截图目录

        Args:
            base_dir: 基础截图目录

        Returns:
            str: 完整的截图目录路径
        """
        test_class = FileUtils.get_test_class_name()
        return os.path.join(base_dir, test_class)
    
    @staticmethod
    def copy_file(src: str, dst: str) -> None:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
        """
        # 确保目标目录存在
        dst_dir = os.path.dirname(dst)
        FileUtils.ensure_dir(dst_dir)
        
        shutil.copy2(src, dst)
    
    @staticmethod
    def get_file_size(file_path: str) -> Optional[int]:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[int]: 文件大小(字节)，文件不存在返回None
        """
        if os.path.exists(file_path):
            return os.path.getsize(file_path)
        return None
