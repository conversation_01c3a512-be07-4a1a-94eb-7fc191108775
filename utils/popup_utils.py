"""
弹窗处理工具模块 - 适配UIAutomator2
提供弹窗处理的装饰器和工具函数
"""
import time
import functools
import uiautomator2 as u2
from typing import Callable, Any, Optional
from core.logger import log
from core.popup_monitor import PopupMonitor, PopupConfig


def with_popup_handling(
    auto_handle: bool = True,
    monitor_during_test: bool = True,
    config_file: str = None,
    handle_before: bool = True,
    handle_after: bool = True,
    max_retries: int = 3
):
    """
    弹窗处理装饰器
    
    Args:
        auto_handle: 是否自动处理弹窗
        monitor_during_test: 是否在测试期间监控弹窗
        config_file: 配置文件路径
        handle_before: 是否在操作前处理弹窗
        handle_after: 是否在操作后处理弹窗
        max_retries: 最大重试次数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取测试实例和driver
            test_instance = args[0] if args else None
            driver = None
            popup_monitor = None
            
            # 尝试获取driver
            if hasattr(test_instance, 'driver'):
                driver = test_instance.driver
            elif hasattr(test_instance, 'app') and hasattr(test_instance.app, 'driver'):
                driver = test_instance.app.driver
            elif hasattr(test_instance, 'page') and hasattr(test_instance.page, 'driver'):
                driver = test_instance.page.driver
            
            if driver is None:
                log.warning("未找到driver，跳过弹窗处理")
                return func(*args, **kwargs)
            
            # 创建弹窗监控器
            config = PopupConfig(config_file) if config_file else PopupConfig()
            popup_monitor = PopupMonitor(driver, config)
            
            # 设置自动处理
            config.config['handling']['auto_handle'] = auto_handle
            
            try:
                # 启动监控
                if monitor_during_test:
                    popup_monitor.start_monitoring()
                
                # 操作前处理弹窗
                if handle_before:
                    popup_monitor.handle_popup_immediately()
                
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 操作后处理弹窗
                if handle_after:
                    time.sleep(0.5)  # 等待可能的弹窗出现
                    popup_monitor.handle_popup_immediately()
                
                return result
                
            except Exception as e:
                # 出错时也尝试处理弹窗
                try:
                    popup_monitor.handle_popup_immediately()
                except:
                    pass
                raise e
                
            finally:
                # 停止监控
                if monitor_during_test and popup_monitor:
                    popup_monitor.stop_monitoring()
        
        return wrapper
    return decorator


def handle_popups_on_error(max_retries: int = 3, delay: float = 1.0):
    """
    错误时处理弹窗的装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟时间
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        log.warning(f"操作失败，尝试处理弹窗后重试 (第{attempt+1}次): {e}")
                        
                        # 尝试获取driver并处理弹窗
                        test_instance = args[0] if args else None
                        driver = None
                        
                        if hasattr(test_instance, 'driver'):
                            driver = test_instance.driver
                        elif hasattr(test_instance, 'app') and hasattr(test_instance.app, 'driver'):
                            driver = test_instance.app.driver
                        
                        if driver:
                            try:
                                popup_monitor = PopupMonitor(driver)
                                popup_monitor.handle_popup_immediately()
                            except Exception as popup_error:
                                log.debug(f"处理弹窗失败: {popup_error}")
                        
                        time.sleep(delay)
                    else:
                        log.error(f"重试{max_retries}次后仍然失败")
            
            raise last_exception
        
        return wrapper
    return decorator


class PopupAwarePageBase:
    """支持弹窗处理的页面基类 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device, config: PopupConfig = None):
        self.driver = driver
        self.popup_monitor = PopupMonitor(driver, config)
        self.auto_handle_popups = True
        self._popup_handling_enabled = True
    
    def enable_popup_handling(self):
        """启用弹窗处理"""
        self._popup_handling_enabled = True
        log.info("页面弹窗处理已启用")
    
    def disable_popup_handling(self):
        """禁用弹窗处理"""
        self._popup_handling_enabled = False
        log.info("页面弹窗处理已禁用")
    
    def start_popup_monitoring(self):
        """启动弹窗监控"""
        if self._popup_handling_enabled:
            self.popup_monitor.start_monitoring()
    
    def stop_popup_monitoring(self):
        """停止弹窗监控"""
        self.popup_monitor.stop_monitoring()
    
    def handle_popups_before_action(self):
        """在执行操作前处理弹窗"""
        if self._popup_handling_enabled and self.auto_handle_popups:
            self.popup_monitor.handle_popup_immediately()
    
    def handle_popups_after_action(self):
        """在执行操作后处理弹窗"""
        if self._popup_handling_enabled and self.auto_handle_popups:
            time.sleep(0.5)  # 等待可能的弹窗出现
            self.popup_monitor.handle_popup_immediately()
    
    @with_popup_handling(handle_before=True, handle_after=True)
    def click(self, locator, timeout: int = 10):
        """点击元素 - 自动处理弹窗"""
        element = self.wait_for_element(locator, timeout)
        element.click()
        return element
    
    @with_popup_handling(handle_before=True, handle_after=False)
    def input_text(self, locator, text: str, timeout: int = 10):
        """输入文本 - 自动处理弹窗"""
        element = self.wait_for_element(locator, timeout)
        element.clear()
        element.send_keys(text)
        return element
    
    @handle_popups_on_error(max_retries=3)
    def wait_for_element(self, selector, timeout: int = 10):
        """等待元素出现 - 处理可能的弹窗干扰 - UIAutomator2版本"""
        try:
            if isinstance(selector, str):
                element = self.driver(resourceId=selector)
            elif isinstance(selector, dict):
                element = self.driver(**selector)
            else:
                raise ValueError(f"不支持的选择器类型: {type(selector)}")

            if element.wait(timeout=timeout):
                return element
            else:
                raise TimeoutError(f"等待元素超时: {selector}")
        except Exception:
            # 超时时尝试处理弹窗
            self.handle_popups_before_action()
            raise
    
    def add_popup_callback(self, event: str, callback: Callable):
        """添加弹窗事件回调"""
        self.popup_monitor.set_callback(event, callback)
    
    def configure_popup_handling(self, **kwargs):
        """配置弹窗处理参数"""
        for key, value in kwargs.items():
            if hasattr(self.popup_monitor.config, key):
                setattr(self.popup_monitor.config, key, value)
            else:
                log.warning(f"未知的配置参数: {key}")


def create_popup_aware_page(page_class):
    """创建支持弹窗处理的页面类"""
    
    class PopupAwarePage(page_class, PopupAwarePageBase):
        def __init__(self, *args, **kwargs):
            # 初始化原页面类
            page_class.__init__(self, *args, **kwargs)
            
            # 初始化弹窗处理
            if hasattr(self, 'driver'):
                PopupAwarePageBase.__init__(self, self.driver)
            else:
                raise ValueError("页面类必须有driver属性")
    
    return PopupAwarePage


class PopupHandlingContext:
    """弹窗处理上下文管理器 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device, auto_handle: bool = True, monitor: bool = True):
        self.driver = driver
        self.auto_handle = auto_handle
        self.monitor = monitor
        self.popup_monitor = None
    
    def __enter__(self):
        self.popup_monitor = PopupMonitor(self.driver)
        self.popup_monitor.config.config['handling']['auto_handle'] = self.auto_handle
        
        if self.monitor:
            self.popup_monitor.start_monitoring()
        
        return self.popup_monitor
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.popup_monitor:
            if self.monitor:
                self.popup_monitor.stop_monitoring()
            
            # 如果有异常，尝试处理弹窗
            if exc_type is not None:
                try:
                    self.popup_monitor.handle_popup_immediately()
                except:
                    pass


# 便捷函数 - 适配UIAutomator2
def handle_popups_immediately(driver: u2.Device) -> bool:
    """立即处理当前屏幕上的弹窗"""
    try:
        popup_monitor = PopupMonitor(driver)
        return popup_monitor.handle_popup_immediately()
    except Exception as e:
        log.error(f"立即处理弹窗失败: {e}")
        return False


def detect_popups(driver: u2.Device) -> list:
    """检测当前屏幕上的弹窗"""
    try:
        popup_monitor = PopupMonitor(driver)
        return popup_monitor.handler.detector.detect_popups()
    except Exception as e:
        log.error(f"检测弹窗失败: {e}")
        return []


def get_popup_stats(driver: u2.Device) -> dict:
    """获取弹窗处理统计信息"""
    try:
        popup_monitor = PopupMonitor(driver)
        return popup_monitor.get_monitor_stats()
    except Exception as e:
        log.error(f"获取弹窗统计失败: {e}")
        return {}


# 使用示例
"""
# 1. 使用装饰器
@with_popup_handling(auto_handle=True, monitor_during_test=True)
def test_with_popup_handling(self):
    # 测试代码，会自动处理弹窗
    pass

# 2. 使用上下文管理器
def test_with_context_manager(self):
    with PopupHandlingContext(self.driver, auto_handle=True) as popup_monitor:
        # 在这个上下文中会自动处理弹窗
        pass

# 3. 使用页面基类
class MyPage(PopupAwarePageBase):
    def __init__(self, driver):
        super().__init__(driver)
    
    def some_action(self):
        self.click(("id", "button"))  # 会自动处理弹窗

# 4. 立即处理弹窗
handle_popups_immediately(driver)
"""
