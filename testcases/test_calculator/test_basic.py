"""
计算器基础功能测试用例
"""
import pytest
import allure
from core.logger import log


@allure.feature("计算器应用")
@allure.story("基础计算功能")
class TestCalculatorBasic:
    """计算器基础功能测试类"""
    
    @allure.title("测试加法运算")
    @allure.description("验证计算器的加法运算功能是否正常")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.calculator
    def test_addition(self, calculator_app, setup_test_case):
        """测试加法运算"""
        with allure.step("执行加法运算: 2 + 3"):
            result = calculator_app.calculate("2+3")
        
        with allure.step("验证计算结果"):
            assert result == "5", f"加法运算结果错误，期望: 5, 实际: {result}"
        
        log.info("加法运算测试通过")
    
    @allure.title("测试减法运算")
    @allure.description("验证计算器的减法运算功能是否正常")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.calculator
    def test_subtraction(self, calculator_app, setup_test_case):
        """测试减法运算"""
        with allure.step("执行减法运算: 10 - 4"):
            result = calculator_app.calculate("10-4")
        
        with allure.step("验证计算结果"):
            assert result == "6", f"减法运算结果错误，期望: 6, 实际: {result}"
        
        log.info("减法运算测试通过")
    
    @allure.title("测试乘法运算")
    @allure.description("验证计算器的乘法运算功能是否正常")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.calculator
    def test_multiplication(self, calculator_app, setup_test_case):
        """测试乘法运算"""
        with allure.step("执行乘法运算: 6 * 7"):
            result = calculator_app.calculate("6*7")
        
        with allure.step("验证计算结果"):
            assert result == "42", f"乘法运算结果错误，期望: 42, 实际: {result}"
        
        log.info("乘法运算测试通过")
    
    @allure.title("测试除法运算")
    @allure.description("验证计算器的除法运算功能是否正常")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.calculator
    def test_division(self, calculator_app, setup_test_case):
        """测试除法运算"""
        with allure.step("执行除法运算: 15 / 3"):
            result = calculator_app.calculate("15/3")
        
        with allure.step("验证计算结果"):
            assert result == "5", f"除法运算结果错误，期望: 5, 实际: {result}"
        
        log.info("除法运算测试通过")
    
    @allure.title("测试小数运算")
    @allure.description("验证计算器的小数运算功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.calculator
    def test_decimal_calculation(self, calculator_app, setup_test_case):
        """测试小数运算"""
        with allure.step("执行小数运算: 1.5 + 2.5"):
            result = calculator_app.calculate("1.5+2.5")
        
        with allure.step("验证计算结果"):
            assert result == "4", f"小数运算结果错误，期望: 4, 实际: {result}"
        
        log.info("小数运算测试通过")
    
    @allure.title("测试连续运算")
    @allure.description("验证计算器的连续运算功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.calculator
    def test_continuous_calculation(self, calculator_app, setup_test_case):
        """测试连续运算"""
        with allure.step("执行连续运算: 2 + 3 * 4"):
            # 注意：这里测试的是计算器的运算优先级
            result = calculator_app.calculate("2+3*4")
        
        with allure.step("验证计算结果"):
            # 根据计算器的实际行为来验证结果
            # 大多数简单计算器是从左到右计算，不遵循数学优先级
            # 所以 2+3*4 可能等于 20 而不是 14
            assert result in ["14", "20"], f"连续运算结果错误，期望: 14 或 20, 实际: {result}"
        
        log.info("连续运算测试通过")
    
    @allure.title("测试清除功能")
    @allure.description("验证计算器的清除功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.calculator
    def test_clear_function(self, calculator_app, setup_test_case):
        """测试清除功能"""
        with allure.step("输入数字 123"):
            calculator_app.input_number("123")
        
        with allure.step("点击清除按钮"):
            calculator_app.clear_all()
        
        with allure.step("验证显示区域已清空"):
            result = calculator_app.get_result()
            formula = calculator_app.get_formula()
            
            # 清除后，结果应该为空或为0
            assert result in ["", "0"], f"清除后结果区域未清空，实际: {result}"
            assert formula in ["", "0"], f"清除后公式区域未清空，实际: {formula}"
        
        log.info("清除功能测试通过")
    
    @allure.title("测试删除功能")
    @allure.description("验证计算器的删除功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.calculator
    def test_delete_function(self, calculator_app, setup_test_case):
        """测试删除功能"""
        with allure.step("输入数字 123"):
            calculator_app.input_number("123")
        
        with allure.step("点击删除按钮"):
            calculator_app.delete_last()
        
        with allure.step("验证最后一位数字被删除"):
            # 获取当前显示的内容
            result = calculator_app.get_result()
            formula = calculator_app.get_formula()
            
            # 删除后应该显示 12
            display_text = result or formula
            assert "12" in display_text, f"删除功能异常，期望包含: 12, 实际: {display_text}"
        
        log.info("删除功能测试通过")
    
    @allure.title("测试零除错误")
    @allure.description("验证计算器对零除错误的处理")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.calculator
    def test_division_by_zero(self, calculator_app, setup_test_case):
        """测试零除错误"""
        with allure.step("执行零除运算: 5 / 0"):
            result = calculator_app.calculate("5/0")
        
        with allure.step("验证错误处理"):
            # 不同的计算器可能有不同的错误提示
            # 常见的有: "错误", "Error", "无法除以零", "∞" 等
            error_messages = ["错误", "Error", "无法除以零", "∞", "Infinity", "Can't divide by 0"]
            
            is_error_handled = any(msg in result for msg in error_messages) if result else False
            assert is_error_handled or result == "", f"零除错误处理异常，实际结果: {result}"
        
        log.info("零除错误测试通过")
