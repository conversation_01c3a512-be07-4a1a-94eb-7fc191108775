"""
设置应用基础功能测试用例
"""
import pytest
import allure
from core.logger import log


@allure.feature("设置应用")
@allure.story("基础导航功能")
class TestSettingsBasic:
    """设置应用基础功能测试类"""
    
    @allure.title("测试进入WiFi设置")
    @allure.description("验证能够正常进入WiFi设置页面")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.settings
    def test_enter_wifi_settings(self, settings_app, setup_test_case):
        """测试进入WiFi设置"""
        with allure.step("点击WiFi设置项"):
            result = settings_app.enter_wifi_settings()
        
        with allure.step("验证进入WiFi设置成功"):
            assert result, "进入WiFi设置失败"
        
        with allure.step("截图记录当前页面"):
            # 使用优化后的截图功能，会自动保存到TestSettingsBasic文件夹
            screenshot_path = settings_app.screenshot("wifi_settings_page.png")
            allure.attach.file(screenshot_path, name="WiFi设置页面",
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("WiFi设置页面进入测试通过")
    
    @allure.title("测试进入蓝牙设置")
    @allure.description("验证能够正常进入蓝牙设置页面")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @pytest.mark.settings
    def test_enter_bluetooth_settings(self, settings_app, setup_test_case):
        """测试进入蓝牙设置"""
        with allure.step("点击蓝牙设置项"):
            result = settings_app.enter_bluetooth_settings()
        
        with allure.step("验证进入蓝牙设置成功"):
            assert result, "进入蓝牙设置失败"
        
        with allure.step("截图记录当前页面"):
            screenshot_path = settings_app.screenshot("bluetooth_settings_page.png")
            allure.attach.file(screenshot_path, name="蓝牙设置页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("蓝牙设置页面进入测试通过")
    
    @allure.title("测试进入显示设置")
    @allure.description("验证能够正常进入显示设置页面")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_enter_display_settings(self, settings_app, setup_test_case):
        """测试进入显示设置"""
        with allure.step("点击显示设置项"):
            result = settings_app.enter_display_settings()
        
        with allure.step("验证进入显示设置成功"):
            assert result, "进入显示设置失败"
        
        with allure.step("截图记录当前页面"):
            screenshot_path = settings_app.screenshot("display_settings_page.png")
            allure.attach.file(screenshot_path, name="显示设置页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("显示设置页面进入测试通过")
    
    @allure.title("测试进入应用设置")
    @allure.description("验证能够正常进入应用设置页面")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_enter_apps_settings(self, settings_app, setup_test_case):
        """测试进入应用设置"""
        with allure.step("点击应用设置项"):
            result = settings_app.enter_apps_settings()
        
        with allure.step("验证进入应用设置成功"):
            assert result, "进入应用设置失败"
        
        with allure.step("截图记录当前页面"):
            screenshot_path = settings_app.screenshot("apps_settings_page.png")
            allure.attach.file(screenshot_path, name="应用设置页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("应用设置页面进入测试通过")
    
    @allure.title("测试进入关于手机")
    @allure.description("验证能够正常进入关于手机页面")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_enter_about_phone(self, settings_app, setup_test_case):
        """测试进入关于手机"""
        with allure.step("滚动查找关于手机设置项"):
            # 关于手机通常在设置列表的底部，需要滚动查找
            found = settings_app.scroll_to_setting("关于手机")
            if not found:
                found = settings_app.scroll_to_setting("About phone")
        
        with allure.step("点击关于手机设置项"):
            result = settings_app.enter_about_phone()
        
        with allure.step("验证进入关于手机成功"):
            assert result, "进入关于手机失败"
        
        with allure.step("截图记录当前页面"):
            screenshot_path = settings_app.screenshot("about_phone_page.png")
            allure.attach.file(screenshot_path, name="关于手机页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("关于手机页面进入测试通过")
    
    @allure.title("测试搜索功能")
    @allure.description("验证设置应用的搜索功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_search_function(self, settings_app, setup_test_case):
        """测试搜索功能"""
        with allure.step("搜索WiFi设置"):
            result = settings_app.search_setting("WiFi")
        
        with allure.step("验证搜索功能执行成功"):
            assert result, "搜索功能执行失败"
        
        with allure.step("截图记录搜索结果"):
            screenshot_path = settings_app.screenshot("search_results.png")
            allure.attach.file(screenshot_path, name="搜索结果页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("搜索功能测试通过")
    
    @allure.title("测试返回功能")
    @allure.description("验证设置页面的返回功能是否正常")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_back_function(self, settings_app, setup_test_case):
        """测试返回功能"""
        with allure.step("进入WiFi设置"):
            settings_app.enter_wifi_settings()
        
        with allure.step("点击返回按钮"):
            result = settings_app.go_back()
        
        with allure.step("验证返回主设置页面成功"):
            assert result, "返回功能执行失败"
            
            # 等待页面加载
            import time
            time.sleep(2)
            
            # 验证是否回到主设置页面
            page_loaded = settings_app.wait_for_page_load(timeout=5)
            assert page_loaded, "未能返回到主设置页面"
        
        with allure.step("截图记录返回后的页面"):
            screenshot_path = settings_app.screenshot("back_to_main_settings.png")
            allure.attach.file(screenshot_path, name="返回主设置页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("返回功能测试通过")
    
    @allure.title("测试滑动功能")
    @allure.description("验证设置页面的滑动功能是否正常")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.regression
    @pytest.mark.settings
    def test_swipe_function(self, settings_app, setup_test_case):
        """测试滑动功能"""
        with allure.step("向下滑动页面"):
            settings_app.swipe_down()
        
        with allure.step("向上滑动页面"):
            settings_app.swipe_up()
        
        with allure.step("截图记录滑动后的页面"):
            screenshot_path = settings_app.screenshot("after_swipe.png")
            allure.attach.file(screenshot_path, name="滑动后页面", 
                             attachment_type=allure.attachment_type.PNG)
        
        log.info("滑动功能测试通过")
