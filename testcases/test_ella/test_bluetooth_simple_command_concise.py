"""
Ella语音助手蓝牙命令测试 - 简洁版本
使用基类和装饰器简化测试编写，提供更清晰的测试结构
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest, ella_command_test
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 简洁版本")
class TestEllaBluetoothCommandConcise(SimpleEllaTest):
    """Ella蓝牙命令测试类 - 简洁版本"""
    
    @allure.title("测试open bluetooth命令 - 简洁版本")
    @allure.description("使用简化的测试框架测试蓝牙开启命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth_concise(self, ella_app):
        """测试open bluetooth命令 - 简洁版本"""
        self.simple_command_test(ella_app, "open bluetooth")
    
    @allure.title("测试close bluetooth命令 - 简洁版本")
    @allure.description("使用简化的测试框架测试蓝牙关闭命令")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_close_bluetooth_concise(self, ella_app):
        """测试close bluetooth命令 - 简洁版本"""
        self.simple_command_test(ella_app, "close bluetooth")
    
    @allure.title("测试蓝牙状态查询 - 简洁版本")
    @allure.description("测试蓝牙状态查询命令，不验证状态变化")
    @allure.severity(allure.severity_level.MINOR)
    def test_bluetooth_status_query_concise(self, ella_app):
        """测试蓝牙状态查询 - 简洁版本"""
        self.simple_command_test(ella_app, "what is bluetooth status", verify_status=False)


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 装饰器版本")
class TestEllaBluetoothCommandDecorator(SimpleEllaTest):
    """Ella蓝牙命令测试类 - 装饰器版本"""
    
    @allure.title("测试open bluetooth命令 - 装饰器版本")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    @ella_command_test("open bluetooth")
    def test_open_bluetooth_decorator(self, ella_app):
        """使用装饰器的蓝牙开启测试"""
        pass  # 装饰器自动处理测试逻辑
    
    @allure.title("测试close bluetooth命令 - 装饰器版本")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    @ella_command_test("close bluetooth")
    def test_close_bluetooth_decorator(self, ella_app):
        """使用装饰器的蓝牙关闭测试"""
        pass  # 装饰器自动处理测试逻辑


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 极简版本")
class TestEllaBluetoothCommandMinimal(SimpleEllaTest):
    """Ella蓝牙命令测试类 - 极简版本"""
    
    @pytest.mark.parametrize("command,expected_change", [
        ("open bluetooth", True),
        ("close bluetooth", True),
        ("bluetooth status", False),
    ])
    @allure.title("参数化蓝牙命令测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_bluetooth_commands_parametrized(self, ella_app, command, expected_change):
        """参数化的蓝牙命令测试"""
        with allure.step(f"测试命令: {command}"):
            self.simple_command_test(ella_app, command, verify_status=expected_change)


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 自定义版本")
class TestEllaBluetoothCommandCustom(SimpleEllaTest):
    """Ella蓝牙命令测试类 - 自定义版本"""
    
    @allure.title("自定义蓝牙测试流程")
    @allure.description("演示如何自定义测试流程")
    @allure.severity(allure.severity_level.NORMAL)
    def test_bluetooth_custom_flow(self, ella_app):
        """自定义蓝牙测试流程"""
        command = "open bluetooth"
        
        with allure.step("获取初始状态"):
            initial_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_status else '关闭'}")
        
        with allure.step("执行命令"):
            self._ensure_page_ready(ella_app)
            self._execute_command(ella_app, command)
        
        with allure.step("验证结果"):
            response_text = self._wait_and_get_response(ella_app)
            final_status = ella_app.check_bluetooth_status_smart()
            
            # 自定义验证逻辑
            if "open" in command:
                assert final_status, "蓝牙应该被开启"
            
            log.info(f"蓝牙最终状态: {'开启' if final_status else '关闭'}")
        
        with allure.step("生成报告"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "custom_test_completed")
        
        log.info("🎉 自定义蓝牙测试完成")


# 函数式测试方法（最简洁）

@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 函数式版本")
class TestEllaBluetoothCommandFunctional:
    """Ella蓝牙命令测试类 - 函数式版本"""
    
    @pytest.fixture(scope="function")
    def ella_app(self):
        """极简fixture"""
        from pages.apps.ella.main_page_refactored import EllaMainPageRefactored
        
        ella_page = EllaMainPageRefactored()
        assert ella_page.start_app() and ella_page.wait_for_page_load(), "应用启动失败"
        
        yield ella_page
        ella_page.stop_app()
    
    @allure.title("函数式蓝牙测试")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_bluetooth_functional(self, ella_app):
        """函数式蓝牙测试 - 最简洁的写法"""
        # 一行代码完成测试
        assert self._test_command(ella_app, "open bluetooth")
    
    def _test_command(self, ella_app, command: str) -> bool:
        """极简命令测试"""
        try:
            # 确保页面就绪
            assert ella_app.ensure_on_chat_page() and ella_app.ensure_input_box_ready()
            
            # 执行命令
            assert ella_app.execute_text_command(command)
            
            # 等待响应
            ella_app.wait_for_response(timeout=5)
            response = ella_app.get_response_text_smart()
            
            # 验证状态（针对蓝牙命令）
            if "bluetooth" in command and "open" in command:
                final_status = ella_app.check_bluetooth_status_smart()
                assert final_status, "蓝牙未开启"
            
            log.info(f"✅ {command} 测试成功，响应: {response}")
            return True
            
        except Exception as e:
            log.error(f"❌ {command} 测试失败: {e}")
            return False


# 使用示例和对比
"""
代码行数对比：

原版本 (test_bluetooth_simple_command.py):
- 总行数: 226行
- 测试方法: ~150行
- Fixture: ~60行

简洁版本 (test_bluetooth_simple_command_concise.py):
- 总行数: 约180行（包含多个测试类）
- 单个测试方法: 3-5行
- 共享基类: 在base_ella_test.py中

简化效果：
1. 测试方法从150行减少到3-5行 (减少95%)
2. 重复代码大幅减少
3. 测试逻辑更清晰
4. 维护成本降低
5. 支持多种编写风格（装饰器、参数化、函数式）

使用建议：
- 简单测试用例：使用SimpleEllaTest.simple_command_test()
- 批量测试：使用@ella_command_test装饰器
- 复杂逻辑：继承BaseEllaTest自定义
- 极简需求：使用函数式方法
"""
