"""
Ella语音助手蓝牙命令测试 - 简洁优化版本
测试通过Ella输入"open bluetooth"命令并验证结果
使用重构后的页面类和简化的测试结构
"""
import pytest
import allure
import time
from pages.apps.ella.main_page_refactored import EllaMainPageRefactored
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 简洁版本")
class TestEllaBluetoothCommandOptimized:
    """Ella蓝牙命令测试类 - 简洁版本"""

    @pytest.fixture(scope="function")
    def ella_app(self):
        """简化的Ella应用fixture"""
        ella_page = EllaMainPageRefactored()

        try:
            # 启动应用并验证
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"

            log.info("✅ Ella应用启动成功")
            yield ella_page

        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            # 清理
            try:
                ella_page.stop_app()
                log.info("✅ Ella应用已停止")
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")

    def execute_bluetooth_command(self, ella_app, command: str):
        """执行蓝牙命令的通用方法"""
        # 记录初始状态
        initial_status = ella_app.check_bluetooth_status()
        log.info(f"蓝牙初始状态: {'开启' if initial_status else '关闭'}")

        # 确保页面就绪并执行命令
        assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"
        assert ella_app.ensure_input_box_ready(), "输入框未就绪"
        assert ella_app.execute_text_command(command), f"执行命令失败: {command}"

        # 等待响应
        ella_app.wait_for_response(timeout=8)
        response_text = ella_app.get_response_text_smart() or ella_app.get_response_text()

        # 验证最终状态
        time.sleep(3)  # 等待状态变化
        final_status = ella_app.check_bluetooth_status_smart()

        log.info(f"蓝牙最终状态: {'开启' if final_status else '关闭'}")
        log.info(f"AI响应: '{response_text}'")

        return initial_status, final_status, response_text
    
    @allure.title("测试open bluetooth命令 - 简洁版本")
    @allure.description("使用简化流程测试蓝牙开启命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth_command_optimized(self, ella_app):
        """测试open bluetooth命令 - 简洁版本"""
        command = "open bluetooth"

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.execute_bluetooth_command(ella_app, command)

        with allure.step("验证结果"):
            # 验证蓝牙已开启
            assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"

            # 记录测试总结
            test_summary = f"""
测试命令: {command}
响应内容: {response_text}
蓝牙初始状态: {'开启' if initial_status else '关闭'}
蓝牙最终状态: {'开启' if final_status else '关闭'}
状态变化: {'是' if initial_status != final_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)

            # 截图记录最终状态
            screenshot_path = ella_app.screenshot("bluetooth_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成", attachment_type=allure.attachment_type.PNG)

            log.info("🎉 open bluetooth命令测试完成")

    @allure.title("测试close bluetooth命令 - 简洁版本")
    @allure.description("使用简化流程测试蓝牙关闭命令")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_close_bluetooth_command_optimized(self, ella_app):
        """测试close bluetooth命令 - 简洁版本"""
        command = "close bluetooth"

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.execute_bluetooth_command(ella_app, command)

        with allure.step("验证结果"):
            # 验证蓝牙已关闭
            assert not final_status, f"蓝牙未关闭: 初始={initial_status}, 最终={final_status}"

            # 记录测试总结
            test_summary = f"""
测试命令: {command}
响应内容: {response_text}
蓝牙初始状态: {'开启' if initial_status else '关闭'}
蓝牙最终状态: {'开启' if final_status else '关闭'}
状态变化: {'是' if initial_status != final_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 close bluetooth命令测试完成")

    @pytest.mark.parametrize("command,expected_status", [
        ("open bluetooth", True),
        ("close bluetooth", False),
    ])
    @allure.title("参数化蓝牙命令测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_bluetooth_commands_parametrized(self, ella_app, command, expected_status):
        """参数化的蓝牙命令测试"""
        with allure.step(f"测试命令: {command}"):
            initial_status, final_status, response_text = self.execute_bluetooth_command(ella_app, command)

            # 验证期望状态
            assert final_status == expected_status, f"蓝牙状态不符合期望: 期望={expected_status}, 实际={final_status}"

            log.info(f"✅ {command} 测试成功")
    

