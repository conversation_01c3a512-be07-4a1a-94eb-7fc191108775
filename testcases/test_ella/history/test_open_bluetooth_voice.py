"""
独立的"open bluetooth"语音输入测试脚本 - 优化版本
支持模拟语音输入和TTS真实语音输入两种方式
使用重构后的页面类，提供更好的模块化和可维护性
"""
import sys
import os
import time
import pytest
import allure

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.logger import log, Logger
from pages.apps.ella.main_page_refactored import EllaMainPageRefactored


class TestOpenBluetoothVoiceOptimized:
    """Open Bluetooth语音输入测试类 - 优化版本"""

    def setup_method(self):
        """测试前设置 - 使用重构后的页面类"""
        self.ella_app = EllaMainPageRefactored()
        self.command = "open bluetooth"
        
        # 启动应用
        log.info("🚀 启动Ella应用...")
        if not self.ella_app.start_app_with_activity():
            pytest.fail("Ella应用启动失败")
        
        # 等待页面加载
        if not self.ella_app.wait_for_page_load(timeout=15):
            pytest.fail("Ella页面加载失败")
        
        log.info("✅ Ella应用启动成功")
    
    def teardown_method(self):
        """测试后清理"""
        try:
            self.ella_app.stop_app()
            log.info("🔚 Ella应用已关闭")
        except Exception as e:
            log.warning(f"关闭应用时出错: {e}")
    
    @allure.title("测试模拟语音输入 - open bluetooth")
    @allure.description("通过模拟语音输入方式执行'open bluetooth'命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.voice
    def test_simulated_voice_open_bluetooth(self):
        """测试模拟语音输入open bluetooth命令"""
        
        with allure.step("记录测试开始状态"):
            # 记录蓝牙初始状态
            initial_bluetooth_status = self.ella_app.check_bluetooth_status()
            log.info(f"🔵 蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
            
            # 截图记录初始状态
            screenshot_path = self.ella_app.screenshot("open_bluetooth_voice_initial.png")
            allure.attach.file(screenshot_path, name="初始状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            allure.attach(
                f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}",
                name="蓝牙初始状态",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("确保页面和输入框就绪"):
            # 确保在对话页面
            chat_ready = self.ella_app.ensure_on_chat_page()
            assert chat_ready, "无法确保在对话页面"
            
            # 确保输入框就绪
            input_ready = self.ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"
            
            log.info("✅ 页面和输入框状态就绪")
        
        with allure.step(f"执行模拟语音命令: {self.command}"):
            # 记录测试开始
            Logger.log_test_start(f"模拟语音输入_{self.command}")
            
            # 执行模拟语音命令
            start_time = time.time()
            success = self.ella_app.execute_voice_command(self.command, duration=3.0)
            execution_time = time.time() - start_time
            
            assert success, f"模拟语音命令执行失败: {self.command}"
            
            # 记录执行信息
            log.info(f"✅ 模拟语音命令执行成功，耗时: {execution_time:.2f}秒")
            Logger.log_performance("模拟语音命令执行", execution_time)
            
            # 截图记录命令发送后状态
            screenshot_path = self.ella_app.screenshot("open_bluetooth_voice_sent.png")
            allure.attach.file(screenshot_path, name="命令发送后", 
                             attachment_type=allure.attachment_type.PNG)
        
        with allure.step("等待和获取AI响应"):
            # 等待AI响应
            response_received = self.ella_app.wait_for_response(timeout=10)
            
            if not response_received:
                log.warning("⚠️ 等待响应超时，尝试直接获取")
                time.sleep(3)
                response_text = self.ella_app.get_response_text_smart()
                if response_text:
                    response_received = True
            
            if response_received:
                time.sleep(2)  # 等待响应完整
                
                # 截图记录响应状态
                screenshot_path = self.ella_app.screenshot("open_bluetooth_voice_response.png")
                allure.attach.file(screenshot_path, name="AI响应", 
                                 attachment_type=allure.attachment_type.PNG)
            
            # 获取响应文本
            response_text = self.ella_app.get_response_text_smart()
            log.info(f"🤖 AI响应内容: '{response_text}'")
            
            allure.attach(f"AI响应: '{response_text}'", name="响应内容",
                         attachment_type=allure.attachment_type.TEXT)
            
            # 验证响应内容
            if response_text:
                command_related = self.ella_app.verify_command_in_response(self.command, response_text)
                if command_related:
                    log.info("✅ 响应内容与命令相关")
                else:
                    log.warning("⚠️ 响应内容与命令不太相关")
        
        with allure.step("验证蓝牙状态变化"):
            # 等待蓝牙状态变化
            time.sleep(3)
            
            # 检查最终蓝牙状态
            final_bluetooth_status = self.ella_app.check_bluetooth_status_smart()
            log.info(f"🔵 蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
            
            allure.attach(
                f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}",
                name="蓝牙最终状态",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 验证蓝牙已开启
            assert final_bluetooth_status, "蓝牙未成功开启"
            log.info("✅ 蓝牙已成功开启")
            
            # 记录状态变化
            status_changed = initial_bluetooth_status != final_bluetooth_status
            log.info(f"📊 蓝牙状态变化: {'是' if status_changed else '否'}")
        
        with allure.step("记录测试完成"):
            # 记录测试结束
            Logger.log_test_end(f"模拟语音输入_{self.command}", True, execution_time)
            
            # 最终截图
            screenshot_path = self.ella_app.screenshot("open_bluetooth_voice_completed.png")
            allure.attach.file(screenshot_path, name="测试完成", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 测试总结
            test_summary = f"""
测试类型: 模拟语音输入
测试命令: {self.command}
执行时间: {execution_time:.2f}秒
AI响应: {response_text}
蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}
蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}
状态变化: {'是' if status_changed else '否'}
测试结果: ✅ 成功
"""
            allure.attach(test_summary, name="测试总结", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("🎉 模拟语音输入open bluetooth测试完成")
    
    @allure.title("测试TTS真实语音输入 - open bluetooth")
    @allure.description("通过TTS真实语音输入方式执行'open bluetooth'命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.tts_voice
    def test_tts_voice_open_bluetooth(self):
        """测试TTS真实语音输入open bluetooth命令"""
        
        with allure.step("记录测试开始状态"):
            # 记录蓝牙初始状态
            initial_bluetooth_status = self.ella_app.check_bluetooth_status()
            log.info(f"🔵 蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
            
            # 截图记录初始状态
            screenshot_path = self.ella_app.screenshot("open_bluetooth_tts_initial.png")
            allure.attach.file(screenshot_path, name="TTS测试初始状态", 
                             attachment_type=allure.attachment_type.PNG)
        
        with allure.step("确保页面和输入框就绪"):
            # 确保在对话页面
            chat_ready = self.ella_app.ensure_on_chat_page()
            assert chat_ready, "无法确保在对话页面"
            
            # 确保输入框就绪
            input_ready = self.ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"
            
            log.info("✅ 页面和输入框状态就绪")
        
        with allure.step(f"执行TTS真实语音命令: {self.command}"):
            # 记录测试开始
            Logger.log_test_start(f"TTS语音输入_{self.command}")
            
            # 执行TTS真实语音命令
            start_time = time.time()
            success = self.ella_app.execute_real_voice_command(
                self.command,
                language='zh-CN',    # 中文TTS
                volume=1,          # 80%音量
                tts_delay=1.5        # 1.5秒延迟
            )
            execution_time = time.time() - start_time
            
            assert success, f"TTS语音命令执行失败: {self.command}"
            
            # 记录执行信息
            log.info(f"✅ TTS语音命令执行成功，耗时: {execution_time:.2f}秒")
            Logger.log_performance("TTS语音命令执行", execution_time)
            
            # 截图记录命令发送后状态
            screenshot_path = self.ella_app.screenshot("open_bluetooth_tts_sent.png")
            allure.attach.file(screenshot_path, name="TTS命令发送后", 
                             attachment_type=allure.attachment_type.PNG)
        
        with allure.step("等待和获取AI响应"):
            # 等待AI响应（TTS可能需要更长时间）
            response_received = self.ella_app.wait_for_response(timeout=12)
            
            if not response_received:
                log.warning("⚠️ 等待响应超时，尝试直接获取")
                time.sleep(5)
                response_text = self.ella_app.get_response_text_smart()
                if response_text:
                    response_received = True
            
            if response_received:
                time.sleep(3)  # 等待响应完整
                
                # 截图记录响应状态
                screenshot_path = self.ella_app.screenshot("open_bluetooth_tts_response.png")
                allure.attach.file(screenshot_path, name="TTS AI响应", 
                                 attachment_type=allure.attachment_type.PNG)
            
            # 获取响应文本
            response_text = self.ella_app.get_response_text_smart()
            log.info(f"🤖 AI响应内容: '{response_text}'")
            
            allure.attach(f"TTS AI响应: '{response_text}'", name="TTS响应内容",
                         attachment_type=allure.attachment_type.TEXT)
            
            # 验证响应内容
            if response_text:
                command_related = self.ella_app.verify_command_in_response(self.command, response_text)
                if command_related:
                    log.info("✅ TTS响应内容与命令相关")
                else:
                    log.warning("⚠️ TTS响应内容与命令不太相关")
        
        with allure.step("验证蓝牙状态变化"):
            # 等待蓝牙状态变化
            time.sleep(3)
            
            # 检查最终蓝牙状态
            final_bluetooth_status = self.ella_app.check_bluetooth_status_smart()
            log.info(f"🔵 蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
            
            # 验证蓝牙已开启
            assert final_bluetooth_status, "蓝牙未成功开启"
            log.info("✅ 蓝牙已成功开启")
            
            # 记录状态变化
            status_changed = initial_bluetooth_status != final_bluetooth_status
            log.info(f"📊 蓝牙状态变化: {'是' if status_changed else '否'}")
        
        with allure.step("记录测试完成"):
            # 记录测试结束
            Logger.log_test_end(f"TTS语音输入_{self.command}", True, execution_time)
            
            # 最终截图
            screenshot_path = self.ella_app.screenshot("open_bluetooth_tts_completed.png")
            allure.attach.file(screenshot_path, name="TTS测试完成", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 测试总结
            test_summary = f"""
测试类型: TTS真实语音输入
测试命令: {self.command}
TTS语言: zh-CN (中文)
TTS音量: 80%
执行时间: {execution_time:.2f}秒
AI响应: {response_text}
蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}
蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}
状态变化: {'是' if status_changed else '否'}
测试结果: ✅ 成功
"""
            allure.attach(test_summary, name="TTS测试总结", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("🎉 TTS真实语音输入open bluetooth测试完成")


def run_standalone_test():
    """独立运行测试的函数"""
    print("=" * 60)
    print("🎤 Open Bluetooth 语音输入独立测试")
    print("=" * 60)
    
    test_instance = TestOpenBluetoothVoice()
    
    tests = [
        ("模拟语音输入", test_instance.test_simulated_voice_open_bluetooth),
        ("TTS真实语音输入", test_instance.test_tts_voice_open_bluetooth)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            # 设置测试环境
            test_instance.setup_method()
            
            # 执行测试
            start_time = time.time()
            test_func()
            execution_time = time.time() - start_time
            
            results[test_name] = {
                'success': True,
                'time': execution_time,
                'error': None
            }
            
            print(f"✅ 测试通过，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            results[test_name] = {
                'success': False,
                'time': 0,
                'error': str(e)
            }
            print(f"❌ 测试失败: {e}")
            
        finally:
            # 清理测试环境
            try:
                test_instance.teardown_method()
            except:
                pass
            
            time.sleep(2)  # 测试间隔
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 Open Bluetooth 语音输入测试报告")
    print("=" * 60)
    
    success_count = sum(1 for result in results.values() if result['success'])
    total_count = len(results)
    
    for test_name, result in results.items():
        if result['success']:
            print(f"✅ {test_name:20} : 通过 ({result['time']:.2f}秒)")
        else:
            print(f"❌ {test_name:20} : 失败 - {result['error']}")
    
    print("-" * 60)
    print(f"📋 总体结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有语音输入测试通过！")
        print("💡 Open Bluetooth语音功能完全正常")
    elif success_count > 0:
        print("⚠️ 部分测试通过，语音功能基本可用")
        print("💡 建议检查失败的测试类型")
    else:
        print("❌ 所有测试失败，语音功能需要检查")
        print("💡 建议检查设备连接和应用状态")
    
    print("=" * 60)
    
    return success_count == total_count


if __name__ == "__main__":
    # 独立运行模式
    success = run_standalone_test()
    exit(0 if success else 1)
