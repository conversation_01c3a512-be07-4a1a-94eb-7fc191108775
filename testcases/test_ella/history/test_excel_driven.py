"""
Ella语音助手Excel数据驱动测试
支持通过Excel读取step作为输入，except作为断言
"""
import pytest
import allure
from pages.apps.ella.history.main_page import EllaMainPage
from core.logger import log
from core.data_driven_test import DataDrivenTest, parametrize_from_excel
from utils.excel_utils import ExcelUtils


@allure.feature("Ella语音助手")
@allure.story("Excel数据驱动测试")
class TestEllaExcelDriven:
    """Ella Excel数据驱动测试类"""
    
    @pytest.fixture(scope="function")
    def ella_app(self):
        """Ella应用fixture"""
        ella_page = EllaMainPage()

        try:
            log.info("🚀 开始启动Ella应用...")

            # 启动应用
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")

                # 等待页面加载
                log.info("⏳ 等待Ella页面加载...")
                if ella_page.wait_for_page_load(timeout=15):
                    log.info("✅ Ella页面加载完成")

                    # 截图记录启动成功状态
                    screenshot_path = ella_page.screenshot("ella_excel_test_started.png")
                    log.info(f"📸 启动成功截图: {screenshot_path}")

                    yield ella_page
                else:
                    log.error("❌ Ella页面加载失败")
                    screenshot_path = ella_page.screenshot("ella_excel_page_load_failed.png")
                    log.error(f"📸 页面加载失败截图: {screenshot_path}")
                    pytest.fail("Ella页面加载失败")
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")

        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            try:
                screenshot_path = ella_page.screenshot("ella_excel_fixture_error.png")
                log.error(f"📸 异常状态截图: {screenshot_path}")
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")

        finally:
            # 清理：停止应用
            try:
                log.info("🧹 清理Ella应用...")
                ella_page.stop_app()
                log.info("✅ Ella应用已停止")
            except Exception as e:
                log.warning(f"⚠️ 停止Ella应用时出现异常: {e}")
    
    @pytest.fixture(scope="class")
    def setup_test_data(self):
        """设置测试数据"""
        # 创建示例Excel文件（如果不存在）
        excel_file = "testcases/test_ella/Ella_Test_Cases.xlsx"
        if not ExcelUtils.read_test_data(excel_file):
            log.info("创建示例Excel测试数据文件...")
            ExcelUtils.create_sample_excel(excel_file)
        
        return excel_file
    
    @allure.title("Excel数据驱动测试 - 完整流程")
    @allure.description("使用Excel文件中的测试数据执行完整的数据驱动测试")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_excel_driven_complete(self, ella_app, setup_test_data):
        """Excel数据驱动测试 - 完整流程"""
        excel_file = setup_test_data
        
        with allure.step("初始化数据驱动测试"):
            # 创建数据驱动测试实例
            data_driven = DataDrivenTest(excel_file)
            
            # 获取测试数据
            test_data = data_driven.get_test_data()
            
            if not test_data:
                pytest.fail("没有可执行的测试数据")
            
            log.info(f"加载了{len(test_data)}条测试数据")
            
            # 将测试数据信息添加到Allure报告
            data_summary = "\n".join([
                f"- {data['case_name']}: {data['step']} -> {data['except']}"
                for data in test_data
            ])
            allure.attach(data_summary, name="测试数据概览", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("执行数据驱动测试"):
            # 运行数据驱动测试
            data_driven.run_data_driven_test(ella_app, "Excel数据驱动测试")
    
    # 使用参数化装饰器从Excel文件生成独立的测试用例
    @parametrize_from_excel("testcases/test_ella/Ella_Test_Cases.xlsx")
    @allure.title("Excel参数化测试: {test_data[case_name]}")
    @allure.description("基于Excel数据的参数化测试用例")
    @pytest.mark.regression
    def test_excel_parametrized(self, ella_app, test_data):
        """Excel参数化测试"""
        case_name = test_data.get('case_name', '未知用例')
        step = test_data['step']
        except_result = test_data['except']
        timeout = test_data.get('timeout', 15)
        description = test_data.get('description', '')
        
        # 添加用例描述到Allure报告
        if description:
            allure.dynamic.description(f"{description}\n步骤: {step}\n期望: {except_result}")
        
        log.info(f"执行参数化测试用例: {case_name}")
        
        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot(f"excel_test_{case_name}_initial.png")
            allure.attach.file(screenshot_path, name="测试开始状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 记录测试用例信息
            case_info = f"""
用例名称: {case_name}
测试步骤: {step}
期望结果: {except_result}
超时时间: {timeout}秒
用例描述: {description}
"""
            allure.attach(case_info, name="用例信息", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step(f"执行测试步骤: {step}"):
            # 创建数据驱动测试实例
            data_driven = DataDrivenTest()
            
            # 执行测试步骤
            step_success = data_driven.execute_step(step, ella_app)
            assert step_success, f"测试步骤执行失败: {step}"
            
            # 截图记录步骤执行后的状态
            screenshot_path = ella_app.screenshot(f"excel_test_{case_name}_step_executed.png")
            allure.attach.file(screenshot_path, name="步骤执行后", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info(f"✅ 成功执行步骤: {step}")
        
        with allure.step("等待响应"):
            # 等待AI响应
            if hasattr(ella_app, 'wait_for_response'):
                response_received = ella_app.wait_for_response(timeout=timeout)
                if not response_received:
                    log.warning("未收到AI响应，继续验证")
                else:
                    log.info("✅ 收到AI响应")
                    
                    # 截图记录响应状态
                    screenshot_path = ella_app.screenshot(f"excel_test_{case_name}_response.png")
                    allure.attach.file(screenshot_path, name="收到响应", 
                                     attachment_type=allure.attachment_type.PNG)
        
        with allure.step(f"验证期望结果: {except_result}"):
            # 验证测试结果
            verification_success = data_driven.verify_result(except_result, ella_app, step)
            assert verification_success, f"结果验证失败: 期望 {except_result}"
            
            # 获取实际结果用于报告
            actual_result = ""
            if hasattr(ella_app, 'get_response_text'):
                actual_result = ella_app.get_response_text()
            elif hasattr(ella_app, 'check_bluetooth_status'):
                if 'bluetooth' in step.lower() or '蓝牙' in except_result:
                    bluetooth_status = ella_app.check_bluetooth_status()
                    actual_result = f"蓝牙状态: {'开启' if bluetooth_status else '关闭'}"
            
            # 记录验证结果
            verification_info = f"""
期望结果: {except_result}
实际结果: {actual_result}
验证状态: {'通过' if verification_success else '失败'}
"""
            allure.attach(verification_info, name="验证结果", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info(f"✅ 验证通过: {except_result}")
        
        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot(f"excel_test_{case_name}_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info(f"🎉 参数化测试用例完成: {case_name}")
    
    @allure.title("高优先级Excel测试")
    @allure.description("只执行高优先级的Excel测试用例")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_excel_high_priority(self, ella_app, setup_test_data):
        """高优先级Excel测试"""
        excel_file = setup_test_data
        
        with allure.step("加载高优先级测试数据"):
            # 创建数据驱动测试实例，只加载高优先级用例
            data_driven = DataDrivenTest(excel_file)
            high_priority_data = data_driven.get_test_data(priority='high')
            
            if not high_priority_data:
                pytest.skip("没有高优先级测试用例")
            
            log.info(f"加载了{len(high_priority_data)}条高优先级测试数据")
            
            # 记录高优先级用例信息
            priority_summary = "\n".join([
                f"- {data['case_name']}: {data['step']} -> {data['except']}"
                for data in high_priority_data
            ])
            allure.attach(priority_summary, name="高优先级测试用例", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("执行高优先级测试"):
            # 临时替换测试数据
            original_data = data_driven.test_data
            data_driven.test_data = high_priority_data
            
            try:
                # 运行高优先级测试
                data_driven.run_data_driven_test(ella_app, "高优先级Excel测试")
            finally:
                # 恢复原始数据
                data_driven.test_data = original_data
    
    @allure.title("蓝牙相关Excel测试")
    @allure.description("只执行蓝牙相关的Excel测试用例")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.bluetooth
    def test_excel_bluetooth_only(self, ella_app, setup_test_data):
        """蓝牙相关Excel测试"""
        excel_file = setup_test_data
        
        with allure.step("加载蓝牙相关测试数据"):
            # 创建数据驱动测试实例，只加载蓝牙相关用例
            data_driven = DataDrivenTest(excel_file)
            bluetooth_data = data_driven.get_test_data(tags='bluetooth')
            
            if not bluetooth_data:
                pytest.skip("没有蓝牙相关测试用例")
            
            log.info(f"加载了{len(bluetooth_data)}条蓝牙相关测试数据")
        
        with allure.step("执行蓝牙相关测试"):
            # 临时替换测试数据
            original_data = data_driven.test_data
            data_driven.test_data = bluetooth_data
            
            try:
                # 运行蓝牙相关测试
                data_driven.run_data_driven_test(ella_app, "蓝牙相关Excel测试")
            finally:
                # 恢复原始数据
                data_driven.test_data = original_data
