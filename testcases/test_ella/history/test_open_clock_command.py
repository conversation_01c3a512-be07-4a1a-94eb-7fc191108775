"""
Ella语音助手打开时钟应用测试用例 - 优化版本
基于pytest框架，测试"open clock"命令功能
使用重构后的页面类，提供更好的模块化和可维护性
"""
import pytest
import allure
import time
import subprocess
from pages.apps.ella.main_page_refactored import EllaMainPageRefactored
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("应用启动控制 - 优化版本")
class TestEllaOpenClockCommandOptimized:
    """Ella打开时钟应用测试类 - 优化版本"""

    @pytest.fixture(scope="function")
    def ella_app(self):
        """Ella应用fixture - 使用重构后的页面类"""
        ella_page = EllaMainPageRefactored()
        clock_package = "com.transsion.deskclock"
        
        # 停止时钟应用（确保初始状态）
        try:
            subprocess.run(
                ["adb", "shell", "am", "force-stop", clock_package],
                capture_output=True,
                timeout=5
            )
        except:
            pass
        
        # 启动Ella应用
        if ella_page.start_app_with_activity():
            log.info("Ella应用启动成功")

            # 等待页面加载
            if ella_page.wait_for_page_load():
                log.info("Ella页面加载完成")
                yield ella_page
            else:
                # 仅在页面加载失败时截图
                try:
                    screenshot_path = ella_page.screenshot("error_ella_page_load_failed.png")
                    log.error(f"📸 页面加载失败截图: {screenshot_path}")
                except:
                    pass
                log.error("Ella页面加载失败")
                pytest.fail("Ella页面加载失败")
        else:
            # 仅在应用启动失败时截图
            try:
                screenshot_path = ella_page.screenshot("error_ella_app_start_failed.png")
                log.error(f"📸 应用启动失败截图: {screenshot_path}")
            except:
                pass
            log.error("Ella应用启动失败")
            pytest.fail("Ella应用启动失败")
        
        # 清理：停止应用
        ella_page.stop_app()
        log.info("Ella应用已停止")
    
    def _check_clock_app_running(self) -> bool:
        """检查时钟应用是否正在运行"""
        clock_package = "com.transsion.deskclock"
        
        try:
            # 检查应用进程
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and clock_package in result.stdout:
                return True
            
            return False
            
        except Exception as e:
            log.error(f"检查时钟应用状态失败: {e}")
            return False
    
    def _verify_clock_app_package(self, ella_page) -> bool:
        """通过UIAutomator2验证时钟应用包名"""
        clock_package = "com.transsion.deskclock"

        try:
            # 获取当前应用信息
            current_app = ella_page.driver.app_current()

            if current_app.get('package') == clock_package:
                return True

            # 检查是否有时钟应用的元素
            clock_elements = ella_page.driver(packageName=clock_package)
            if clock_elements.exists:
                return True

            return False

        except Exception as e:
            log.error(f"UIAutomator2验证时钟应用失败: {e}")
            return False

    def _wait_for_clock_app_launch(self, ella_page, timeout: int = 10) -> bool:
        """
        智能等待时钟应用启动

        Args:
            ella_page: Ella页面对象
            timeout: 超时时间

        Returns:
            bool: 时钟应用是否成功启动
        """
        clock_package = "com.transsion.deskclock"
        log.info(f"智能等待时钟应用启动，超时时间: {timeout}秒")

        # 创建时钟应用包元素用于等待
        clock_app_element = ella_page.create_element(
            {"packageName": clock_package},
            "时钟应用包"
        )

        try:
            # 等待时钟应用包出现
            if clock_app_element.wait_for_element(timeout=timeout):
                log.info("✅ 检测到时钟应用包，应用已启动")
                return True
            else:
                log.warning("⚠️ 未检测到时钟应用包，尝试其他验证方式")
                # 备用验证：检查应用进程
                return self._check_clock_app_running()

        except Exception as e:
            log.error(f"等待时钟应用启动失败: {e}")
            return False
    
    @allure.title("测试open clock命令")
    @allure.description("通过Ella输入'open clock'命令，验证时钟应用是否打开")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_clock_command(self, ella_app):
        """测试open clock命令"""
        command = "open clock"
        clock_package = "com.transsion.deskclock"

        with allure.step("记录测试开始状态"):
            # 记录时钟应用初始状态
            initial_clock_status = self._check_clock_app_running()
            log.info(f"时钟应用初始状态: {'运行中' if initial_clock_status else '未运行'}")
            allure.attach(
                f"时钟应用初始状态: {'运行中' if initial_clock_status else '未运行'}",
                name="时钟应用初始状态",
                attachment_type=allure.attachment_type.TEXT
            )

        with allure.step("点击输入框"):
            try:
                # 点击输入框
                assert ella_app.input_box.is_exists(), "未找到输入框"
                assert ella_app.input_box.click(), "点击输入框失败"
                log.info("✅ 输入框点击成功")
            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_input_box_click.png")
                allure.attach.file(screenshot_path, name="输入框点击失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 输入框点击失败: {e}")
                raise

        with allure.step(f"输入命令: {command}"):
            try:
                # 输入命令
                assert ella_app.input_box.send_keys(command), f"输入命令失败: {command}"
                # 等待发送按钮可用（替代固定等待时间）
                if ella_app.send_button.wait_for_element(timeout=5):
                    log.info(f"✅ 成功输入命令，发送按钮已可用: {command}")
                else:
                    log.warning(f"⚠️ 发送按钮未及时出现，但命令已输入: {command}")
                log.info(f"✅ 成功输入命令: {command}")
            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_command_input.png")
                allure.attach.file(screenshot_path, name="命令输入失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 命令输入失败: {e}")
                raise

        with allure.step("发送命令"):
            try:
                # 点击发送按钮
                assert ella_app.send_button.is_exists(), "未找到发送按钮"
                assert ella_app.send_button.click(), "点击发送按钮失败"
                # 等待TTS响应或AI处理完成（替代固定等待时间）
                if ella_app.tts_play_button.wait_for_element(timeout=8):
                    log.info("✅ 检测到TTS响应，命令处理完成")
                else:
                    log.info("✅ 命令已发送，继续执行（未检测到TTS但不影响流程）")
                log.info("✅ 命令发送成功")
            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_send_command.png")
                allure.attach.file(screenshot_path, name="命令发送失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 命令发送失败: {e}")
                raise

        with allure.step("等待时钟应用启动"):
            # 智能等待应用启动（替代固定等待时间）
            app_launch_detected = self._wait_for_clock_app_launch(ella_app, timeout=10)
            if app_launch_detected:
                log.info("✅ 时钟应用启动检测成功")
            else:
                log.info("⚠️ 未直接检测到应用启动，将通过后续验证确认")

        with allure.step("验证时钟应用状态"):
            try:
                # 检查时钟应用最终状态
                final_clock_status = self._check_clock_app_running()
                log.info(f"时钟应用最终状态: {'运行中' if final_clock_status else '未运行'}")

                # 通过UIAutomator2验证
                ui_clock_status = self._verify_clock_app_package(ella_app)
                log.info(f"UI验证状态: {'通过' if ui_clock_status else '未通过'}")

                allure.attach(
                    f"时钟应用最终状态: {'运行中' if final_clock_status else '未运行'}\n"
                    f"UI验证状态: {'通过' if ui_clock_status else '未通过'}\n"
                    f"状态变化: {'是' if initial_clock_status != final_clock_status else '否'}",
                    name="时钟应用状态验证",
                    attachment_type=allure.attachment_type.TEXT
                )

                # 验证时钟应用是否已打开（任一验证通过即可）
                app_opened = final_clock_status or ui_clock_status

                if not app_opened:
                    # 仅在验证失败时截图
                    screenshot_path = ella_app.screenshot("error_clock_app_not_opened.png")
                    allure.attach.file(screenshot_path, name="时钟应用未打开截图",
                                     attachment_type=allure.attachment_type.PNG)
                    log.error(f"❌ 时钟应用未打开: {clock_package}")
                    pytest.fail(f"时钟应用未打开 ({clock_package})")

                log.info(f"✅ 时钟应用已成功打开: {clock_package}")

            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_app_verification.png")
                allure.attach.file(screenshot_path, name="应用验证失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 应用状态验证失败: {e}")
                raise

        with allure.step("验证输入命令在响应中的体现"):
            # 这里可以添加对AI响应内容的验证
            # 由于时钟应用已经打开，说明命令被正确识别和执行

            # 验证命令执行效果
            if app_opened:
                log.info("✅ 输入命令得到正确响应（时钟应用已打开）")

                # 记录成功信息
                allure.attach(
                    f"命令: {command}\n"
                    f"执行结果: 成功\n"
                    f"目标应用: {clock_package}\n"
                    f"应用状态: 已打开",
                    name="命令执行验证",
                    attachment_type=allure.attachment_type.TEXT
                )
            else:
                pytest.fail("命令执行失败，时钟应用未打开")

        with allure.step("记录测试完成状态"):
            # 总结测试结果
            test_summary = f"""
测试命令: {command}
目标应用: {clock_package}
时钟应用初始状态: {'运行中' if initial_clock_status else '未运行'}
时钟应用最终状态: {'运行中' if final_clock_status else '未运行'}
UI验证结果: {'通过' if ui_clock_status else '未通过'}
状态变化: {'是' if initial_clock_status != final_clock_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 open clock命令测试完成")
    
    @allure.title("测试时钟应用包名断言")
    @allure.description("验证时钟应用的包名是否正确")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_clock_app_package_assertion(self, ella_app):
        """测试时钟应用包名断言"""
        command = "open clock"
        expected_package = "com.transsion.deskclock"

        with allure.step("执行open clock命令"):
            try:
                # 点击输入框并输入命令
                ella_app.input_box.click()
                ella_app.input_box.send_keys(command)
                # 等待发送按钮可用（替代固定等待时间）
                ella_app.send_button.wait_for_element(timeout=5)

                # 发送命令
                ella_app.send_button.click()
                # 智能等待时钟应用启动（替代固定等待时间）
                self._wait_for_clock_app_launch(ella_app, timeout=8)
            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_package_test_command_execution.png")
                allure.attach.file(screenshot_path, name="命令执行失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 命令执行失败: {e}")
                raise

        with allure.step("验证时钟应用包名"):
            try:
                # 获取当前应用信息
                current_app = ella_app.driver.app_current()
                current_package = current_app.get('package', '')

                log.info(f"当前应用包名: {current_package}")
                log.info(f"期望包名: {expected_package}")

                # 断言包名是否正确
                if current_package != expected_package:
                    # 仅在断言失败时截图
                    screenshot_path = ella_app.screenshot("error_package_name_mismatch.png")
                    allure.attach.file(screenshot_path, name="包名不匹配截图",
                                     attachment_type=allure.attachment_type.PNG)
                    log.error(f"❌ 时钟应用包名不匹配: 期望 {expected_package}, 实际 {current_package}")
                    pytest.fail(f"时钟应用包名不匹配: 期望 {expected_package}, 实际 {current_package}")

                log.info(f"✅ 时钟应用包名验证通过: {expected_package}")

                # 记录验证结果
                allure.attach(
                    f"期望包名: {expected_package}\n"
                    f"实际包名: {current_package}\n"
                    f"验证结果: 通过",
                    name="包名验证结果",
                    attachment_type=allure.attachment_type.TEXT
                )

            except Exception as e:
                # 仅在出错时截图
                screenshot_path = ella_app.screenshot("error_package_verification.png")
                allure.attach.file(screenshot_path, name="包名验证失败截图",
                                 attachment_type=allure.attachment_type.PNG)
                log.error(f"❌ 包名验证失败: {e}")
                raise
