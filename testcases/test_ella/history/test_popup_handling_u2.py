"""
Ella语音助手弹窗处理测试 - UIAutomator2适配版本
展示如何在UIAutomator2框架下使用智能弹窗处理功能
"""
import pytest
import allure
import time
from pages.apps.ella.history.main_page_with_popup import EllaMainPageWithPopup
from utils.popup_utils import with_popup_handling, PopupHandlingContext, handle_popups_immediately
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("UIAutomator2弹窗处理测试")
class TestEllaPopupHandlingU2:
    """Ella弹窗处理测试类 - UIAutomator2版本"""
    
    @pytest.fixture(scope="function")
    def ella_app_u2(self):
        """Ella应用fixture - UIAutomator2版本"""
        # 获取设备ID（可以从配置文件或环境变量获取）
        device_id = None  # 使用默认设备
        ella_page = EllaMainPageWithPopup(device_id)
        
        try:
            log.info("🚀 启动Ella应用（UIAutomator2 + 弹窗处理）...")
            
            # 启动应用（自动处理启动弹窗）
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")
                
                # 处理启动后可能的弹窗
                ella_page.handle_ella_specific_popups()
                
                # 截图记录启动状态
                screenshot_path = ella_page.screenshot("ella_u2_popup_started.png")
                allure.attach.file(screenshot_path, name="启动成功", 
                                 attachment_type=allure.attachment_type.PNG)
                
                yield ella_page
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")
                
        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            try:
                screenshot_path = ella_page.screenshot("ella_u2_fixture_error.png")
                allure.attach.file(screenshot_path, name="异常状态", 
                                 attachment_type=allure.attachment_type.PNG)
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")
        
        finally:
            # 清理
            try:
                log.info("🧹 清理Ella应用...")
                
                # 获取弹窗处理统计
                stats = ella_page.get_popup_stats()
                if stats:
                    stats_info = f"""
UIAutomator2弹窗处理统计:
- 总检测数: {stats.get('popups_detected', 0)}
- 总处理数: {stats.get('popups_handled', 0)}
- 成功率: {stats.get('handler_stats', {}).get('success_rate', 0):.2%}
- 类型分布: {stats.get('handler_stats', {}).get('type_counts', {})}
"""
                    allure.attach(stats_info, name="UIAutomator2弹窗处理统计", 
                                attachment_type=allure.attachment_type.TEXT)
                    log.info(stats_info)
                
                ella_page.cleanup()
                log.info("✅ Ella应用已清理")
            except Exception as e:
                log.warning(f"⚠️ 清理Ella应用时出现异常: {e}")
    
    @allure.title("UIAutomator2蓝牙控制测试 - 智能弹窗处理")
    @allure.description("测试在UIAutomator2框架下通过语音命令控制蓝牙，自动处理权限弹窗")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_u2_bluetooth_control_with_popup_handling(self, ella_app_u2):
        """UIAutomator2蓝牙控制测试 - 智能弹窗处理"""
        
        with allure.step("执行开启蓝牙命令"):
            # 执行命令（自动处理权限弹窗）
            command = "open bluetooth"
            result = ella_app_u2.execute_text_command(command)
            assert result, f"执行命令失败: {command}"
            
            # 记录命令执行
            allure.attach(f"UIAutomator2执行命令: {command}", name="语音命令", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("等待AI响应"):
            # 等待响应（自动处理网络错误弹窗）
            response_received = ella_app_u2.wait_for_response(timeout=20)
            
            if not response_received:
                log.warning("未收到AI响应，继续验证蓝牙状态")
            else:
                # 获取响应内容
                response_text = ella_app_u2.get_response_text()
                allure.attach(f"AI响应: {response_text}", name="AI响应内容", 
                             attachment_type=allure.attachment_type.TEXT)
                
                # 验证响应包含命令内容
                assert command in response_text, f"响应中未包含命令: {command}"
        
        with allure.step("验证蓝牙状态"):
            # 检查蓝牙状态（自动处理蓝牙权限弹窗）
            bluetooth_status = ella_app_u2.check_bluetooth_status()
            assert bluetooth_status, "蓝牙未成功开启"
            
            log.info("✅ UIAutomator2蓝牙已成功开启")
            allure.attach("蓝牙状态: 已开启", name="蓝牙验证结果", 
                         attachment_type=allure.attachment_type.TEXT)
        
        with allure.step("记录测试完成状态"):
            screenshot_path = ella_app_u2.screenshot("u2_bluetooth_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成", 
                             attachment_type=allure.attachment_type.PNG)
    
    @allure.title("UIAutomator2弹窗检测测试")
    @allure.description("测试UIAutomator2框架下的弹窗检测功能")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_u2_popup_detection(self, ella_app_u2):
        """UIAutomator2弹窗检测测试"""
        
        with allure.step("手动检测当前弹窗"):
            # 使用便捷函数检测弹窗
            popups = ella_app_u2.popup_monitor.handler.detector.detect_popups()
            
            popup_info = f"检测到 {len(popups)} 个弹窗"
            if popups:
                popup_details = []
                for i, popup in enumerate(popups, 1):
                    popup_details.append(f"弹窗{i}: 类型={popup.type}, 文本={popup.text}, 置信度={popup.confidence:.2f}")
                popup_info += "\n" + "\n".join(popup_details)
            
            allure.attach(popup_info, name="弹窗检测结果", 
                         attachment_type=allure.attachment_type.TEXT)
            log.info(popup_info)
        
        with allure.step("测试立即处理弹窗功能"):
            # 测试立即处理弹窗
            handled = handle_popups_immediately(ella_app_u2.driver)
            
            result_info = f"立即处理弹窗结果: {'成功' if handled else '无弹窗或处理失败'}"
            allure.attach(result_info, name="立即处理结果", 
                         attachment_type=allure.attachment_type.TEXT)
            log.info(result_info)
    
    @with_popup_handling(auto_handle=True, monitor_during_test=True)
    @allure.title("UIAutomator2装饰器弹窗处理测试")
    @allure.description("使用装饰器在UIAutomator2框架下进行弹窗处理的测试")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_u2_decorator_popup_handling(self, ella_app_u2):
        """UIAutomator2装饰器弹窗处理测试"""
        
        with allure.step("执行多个命令测试"):
            commands = ["what time is it", "what's the weather", "open calculator"]
            
            for i, command in enumerate(commands, 1):
                with allure.step(f"执行第{i}个命令: {command}"):
                    # 装饰器会自动处理弹窗
                    result = ella_app_u2.execute_text_command(command)
                    assert result, f"命令执行失败: {command}"
                    
                    # 等待响应
                    ella_app_u2.wait_for_response(timeout=10)
                    
                    # 短暂等待
                    time.sleep(1)
    
    @allure.title("UIAutomator2上下文管理器弹窗处理测试")
    @allure.description("使用上下文管理器在UIAutomator2框架下进行弹窗处理")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_u2_context_manager_popup_handling(self, ella_app_u2):
        """UIAutomator2上下文管理器弹窗处理测试"""
        
        with allure.step("使用上下文管理器处理弹窗"):
            with PopupHandlingContext(ella_app_u2.driver, auto_handle=True, monitor=True) as popup_monitor:
                
                # 在这个上下文中，弹窗会被自动处理
                with allure.step("执行网络相关命令"):
                    command = "what is the weather today"
                    result = ella_app_u2.execute_text_command(command)
                    assert result, f"命令执行失败: {command}"
                
                with allure.step("等待响应（可能有网络弹窗）"):
                    # 等待响应，期间可能出现网络错误弹窗
                    response_received = ella_app_u2.wait_for_response(timeout=20)
                    
                    if response_received:
                        response_text = ella_app_u2.get_response_text()
                        allure.attach(f"天气响应: {response_text}", name="天气查询结果", 
                                     attachment_type=allure.attachment_type.TEXT)
                
                # 获取上下文中的弹窗处理统计
                context_stats = popup_monitor.get_monitor_stats()
                allure.attach(str(context_stats), name="上下文弹窗统计", 
                             attachment_type=allure.attachment_type.TEXT)
    
    @allure.title("UIAutomator2弹窗处理性能测试")
    @allure.description("测试UIAutomator2框架下弹窗处理的性能表现")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.performance
    def test_u2_popup_handling_performance(self, ella_app_u2):
        """UIAutomator2弹窗处理性能测试"""
        
        with allure.step("性能测试准备"):
            # 记录开始时间
            start_time = time.time()
            detection_times = []
            handling_times = []
        
        with allure.step("执行性能测试"):
            # 执行多次弹窗检测和处理
            for i in range(5):
                with allure.step(f"第{i+1}次性能测试"):
                    # 检测性能
                    detect_start = time.time()
                    popups = ella_app_u2.popup_monitor.handler.detector.detect_popups()
                    detect_time = time.time() - detect_start
                    detection_times.append(detect_time)
                    
                    # 处理性能
                    if popups:
                        handle_start = time.time()
                        ella_app_u2.popup_monitor.handle_popup_immediately()
                        handle_time = time.time() - handle_start
                        handling_times.append(handle_time)
                    
                    # 等待间隔
                    time.sleep(1)
        
        with allure.step("性能结果分析"):
            total_time = time.time() - start_time
            avg_detection_time = sum(detection_times) / len(detection_times) if detection_times else 0
            avg_handling_time = sum(handling_times) / len(handling_times) if handling_times else 0
            
            performance_report = f"""
UIAutomator2弹窗处理性能报告:
================================
总测试时间: {total_time:.2f}秒
检测次数: {len(detection_times)}
处理次数: {len(handling_times)}
平均检测时间: {avg_detection_time:.3f}秒
平均处理时间: {avg_handling_time:.3f}秒
检测时间范围: {min(detection_times):.3f}s - {max(detection_times):.3f}s
"""
            
            if handling_times:
                performance_report += f"处理时间范围: {min(handling_times):.3f}s - {max(handling_times):.3f}s\n"
            
            allure.attach(performance_report, name="性能测试报告", 
                         attachment_type=allure.attachment_type.TEXT)
            
            # 性能断言
            assert avg_detection_time < 2.0, f"检测时间过长: {avg_detection_time:.3f}s"
            if avg_handling_time > 0:
                assert avg_handling_time < 3.0, f"处理时间过长: {avg_handling_time:.3f}s"
            
            log.info(f"✅ UIAutomator2弹窗处理性能测试通过")
    
    @allure.title("UIAutomator2弹窗分类准确性测试")
    @allure.description("测试UIAutomator2框架下弹窗分类的准确性")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_u2_popup_classification_accuracy(self, ella_app_u2):
        """UIAutomator2弹窗分类准确性测试"""
        
        with allure.step("测试弹窗分类功能"):
            # 获取分类器
            classifier = ella_app_u2.popup_monitor.handler.classifier
            
            # 模拟不同类型的弹窗信息进行分类测试
            test_popups = [
                {
                    'text': '是否允许Ella访问麦克风权限？',
                    'buttons': ['允许', '拒绝'],
                    'package': 'com.transsion.ella',
                    'expected_type': 'ella_voice_permission'
                },
                {
                    'text': 'Ella有新版本可用，是否立即更新？',
                    'buttons': ['立即更新', '稍后'],
                    'package': 'com.transsion.ella',
                    'expected_type': 'ella_update_dialog'
                },
                {
                    'text': '网络连接失败，请检查网络设置',
                    'buttons': ['重试', '设置'],
                    'package': 'com.transsion.ella',
                    'expected_type': 'ella_network_error'
                }
            ]
            
            classification_results = []
            
            for test_popup in test_popups:
                # 创建弹窗信息对象
                from core.popup_handler import PopupInfo
                popup_info = PopupInfo(
                    text=test_popup['text'],
                    buttons=test_popup['buttons'],
                    package=test_popup['package']
                )
                
                # 执行分类
                classified_type = classifier.classify_popup(popup_info)
                expected_type = test_popup['expected_type']
                
                is_correct = classified_type == expected_type
                classification_results.append({
                    'text': test_popup['text'],
                    'expected': expected_type,
                    'actual': classified_type,
                    'correct': is_correct,
                    'confidence': popup_info.confidence
                })
        
        with allure.step("分析分类结果"):
            correct_count = sum(1 for result in classification_results if result['correct'])
            total_count = len(classification_results)
            accuracy = correct_count / total_count if total_count > 0 else 0
            
            classification_report = f"""
UIAutomator2弹窗分类准确性报告:
==============================
总测试数: {total_count}
正确数: {correct_count}
准确率: {accuracy:.2%}

详细结果:
"""
            
            for i, result in enumerate(classification_results, 1):
                status = "✅" if result['correct'] else "❌"
                classification_report += f"""
{i}. {status} 置信度: {result['confidence']:.2f}
   文本: {result['text'][:50]}...
   期望: {result['expected']}
   实际: {result['actual']}
"""
            
            allure.attach(classification_report, name="分类准确性报告", 
                         attachment_type=allure.attachment_type.TEXT)
            
            # 准确性断言
            assert accuracy >= 0.8, f"分类准确率过低: {accuracy:.2%}"
            
            log.info(f"✅ UIAutomator2弹窗分类准确性测试通过: {accuracy:.2%}")


# 运行命令示例
"""
# 运行所有UIAutomator2弹窗处理测试
python -m pytest testcases/test_ella/test_popup_handling_u2.py -v -s

# 运行特定测试
python -m pytest testcases/test_ella/test_popup_handling_u2.py::TestEllaPopupHandlingU2::test_u2_bluetooth_control_with_popup_handling -v -s

# 运行性能测试
python -m pytest testcases/test_ella/test_popup_handling_u2.py -m performance -v -s

# 生成Allure报告
python -m pytest testcases/test_ella/test_popup_handling_u2.py --alluredir=reports/allure-results
allure generate reports/allure-results -o reports/allure-report --clean
"""
