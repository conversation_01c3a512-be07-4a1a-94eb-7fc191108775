"""
Ella语音助手蓝牙命令测试
测试通过Ella输入"open bluetooth"命令并验证结果
"""
import pytest
import allure
import time
from pages.apps.ella.history.main_page import EllaMainPage
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令")
class TestEllaBluetoothCommand:
    """Ella蓝牙命令测试类"""
    
    @pytest.fixture(scope="function")
    def ella_app(self):
        """Ella应用fixture"""
        ella_page = EllaMainPage()

        try:
            log.info("🚀 开始启动Ella应用...")

            # 启动应用
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")

                # 等待页面加载，增加超时时间
                log.info("⏳ 等待Ella页面加载...")
                if ella_page.wait_for_page_load(timeout=15):
                    log.info("✅ Ella页面加载完成")

                    # 截图记录启动成功状态
                    screenshot_path = ella_page.screenshot("ella_app_started.png")
                    log.info(f"📸 启动成功截图: {screenshot_path}")

                    yield ella_page
                else:
                    log.error("❌ Ella页面加载失败")
                    # 截图记录失败状态
                    screenshot_path = ella_page.screenshot("ella_page_load_failed.png")
                    log.error(f"📸 页面加载失败截图: {screenshot_path}")
                    pytest.fail("Ella页面加载失败")
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")

        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            # 截图记录异常状态
            try:
                screenshot_path = ella_page.screenshot("ella_fixture_error.png")
                log.error(f"📸 异常状态截图: {screenshot_path}")
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")

        finally:
            # 清理：停止应用
            try:
                log.info("🧹 清理Ella应用...")
                ella_page.stop_app()
                log.info("✅ Ella应用已停止")
            except Exception as e:
                log.warning(f"⚠️ 停止Ella应用时出现异常: {e}")
    
    @allure.title("测试open bluetooth命令")
    @allure.description("通过Ella输入'open bluetooth'命令，验证响应和蓝牙状态")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth_command(self, ella_app):
        """测试open bluetooth命令"""
        command = "open bluetooth"
        
        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 记录蓝牙初始状态
            initial_bluetooth_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
            allure.attach(
                f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}",
                name="蓝牙初始状态",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"输入命令: {command}"):
            # 执行文本命令（现在包含了确保在对话页面的逻辑）
            success = ella_app.execute_text_command(command)
            assert success, f"执行命令失败: {command}"
            
            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_command_sent.png")
            allure.attach.file(screenshot_path, name="命令发送后", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info(f"✅ 成功执行命令: {command}")
        
        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=8)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                # 等待额外时间确保响应完整
                time.sleep(3)

                # 截图记录响应状态
                screenshot_path = ella_app.screenshot("ella_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)

                log.info("✅ 收到AI响应")
            else:
                # 截图记录无响应状态
                screenshot_path = ella_app.screenshot("ella_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)

                # 获取页面文本快照用于调试
                debug_snapshot = ella_app._get_page_text_snapshot()
                allure.attach(debug_snapshot, name="页面文本快照",
                             attachment_type=allure.attachment_type.TEXT)

                log.warning("⚠️ 未检测到AI响应，继续测试以验证蓝牙状态")
        
        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")
        
        with allure.step("验证蓝牙状态"):
            # 等待蓝牙状态可能的变化
            time.sleep(2)
            
            # 使用智能方法检查蓝牙最终状态（包含进程检测）
            final_bluetooth_status = ella_app.check_bluetooth_status_smart()
            log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
            
            allure.attach(
                f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}",
                name="蓝牙最终状态",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 验证蓝牙是否已开启
            assert final_bluetooth_status, "蓝牙未开启"
            
            log.info("✅ 蓝牙已成功开启")
        
        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 总结测试结果
            test_summary = f"""
测试命令: {command}
响应内容: {response_text}
蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}
蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}
状态变化: {'是' if initial_bluetooth_status != final_bluetooth_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="测试总结", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("🎉 open bluetooth命令测试完成")
    
    @allure.title("测试close bluetooth命令")
    @allure.description("通过Ella输入'close bluetooth'命令，验证响应和蓝牙状态")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_close_bluetooth_command(self, ella_app):
        """测试close bluetooth命令"""
        command = "close bluetooth"
        
        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_close_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 记录蓝牙初始状态
            initial_bluetooth_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
        
        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"输入命令: {command}"):
            # 执行文本命令（现在包含了确保在对话页面的逻辑）
            success = ella_app.execute_text_command(command)
            assert success, f"执行命令失败: {command}"

            log.info(f"✅ 成功执行命令: {command}")
        
        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=20)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.error("❌ 完全未收到AI响应")

            # 如果仍然没有响应，记录详细信息但不立即失败
            if not response_received:
                # 截图记录当前状态
                screenshot_path = ella_app.screenshot("ella_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)

                # 获取页面所有文本用于调试
                debug_snapshot = ella_app._get_page_text_snapshot()
                allure.attach(debug_snapshot, name="页面文本快照",
                             attachment_type=allure.attachment_type.TEXT)

                log.warning("⚠️ 未检测到AI响应，但继续测试以获取更多信息")
            else:
                # 等待额外时间确保响应完整
                time.sleep(3)
                log.info("✅ 收到AI响应")
        
        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")
        
        with allure.step("验证蓝牙状态"):
            # 等待蓝牙状态可能的变化
            time.sleep(2)
            
            # 使用智能方法检查蓝牙最终状态（包含进程检测）
            final_bluetooth_status = ella_app.check_bluetooth_status_smart()
            log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
            
            # 验证蓝牙是否已关闭
            assert not final_bluetooth_status, "蓝牙未关闭"
            
            log.info("✅ 蓝牙已成功关闭")
        
        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_close_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info("🎉 close bluetooth命令测试完成")
    
    @allure.title("测试蓝牙状态查询命令")
    @allure.description("通过Ella查询蓝牙状态")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.regression
    def test_bluetooth_status_query(self, ella_app):
        """测试蓝牙状态查询"""
        commands = [
            "bluetooth status",
            "is bluetooth on",
            "check bluetooth"
        ]
        
        for command in commands:
            with allure.step(f"测试命令: {command}"):
                # 确保在对话页面并准备输入
                chat_page_ready = ella_app.ensure_on_chat_page()
                if not chat_page_ready:
                    log.warning(f"无法确保在对话页面，跳过命令: {command}")
                    continue

                input_ready = ella_app.ensure_input_box_ready()
                if not input_ready:
                    log.warning(f"输入框未就绪，跳过命令: {command}")
                    continue

                # 执行命令
                success = ella_app.execute_text_command(command)
                if not success:
                    log.warning(f"命令执行失败: {command}")
                    continue
                
                # 等待响应
                if ella_app.wait_for_response(timeout=3):
                    response_text = ella_app.get_response_text()
                    log.info(f"命令 '{command}' 响应: {response_text}")
                    
                    # 验证响应包含蓝牙相关内容
                    bluetooth_keywords = ["bluetooth", "蓝牙", "开启", "关闭", "on", "off"]
                    response_lower = response_text.lower()
                    
                    has_bluetooth_content = any(keyword in response_lower for keyword in bluetooth_keywords)
                    if has_bluetooth_content:
                        log.info(f"✅ 命令 '{command}' 响应包含蓝牙相关内容")
                    else:
                        log.warning(f"⚠️ 命令 '{command}' 响应未包含蓝牙相关内容")
                
                # time.sleep(2)  # 命令间隔

    @allure.title("测试语音输入open bluetooth命令")
    @allure.description("通过Ella语音输入'open bluetooth'命令，验证响应和蓝牙状态")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.voice
    def test_voice_open_bluetooth_command(self, ella_app):
        """测试语音输入open bluetooth命令"""
        command = "open bluetooth"

        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_voice_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态",
                             attachment_type=allure.attachment_type.PNG)

            # 记录蓝牙初始状态
            initial_bluetooth_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
            allure.attach(
                f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}",
                name="蓝牙初始状态",
                attachment_type=allure.attachment_type.TEXT
            )

        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"语音输入命令: {command}"):
            # 执行语音命令（包含回退到文本输入的逻辑）
            success = ella_app.execute_voice_command(command, duration=3.0)
            assert success, f"执行语音命令失败: {command}"

            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_voice_command_sent.png")
            allure.attach.file(screenshot_path, name="语音命令发送后",
                             attachment_type=allure.attachment_type.PNG)

            log.info(f"✅ 成功执行语音命令: {command}")

        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=10)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                # 等待额外时间确保响应完整
                time.sleep(3)

                # 截图记录响应状态
                screenshot_path = ella_app.screenshot("ella_voice_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)

                log.info("✅ 收到AI响应")
            else:
                # 截图记录无响应状态
                screenshot_path = ella_app.screenshot("ella_voice_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)

                log.warning("⚠️ 未检测到AI响应，继续测试以验证蓝牙状态")

        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")

        with allure.step("验证蓝牙状态"):
            # 等待蓝牙状态可能的变化
            time.sleep(2)

            # 使用智能方法检查蓝牙最终状态（包含进程检测）
            final_bluetooth_status = ella_app.check_bluetooth_status_smart()
            log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")

            allure.attach(
                f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}",
                name="蓝牙最终状态",
                attachment_type=allure.attachment_type.TEXT
            )

            # 验证蓝牙是否已开启
            assert final_bluetooth_status, "蓝牙未开启"

            log.info("✅ 蓝牙已成功开启")

        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_voice_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态",
                             attachment_type=allure.attachment_type.PNG)

            # 总结测试结果
            test_summary = f"""
测试类型: 语音输入
测试命令: {command}
响应内容: {response_text}
蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}
蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}
状态变化: {'是' if initial_bluetooth_status != final_bluetooth_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="语音测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 语音输入open bluetooth命令测试完成")

    @allure.title("测试语音输入close bluetooth命令")
    @allure.description("通过Ella语音输入'close bluetooth'命令，验证响应和蓝牙状态")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.voice
    def test_voice_close_bluetooth_command(self, ella_app):
        """测试语音输入close bluetooth命令"""
        command = "close bluetooth"

        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_voice_close_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态",
                             attachment_type=allure.attachment_type.PNG)

            # 记录蓝牙初始状态
            initial_bluetooth_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")

        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

        with allure.step(f"语音输入命令: {command}"):
            # 执行语音命令（包含回退到文本输入的逻辑）
            success = ella_app.execute_voice_command(command, duration=3.0)
            assert success, f"执行语音命令失败: {command}"

            log.info(f"✅ 成功执行语音命令: {command}")

        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=10)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                time.sleep(5)
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                time.sleep(3)
                log.info("✅ 收到AI响应")
            else:
                log.warning("⚠️ 未检测到AI响应，继续测试以验证蓝牙状态")

        with allure.step("获取并验证响应内容"):
            response_text = ella_app.get_response_text_smart()
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step("验证蓝牙状态"):
            # 等待蓝牙状态可能的变化
            time.sleep(2)

            # 使用智能方法检查蓝牙最终状态（包含进程检测）
            final_bluetooth_status = ella_app.check_bluetooth_status_smart()
            log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")

            # 验证蓝牙是否已关闭
            assert not final_bluetooth_status, "蓝牙未关闭"

            log.info("✅ 蓝牙已成功关闭")

        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_voice_close_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态",
                             attachment_type=allure.attachment_type.PNG)

            log.info("🎉 语音输入close bluetooth命令测试完成")

    @allure.title("测试真实TTS语音输入open bluetooth命令")
    @allure.description("通过TTS将'open bluetooth'转换为语音，通过麦克风播放给手机")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.tts_voice
    def test_real_tts_voice_open_bluetooth_command(self, ella_app):
        """测试真实TTS语音输入open bluetooth命令"""
        command = "open bluetooth"

        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_tts_voice_initial_state.png")
            allure.attach.file(screenshot_path, name="Ella初始状态",
                             attachment_type=allure.attachment_type.PNG)

            # 记录蓝牙初始状态
            initial_bluetooth_status = ella_app.check_bluetooth_status()
            log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
            allure.attach(
                f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}",
                name="蓝牙初始状态",
                attachment_type=allure.attachment_type.TEXT
            )

        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"TTS语音输入命令: {command}"):
            # 执行真实TTS语音命令
            success = ella_app.execute_real_voice_command(
                command,
                language='zh-CN',  # 中文
                volume=0.8,        # 音量80%
                tts_delay=1.5      # TTS播放前延迟1.5秒
            )
            assert success, f"执行TTS语音命令失败: {command}"

            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_tts_voice_command_sent.png")
            allure.attach.file(screenshot_path, name="TTS语音命令发送后",
                             attachment_type=allure.attachment_type.PNG)

            log.info(f"✅ 成功执行TTS语音命令: {command}")

        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=12)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                # 等待额外时间确保响应完整
                time.sleep(3)

                # 截图记录响应状态
                screenshot_path = ella_app.screenshot("ella_tts_voice_response_received.png")
                allure.attach.file(screenshot_path, name="收到AI响应",
                                 attachment_type=allure.attachment_type.PNG)

                log.info("✅ 收到AI响应")
            else:
                # 截图记录无响应状态
                screenshot_path = ella_app.screenshot("ella_tts_voice_no_response_debug.png")
                allure.attach.file(screenshot_path, name="无响应调试截图",
                                 attachment_type=allure.attachment_type.PNG)

                log.warning("⚠️ 未检测到AI响应，继续测试以验证蓝牙状态")

        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本（包含进程检测）
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本（即使为空也要记录）
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 如果有响应文本，验证是否包含命令相关内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")

        with allure.step("验证蓝牙状态"):
            # 等待蓝牙状态可能的变化
            time.sleep(2)

            # 使用智能方法检查蓝牙最终状态（包含进程检测）
            final_bluetooth_status = ella_app.check_bluetooth_status_smart()
            log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")

            allure.attach(
                f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}",
                name="蓝牙最终状态",
                attachment_type=allure.attachment_type.TEXT
            )

            # 验证蓝牙是否已开启
            assert final_bluetooth_status, "蓝牙未开启"

            log.info("✅ 蓝牙已成功开启")

        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_tts_voice_test_completed.png")
            allure.attach.file(screenshot_path, name="测试完成状态",
                             attachment_type=allure.attachment_type.PNG)

            # 总结测试结果
            test_summary = f"""
测试类型: TTS真实语音输入
测试命令: {command}
TTS语言: zh-CN
TTS音量: 80%
响应内容: {response_text}
蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}
蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}
状态变化: {'是' if initial_bluetooth_status != final_bluetooth_status else '否'}
测试结果: 成功
"""
            allure.attach(test_summary, name="TTS语音测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            log.info("🎉 TTS真实语音输入open bluetooth命令测试完成")
