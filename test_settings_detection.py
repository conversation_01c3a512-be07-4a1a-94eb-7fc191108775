#!/usr/bin/env python3
"""
测试Settings应用检测功能
"""
import subprocess
import time

def test_settings_detection():
    """测试Settings应用检测"""
    print('=== 测试Settings应用检测 ===')
    
    # 1. 检查当前运行的应用
    print('\n1. 检查当前运行的应用:')
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            activity_output = result.stdout
            
            # 检查Settings相关应用
            settings_packages = [
                "com.android.settings",           # 标准Android设置
                "com.transsion.settings",         # Transsion设置
                "com.sec.android.app.settings",   # 三星设置
                "com.huawei.android.settings",    # 华为设置
                "com.xiaomi.misettings",          # 小米设置
                "com.oppo.settings",              # OPPO设置
                "com.vivo.settings",              # Vivo设置
            ]
            
            found_packages = []
            for package in settings_packages:
                if package in activity_output:
                    found_packages.append(package)
            
            if found_packages:
                print(f'   找到Settings应用: {found_packages}')
            else:
                print('   未找到Settings应用包名')
                
                # 检查是否有settings关键词
                if 'settings' in activity_output.lower():
                    lines = activity_output.split('\n')
                    settings_lines = [line.strip() for line in lines if 'settings' in line.lower()]
                    print(f'   找到Settings相关行: {len(settings_lines)} 条')
                    for line in settings_lines[:3]:  # 只显示前3条
                        print(f'     {line}')
                else:
                    print('   未找到Settings相关内容')
        else:
            print(f'   获取失败: {result.stderr}')
    except Exception as e:
        print(f'   异常: {e}')
    
    # 2. 检查当前前台应用
    print('\n2. 检查当前前台应用:')
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "(mCurrentFocus|settings)"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print(f'   前台窗口信息: {result.stdout.strip()}')
        else:
            print('   未找到Settings相关前台窗口')
    except Exception as e:
        print(f'   异常: {e}')
    
    # 3. 检查运行中的进程
    print('\n3. 检查运行中的进程:')
    try:
        result = subprocess.run(
            ["adb", "shell", "ps", "|", "grep", "settings"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            processes = result.stdout.strip().split('\n')
            print(f'   找到Settings进程: {len(processes)} 个')
            for process in processes[:5]:  # 只显示前5个
                print(f'     {process}')
        else:
            print('   未找到Settings相关进程')
    except Exception as e:
        print(f'   异常: {e}')
    
    # 4. 检查设备上安装的Settings应用
    print('\n4. 检查设备上安装的Settings应用:')
    try:
        result = subprocess.run(
            ["adb", "shell", "pm", "list", "packages", "|", "grep", "settings"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            packages = result.stdout.strip().split('\n')
            print(f'   安装的Settings应用: {len(packages)} 个')
            for package in packages:
                print(f'     {package}')
        else:
            print('   未安装Settings应用')
    except Exception as e:
        print(f'   异常: {e}')
    
    # 5. 测试实际检测函数
    print('\n5. 测试实际检测函数:')
    try:
        from pages.apps.ella.ella_app_detector import EllaAppDetector

        detector = EllaAppDetector()
        is_settings_open = detector.check_settings_opened()
        print(f'   Settings应用状态: {is_settings_open}')
        
    except Exception as e:
        print(f'   测试失败: {e}')

def open_settings_app():
    """打开Settings应用"""
    print('\n=== 打开Settings应用 ===')
    try:
        # 尝试启动Settings应用
        result = subprocess.run(
            ["adb", "shell", "am", "start", "-a", "android.settings.SETTINGS"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print('✅ Settings应用启动命令执行成功')
            print(f'   输出: {result.stdout.strip()}')
        else:
            print(f'❌ Settings应用启动失败: {result.stderr}')
            
            # 尝试通用启动方式
            result = subprocess.run(
                ["adb", "shell", "am", "start", "-n", "com.android.settings/.Settings"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print('✅ 通过包名启动Settings应用成功')
            else:
                print(f'❌ 包名启动也失败: {result.stderr}')
                
    except Exception as e:
        print(f'异常: {e}')

def test_settings_detection_when_open():
    """测试Settings应用打开时的检测"""
    print('\n=== 测试Settings应用打开时的检测 ===')
    
    # 等待应用启动
    print('等待3秒让应用完全启动...')
    time.sleep(3)
    
    try:
        from pages.apps.ella.ella_app_detector import EllaAppDetector

        detector = EllaAppDetector()
        is_settings_open = detector.check_settings_opened()
        print(f'Settings应用检测结果: {is_settings_open}')
        
        if is_settings_open:
            print('✅ 成功检测到Settings应用已打开')
        else:
            print('❌ 未检测到Settings应用打开，可能需要进一步优化')
            
    except Exception as e:
        print(f'检测失败: {e}')

if __name__ == '__main__':
    # 1. 先检查当前状态
    print('=== 步骤1: 检查当前状态 ===')
    test_settings_detection()
    
    # 2. 打开Settings应用
    print('\n=== 步骤2: 打开Settings应用 ===')
    open_settings_app()
    
    # 3. 测试检测功能
    test_settings_detection_when_open()
    
    print('\n=== 测试完成 ===')
