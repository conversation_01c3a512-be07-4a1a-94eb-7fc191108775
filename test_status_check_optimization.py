#!/usr/bin/env python3
"""
测试优化后的状态检查功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


class MockEllaApp:
    """模拟Ella应用"""
    
    def __init__(self):
        self.bluetooth_status = False
        self.contacts_opened = False
        self.weather_opened = False
        self.camera_opened = False
        self.wifi_status = True
    
    def check_bluetooth_status(self):
        return self.bluetooth_status
    
    def check_bluetooth_status_smart(self):
        return not self.bluetooth_status  # 模拟状态变化
    
    def check_contacts_app_opened(self):
        return self.contacts_opened
    
    def check_contacts_app_opened_smart(self):
        return True  # 模拟应用已打开
    
    def check_weather_app_opened(self):
        return self.weather_opened
    
    def check_camera_app_opened(self):
        return self.camera_opened
    
    def check_wifi_status(self):
        return self.wifi_status


class TestStatusCheckOptimization:
    """测试状态检查优化功能"""
    
    def __init__(self):
        self.test_instance = BaseEllaTest()
        self.mock_app = MockEllaApp()
    
    def test_command_type_detection(self):
        """测试命令类型检测"""
        print("\n📋 测试1: 命令类型检测")
        
        test_commands = [
            ("open bluetooth", "bluetooth"),
            ("打开蓝牙", "bluetooth"),
            ("open contacts", "contacts"),
            ("打开联系人", "contacts"),
            ("show weather", "weather"),
            ("天气预报", "weather"),
            ("take photo", "camera"),
            ("拍照", "camera"),
            ("turn on wifi", "wifi"),
            ("打开无线网络", "wifi"),
            ("unknown command", None)
        ]
        
        for command, expected_type in test_commands:
            detected_type = self.test_instance._detect_command_type(command)
            status = "✅" if detected_type == expected_type else "❌"
            print(f"  {status} '{command}' -> {detected_type} (期望: {expected_type})")
    
    def test_status_by_type(self):
        """测试按类型获取状态"""
        print("\n📋 测试2: 按类型获取状态")
        
        # 测试初始状态
        initial_bluetooth = self.test_instance._get_status_by_type(
            self.mock_app, "bluetooth", is_final=False
        )
        print(f"  蓝牙初始状态: {initial_bluetooth}")
        
        # 测试最终状态（模拟等待时间为0.1秒）
        final_bluetooth = self.test_instance._get_status_by_type(
            self.mock_app, "bluetooth", is_final=True, wait_time=0.1
        )
        print(f"  蓝牙最终状态: {final_bluetooth}")
        
        # 测试不存在的类型
        unknown_status = self.test_instance._get_status_by_type(
            self.mock_app, "unknown", is_final=False
        )
        print(f"  未知类型状态: {unknown_status}")
    
    def test_optimized_initial_final_status(self):
        """测试优化后的初始和最终状态方法"""
        print("\n📋 测试3: 优化后的初始和最终状态方法")
        
        test_commands = [
            "open bluetooth",
            "open contacts", 
            "show weather",
            "unknown command"
        ]
        
        for command in test_commands:
            print(f"\n  命令: '{command}'")
            
            # 获取初始状态
            initial_status = self.test_instance._get_initial_status(self.mock_app, command)
            print(f"    初始状态: {initial_status}")
            
            # 获取最终状态（等待时间设为0.1秒）
            final_status = self.test_instance._get_final_status(self.mock_app, command, wait_time=0.1)
            print(f"    最终状态: {final_status}")
    
    def test_get_status_by_command(self):
        """测试通用状态获取方法"""
        print("\n📋 测试4: 通用状态获取方法")
        
        test_commands = [
            "open bluetooth",
            "open contacts",
            "unknown command"
        ]
        
        for command in test_commands:
            print(f"\n  命令: '{command}'")
            
            # 获取初始状态信息
            initial_info = self.test_instance.get_status_by_command(
                self.mock_app, command, is_final=False
            )
            print(f"    初始状态信息: {initial_info}")
            
            # 获取最终状态信息
            final_info = self.test_instance.get_status_by_command(
                self.mock_app, command, is_final=True, wait_time=0.1
            )
            print(f"    最终状态信息: {final_info}")
    
    def test_custom_status_check(self):
        """测试自定义状态检查"""
        print("\n📋 测试5: 自定义状态检查")
        
        # 添加自定义状态检查
        self.test_instance.add_custom_status_check(
            command_type="calculator",
            keywords=["calculator", "计算器", "calc"],
            initial_method="check_calculator_opened",
            final_method="check_calculator_opened_smart",
            description="计算器应用状态"
        )
        
        # 为mock app添加对应方法
        def check_calculator_opened():
            return False
        
        def check_calculator_opened_smart():
            return True
        
        setattr(self.mock_app, 'check_calculator_opened', check_calculator_opened)
        setattr(self.mock_app, 'check_calculator_opened_smart', check_calculator_opened_smart)
        
        # 测试自定义命令
        command = "open calculator"
        command_type = self.test_instance._detect_command_type(command)
        print(f"  自定义命令检测: '{command}' -> {command_type}")
        
        status_info = self.test_instance.get_status_by_command(
            self.mock_app, command, is_final=True, wait_time=0.1
        )
        print(f"  自定义状态信息: {status_info}")
    
    def test_utility_methods(self):
        """测试实用方法"""
        print("\n📋 测试6: 实用方法")
        
        # 获取支持的命令类型
        supported_types = self.test_instance.get_supported_command_types()
        print(f"  支持的命令类型: {supported_types}")
        
        # 获取所有关键词映射
        all_keywords = self.test_instance.get_command_keywords()
        print(f"  所有关键词映射: {all_keywords}")
        
        # 获取特定类型的关键词
        bluetooth_keywords = self.test_instance.get_command_keywords("bluetooth")
        print(f"  蓝牙关键词: {bluetooth_keywords}")
        
        # 获取不存在类型的关键词
        unknown_keywords = self.test_instance.get_command_keywords("unknown")
        print(f"  未知类型关键词: {unknown_keywords}")
    
    def test_performance_comparison(self):
        """测试性能对比"""
        print("\n📋 测试7: 性能对比")
        
        import time
        
        # 测试优化后方法的性能
        start_time = time.time()
        for _ in range(1000):
            self.test_instance._detect_command_type("open bluetooth")
            self.test_instance._get_status_by_type(self.mock_app, "bluetooth", is_final=False)
        
        optimized_time = time.time() - start_time
        print(f"  优化后方法 1000次调用: {optimized_time:.4f}秒")
        print(f"  平均每次: {optimized_time/1000:.6f}秒")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始测试优化后的状态检查功能...")
        
        test_methods = [
            self.test_command_type_detection,
            self.test_status_by_type,
            self.test_optimized_initial_final_status,
            self.test_get_status_by_command,
            self.test_custom_status_check,
            self.test_utility_methods,
            self.test_performance_comparison
        ]
        
        passed = 0
        total = len(test_methods)
        
        for test_method in test_methods:
            try:
                test_method()
                passed += 1
            except Exception as e:
                print(f"❌ 测试方法 {test_method.__name__} 执行异常: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n🎉 测试完成: {passed}/{total} 个测试通过")


if __name__ == "__main__":
    tester = TestStatusCheckOptimization()
    tester.run_all_tests()
