"""
弹窗处理核心模块 - 适配UIAutomator2
提供智能弹窗检测、分类和处理功能
"""
import time
import threading
import os
import uiautomator2 as u2
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from core.logger import log


class PopupType(Enum):
    """弹窗类型枚举"""
    SYSTEM_UPDATE = "system_update"
    PERMISSION_DIALOG = "permission_dialog"
    APP_RATING = "app_rating"
    ADVERTISEMENT = "advertisement"
    GUIDE_DIALOG = "guide_dialog"
    ERROR_DIALOG = "error_dialog"
    LOADING_DIALOG = "loading_dialog"
    NOTIFICATION = "notification"
    NETWORK_ERROR = "network_error"
    LOGIN_DIALOG = "login_dialog"
    UNKNOWN = "unknown"


@dataclass
class PopupInfo:
    """弹窗信息数据类"""
    type: str = "unknown"
    location: Dict[str, int] = None
    text: str = ""
    buttons: List[str] = None
    elements: List[str] = None
    package: str = ""
    confidence: float = 0.0
    detection_method: str = ""
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.location is None:
            self.location = {}
        if self.buttons is None:
            self.buttons = []
        if self.elements is None:
            self.elements = []
        if self.timestamp == 0.0:
            self.timestamp = time.time()


@dataclass
class HandlingResult:
    """处理结果数据类"""
    success: bool
    method: str = ""
    error: str = ""
    duration: float = 0.0
    retry_count: int = 0


class PopupDetector:
    """弹窗检测器 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device):
        self.driver = driver
        self.detection_methods = {
            'elements': self._detect_by_elements,
            'text': self._detect_by_text,
            'layout': self._detect_by_layout,
            'package': self._detect_by_package
        }
    
    def detect_popups(self) -> List[PopupInfo]:
        """检测弹窗"""
        detected_popups = []
        
        for method_name, method in self.detection_methods.items():
            try:
                popups = method()
                for popup in popups:
                    popup.detection_method = method_name
                detected_popups.extend(popups)
            except Exception as e:
                log.debug(f"检测方法 {method_name} 失败: {e}")
        
        # 去重和合并结果
        return self._merge_detection_results(detected_popups)
    
    def _detect_by_elements(self) -> List[PopupInfo]:
        """基于UI元素检测弹窗 - UIAutomator2版本"""
        popups = []

        try:
            # 检测对话框元素 - 使用UIAutomator2的选择器
            dialog_selectors = [
                {'className': 'android.app.Dialog'},
                {'className': 'android.widget.PopupWindow'},
                {'resourceId': lambda x: 'dialog' in x.lower()},
                {'resourceId': lambda x: 'popup' in x.lower()},
                {'className': lambda x: 'Dialog' in x},
                {'className': lambda x: 'Popup' in x}
            ]

            for selector in dialog_selectors:
                try:
                    # 使用UIAutomator2的查找方法
                    if 'className' in selector:
                        if callable(selector['className']):
                            # 获取所有元素并过滤
                            all_elements = self.driver.dump_hierarchy()
                            # 这里需要解析XML并过滤
                            continue
                        else:
                            elements = self.driver(className=selector['className'])
                    elif 'resourceId' in selector:
                        if callable(selector['resourceId']):
                            # 获取所有元素并过滤
                            continue
                        else:
                            elements = self.driver(resourceId=selector['resourceId'])
                    else:
                        continue

                    # 检查元素是否存在且可见
                    if elements.exists():
                        for i in range(elements.count):
                            element = elements[i] if elements.count > 1 else elements
                            popup_info = self._analyze_u2_element(element)
                            if popup_info:
                                popups.append(popup_info)

                except Exception as e:
                    log.debug(f"检测选择器失败 {selector}: {e}")
                    continue

        except Exception as e:
            log.debug(f"元素检测失败: {e}")

        return popups
    
    def _detect_by_text(self) -> List[PopupInfo]:
        """基于文本内容检测弹窗 - UIAutomator2版本"""
        popups = []

        try:
            # 检测包含弹窗关键词的文本元素
            popup_keywords = [
                "更新", "update", "升级", "upgrade",
                "权限", "permission", "允许", "allow",
                "评分", "rating", "评价", "review",
                "广告", "advertisement", "推广",
                "错误", "error", "失败", "failed",
                "网络", "network", "连接", "connection"
            ]

            # 使用UIAutomator2查找TextView元素
            try:
                text_elements = self.driver(className="android.widget.TextView")

                if text_elements.exists():
                    for i in range(text_elements.count):
                        element = text_elements[i] if text_elements.count > 1 else text_elements
                        try:
                            element_info = element.info
                            text = element_info.get('text', '').lower()

                            if text and any(keyword in text for keyword in popup_keywords):
                                popup_info = self._create_popup_from_u2_text(element, element_info)
                                if popup_info:
                                    popups.append(popup_info)
                        except Exception as e:
                            log.debug(f"处理文本元素失败: {e}")
                            continue

            except Exception as e:
                log.debug(f"查找TextView失败: {e}")

        except Exception as e:
            log.debug(f"文本检测失败: {e}")

        return popups
    
    def _detect_by_layout(self) -> List[PopupInfo]:
        """基于布局特征检测弹窗 - UIAutomator2版本"""
        popups = []

        try:
            # 检测覆盖层布局 - 使用UIAutomator2选择器
            overlay_selectors = [
                {'className': 'android.widget.FrameLayout'},
                {'clickable': False, 'focusable': False},
                {'resourceId': lambda x: 'content' in x.lower() if x else False}
            ]

            for selector in overlay_selectors:
                try:
                    if 'className' in selector:
                        elements = self.driver(className=selector['className'])
                    elif 'clickable' in selector and 'focusable' in selector:
                        elements = self.driver(clickable=selector['clickable'], focusable=selector['focusable'])
                    else:
                        continue

                    if elements.exists():
                        for i in range(elements.count):
                            element = elements[i] if elements.count > 1 else elements
                            if self._is_u2_popup_layout(element):
                                popup_info = self._create_popup_from_u2_layout(element)
                                if popup_info:
                                    popups.append(popup_info)

                except Exception as e:
                    log.debug(f"布局选择器检测失败 {selector}: {e}")
                    continue

        except Exception as e:
            log.debug(f"布局检测失败: {e}")

        return popups
    
    def _detect_by_package(self) -> List[PopupInfo]:
        """基于包名检测弹窗 - UIAutomator2版本"""
        popups = []

        try:
            # 检测系统弹窗包名
            system_packages = [
                "com.android.packageinstaller",
                "com.android.permissioncontroller",
                "com.android.settings",
                "com.google.android.gms",
                "com.android.systemui"
            ]

            # 使用UIAutomator2获取当前应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')

            if current_package in system_packages:
                popup_info = PopupInfo(
                    package=current_package,
                    text=f"系统弹窗: {current_activity}",
                    confidence=0.8
                )
                popups.append(popup_info)

        except Exception as e:
            log.debug(f"包名检测失败: {e}")

        return popups

    def _analyze_u2_element(self, element) -> Optional[PopupInfo]:
        """分析UIAutomator2元素获取弹窗信息"""
        try:
            # 获取元素信息
            element_info = element.info
            bounds = element_info.get('bounds', {})
            text = element_info.get('text', '')

            # 查找按钮 - 使用UIAutomator2方式
            buttons = []
            try:
                # 在当前元素内查找按钮
                button_elements = self.driver(className="android.widget.Button")
                if button_elements.exists():
                    for i in range(button_elements.count):
                        btn = button_elements[i] if button_elements.count > 1 else button_elements
                        btn_info = btn.info
                        btn_text = btn_info.get('text', '')
                        if btn_text:
                            buttons.append(btn_text)
            except Exception as e:
                log.debug(f"查找按钮失败: {e}")

            popup_info = PopupInfo(
                location={
                    'x': bounds.get('left', 0),
                    'y': bounds.get('top', 0),
                    'width': bounds.get('right', 0) - bounds.get('left', 0),
                    'height': bounds.get('bottom', 0) - bounds.get('top', 0)
                },
                text=text,
                buttons=buttons,
                package=element_info.get('packageName', '')
            )

            return popup_info

        except Exception as e:
            log.debug(f"分析UIAutomator2元素失败: {e}")
            return None

    def _analyze_element(self, element) -> Optional[PopupInfo]:
        """分析元素获取弹窗信息"""
        try:
            # 获取元素信息
            bounds = element.rect
            text = element.text or ""
            
            # 查找按钮
            buttons = []
            button_elements = element.find_elements("xpath", ".//android.widget.Button")
            for btn in button_elements:
                if btn.text:
                    buttons.append(btn.text)
            
            popup_info = PopupInfo(
                location={
                    'x': bounds['x'],
                    'y': bounds['y'],
                    'width': bounds['width'],
                    'height': bounds['height']
                },
                text=text,
                buttons=buttons
            )
            
            return popup_info
            
        except Exception as e:
            log.debug(f"分析元素失败: {e}")
            return None

    def _create_popup_from_u2_text(self, element, element_info: dict) -> Optional[PopupInfo]:
        """从UIAutomator2文本元素创建弹窗信息"""
        try:
            text = element_info.get('text', '')
            bounds = element_info.get('bounds', {})

            popup_info = PopupInfo(
                text=text,
                location={
                    'x': bounds.get('left', 0),
                    'y': bounds.get('top', 0),
                    'width': bounds.get('right', 0) - bounds.get('left', 0),
                    'height': bounds.get('bottom', 0) - bounds.get('top', 0)
                },
                package=element_info.get('packageName', '')
            )

            return popup_info

        except Exception as e:
            log.debug(f"从UIAutomator2文本创建弹窗信息失败: {e}")
            return None

    def _create_popup_from_text(self, element) -> Optional[PopupInfo]:
        """从文本元素创建弹窗信息"""
        try:
            text = element.text
            bounds = element.rect
            
            popup_info = PopupInfo(
                text=text,
                location={
                    'x': bounds['x'],
                    'y': bounds['y'],
                    'width': bounds['width'],
                    'height': bounds['height']
                }
            )
            
            return popup_info
            
        except Exception as e:
            log.debug(f"从文本创建弹窗信息失败: {e}")
            return None

    def _create_popup_from_u2_layout(self, element) -> Optional[PopupInfo]:
        """从UIAutomator2布局元素创建弹窗信息"""
        try:
            element_info = element.info
            bounds = element_info.get('bounds', {})

            # 检查是否是全屏覆盖
            window_size = self.driver.window_size()
            screen_width, screen_height = window_size

            element_width = bounds.get('right', 0) - bounds.get('left', 0)
            element_height = bounds.get('bottom', 0) - bounds.get('top', 0)

            is_fullscreen = (element_width >= screen_width * 0.8 and
                           element_height >= screen_height * 0.8)

            if is_fullscreen:
                popup_info = PopupInfo(
                    location={
                        'x': bounds.get('left', 0),
                        'y': bounds.get('top', 0),
                        'width': element_width,
                        'height': element_height
                    },
                    elements=['fullscreen_overlay'],
                    package=element_info.get('packageName', '')
                )
                return popup_info

        except Exception as e:
            log.debug(f"从UIAutomator2布局创建弹窗信息失败: {e}")

        return None

    def _create_popup_from_layout(self, element) -> Optional[PopupInfo]:
        """从布局元素创建弹窗信息"""
        try:
            bounds = element.rect
            
            # 检查是否是全屏覆盖
            screen_size = self.driver.get_window_size()
            is_fullscreen = (bounds['width'] >= screen_size['width'] * 0.8 and
                           bounds['height'] >= screen_size['height'] * 0.8)
            
            if is_fullscreen:
                popup_info = PopupInfo(
                    location=bounds,
                    elements=['fullscreen_overlay']
                )
                return popup_info
            
        except Exception as e:
            log.debug(f"从布局创建弹窗信息失败: {e}")
        
        return None

    def _is_u2_popup_layout(self, element) -> bool:
        """判断UIAutomator2元素是否是弹窗布局"""
        try:
            element_info = element.info

            # 检查元素是否可见
            if not element_info.get('visible', False):
                return False

            # 检查元素大小
            bounds = element_info.get('bounds', {})
            width = bounds.get('right', 0) - bounds.get('left', 0)
            height = bounds.get('bottom', 0) - bounds.get('top', 0)

            if width < 100 or height < 100:
                return False

            # 检查是否有弹窗特征
            class_name = element_info.get('className', '')
            resource_id = element_info.get('resourceId', '')

            popup_indicators = ['dialog', 'popup', 'modal', 'overlay']

            if any(indicator in class_name.lower() for indicator in popup_indicators):
                return True

            if any(indicator in resource_id.lower() for indicator in popup_indicators):
                return True

            return False

        except Exception as e:
            log.debug(f"判断UIAutomator2弹窗布局失败: {e}")
            return False

    def _is_popup_layout(self, element) -> bool:
        """判断是否是弹窗布局"""
        try:
            # 检查元素是否可见且在前台
            if not element.is_displayed():
                return False
            
            # 检查元素大小
            bounds = element.rect
            if bounds['width'] < 100 or bounds['height'] < 100:
                return False
            
            # 检查是否有弹窗特征
            class_name = element.get_attribute('class')
            if any(keyword in class_name.lower() for keyword in ['dialog', 'popup', 'modal']):
                return True
            
            return False
            
        except Exception as e:
            log.debug(f"判断弹窗布局失败: {e}")
            return False


class PopupClassifier:
    """弹窗分类器"""

    def __init__(self):
        self.classification_rules = self._load_classification_rules()

    def classify_popup(self, popup_info: PopupInfo) -> str:
        """分类弹窗"""
        max_confidence = 0
        best_type = PopupType.UNKNOWN.value

        for popup_type, rules in self.classification_rules.items():
            confidence = self._calculate_confidence(popup_info, rules)
            if confidence > max_confidence:
                max_confidence = confidence
                best_type = popup_type

        popup_info.confidence = max_confidence
        return best_type

    def _calculate_confidence(self, popup_info: PopupInfo, rules: dict) -> float:
        """计算分类置信度"""
        confidence = 0.0
        total_weight = 0

        # 文本匹配 (权重: 0.4)
        if 'keywords' in rules and popup_info.text:
            text_match = any(keyword.lower() in popup_info.text.lower()
                           for keyword in rules['keywords'])
            if text_match:
                confidence += 0.4
            total_weight += 0.4

        # 按钮匹配 (权重: 0.3)
        if 'buttons' in rules and popup_info.buttons:
            button_match = any(any(btn_keyword.lower() in button.lower()
                                 for button in popup_info.buttons)
                             for btn_keyword in rules['buttons'])
            if button_match:
                confidence += 0.3
            total_weight += 0.3

        # 包名匹配 (权重: 0.2)
        if 'package' in rules and popup_info.package:
            if rules['package'] in popup_info.package:
                confidence += 0.2
            total_weight += 0.2

        # 元素匹配 (权重: 0.1)
        if 'elements' in rules and popup_info.elements:
            element_match = any(element in popup_info.elements
                              for element in rules['elements'])
            if element_match:
                confidence += 0.1
            total_weight += 0.1

        return confidence / total_weight if total_weight > 0 else 0.0

    def _load_classification_rules(self) -> dict:
        """加载分类规则"""
        return {
            PopupType.SYSTEM_UPDATE.value: {
                'keywords': ['更新', 'update', '升级', 'upgrade', '新版本', 'new version'],
                'buttons': ['立即更新', '稍后提醒', 'Update Now', 'Later', '更新', 'Update'],
                'package': 'com.android.packageinstaller'
            },
            PopupType.PERMISSION_DIALOG.value: {
                'keywords': ['权限', 'permission', '允许', 'allow', '访问', 'access'],
                'buttons': ['允许', '拒绝', 'Allow', 'Deny', '确定', 'OK'],
                'package': 'com.android.permissioncontroller'
            },
            PopupType.APP_RATING.value: {
                'keywords': ['评分', 'rating', '评价', 'review', '星级', 'star'],
                'buttons': ['评分', '稍后', 'Rate', 'Later', '五星好评'],
                'elements': ['星星', 'star', '⭐']
            },
            PopupType.ADVERTISEMENT.value: {
                'keywords': ['广告', 'ad', '推广', 'promotion', '赞助', 'sponsored'],
                'buttons': ['关闭', '跳过', 'Close', 'Skip', '×', '✕'],
                'elements': ['广告标识', 'ad_label', 'ad_marker']
            },
            PopupType.GUIDE_DIALOG.value: {
                'keywords': ['引导', 'guide', '教程', 'tutorial', '帮助', 'help'],
                'buttons': ['知道了', '下一步', 'Got it', 'Next', '继续', 'Continue'],
                'elements': ['箭头', 'arrow', '高亮', 'highlight']
            },
            PopupType.ERROR_DIALOG.value: {
                'keywords': ['错误', 'error', '失败', 'failed', '异常', 'exception'],
                'buttons': ['确定', '重试', 'OK', 'Retry', '关闭', 'Close'],
                'elements': ['错误图标', 'error_icon', '警告图标']
            },
            PopupType.NETWORK_ERROR.value: {
                'keywords': ['网络', 'network', '连接', 'connection', '无网络', 'no network'],
                'buttons': ['重试', '设置', 'Retry', 'Settings', '刷新', 'Refresh'],
                'elements': ['网络图标', 'network_icon']
            },
            PopupType.LOGIN_DIALOG.value: {
                'keywords': ['登录', 'login', '注册', 'register', '账号', 'account'],
                'buttons': ['登录', '注册', '跳过', 'Login', 'Register', 'Skip'],
                'elements': ['输入框', 'edittext', '用户名', 'username']
            }
        }

    def add_custom_rule(self, popup_type: str, rules: dict):
        """添加自定义分类规则"""
        self.classification_rules[popup_type] = rules
        log.info(f"添加自定义弹窗分类规则: {popup_type}")


class PopupHandlingStrategy:
    """弹窗处理策略基类 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device):
        self.driver = driver

    def handle(self, popup_info: PopupInfo) -> HandlingResult:
        """处理弹窗"""
        raise NotImplementedError

    def validate_result(self, popup_info: PopupInfo) -> bool:
        """验证处理结果"""
        try:
            # 简单验证：检查弹窗是否还存在
            time.sleep(0.5)  # 等待动画完成

            # 重新检测弹窗
            detector = PopupDetector(self.driver)
            current_popups = detector.detect_popups()

            # 检查是否还有相同的弹窗
            for current_popup in current_popups:
                if self._is_same_popup(popup_info, current_popup):
                    return False

            return True

        except Exception as e:
            log.debug(f"验证处理结果失败: {e}")
            return False

    def _is_same_popup(self, popup1: PopupInfo, popup2: PopupInfo) -> bool:
        """判断是否是同一个弹窗"""
        # 基于文本和位置判断
        if popup1.text and popup2.text:
            if popup1.text.strip() == popup2.text.strip():
                return True

        if popup1.location and popup2.location:
            x_diff = abs(popup1.location.get('x', 0) - popup2.location.get('x', 0))
            y_diff = abs(popup1.location.get('y', 0) - popup2.location.get('y', 0))
            if x_diff < 30 and y_diff < 30:
                return True

        return False


class ClickCloseStrategy(PopupHandlingStrategy):
    """点击关闭策略 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device):
        super().__init__(driver)
        self.close_button_patterns = [
            "关闭", "close", "×", "✕", "dismiss", "cancel",
            "取消", "稍后", "later", "skip", "跳过", "确定", "ok"
        ]

    def handle(self, popup_info: PopupInfo) -> HandlingResult:
        """点击关闭按钮"""
        start_time = time.time()

        try:
            # 查找关闭按钮
            close_button = self._find_close_button(popup_info)

            if close_button:
                close_button.click()
                time.sleep(1)  # 等待动画完成

                # 验证弹窗是否消失
                if self.validate_result(popup_info):
                    duration = time.time() - start_time
                    return HandlingResult(
                        success=True,
                        method="click_close",
                        duration=duration
                    )
                else:
                    return HandlingResult(
                        success=False,
                        error="弹窗未消失",
                        duration=time.time() - start_time
                    )
            else:
                return HandlingResult(
                    success=False,
                    error="未找到关闭按钮",
                    duration=time.time() - start_time
                )

        except Exception as e:
            return HandlingResult(
                success=False,
                error=str(e),
                duration=time.time() - start_time
            )

    def _find_close_button(self, popup_info: PopupInfo):
        """查找关闭按钮 - UIAutomator2版本"""
        try:
            # 首先按文本查找关闭按钮
            for pattern in self.close_button_patterns:
                try:
                    # 使用UIAutomator2的文本匹配
                    button = self.driver(text=pattern)
                    if button.exists():
                        return button

                    # 尝试模糊匹配
                    button = self.driver(textContains=pattern)
                    if button.exists():
                        return button

                    # 尝试描述匹配
                    button = self.driver(description=pattern)
                    if button.exists():
                        return button

                    button = self.driver(descriptionContains=pattern)
                    if button.exists():
                        return button

                except Exception as e:
                    log.debug(f"查找按钮模式失败 {pattern}: {e}")
                    continue

            # 如果没找到，尝试查找常见的关闭按钮资源ID
            close_resource_ids = [
                "android:id/button2",  # 取消按钮
                "android:id/button1",  # 确定按钮
                "android:id/button_negative",  # 负面按钮
                "android:id/button_positive"   # 正面按钮
            ]

            for resource_id in close_resource_ids:
                try:
                    button = self.driver(resourceId=resource_id)
                    if button.exists():
                        return button
                except Exception as e:
                    log.debug(f"查找资源ID失败 {resource_id}: {e}")
                    continue

            # 最后尝试查找Button类型的元素
            try:
                buttons = self.driver(className="android.widget.Button")
                if buttons.exists():
                    # 如果有多个按钮，优先选择包含关闭关键词的
                    for i in range(buttons.count):
                        button = buttons[i] if buttons.count > 1 else buttons
                        button_info = button.info
                        button_text = button_info.get('text', '').lower()

                        if any(pattern in button_text for pattern in self.close_button_patterns):
                            return button

                    # 如果没有匹配的文本，返回第一个按钮
                    return buttons[0] if buttons.count > 1 else buttons

            except Exception as e:
                log.debug(f"查找Button类型元素失败: {e}")

            return None

        except Exception as e:
            log.debug(f"查找关闭按钮失败: {e}")
            return None


class BackKeyStrategy(PopupHandlingStrategy):
    """返回键策略 - 适配UIAutomator2"""

    def handle(self, popup_info: PopupInfo) -> HandlingResult:
        """按返回键关闭弹窗"""
        start_time = time.time()

        try:
            # 使用UIAutomator2的返回键方法
            self.driver.press("back")
            time.sleep(1)

            if self.validate_result(popup_info):
                duration = time.time() - start_time
                return HandlingResult(
                    success=True,
                    method="back_key",
                    duration=duration
                )
            else:
                return HandlingResult(
                    success=False,
                    error="返回键无效",
                    duration=time.time() - start_time
                )

        except Exception as e:
            return HandlingResult(
                success=False,
                error=str(e),
                duration=time.time() - start_time
            )


class SwipeDismissStrategy(PopupHandlingStrategy):
    """滑动消除策略"""

    def handle(self, popup_info: PopupInfo) -> HandlingResult:
        """通过滑动消除弹窗"""
        start_time = time.time()

        try:
            # 根据弹窗类型选择滑动方向
            if popup_info.type in [PopupType.NOTIFICATION.value, 'banner']:
                # 向上滑动消除通知类弹窗
                self._swipe_up(popup_info.location)
            elif popup_info.type in ['side_panel']:
                # 向左滑动消除侧边栏
                self._swipe_left(popup_info.location)
            else:
                # 默认向上滑动
                self._swipe_up(popup_info.location)

            time.sleep(1)

            if self.validate_result(popup_info):
                duration = time.time() - start_time
                return HandlingResult(
                    success=True,
                    method="swipe_dismiss",
                    duration=duration
                )
            else:
                return HandlingResult(
                    success=False,
                    error="滑动无效",
                    duration=time.time() - start_time
                )

        except Exception as e:
            return HandlingResult(
                success=False,
                error=str(e),
                duration=time.time() - start_time
            )

    def _swipe_up(self, location: Dict[str, int]):
        """向上滑动 - UIAutomator2版本"""
        if location:
            start_x = location.get('x', 0) + location.get('width', 100) // 2
            start_y = location.get('y', 0) + location.get('height', 100) // 2
            end_x = start_x
            end_y = start_y - 200  # 向上滑动200像素

            # 使用UIAutomator2的滑动方法
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)

    def _swipe_left(self, location: Dict[str, int]):
        """向左滑动 - UIAutomator2版本"""
        if location:
            start_x = location.get('x', 0) + location.get('width', 100) // 2
            start_y = location.get('y', 0) + location.get('height', 100) // 2
            end_x = start_x - 200  # 向左滑动200像素
            end_y = start_y

            # 使用UIAutomator2的滑动方法
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)


class WaitDisappearStrategy(PopupHandlingStrategy):
    """等待消失策略"""

    def __init__(self, driver, timeout=10):
        super().__init__(driver)
        self.timeout = timeout

    def handle(self, popup_info: PopupInfo) -> HandlingResult:
        """等待弹窗自动消失"""
        start_time = time.time()

        try:
            while time.time() - start_time < self.timeout:
                if self.validate_result(popup_info):
                    duration = time.time() - start_time
                    return HandlingResult(
                        success=True,
                        method="wait_disappear",
                        duration=duration
                    )
                time.sleep(0.5)

            return HandlingResult(
                success=False,
                error="等待超时",
                duration=time.time() - start_time
            )

        except Exception as e:
            return HandlingResult(
                success=False,
                error=str(e),
                duration=time.time() - start_time
            )


class PopupHandler:
    """弹窗处理器主类 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device):
        self.driver = driver
        self.classifier = PopupClassifier()
        self.detector = PopupDetector(driver)

        # 初始化处理策略
        self.strategies = {
            'click_close': ClickCloseStrategy(driver),
            'back_key': BackKeyStrategy(driver),
            'swipe_dismiss': SwipeDismissStrategy(driver),
            'wait_disappear': WaitDisappearStrategy(driver)
        }

        # 弹窗类型与处理策略的映射
        self.type_strategy_map = {
            PopupType.SYSTEM_UPDATE.value: ['click_close', 'back_key'],
            PopupType.PERMISSION_DIALOG.value: ['click_close'],
            PopupType.APP_RATING.value: ['click_close', 'back_key'],
            PopupType.ADVERTISEMENT.value: ['click_close', 'swipe_dismiss'],
            PopupType.NOTIFICATION.value: ['swipe_dismiss', 'click_close'],
            PopupType.GUIDE_DIALOG.value: ['click_close'],
            PopupType.ERROR_DIALOG.value: ['click_close', 'back_key'],
            PopupType.LOADING_DIALOG.value: ['wait_disappear'],
            PopupType.NETWORK_ERROR.value: ['click_close', 'back_key'],
            PopupType.LOGIN_DIALOG.value: ['back_key', 'click_close'],
            'default': ['click_close', 'back_key', 'swipe_dismiss']
        }

        # 统计信息
        self.stats = {
            'total_handled': 0,
            'success_count': 0,
            'failure_count': 0,
            'type_counts': {},
            'method_counts': {}
        }

    def handle_popup(self, popup_info: PopupInfo, max_retries: int = 3) -> HandlingResult:
        """处理弹窗"""
        # 分类弹窗
        popup_type = self.classifier.classify_popup(popup_info)
        popup_info.type = popup_type

        log.info(f"检测到弹窗: {popup_type} (置信度: {popup_info.confidence:.2f})")

        # 获取处理策略
        strategies = self.type_strategy_map.get(popup_type, self.type_strategy_map['default'])

        # 尝试各种策略
        for retry in range(max_retries):
            for strategy_name in strategies:
                strategy = self.strategies.get(strategy_name)
                if strategy:
                    log.info(f"尝试策略 {strategy_name} 处理弹窗 (第{retry+1}次尝试)")

                    result = strategy.handle(popup_info)
                    result.retry_count = retry

                    if result.success:
                        log.info(f"成功处理弹窗: {popup_type} - {strategy_name}")
                        self._update_stats(popup_type, strategy_name, True)
                        return result
                    else:
                        log.warning(f"策略失败: {strategy_name} - {result.error}")

            # 如果所有策略都失败，等待一下再重试
            if retry < max_retries - 1:
                time.sleep(1)

        log.error(f"所有策略都失败，无法处理弹窗: {popup_type}")
        self._update_stats(popup_type, "failed", False)
        return HandlingResult(success=False, error="所有处理策略都失败")

    def handle_all_popups(self) -> List[HandlingResult]:
        """处理当前屏幕上的所有弹窗"""
        results = []

        try:
            # 检测所有弹窗
            popups = self.detector.detect_popups()

            for popup in popups:
                result = self.handle_popup(popup)
                results.append(result)

                # 如果处理成功，稍等一下再处理下一个
                if result.success:
                    time.sleep(0.5)

        except Exception as e:
            log.error(f"处理所有弹窗失败: {e}")
            results.append(HandlingResult(success=False, error=str(e)))

        return results

    def register_custom_strategy(self, name: str, strategy: PopupHandlingStrategy):
        """注册自定义处理策略"""
        self.strategies[name] = strategy
        log.info(f"注册自定义策略: {name}")

    def add_type_strategy_mapping(self, popup_type: str, strategies: List[str]):
        """添加弹窗类型与策略的映射"""
        self.type_strategy_map[popup_type] = strategies
        log.info(f"添加策略映射: {popup_type} -> {strategies}")

    def _update_stats(self, popup_type: str, method: str, success: bool):
        """更新统计信息"""
        self.stats['total_handled'] += 1

        if success:
            self.stats['success_count'] += 1
            # 统计成功的方法
            if method not in self.stats['method_counts']:
                self.stats['method_counts'][method] = 0
            self.stats['method_counts'][method] += 1
        else:
            self.stats['failure_count'] += 1

        # 统计弹窗类型
        if popup_type not in self.stats['type_counts']:
            self.stats['type_counts'][popup_type] = 0
        self.stats['type_counts'][popup_type] += 1

    def get_stats(self) -> dict:
        """获取统计信息"""
        success_rate = 0.0
        if self.stats['total_handled'] > 0:
            success_rate = self.stats['success_count'] / self.stats['total_handled']

        return {
            **self.stats,
            'success_rate': success_rate
        }
    
    def _merge_detection_results(self, popups: List[PopupInfo]) -> List[PopupInfo]:
        """合并检测结果，去重"""
        if not popups:
            return []
        
        # 简单去重：基于位置和文本
        unique_popups = []
        for popup in popups:
            is_duplicate = False
            for existing in unique_popups:
                if self._is_same_popup(popup, existing):
                    # 合并信息，保留置信度更高的
                    if popup.confidence > existing.confidence:
                        unique_popups.remove(existing)
                        unique_popups.append(popup)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_popups.append(popup)
        
        return unique_popups
    
    def _is_same_popup(self, popup1: PopupInfo, popup2: PopupInfo) -> bool:
        """判断是否是同一个弹窗"""
        # 基于位置判断
        if popup1.location and popup2.location:
            x_diff = abs(popup1.location.get('x', 0) - popup2.location.get('x', 0))
            y_diff = abs(popup1.location.get('y', 0) - popup2.location.get('y', 0))
            if x_diff < 50 and y_diff < 50:  # 位置相近
                return True
        
        # 基于文本判断
        if popup1.text and popup2.text:
            if popup1.text.strip() == popup2.text.strip():
                return True
        
        return False
