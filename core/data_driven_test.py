"""
数据驱动测试基类
提供Excel数据驱动测试的基础功能
"""
import pytest
import allure
from typing import List, Dict, Any, Callable
from core.logger import log
from utils.excel_utils import ExcelUtils


class DataDrivenTest:
    """数据驱动测试基类"""
    
    def __init__(self, excel_file: str = None, sheet_name: str = None):
        """
        初始化数据驱动测试
        
        Args:
            excel_file: Excel文件路径
            sheet_name: 工作表名称
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.test_data = []
        self._load_test_data()
    
    def _load_test_data(self):
        """加载测试数据"""
        if self.excel_file:
            self.test_data = ExcelUtils.read_test_data(self.excel_file, self.sheet_name)
        else:
            # 默认加载Ella测试数据
            self.test_data = ExcelUtils.read_ella_test_data()
        
        if not ExcelUtils.validate_test_data(self.test_data):
            log.error("测试数据验证失败")
            self.test_data = []
    
    def get_test_data(self, priority: str = None, tags: str = None) -> List[Dict[str, Any]]:
        """
        获取测试数据
        
        Args:
            priority: 优先级过滤
            tags: 标签过滤
            
        Returns:
            List[Dict[str, Any]]: 过滤后的测试数据
        """
        return ExcelUtils.filter_test_data(self.test_data, priority, tags)
    
    def execute_step(self, step: str, app_instance) -> bool:
        """
        执行测试步骤
        
        Args:
            step: 测试步骤
            app_instance: 应用实例
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行测试步骤: {step}")
            
            # 根据步骤类型执行不同的操作
            if hasattr(app_instance, 'execute_text_command'):
                # 对于Ella应用，执行文本命令
                return app_instance.execute_text_command(step)
            elif hasattr(app_instance, 'calculate'):
                # 对于计算器应用，执行计算
                result = app_instance.calculate(step)
                return result is not None
            else:
                log.error(f"应用实例不支持步骤执行: {type(app_instance)}")
                return False
                
        except Exception as e:
            log.error(f"执行测试步骤失败: {e}")
            return False
    
    def verify_result(self, except_result: str, app_instance, step: str = None) -> bool:
        """
        验证测试结果
        
        Args:
            except_result: 期望结果
            app_instance: 应用实例
            step: 原始测试步骤（用于上下文验证）
            
        Returns:
            bool: 验证是否通过
        """
        try:
            log.info(f"验证期望结果: {except_result}")
            
            # 根据期望结果类型进行不同的验证
            if self._is_bluetooth_assertion(except_result):
                return self._verify_bluetooth_status(except_result, app_instance)
            elif self._is_app_assertion(except_result):
                return self._verify_app_opened(except_result, app_instance)
            elif self._is_response_assertion(except_result):
                return self._verify_response_content(except_result, app_instance, step)
            elif self._is_calculation_assertion(except_result):
                return self._verify_calculation_result(except_result, app_instance, step)
            else:
                # 默认验证：检查响应中是否包含期望内容
                return self._verify_default_content(except_result, app_instance)
                
        except Exception as e:
            log.error(f"验证测试结果失败: {e}")
            return False
    
    def _is_bluetooth_assertion(self, except_result: str) -> bool:
        """判断是否为蓝牙状态断言"""
        bluetooth_keywords = ['蓝牙已开启', '蓝牙已关闭', 'bluetooth_on', 'bluetooth_off']
        return any(keyword in except_result for keyword in bluetooth_keywords)
    
    def _is_app_assertion(self, except_result: str) -> bool:
        """判断是否为应用打开断言"""
        return except_result.startswith('com.') or 'package' in except_result.lower()
    
    def _is_response_assertion(self, except_result: str) -> bool:
        """判断是否为响应内容断言"""
        response_keywords = ['响应包含', '回复包含', '包含']
        return any(keyword in except_result for keyword in response_keywords)
    
    def _is_calculation_assertion(self, except_result: str) -> bool:
        """判断是否为计算结果断言"""
        return except_result.replace('.', '').replace('-', '').isdigit()
    
    def _verify_bluetooth_status(self, except_result: str, app_instance) -> bool:
        """验证蓝牙状态"""
        if not hasattr(app_instance, 'check_bluetooth_status'):
            log.error("应用实例不支持蓝牙状态检查")
            return False
        
        actual_status = app_instance.check_bluetooth_status()
        
        if '蓝牙已开启' in except_result or 'bluetooth_on' in except_result:
            expected_status = True
        elif '蓝牙已关闭' in except_result or 'bluetooth_off' in except_result:
            expected_status = False
        else:
            log.error(f"无法解析蓝牙状态期望: {except_result}")
            return False
        
        result = actual_status == expected_status
        log.info(f"蓝牙状态验证: 期望={expected_status}, 实际={actual_status}, 结果={'通过' if result else '失败'}")
        return result
    
    def _verify_app_opened(self, except_result: str, app_instance) -> bool:
        """验证应用是否打开"""
        try:
            import subprocess
            
            # 获取当前前台应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "mCurrentFocus"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )
            
            if result.returncode == 0:
                current_focus = result.stdout.strip()
                log.info(f"当前焦点应用: {current_focus}")
                
                # 检查是否包含期望的包名
                is_opened = except_result in current_focus
                log.info(f"应用打开验证: 期望包名={except_result}, 结果={'通过' if is_opened else '失败'}")
                return is_opened
            else:
                log.error("获取当前应用失败")
                return False
                
        except Exception as e:
            log.error(f"验证应用打开状态失败: {e}")
            return False
    
    def _verify_response_content(self, except_result: str, app_instance, step: str = None) -> bool:
        """验证响应内容"""
        if not hasattr(app_instance, 'get_response_text'):
            log.error("应用实例不支持响应文本获取")
            return False
        
        response_text = app_instance.get_response_text()
        
        # 提取期望内容
        if '响应包含' in except_result:
            expected_content = except_result.replace('响应包含', '').strip()
        elif '回复包含' in except_result:
            expected_content = except_result.replace('回复包含', '').strip()
        elif '包含' in except_result:
            expected_content = except_result.replace('包含', '').strip()
        else:
            expected_content = except_result
        
        result = expected_content in response_text
        log.info(f"响应内容验证: 期望包含='{expected_content}', 实际响应='{response_text}', 结果={'通过' if result else '失败'}")
        return result
    
    def _verify_calculation_result(self, except_result: str, app_instance, step: str = None) -> bool:
        """验证计算结果"""
        if not hasattr(app_instance, 'get_result'):
            log.error("应用实例不支持计算结果获取")
            return False
        
        actual_result = app_instance.get_result()
        expected_result = except_result.strip()
        
        result = str(actual_result) == expected_result
        log.info(f"计算结果验证: 期望={expected_result}, 实际={actual_result}, 结果={'通过' if result else '失败'}")
        return result
    
    def _verify_default_content(self, except_result: str, app_instance) -> bool:
        """默认内容验证"""
        if hasattr(app_instance, 'get_response_text'):
            response_text = app_instance.get_response_text()
            result = except_result in response_text
            log.info(f"默认内容验证: 期望包含='{except_result}', 实际响应='{response_text}', 结果={'通过' if result else '失败'}")
            return result
        else:
            log.warning("应用实例不支持响应验证，跳过验证")
            return True
    
    def run_data_driven_test(self, app_instance, test_method_name: str = "数据驱动测试"):
        """
        运行数据驱动测试
        
        Args:
            app_instance: 应用实例
            test_method_name: 测试方法名称
        """
        test_data = self.get_test_data()
        
        if not test_data:
            log.error("没有可执行的测试数据")
            pytest.fail("没有可执行的测试数据")
        
        log.info(f"开始执行数据驱动测试，共{len(test_data)}条用例")
        
        failed_cases = []
        
        for i, data in enumerate(test_data):
            case_name = data.get('case_name', f"测试用例_{i+1}")
            step = data['step']
            except_result = data['except']
            timeout = data.get('timeout', 15)
            
            with allure.step(f"执行用例: {case_name}"):
                log.info(f"执行第{i+1}条用例: {case_name}")
                
                try:
                    # 执行测试步骤
                    with allure.step(f"执行步骤: {step}"):
                        step_success = self.execute_step(step, app_instance)
                        if not step_success:
                            failed_cases.append(f"{case_name}: 步骤执行失败")
                            continue
                    
                    # 等待响应（如果应用支持）
                    if hasattr(app_instance, 'wait_for_response'):
                        with allure.step("等待响应"):
                            response_received = app_instance.wait_for_response(timeout=timeout)
                            if not response_received:
                                log.warning(f"用例{case_name}未收到响应，继续验证")
                    
                    # 验证结果
                    with allure.step(f"验证期望结果: {except_result}"):
                        verification_success = self.verify_result(except_result, app_instance, step)
                        if not verification_success:
                            failed_cases.append(f"{case_name}: 结果验证失败")
                            continue
                    
                    log.info(f"✅ 用例{case_name}执行成功")
                    
                except Exception as e:
                    log.error(f"❌ 用例{case_name}执行异常: {e}")
                    failed_cases.append(f"{case_name}: 执行异常 - {e}")
        
        # 汇总测试结果
        total_cases = len(test_data)
        failed_count = len(failed_cases)
        success_count = total_cases - failed_count
        
        test_summary = f"""
数据驱动测试完成
总用例数: {total_cases}
成功用例: {success_count}
失败用例: {failed_count}
成功率: {success_count/total_cases*100:.1f}%
"""
        
        if failed_cases:
            test_summary += f"\n失败用例详情:\n" + "\n".join(f"- {case}" for case in failed_cases)
        
        log.info(test_summary)
        allure.attach(test_summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)
        
        # 如果有失败用例，抛出断言错误
        if failed_cases:
            pytest.fail(f"数据驱动测试失败，{failed_count}/{total_cases}个用例失败")


def parametrize_from_excel(excel_file: str, sheet_name: str = None, 
                          priority: str = None, tags: str = None):
    """
    从Excel文件生成pytest参数化装饰器
    
    Args:
        excel_file: Excel文件路径
        sheet_name: 工作表名称
        priority: 优先级过滤
        tags: 标签过滤
        
    Returns:
        pytest.mark.parametrize装饰器
    """
    test_data = ExcelUtils.read_test_data(excel_file, sheet_name)
    filtered_data = ExcelUtils.filter_test_data(test_data, priority, tags)
    
    if not filtered_data:
        log.warning("没有找到匹配的测试数据")
        return pytest.mark.parametrize("test_data", [])
    
    # 生成参数化数据
    param_data = []
    for data in filtered_data:
        param_data.append(pytest.param(
            data,
            id=data.get('case_name', data.get('case_id', 'unknown'))
        ))
    
    return pytest.mark.parametrize("test_data", param_data)
