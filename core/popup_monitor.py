"""
弹窗监控器模块 - 适配UIAutomator2
提供后台弹窗监控和自动处理功能
"""
import time
import threading
import yaml
import os
import uiautomator2 as u2
from typing import List, Dict, Any, Optional, Callable
from core.logger import log
from core.popup_handler import <PERSON>up<PERSON>andler, PopupInfo, HandlingResult


class PopupConfig:
    """弹窗处理配置"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or "config/popup_config.yaml"
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            'detection': {
                'interval': 2,  # 检测间隔(秒)
                'timeout': 30,  # 检测超时(秒)
                'min_confidence': 0.6,  # 最小置信度
                'methods': ['elements', 'text', 'layout', 'package'],
                'enabled': True  # 是否启用检测
            },
            'handling': {
                'max_retries': 3,  # 最大重试次数
                'retry_delay': 1,  # 重试延迟(秒)
                'screenshot_on_failure': True,  # 失败时截图
                'log_all_attempts': True,  # 记录所有尝试
                'auto_handle': True  # 是否自动处理
            },
            'ignored_types': [
                'loading_dialog',  # 忽略加载对话框
                'progress_dialog'  # 忽略进度对话框
            ],
            'priority_types': [
                'permission_dialog',  # 优先处理权限对话框
                'error_dialog',  # 优先处理错误对话框
                'system_update'  # 优先处理系统更新
            ],
            'custom_rules': {},  # 自定义规则
            'monitoring': {
                'enabled': False,  # 是否启用后台监控
                'daemon_thread': True,  # 是否使用守护线程
                'stop_on_error': False  # 出错时是否停止监控
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
                    # 深度合并配置
                    self.config = self._deep_merge(default_config, user_config)
            else:
                self.config = default_config
                self.save_config()  # 保存默认配置
        except Exception as e:
            log.error(f"加载弹窗配置失败: {e}")
            self.config = default_config
    
    def save_config(self):
        """保存配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            log.info(f"弹窗配置已保存: {self.config_file}")
        except Exception as e:
            log.error(f"保存弹窗配置失败: {e}")
    
    def _deep_merge(self, default: dict, user: dict) -> dict:
        """深度合并字典"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    # 配置属性访问器
    @property
    def detection_interval(self) -> float:
        return self.config['detection']['interval']
    
    @property
    def min_confidence(self) -> float:
        return self.config['detection']['min_confidence']
    
    @property
    def ignored_popup_types(self) -> List[str]:
        return self.config['ignored_types']
    
    @property
    def priority_popup_types(self) -> List[str]:
        return self.config['priority_types']
    
    @property
    def max_retries(self) -> int:
        return self.config['handling']['max_retries']
    
    @property
    def auto_handle_enabled(self) -> bool:
        return self.config['handling']['auto_handle']
    
    @property
    def monitoring_enabled(self) -> bool:
        return self.config['monitoring']['enabled']
    
    def update_config(self, key_path: str, value: Any):
        """更新配置值"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        self.save_config()
        log.info(f"配置已更新: {key_path} = {value}")


class PopupMonitor:
    """弹窗监控器 - 适配UIAutomator2"""

    def __init__(self, driver: u2.Device, config: PopupConfig = None):
        self.driver = driver
        self.config = config or PopupConfig()
        self.handler = PopupHandler(driver)
        
        # 监控状态
        self.is_monitoring = False
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_popup_detected: Optional[Callable[[PopupInfo], None]] = None
        self.on_popup_handled: Optional[Callable[[PopupInfo, HandlingResult], None]] = None
        self.on_monitoring_error: Optional[Callable[[Exception], None]] = None
        
        # 统计信息
        self.monitor_stats = {
            'start_time': 0,
            'total_cycles': 0,
            'popups_detected': 0,
            'popups_handled': 0,
            'errors': 0
        }
    
    def start_monitoring(self):
        """开始监控弹窗"""
        if self.is_monitoring:
            log.warning("弹窗监控已在运行")
            return
        
        if not self.config.monitoring_enabled:
            log.info("弹窗监控已禁用，跳过启动")
            return
        
        self.is_monitoring = True
        self.stop_event.clear()
        self.monitor_stats['start_time'] = time.time()
        
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            name="PopupMonitor"
        )
        self.monitoring_thread.daemon = self.config.config['monitoring']['daemon_thread']
        self.monitoring_thread.start()
        
        log.info("弹窗监控已启动")
    
    def stop_monitoring(self):
        """停止监控弹窗"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
            if self.monitoring_thread.is_alive():
                log.warning("监控线程未能正常停止")
        
        log.info("弹窗监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        log.info("弹窗监控循环开始")
        
        while self.is_monitoring and not self.stop_event.is_set():
            try:
                self.monitor_stats['total_cycles'] += 1
                
                # 检测弹窗
                popups = self.handler.detector.detect_popups()
                
                if popups:
                    self.monitor_stats['popups_detected'] += len(popups)
                    log.debug(f"检测到 {len(popups)} 个弹窗")
                    
                    for popup in popups:
                        # 触发检测回调
                        if self.on_popup_detected:
                            try:
                                self.on_popup_detected(popup)
                            except Exception as e:
                                log.error(f"弹窗检测回调失败: {e}")
                        
                        # 判断是否应该处理弹窗
                        if self._should_handle_popup(popup):
                            # 处理弹窗
                            result = self.handler.handle_popup(popup)
                            
                            if result.success:
                                self.monitor_stats['popups_handled'] += 1
                            
                            # 触发处理回调
                            if self.on_popup_handled:
                                try:
                                    self.on_popup_handled(popup, result)
                                except Exception as e:
                                    log.error(f"弹窗处理回调失败: {e}")
                            
                            # 记录处理结果
                            self._log_handling_result(popup, result)
                
                # 等待下一次检测
                self.stop_event.wait(self.config.detection_interval)
                
            except Exception as e:
                self.monitor_stats['errors'] += 1
                log.error(f"弹窗监控异常: {e}")
                
                # 触发错误回调
                if self.on_monitoring_error:
                    try:
                        self.on_monitoring_error(e)
                    except Exception as callback_error:
                        log.error(f"监控错误回调失败: {callback_error}")
                
                # 检查是否应该停止监控
                if self.config.config['monitoring']['stop_on_error']:
                    log.error("监控出错，停止监控")
                    break
                
                # 等待一段时间再继续
                self.stop_event.wait(self.config.detection_interval)
        
        log.info("弹窗监控循环结束")
    
    def _should_handle_popup(self, popup: PopupInfo) -> bool:
        """判断是否应该处理弹窗"""
        # 检查是否启用自动处理
        if not self.config.auto_handle_enabled:
            return False
        
        # 分类弹窗
        popup_type = self.handler.classifier.classify_popup(popup)
        popup.type = popup_type
        
        # 检查是否在忽略列表中
        if popup_type in self.config.ignored_popup_types:
            log.debug(f"忽略弹窗类型: {popup_type}")
            return False
        
        # 检查置信度
        if popup.confidence < self.config.min_confidence:
            log.debug(f"弹窗置信度过低: {popup.confidence:.2f} < {self.config.min_confidence}")
            return False
        
        return True
    
    def _log_handling_result(self, popup: PopupInfo, result: HandlingResult):
        """记录处理结果"""
        if result.success:
            log.info(f"✅ 成功处理弹窗: {popup.type} - {result.method} ({result.duration:.2f}s)")
        else:
            log.warning(f"❌ 处理弹窗失败: {popup.type} - {result.error} ({result.duration:.2f}s)")
            
            # 失败时截图
            if self.config.config['handling']['screenshot_on_failure']:
                try:
                    screenshot_path = f"reports/screenshots/popup_failure_{int(time.time())}.png"
                    os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                    self.driver.save_screenshot(screenshot_path)
                    log.info(f"失败截图已保存: {screenshot_path}")
                except Exception as e:
                    log.error(f"保存失败截图失败: {e}")
    
    def handle_popup_immediately(self, popup_type: str = None) -> bool:
        """立即处理弹窗"""
        try:
            popups = self.handler.detector.detect_popups()
            
            for popup in popups:
                # 分类弹窗
                classified_type = self.handler.classifier.classify_popup(popup)
                popup.type = classified_type
                
                # 如果指定了类型，只处理匹配的弹窗
                if popup_type is None or popup.type == popup_type:
                    result = self.handler.handle_popup(popup)
                    self._log_handling_result(popup, result)
                    
                    if result.success:
                        return True
            
            return False
            
        except Exception as e:
            log.error(f"立即处理弹窗失败: {e}")
            return False
    
    def get_monitor_stats(self) -> dict:
        """获取监控统计信息"""
        runtime = time.time() - self.monitor_stats['start_time'] if self.monitor_stats['start_time'] > 0 else 0
        
        return {
            **self.monitor_stats,
            'runtime': runtime,
            'is_monitoring': self.is_monitoring,
            'handler_stats': self.handler.get_stats()
        }
    
    def set_callback(self, event: str, callback: Callable):
        """设置回调函数"""
        if event == 'popup_detected':
            self.on_popup_detected = callback
        elif event == 'popup_handled':
            self.on_popup_handled = callback
        elif event == 'monitoring_error':
            self.on_monitoring_error = callback
        else:
            raise ValueError(f"未知的回调事件: {event}")
        
        log.info(f"设置回调函数: {event}")
    
    def add_custom_popup_rule(self, popup_type: str, rules: dict):
        """添加自定义弹窗规则"""
        self.handler.classifier.add_custom_rule(popup_type, rules)
        
        # 保存到配置
        self.config.config['custom_rules'][popup_type] = rules
        self.config.save_config()
        
        log.info(f"添加自定义弹窗规则: {popup_type}")
    
    def update_strategy_mapping(self, popup_type: str, strategies: List[str]):
        """更新弹窗处理策略映射"""
        self.handler.add_type_strategy_mapping(popup_type, strategies)
        log.info(f"更新策略映射: {popup_type} -> {strategies}")
