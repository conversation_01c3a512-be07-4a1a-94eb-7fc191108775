"""
日志模块 - 优化版本
基于loguru实现的分类日志系统，支持日志轮转和分类管理
"""
import os
import sys
from datetime import datetime
from loguru import logger
from utils.yaml_utils import YamlUtils
from utils.file_utils import FileUtils


class Logger:
    """优化的日志管理类"""

    _instance = None
    _initialized = False
    _loggers = {}  # 存储不同类型的日志器

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化日志配置"""
        if not self._initialized:
            self._setup_logger()
            Logger._initialized = True
    
    def _setup_logger(self):
        """设置优化的日志配置"""
        try:
            # 加载配置
            config_path = YamlUtils.get_config_path("config.yaml")
            config = YamlUtils.load_yaml(config_path)
            log_config = config.get("logging", {})

            # 移除默认处理器
            logger.remove()

            # 获取项目根目录
            project_root = YamlUtils.get_project_root()

            # 创建日志目录结构
            self._create_log_directories(project_root)

            # 日志格式
            log_format = log_config.get("format",
                "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")

            # 日志级别
            log_level = log_config.get("level", "INFO")

            # 添加控制台处理器
            logger.add(
                sys.stdout,
                format=log_format,
                level=log_level,
                colorize=True,
                filter=self._console_filter
            )

            # 设置分类日志处理器
            self._setup_categorized_loggers(project_root, log_config, log_format, log_level)

        except Exception as e:
            # 如果配置加载失败，使用默认配置
            logger.add(sys.stdout, level="INFO")
            logger.error(f"日志配置加载失败，使用默认配置: {e}")

    def _create_log_directories(self, project_root: str):
        """创建日志目录结构"""
        log_dirs = [
            "logs/general",      # 通用日志
            "logs/test",         # 测试日志
            "logs/debug",        # 调试日志
            "logs/error",        # 错误日志
            "logs/performance",  # 性能日志
            "logs/archive"       # 归档日志
        ]

        for log_dir in log_dirs:
            full_path = os.path.join(project_root, log_dir)
            FileUtils.ensure_dir(full_path)

    def _console_filter(self, record):
        """控制台日志过滤器，只显示重要信息"""
        # 只在控制台显示INFO及以上级别的日志
        return record["level"].no >= 20  # INFO=20, WARNING=30, ERROR=40

    def _setup_categorized_loggers(self, project_root: str, log_config: dict, log_format: str, log_level: str):
        """设置分类日志处理器"""
        today = datetime.now().strftime("%Y%m%d")

        # 日志配置
        log_categories = {
            "general": {
                "path": f"logs/general/app_{today}.log",
                "rotation": "5 MB",
                "retention": "10 days",
                "level": log_level,
                "filter": lambda record: not any(keyword in str(record["message"]).lower()
                                                for keyword in ["test", "debug", "error", "performance"])
            },
            "test": {
                "path": f"logs/test/test_{today}.log",
                "rotation": "10 MB",
                "retention": "7 days",
                "level": log_level,
                "filter": lambda record: "test" in str(record["message"]).lower() or
                                        "pytest" in str(record["name"]).lower() or
                                        "testcase" in str(record["name"]).lower()
            },
            "debug": {
                "path": f"logs/debug/debug_{today}.log",
                "rotation": "3 MB",
                "retention": "5 days",
                "level": "DEBUG",
                "filter": lambda record: record["level"].name == "DEBUG" or
                                        "debug" in str(record["message"]).lower()
            },
            "error": {
                "path": f"logs/error/error_{today}.log",
                "rotation": "5 MB",
                "retention": "30 days",
                "level": "ERROR",
                "filter": lambda record: record["level"].no >= 40  # ERROR=40, CRITICAL=50
            },
            "performance": {
                "path": f"logs/performance/perf_{today}.log",
                "rotation": "2 MB",
                "retention": "14 days",
                "level": log_level,
                "filter": lambda record: any(keyword in str(record["message"]).lower()
                                            for keyword in ["performance", "timing", "speed", "duration", "响应时间"])
            }
        }

        # 添加分类日志处理器
        for category, config in log_categories.items():
            log_file = os.path.join(project_root, config["path"])

            logger.add(
                log_file,
                format=log_format,
                level=config["level"],
                rotation=config["rotation"],
                retention=config["retention"],
                encoding="utf-8",
                filter=config["filter"],
                enqueue=True  # 异步写入，提高性能
            )
    
    @staticmethod
    def get_logger():
        """
        获取日志实例

        Returns:
            logger: loguru日志实例
        """
        Logger()  # 确保初始化
        return logger

    @staticmethod
    def get_test_logger():
        """获取测试专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="test")

    @staticmethod
    def get_debug_logger():
        """获取调试专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="debug")

    @staticmethod
    def get_performance_logger():
        """获取性能专用日志器"""
        Logger()  # 确保初始化
        return logger.bind(category="performance")

    @staticmethod
    def log_test_start(test_name: str):
        """记录测试开始"""
        logger.info(f"🧪 测试开始: {test_name}")

    @staticmethod
    def log_test_end(test_name: str, success: bool, duration: float = None):
        """记录测试结束"""
        status = "✅ 成功" if success else "❌ 失败"
        duration_str = f", 耗时: {duration:.2f}秒" if duration else ""
        logger.info(f"🏁 测试结束: {test_name} - {status}{duration_str}")

    @staticmethod
    def log_performance(operation: str, duration: float, details: str = ""):
        """记录性能信息"""
        logger.info(f"⚡ 性能记录: {operation} 耗时 {duration:.3f}秒 {details}")

    @staticmethod
    def log_step(step_name: str):
        """记录测试步骤"""
        logger.info(f"📋 执行步骤: {step_name}")

    @staticmethod
    def log_error_with_screenshot(error_msg: str, screenshot_path: str = None):
        """记录错误并关联截图"""
        if screenshot_path:
            logger.error(f"❌ 错误: {error_msg} | 截图: {screenshot_path}")
        else:
            logger.error(f"❌ 错误: {error_msg}")

    @staticmethod
    def cleanup_old_logs(days: int = 30):
        """清理旧日志文件"""
        try:
            project_root = YamlUtils.get_project_root()
            logs_dir = os.path.join(project_root, "logs")

            if not os.path.exists(logs_dir):
                return

            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            cleaned_count = 0
            for root, dirs, files in os.walk(logs_dir):
                for file in files:
                    if file.endswith('.log'):
                        file_path = os.path.join(root, file)
                        if os.path.getmtime(file_path) < cutoff_time:
                            try:
                                os.remove(file_path)
                                cleaned_count += 1
                            except Exception as e:
                                logger.debug(f"清理日志文件失败: {file_path}, 错误: {e}")

            if cleaned_count > 0:
                logger.info(f"🧹 清理了 {cleaned_count} 个超过 {days} 天的日志文件")

        except Exception as e:
            logger.error(f"清理日志文件时出错: {e}")


# 全局日志实例
log = Logger.get_logger()

# 专用日志实例
test_log = Logger.get_test_logger()
debug_log = Logger.get_debug_logger()
perf_log = Logger.get_performance_logger()
