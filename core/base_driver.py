"""
驱动基类
基于UIAutomator2的设备连接和管理
"""
import os
import uiautomator2 as u2
from typing import Optional, Dict, Any, List
from core.logger import log
from utils.yaml_utils import YamlUtils
from utils.uiautomator2_manager import uiautomator2_manager
from utils.device_discovery import device_discovery
from utils.device_config_manager import device_config_manager


class BaseDriver:
    """设备驱动基类"""
    
    _instance = None
    _driver = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化驱动"""
        if self._driver is None:
            self._connect_device()
    
    def _connect_device(self) -> None:
        """连接设备"""
        try:
            # 加载设备配置
            device_config = self._load_device_config()
            device_id = device_config.get("device_id", "")
            device_name = device_config.get("device_name", "Unknown Device")
            platform_version = device_config.get("platform_version", "")

            # 连接设备前先检查UIAutomator2服务
            if device_id:
                log.info(f"正在连接指定设备: {device_name} ({device_id})")
                self._ensure_uiautomator2_service(device_id)
                self._driver = u2.connect(device_id)
            else:
                log.info("正在连接默认设备")
                self._ensure_uiautomator2_service()
                self._driver = u2.connect()

            # 获取设备信息
            device_info = self._driver.device_info
            log.info(f"设备连接成功: {device_info}")

            # 验证设备信息是否匹配配置
            self._verify_device_info(device_info, device_config)

            # 设置隐式等待
            self._setup_implicit_wait()

        except Exception as e:
            log.error(f"设备连接失败: {e}")
            # 如果连接失败，尝试修复版本相关问题
            if "Invalid version" in str(e) or "version" in str(e).lower():
                log.info("检测到版本相关错误，尝试修复...")
                try:
                    # 使用UIAutomator2管理器修复版本问题
                    if uiautomator2_manager.fix_version_issue(device_id if device_id else None):
                        # 重新尝试连接
                        if device_id:
                            self._driver = u2.connect(device_id)
                        else:
                            self._driver = u2.connect()
                        log.info("✅ 版本问题修复后连接成功")

                        # 重新设置隐式等待
                        self._setup_implicit_wait()
                        return
                    else:
                        log.error("❌ 版本问题修复失败")
                except Exception as retry_e:
                    log.error(f"修复版本问题后仍然连接失败: {retry_e}")
            raise
    
    def _load_device_config(self) -> Dict[str, Any]:
        """加载设备配置"""
        try:
            # 首先主动识别当前连接的设备并更新配置
            self._discover_and_update_current_device()

            config_path = YamlUtils.get_config_path("devices.yaml")
            devices_config = YamlUtils.load_yaml(config_path)

            current_device = devices_config.get("current_device", "default")
            device_config = devices_config.get("devices", {}).get(current_device, {})

            if device_config:
                log.info(f"加载设备配置: {current_device}")
                log.info(f"  设备名称: {device_config.get('device_name', '')}")
                log.info(f"  设备ID: {device_config.get('device_id', '')}")
                log.info(f"  HiOS版本: {device_config.get('platform_version', '')}")
            else:
                log.warning(f"设备配置 '{current_device}' 不存在，使用默认配置")

            return device_config

        except Exception as e:
            log.warning(f"设备配置加载失败，使用默认配置: {e}")
            return {}

    def _verify_device_info(self, actual_info: Dict[str, Any], expected_config: Dict[str, Any]) -> None:
        """
        验证实际连接的设备是否与配置匹配

        Args:
            actual_info: 实际设备信息
            expected_config: 期望的设备配置
        """
        try:
            if not expected_config.get("device_id"):
                # 如果配置中没有device_id，跳过验证
                return

            # 获取实际设备的序列号
            actual_serial = actual_info.get("serial", "")
            expected_serial = expected_config.get("device_id", "")

            if actual_serial and expected_serial and actual_serial != expected_serial:
                log.warning(f"设备序列号不匹配:")
                log.warning(f"  期望: {expected_serial}")
                log.warning(f"  实际: {actual_serial}")
                log.warning("可能连接了错误的设备，请检查设备配置")
            else:
                log.info("✅ 设备验证通过")

        except Exception as e:
            log.debug(f"设备信息验证失败: {e}")

    def _ensure_uiautomator2_service(self, device_id: Optional[str] = None) -> None:
        """
        确保UIAutomator2服务正常运行

        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        try:
            log.info("检查UIAutomator2服务状态...")

            # 使用UIAutomator2管理器检查服务健康状态
            if not uiautomator2_manager.check_service_health(device_id):
                log.warning("UIAutomator2服务状态异常，尝试重启...")
                self._restart_uiautomator2_service(device_id)
            else:
                log.info("✅ UIAutomator2服务状态正常")

        except Exception as e:
            log.warning(f"检查UIAutomator2服务状态失败: {e}")

    def _restart_uiautomator2_service(self, device_id: Optional[str] = None) -> None:
        """
        重启UIAutomator2服务

        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        try:
            log.info("重启UIAutomator2服务...")

            # 使用UIAutomator2管理器重启服务
            if uiautomator2_manager.restart_service(device_id):
                log.info("✅ UIAutomator2服务重启成功")
            else:
                log.error("❌ UIAutomator2服务重启失败")
                raise Exception("UIAutomator2服务重启失败")

        except Exception as e:
            log.error(f"重启UIAutomator2服务失败: {e}")
            raise

    def _setup_implicit_wait(self) -> None:
        """设置隐式等待"""
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            config = YamlUtils.load_yaml(config_path)
            timeout = config.get("app", {}).get("default_timeout", 10)
            
            # 设置元素查找超时时间
            self._driver.implicitly_wait(timeout)
            log.info(f"设置隐式等待时间: {timeout}秒")
            
        except Exception as e:
            log.warning(f"隐式等待设置失败: {e}")
    
    @property
    def driver(self) -> u2.Device:
        """
        获取驱动实例
        
        Returns:
            u2.Device: UIAutomator2设备实例
        """
        if self._driver is None:
            self._connect_device()
        return self._driver
    
    def get_device_info(self) -> Dict[str, Any]:
        """
        获取设备信息
        
        Returns:
            Dict: 设备信息字典
        """
        return self.driver.device_info
    
    def get_window_size(self) -> tuple:
        """
        获取屏幕尺寸
        
        Returns:
            tuple: (width, height)
        """
        info = self.driver.window_size()
        return info[0], info[1]
    
    def start_app(self, package_name: str) -> None:
        """
        启动应用
        
        Args:
            package_name: 应用包名
        """
        try:
            log.info(f"启动应用: {package_name}")
            self.driver.app_start(package_name)
        except Exception as e:
            log.error(f"启动应用失败: {e}")
            raise
    
    def stop_app(self, package_name: str) -> None:
        """
        停止应用
        
        Args:
            package_name: 应用包名
        """
        try:
            log.info(f"停止应用: {package_name}")
            self.driver.app_stop(package_name)
        except Exception as e:
            log.error(f"停止应用失败: {e}")
            raise
    
    def is_app_running(self, package_name: str) -> bool:
        """
        检查应用是否在运行
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否在运行
        """
        try:
            return self.driver.app_current().get("package") == package_name
        except Exception as e:
            log.error(f"检查应用状态失败: {e}")
            return False
    
    def screenshot(self, filename: Optional[str] = None, use_test_class_dir: bool = True) -> str:
        """
        截图

        Args:
            filename: 截图文件名，为None时自动生成
            use_test_class_dir: 是否使用测试类名称作为子目录

        Returns:
            str: 截图文件路径
        """
        try:
            from utils.file_utils import FileUtils

            if filename is None:
                filename = FileUtils.get_screenshot_name()

            # 获取截图保存路径
            config_path = YamlUtils.get_config_path("config.yaml")
            config = YamlUtils.load_yaml(config_path)
            base_screenshot_dir = config.get("app", {}).get("screenshot_path", "reports/screenshots")

            # 根据是否使用测试类目录来确定最终目录
            if use_test_class_dir:
                screenshot_dir = FileUtils.get_screenshot_dir_by_test_class(base_screenshot_dir)
            else:
                screenshot_dir = base_screenshot_dir

            # 确保目录存在
            project_root = YamlUtils.get_project_root()
            full_dir = os.path.join(project_root, screenshot_dir)
            FileUtils.ensure_dir(full_dir)

            # 截图
            screenshot_path = os.path.join(full_dir, filename)
            self.driver.screenshot(screenshot_path)

            log.info(f"截图保存: {screenshot_path}")
            return screenshot_path

        except Exception as e:
            log.error(f"截图失败: {e}")
            raise
    
    def _discover_and_update_current_device(self) -> None:
        """
        主动识别当前连接的设备并更新配置
        如果设备不在配置中则新增，如果已存在则设置为当前设备
        """
        try:
            log.info("🔍 主动识别当前连接的设备...")

            # 发现当前连接的设备
            discovered_devices = device_discovery.discover_all_devices()

            if not discovered_devices:
                log.warning("未发现任何连接的设备，跳过设备配置更新")
                return

            # 取第一个发现的设备作为当前设备
            current_device_info = discovered_devices[0]
            device_id = current_device_info.get('device_id', '')
            device_name = current_device_info.get('device_name', 'Unknown Device')

            log.info(f"发现设备: {device_name} ({device_id})")

            # 加载当前配置
            config_path = YamlUtils.get_config_path("devices.yaml")
            devices_config = YamlUtils.load_yaml(config_path)

            # 查找设备是否已存在于配置中
            existing_config_name = self._find_device_config_by_id(devices_config, device_id)

            if existing_config_name:
                # 设备已存在，直接设置为当前设备
                log.info(f"设备已存在于配置中: {existing_config_name}")
                if devices_config.get('current_device') != existing_config_name:
                    devices_config['current_device'] = existing_config_name
                    YamlUtils.save_yaml(devices_config, config_path)
                    log.info(f"✅ 已将设备 '{existing_config_name}' 设置为当前设备")
                else:
                    log.info(f"✅ 设备 '{existing_config_name}' 已是当前设备")
            else:
                # 设备不存在，新增设备配置
                log.info("设备不存在于配置中，正在新增...")
                success = device_config_manager.update_config_with_discovered_devices(
                    [current_device_info],
                    set_as_current=True
                )
                if success:
                    log.info("✅ 新设备配置已添加并设置为当前设备")
                else:
                    log.warning("❌ 新增设备配置失败")

        except Exception as e:
            log.warning(f"识别和更新当前设备失败: {e}")

    def _find_device_config_by_id(self, devices_config: Dict[str, Any], device_id: str) -> Optional[str]:
        """
        根据设备ID查找现有的设备配置名称

        Args:
            devices_config: 设备配置字典
            device_id: 要查找的设备ID

        Returns:
            Optional[str]: 找到的配置名称，未找到返回None
        """
        try:
            devices = devices_config.get('devices', {})

            for config_name, device_config in devices.items():
                if device_config.get('device_id') == device_id:
                    return config_name

            return None

        except Exception as e:
            log.debug(f"查找设备配置失败: {e}")
            return None

    def quit(self) -> None:
        """退出驱动"""
        if self._driver:
            log.info("关闭设备连接")
            # UIAutomator2不需要显式关闭连接
            self._driver = None


# 全局驱动实例
driver_manager = BaseDriver()
