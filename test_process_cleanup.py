#!/usr/bin/env python3
"""
测试进程清理功能的脚本
验证优化后的 clear_all_running_processes 方法
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def check_running_apps():
    """检查当前运行的应用"""
    try:
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "mResumedActivity"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0:
            log.info(f"当前活动应用: {result.stdout.strip()}")
        else:
            log.warning("无法获取当前活动应用信息")
            
        # 检查后台应用
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "recents"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result2.returncode == 0:
            lines = result2.stdout.split('\n')[:10]  # 只显示前10行
            log.info("最近应用列表:")
            for line in lines:
                if line.strip():
                    log.info(f"  {line.strip()}")
                    
    except Exception as e:
        log.error(f"检查运行应用失败: {e}")


def test_google_maps_cleanup():
    """测试 Google Maps 清理"""
    try:
        log.info("🗺️ 启动 Google Maps 进行测试...")
        
        # 启动 Google Maps
        subprocess.run(
            ["adb", "shell", "am", "start", "-n", "com.google.android.apps.maps/.MapsActivity"],
            capture_output=True,
            text=True,
            timeout=10
        )
        time.sleep(3)
        
        log.info("Google Maps 已启动，检查运行状态...")
        check_running_apps()
        
        # 执行清理
        log.info("🧹 开始执行清理...")
        base_test = BaseEllaTest()
        base_test.clear_all_running_processes()
        
        time.sleep(2)
        log.info("清理完成，再次检查运行状态...")
        check_running_apps()
        
        # 验证 Google Maps 是否被清理
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "maps"],
            capture_output=True,
            text=True,
            timeout=5,
            shell=True
        )
        
        if "maps" not in result.stdout.lower():
            log.info("✅ Google Maps 已成功清理")
        else:
            log.warning("⚠️ Google Maps 可能仍在运行")
            
    except Exception as e:
        log.error(f"测试 Google Maps 清理失败: {e}")


def test_multiple_apps_cleanup():
    """测试多个应用的清理"""
    try:
        log.info("📱 启动多个应用进行测试...")
        
        # 启动多个应用
        apps_to_start = [
            ("com.google.android.apps.maps", "Google Maps"),
            ("com.android.chrome", "Chrome"),
            ("com.android.settings", "Settings")
        ]
        
        for package, name in apps_to_start:
            try:
                subprocess.run(
                    ["adb", "shell", "monkey", "-p", package, "-c", "android.intent.category.LAUNCHER", "1"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                log.info(f"启动应用: {name}")
                time.sleep(1)
            except:
                log.warning(f"启动 {name} 失败")
        
        time.sleep(2)
        log.info("所有应用已启动，检查运行状态...")
        check_running_apps()
        
        # 执行清理
        log.info("🧹 开始执行清理...")
        base_test = BaseEllaTest()
        base_test.clear_all_running_processes()
        
        time.sleep(3)
        log.info("清理完成，最终检查运行状态...")
        check_running_apps()
        
    except Exception as e:
        log.error(f"测试多应用清理失败: {e}")


def main():
    """主函数"""
    log.info("🚀 开始测试进程清理功能...")
    
    # 检查 ADB 连接
    try:
        result = subprocess.run(
            ["adb", "devices"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if "device" not in result.stdout:
            log.error("❌ 未检测到 ADB 设备连接")
            return
        else:
            log.info("✅ ADB 设备连接正常")
            
    except Exception as e:
        log.error(f"❌ ADB 连接检查失败: {e}")
        return
    
    # 测试1: Google Maps 清理
    log.info("\n" + "="*50)
    log.info("测试1: Google Maps 清理")
    log.info("="*50)
    test_google_maps_cleanup()
    
    time.sleep(5)
    
    # 测试2: 多应用清理
    log.info("\n" + "="*50)
    log.info("测试2: 多应用清理")
    log.info("="*50)
    test_multiple_apps_cleanup()
    
    log.info("\n🎉 进程清理功能测试完成!")


if __name__ == "__main__":
    main()
